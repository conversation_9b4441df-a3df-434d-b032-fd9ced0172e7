{"bazel.projectview.open": false, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, ".eclipse": false, ".git": false, ".vscode": false, ".zerorules": false, "3rdparty": false, "daip-patcher-doc": false, "daip-patcher-handler": false, "daip-patcher-init": false, "daip-patcher-iui": false, "daip-patcher-service": false, "daip-patcher-swagger": false, "daip-patcher-task-worker": false, "daip-patcher-test-case": false, "daip-patcher-testcase": false, "design": false, "docs": false, "20250625_03": false, "20250625_04": false, "20250625_05": false}}