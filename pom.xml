<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zte.daip.manager</groupId>
        <artifactId>daip-dependencies</artifactId>
        <version>15.5.1-SNAPSHOT</version>
    </parent>

    <groupId>com.zte.daip.manager.patcher</groupId>
    <artifactId>daip-patcher</artifactId>
    <version>deletePatch</version>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>

        <depend.svr.version>15.5.1-SNAPSHOT</depend.svr.version>

        <maven.build.timestamp.format>yyMMddHHmm</maven.build.timestamp.format>
        <paas.service.version>${product.version}.n${maven.build.timestamp}</paas.service.version>

        <service.name>daip-patcher</service.name>
        <ms.market_version.name>${service.name}-market-${product.version}</ms.market_version.name>
        <iui.microservice.name>daip-patcher-iui</iui.microservice.name>
        <svr.microservice.name>daip-patcher-svr</svr.microservice.name>
        <handler.microservice.name>daip-patcher-handler</handler.microservice.name>
        <iui.common.microservice.name>daip-common-iui</iui.common.microservice.name>
        <init.microservice.name>daip-patcher-init</init.microservice.name>
        <ms.release_version.name>${product.id}_PATCHER_${product.version}</ms.release_version.name>
        <non.root.uid>3001</non.root.uid>

    </properties>

    <modules>
        <module>daip-patcher-iui</module>
        <module>daip-patcher-handler</module>
        <module>daip-patcher-init</module>
        <module>daip-patcher-service</module>
        <module>daip-patcher-task-worker</module>
        <module>daip-patcher-swagger</module>
    </modules>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zte.daip.manager</groupId>
                <artifactId>daip-dependencies</artifactId>
                <version>${depend.svr.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.awaitility</groupId>
                <artifactId>awaitility</artifactId>
                <version>4.0.3</version>
                <scope>test</scope>
            </dependency>
        </dependencies>

    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-sensitive-log</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-security</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-utils-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-common-utils</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-logback-appender</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-deployer-client-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-communication</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-deployer-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-cache-common</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-init-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-utils-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-utils-msb</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-httpclient</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-communication-replyproducer</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-configcenter-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-monitor-client-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-monitor-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-i18n-tcf</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>https://artnj.zte.com.cn:443/artifactory/dap-release-maven/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>https://artnj.zte.com.cn:443/artifactory/dap-snapshot-maven/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>3.7.0.1746</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.11</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- no argLine here -->
                </configuration>
            </plugin>
        </plugins>

    </build>

    <profiles>
        <profile>
            <id>coverage</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>prepare-agent</id>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>report</id>
                                <goals>
                                    <goal>report</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>

