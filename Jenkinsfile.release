node('manager_common_build') {
  timestamps {
    stage('Preparation') {
      checkout scm
    }

    def lib = load pwd() + "/Jenkinsfile.lib.groovy"

    stage('Build') {
      lib.clean()
      lib.build()
    }

    stage('Sonar Analysis') {
      //lib.sonar_analysis()
    }

    try {
      stage('Deploy') {
        retry_with_delay(count: 2, delay: 90) {
          lib.deploy()
        }
      }

      stage('Smoke Tests') {
        retry_with_delay(count: 2, delay: 10) {
          lib.run_smoke_tests()
        }
      }
    } finally {
      lib.undeploy()
    }

    stage('make patch') {
          try{
            lib.download_base_version()
            lib.make_patch()
            out=sh(script:"ls ${WORKSPACE}/patchTemp/DAP-AGENT-${product_release}-${patch_number}-${patch_date}.zip",returnStatus:true)
		    if(out !=0)
		        return
	        lib.upload_patch()
           }finally{
		     lib.clean_patch()
		 }
    }

    stage('Publish') {
      parallel(
        artifacts: {
          lib.upload_artifacts()
        },
        jars: {
          lib.deploy_jars()
        }
      )
    }
  }
}
