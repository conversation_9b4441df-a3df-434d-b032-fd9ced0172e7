def clean() {
    sh './start-build-env.sh mvn clean -Dmaven.repo.local=${WORKSPACE}/.m2/repository.manager'
}

def scanForcoverity() {

    sh "${COVERITY_HOME}/cov-configure --java"
    sh "${COVERITY_HOME}/cov-build --dir ${WORKSPACE}/output/coverity mvn -U package  -Dmaven.test.failure.ignore=true -Dmaven.repo.local=${WORKSPACE}/.m2/repository.manager"
    sh "${COVERITY_HOME}/cov-analyze --dir ${WORKSPACE}/output/coverity  --android-security --webapp-security --enable-fb --enable-constraint-fpp --enable-callgraph-metrics --enable ATOMICITY --enable ORM_LOST_UPDATE --enable USE_AFTER_FREE"

    //generate json
    sh "${COVERITY_HOME}/cov-format-errors --dir ${WORKSPACE}/output/coverity/ --json-output-v6 ${WORKSPACE}/output/coverity/coverity-report.json"

    //export excel and calucate threshold
    sh "python ${WORKSPACE}/coverityReport.py  ${WORKSPACE}/output/coverity/coverity-report.json ${COVERITY_REPORT_HOME}  ${projectName}  ${HighNumber}  ${MediumNumber}"

}
def commitReport(){
    sh "${COVERITY_HOME}/cov-commit-defects --dir ${WORKSPACE}/output/coverity --host ${cov_address} --user ${cov_username} --password ${cov_password} --stream ${projectName}"
}

return this
