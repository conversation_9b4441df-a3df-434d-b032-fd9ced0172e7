options:
  resolverType: "coursier"
  resolvers:
    - id: "proxy.repository"
      type: "default"
      url: "https://artnj.zte.com.cn:443/artifactory/daip-maven-virtual/"
    - id: "public"
      type: "default"
      url: "https://artnj.zte.com.cn:443/artifactory/daip-maven-virtual/"
  authFile: //:.netrc
dependencies:
  antlr:
    antlr:jar:
      lang: java
      version: "2.7.7"
  ch.qos.logback:
    logback-access:jar:
      lang: java
      version: "1.2.3"
    logback-classic:jar:
      lang: java
      version: "1.2.3"
    logback-core:jar:
      lang: java
      version: "1.2.3"
  com.101tec:
    zkclient:jar:
      lang: java
      version: "0.11"
  com.alibaba:
    cooma:jar:
      lang: java
      version: "0.4.1"
    fastjson:jar:
      lang: java
      version: "1.2.83"
  com.auth0:
    java-jwt:jar:
      lang: java
      version: "3.19.2"
  com.fasterxml:
    classmate:jar:
      lang: java
      version: "1.5.1"
  com.fasterxml.jackson.core:
    jackson-annotations:jar:
      lang: java
      version: "2.13.2"
    jackson-core:jar:
      lang: java
      version: "2.13.2"
    jackson-databind:jar:
      lang: java
      version: "2.13.5"
  com.fasterxml.jackson.dataformat:
    jackson-dataformat-yaml:jar:
      lang: java
      version: "2.13.2"
  com.fasterxml.jackson.datatype:
    jackson-datatype-guava:jar:
      lang: java
      version: "2.10.5"
    jackson-datatype-jdk8:jar:
      lang: java
      version: "2.10.5"
    jackson-datatype-joda:jar:
      lang: java
      version: "2.10.5"
    jackson-datatype-jsr310:jar:
      lang: java
      version: "2.10.5"
  com.fasterxml.jackson.jaxrs:
    jackson-jaxrs-base:jar:
      lang: java
      version: "2.10.5"
    jackson-jaxrs-json-provider:jar:
      lang: java
      version: "2.10.5"
  com.fasterxml.jackson.module:
    jackson-module-afterburner:jar:
      lang: java
      version: "2.10.5"
    jackson-module-jaxb-annotations:jar:
      lang: java
      version: "2.10.5"
    jackson-module-parameter-names:jar:
      lang: java
      version: "2.10.5"
  com.github.ben-manes.caffeine:
    caffeine:jar:
      lang: java
      version: "2.8.8"
  com.github.loki4j:
    loki-logback-appender-jdk8:jar:
      lang: java
      version: "1.3.0-rc1"
  com.github.luben:
    zstd-jni:jar:
      lang: java
      version: "1.3.7-1"
  com.google.code.findbugs:
    jsr305:jar:
      lang: java
      version: "3.0.2"
  com.google.errorprone:
    error_prone_annotations:jar:
      lang: java
      version: "2.1.3"
  com.google.guava:
    failureaccess:jar:
      lang: java
      version: "1.0.1"
    guava:jar:
      lang: java
      version: "32.0.1-jre"
    listenablefuture:jar:
      lang: java
      version: "9999.0-empty-to-avoid-conflict-with-guava"
  com.google.j2objc:
    j2objc-annotations:jar:
      lang: java
      version: "1.1"
  com.googlecode.java-ipv6:
    java-ipv6:jar:
      lang: java
      version: "0.17"
  com.helger:
    profiler:jar:
      lang: java
      version: "1.1.1"
  com.io7m.xom:
    xom:jar:
      lang: java
      version: "1.2.10"
  com.jayway.jsonpath:
    json-path:jar:
      lang: java
      version: "2.4.0"
  com.lmax:
    disruptor:jar:
      lang: java
      version: "3.4.4"
  com.mikesamuel:
    json-sanitizer:jar:
      lang: java
      version: "1.2.2"
  com.netflix.archaius:
    archaius-core:jar:
      lang: java
      version: "0.7.7"
  com.netflix.hystrix:
    hystrix-core:jar:
      lang: java
      version: "1.5.18"
  com.networknt:
    json-schema-validator:jar:
      lang: java
      version: "1.0.50"
  com.squareup.okhttp3:
    okhttp:jar:
      lang: java
      version: "3.12.0"
  com.squareup.okio:
    okio:jar:
      lang: java
      version: "1.17.6"
  com.squareup.retrofit2:
    retrofit:jar:
      lang: java
      version: "2.5.0"
  com.sun.activation:
    jakarta.activation:jar:
      lang: java
      version: "1.2.2"
  com.sun.istack:
    istack-commons-runtime:jar:
      lang: java
      version: "3.0.11"
  com.typesafe.scala-logging:
    scala-logging_2.12:jar:
      lang: java
      version: "3.9.0"
  com.vaadin.external.google:
    android-json:jar:
      lang: java
      version: "0.0.20131108.vaadin1"
  com.yammer.metrics:
    metrics-core:jar:
      lang: java
      version: "2.2.0"
  com.zaxxer:
    HikariCP:jar:
      lang: java
      version: "3.2.0"
  com.zte.daip.manager.common:
    daip-alarm-api:jar:
      lang: java
      version: "14.4.1-20240423.033653-7"
    daip-alarm-client-paas:jar:
      lang: java
      version: "14.4.1-20240423.033657-7"
    daip-cache-common:jar:
      lang: java
      version: "14.4.1-20240423.033543-7"
    daip-cache-paas:jar:
      lang: java
      version: "14.4.1-20240423.033644-7"
    daip-common-client-paas:jar:
      lang: java
      version: "14.4.1-20240423.033431-7"
    daip-common-i18n:jar:
      lang: java
      version: "14.4.1-20240423.033435-7"
    daip-common-utils:jar:
      lang: java
      version: "14.4.1-20240423.033422-7"
    daip-communication:jar:
      lang: java
      version: "14.4.1-20240423.033610-7"
    daip-communication-common:jar:
      lang: java
      version: "14.4.1-20240423.033550-7"
    daip-communication-kafka:jar:
      lang: java
      version: "14.4.1-20240423.033538-7"
    daip-communication-replyproducer:jar:
      lang: java
      version: "14.4.1-20240423.033617-7"
    daip-configcenter-api:jar:
      lang: java
      version: "14.4.1-20240423.033444-7"
    daip-configcenter-client-paas:jar:
      lang: java
      version: "14.4.1-20240423.033449-7"
    daip-deployer-api:jar:
      lang: java
      version: "14.4.1-20240423.033508-7"
    daip-deployer-client-paas:jar:
      lang: java
      version: "14.4.1-20240423.033519-7"
    daip-event-api:jar:
      lang: java
      version: "14.4.1-20240423.033737-7"
    daip-event-beans:jar:
      lang: java
      version: "14.4.1-20240423.033732-7"
    daip-event-reporter-api:jar:
      lang: java
      version: "14.4.1-20240423.033742-7"
    daip-event-reporter-client:jar:
      lang: java
      version: "14.4.1-20240423.033748-7"
    daip-filemanagement-api:jar:
      lang: java
      version: "14.4.1-20240423.034112-7"
    daip-filemanagement-client-paas:jar:
      lang: java
      version: "14.4.1-20240423.034115-7"
    daip-filemanagement-common-util:jar:
      lang: java
      version: "14.4.1-20240423.033728-7"
    daip-filemanagement-download-provider:jar:
      lang: java
      version: "14.4.1-20240423.034124-7"
    daip-http-auth:jar:
      lang: java
      version: "14.4.1-20240423.033724-7"
    daip-httpclient:jar:
      lang: java
      version: "14.4.1-20240423.033810-7"
    daip-init-paas:jar:
      lang: java
      version: "14.4.1-20240423.033636-7"
    daip-inspection-api:jar:
      lang: java
      version: "14.4.1-20240423.033857-7"
    daip-inspection-client-paas:jar:
      lang: java
      version: "14.4.1-20240423.033907-7"
    daip-inspection-common:jar:
      lang: java
      version: "14.4.1-20240423.033849-7"
    daip-logback-appender:jar:
      lang: java
      version: "14.4.1-20240423.033408-8"
    daip-patcher-api:jar:
      lang: java
      version: "14.4.1-20240423.033456-7"
    daip-patcher-impl-client-paas:jar:
      lang: java
      version: "14.4.1-20240423.033720-7"
    daip-response:jar:
      lang: java
      version: "14.4.1-20240423.034012-7"
    daip-security:jar:
      lang: java
      version: "14.4.1-20240423.033820-7"
    daip-sensitive-log:jar:
      lang: java
      version: "14.4.1-20240423.033406-8"
    daip-task-api:jar:
      lang: java
      version: "14.4.1-20240423.034042-7"
    daip-task-client:jar:
      lang: java
      version: "14.4.1-20240423.034052-7"
    daip-task-common-bean:jar:
      lang: java
      version: "14.4.1-20240423.034032-7"
    daip-task-message:jar:
      lang: java
      version: "14.4.1-20240423.034036-7"
    daip-task-worker:jar:
      lang: java
      version: "14.4.1-20240423.034048-7"
    daip-utils-msb:jar:
      lang: java
      version: "14.4.1-20240423.033815-7"
    daip-utils-paas:jar:
      lang: java
      version: "14.4.1-20240423.033631-7"
    daip-whale-api:jar:
      lang: java
      version: "14.4.1-20240423.034135-7"
    daip-whale-client-paas:jar:
      lang: java
      version: "14.4.1-20240423.034138-7"
    spring-message-resource-starter:jar:
      lang: java
      version: "14.4.1-20240423.034008-7"
  com.zte.daip.manager.miniagent:
    daip-miniagent-seed:jar:
      lang: java
      version: "14.4.1-20240423.044309-2"
  com.zte.daip.manager.patcher:
    daip-patcher-application:jar:
      lang: java
      version: "14.4.1-20240423.072423-7"
    daip-patcher-domain:jar:
      lang: java
      version: "14.4.1-20240423.072410-7"
    daip-patcher-handler-api:jar:
      lang: java
      version: "14.4.1-20240423.072244-7"
    daip-patcher-impl-inner-client-paas:jar:
      lang: java
      version: "14.4.1-20240423.072253-7"
    daip-patcher-infrastructure-api:jar:
      lang: java
      version: "14.4.1-20240423.072348-7"
    daip-patcher-inner-api:jar:
      lang: java
      version: "14.4.1-20240423.072242-7"
    daip-patcher-interfaces:jar:
      lang: java
      version: "14.4.1-20240423.072429-7"
    daip-patcher-task-worker:jar:
      lang: java
      version: "14.4.1-20240423.072438-7"
  com.zte.oes.dexcloud:
    dexcloud-springboot-common-utils:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-commons-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-configcenter-commons:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-configcenter-configclient:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-datetime-utils:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-db-jdbc-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-gr-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-initckeck-utils:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-internalcontrol-agent-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-jersey-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-jersey-swagger-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-jetty-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-kafka-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-msb-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-redis-redisson-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-redis-redisson-mult-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-rpc-retrofit-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-sm-impl:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-commons:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-configcenter:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-db-jdbc:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-gr:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-internalcontrol-agent:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-jersey:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-jetty:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-kafka:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-msb:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-redis-redisson:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-rpc-retrofit:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-sm:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-utils-service:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-starter-web:jar:
      lang: java
      version: "**********"
    dexcloud-springboot-uiframe-agent-impl:jar:
      lang: java
      version: "**********"
    redisson:jar:
      lang: java
      version: "3.14.0"
  com.zte.ums.zenap.dropwizard.ext:
    zenap-dropwizard-ext:jar:
      lang: java
      version: "**********"
  com.zte.ums.zenap.hk2:
    zenap-hk2:jar:
      lang: java
      version: "**********"
    zenap-hk2-core:jar:
      lang: java
      version: "**********"
    zenap-hk2-core:pom:
      lang: java
      version: "**********"
  com.zte.ums.zenap.httpclient:
    zenap-httpclient-retrofit:jar:
      lang: java
      version: "**********"
    zenap-httpclient-retrofit-circuitbreaker:jar:
      lang: java
      version: "**********"
    zenap-httpclient-retrofit-core:jar:
      lang: java
      version: "**********"
    zenap-httpclient-retrofit-istio:jar:
      lang: java
      version: "**********"
  com.zte.ums.zenap.i18n:
    zenap-i18n-core:jar:
      lang: java
      version: "**********"
  com.zte.ums.zenap.kafka:
    zenap-kafka-rule:jar:
      lang: java
      version: "**********"
  com.zte.ums.zenap.logback:
    zenap-logback-core:jar:
      lang: java
      version: "**********"
  com.zte.ums.zenap.msb.client:
    zenap-msb-client-bundle:jar:
      lang: java
      version: "**********"
    zenap-msb-client-core:jar:
      lang: java
      version: "**********"
  com.zte.ums.zenap.msb.components:
    msb-service:jar:
      lang: java
      version: "**********"
  com.zte.ums.zenap.okhttp:
    okhttp:jar:
      lang: java
      version: "3.12.0"
  com.zte.ums.zenap.util.cipher:
    zenap-center-cipher-springboot-agent:jar:
      lang: java
      version: "**********"
    zenap-cipher:jar:
      lang: java
      version: "**********"
    zenap-kms-agent-sdk:jar:
      lang: java
      version: "**********"
  com.zte.zdh:
    zdh-commons:jar:
      lang: java
      version: "14.4.1-20240423.005029-1"
  commons-beanutils:
    commons-beanutils:jar:
      lang: java
      version: "1.9.4"
  commons-codec:
    commons-codec:jar:
      lang: java
      version: "1.15"
  commons-collections:
    commons-collections:jar:
      lang: java
      version: "3.2.2"
  commons-configuration:
    commons-configuration:jar:
      lang: java
      version: "1.9"
  commons-fileupload:
    commons-fileupload:jar:
      lang: java
      version: "1.5"
  commons-io:
    commons-io:jar:
      lang: java
      version: "2.11.0"
  commons-lang:
    commons-lang:jar:
      lang: java
      version: "2.6"
  commons-logging:
    commons-logging:jar:
      lang: java
      version: "1.2"
  de.ruedigermoeller:
    fst:jar:
      lang: java
      version: "2.57"
  guru.nidi:
    jdepend:jar:
      lang: java
      version: "2.9.2"
  io.dropwizard:
    dropwizard-configuration:jar:
      lang: java
      version: "2.0.19"
    dropwizard-core:jar:
      lang: java
      version: "2.0.19"
    dropwizard-jackson:jar:
      lang: java
      version: "2.0.19"
    dropwizard-jersey:jar:
      lang: java
      version: "2.0.19"
    dropwizard-jetty:jar:
      lang: java
      version: "2.0.19"
    dropwizard-lifecycle:jar:
      lang: java
      version: "2.0.19"
    dropwizard-logging:jar:
      lang: java
      version: "2.0.19"
    dropwizard-metrics:jar:
      lang: java
      version: "2.0.19"
    dropwizard-request-logging:jar:
      lang: java
      version: "2.0.19"
    dropwizard-servlets:jar:
      lang: java
      version: "1.1.4"
    dropwizard-util:jar:
      lang: java
      version: "2.0.19"
    dropwizard-validation:jar:
      lang: java
      version: "2.0.19"
  io.dropwizard.logback:
    logback-throttling-appender:jar:
      lang: java
      version: "1.1.0"
  io.dropwizard.metrics:
    metrics-annotation:jar:
      lang: java
      version: "4.1.17"
    metrics-core:jar:
      lang: java
      version: "4.1.17"
    metrics-healthchecks:jar:
      lang: java
      version: "4.1.17"
    metrics-jersey2:jar:
      lang: java
      version: "4.1.17"
    metrics-jetty9:jar:
      lang: java
      version: "4.1.17"
    metrics-jmx:jar:
      lang: java
      version: "4.1.17"
    metrics-json:jar:
      lang: java
      version: "4.1.17"
    metrics-jvm:jar:
      lang: java
      version: "4.1.17"
    metrics-logback:jar:
      lang: java
      version: "4.1.17"
    metrics-servlets:jar:
      lang: java
      version: "4.1.17"
  io.github.hakky54:
    sslcontext-kickstart:jar:
      lang: java
      version: "8.1.2"
    sslcontext-kickstart-for-pem:jar:
      lang: java
      version: "8.1.2"
  io.github.openfeign:
    feign-core:jar:
      lang: java
      version: "10.10.1"
    feign-hystrix:jar:
      lang: java
      version: "10.10.1"
    feign-slf4j:jar:
      lang: java
      version: "10.10.1"
  io.github.resilience4j:
    resilience4j-circuitbreaker:jar:
      lang: java
      version: "0.12.0"
    resilience4j-circularbuffer:jar:
      lang: java
      version: "0.12.0"
    resilience4j-consumer:jar:
      lang: java
      version: "0.12.0"
    resilience4j-core:jar:
      lang: java
      version: "0.12.0"
    resilience4j-rxjava2:jar:
      lang: java
      version: "0.12.0"
  io.jsonwebtoken:
    jjwt:jar:
      lang: java
      version: "0.9.0"
  io.micrometer:
    micrometer-core:jar:
      lang: java
      version: "1.5.10"
  io.netty:
    netty-buffer:jar:
      lang: java
      version: "4.1.100.Final"
    netty-codec:jar:
      lang: java
      version: "4.1.100.Final"
    netty-codec-dns:jar:
      lang: java
      version: "4.1.100.Final"
    netty-common:jar:
      lang: java
      version: "4.1.100.Final"
    netty-handler:jar:
      lang: java
      version: "4.1.100.Final"
    netty-resolver:jar:
      lang: java
      version: "4.1.100.Final"
    netty-resolver-dns:jar:
      lang: java
      version: "4.1.100.Final"
    netty-transport:jar:
      lang: java
      version: "4.1.100.Final"
    netty-transport-classes-epoll:jar:
      lang: java
      version: "4.1.100.Final"
    netty-transport-native-epoll:jar:
      lang: java
      version: "4.1.100.Final"
    netty-transport-native-unix-common:jar:
      lang: java
      version: "4.1.100.Final"
  io.projectreactor:
    reactor-core:jar:
      lang: java
      version: "3.3.9.RELEASE"
  io.reactivex:
    rxjava:jar:
      lang: java
      version: "1.3.8"
  io.reactivex.rxjava2:
    rxjava:jar:
      lang: java
      version: "2.2.12"
  io.reactivex.rxjava3:
    rxjava:jar:
      lang: java
      version: "3.0.7"
  io.springfox:
    springfox-core:jar:
      lang: java
      version: "2.9.2"
    springfox-schema:jar:
      lang: java
      version: "2.9.2"
    springfox-spi:jar:
      lang: java
      version: "2.9.2"
    springfox-spring-web:jar:
      lang: java
      version: "2.9.2"
    springfox-swagger-common:jar:
      lang: java
      version: "2.9.2"
    springfox-swagger-ui:jar:
      lang: java
      version: "2.9.2"
    springfox-swagger2:jar:
      lang: java
      version: "2.9.2"
  io.swagger:
    swagger-annotations:jar:
      lang: java
      version: "1.5.18"
    swagger-core:jar:
      lang: java
      version: "1.5.18"
    swagger-jaxrs:jar:
      lang: java
      version: "1.5.18"
    swagger-jersey2-jaxrs:jar:
      lang: java
      version: "1.5.18"
    swagger-models:jar:
      lang: java
      version: "1.5.18"
  io.vavr:
    vavr:jar:
      lang: java
      version: "0.9.2"
    vavr-match:jar:
      lang: java
      version: "0.9.2"
  io.zipkin.brave:
    brave:jar:
      lang: java
      version: "5.12.7"
    brave-context-slf4j:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-http:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-httpasyncclient:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-httpclient:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-jms:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-kafka-clients:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-kafka-streams:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-messaging:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-rpc:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-servlet:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-spring-rabbit:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-spring-web:jar:
      lang: java
      version: "5.12.7"
    brave-instrumentation-spring-webmvc:jar:
      lang: java
      version: "5.12.7"
  io.zipkin.reporter2:
    zipkin-reporter:jar:
      lang: java
      version: "2.15.2"
    zipkin-reporter-brave:jar:
      lang: java
      version: "2.15.2"
    zipkin-reporter-metrics-micrometer:jar:
      lang: java
      version: "2.15.2"
  io.zipkin.zipkin2:
    zipkin:jar:
      lang: java
      version: "2.21.7"
  jakarta.activation:
    jakarta.activation-api:jar:
      lang: java
      version: "1.2.2"
  jakarta.annotation:
    jakarta.annotation-api:jar:
      lang: java
      version: "1.3.5"
  jakarta.persistence:
    jakarta.persistence-api:jar:
      lang: java
      version: "2.2.3"
  jakarta.servlet:
    jakarta.servlet-api:jar:
      lang: java
      version: "4.0.4"
  jakarta.transaction:
    jakarta.transaction-api:jar:
      lang: java
      version: "1.3.3"
  jakarta.validation:
    jakarta.validation-api:jar:
      lang: java
      version: "2.0.2"
  jakarta.websocket:
    jakarta.websocket-api:jar:
      lang: java
      version: "1.1.2"
  jakarta.ws.rs:
    jakarta.ws.rs-api:jar:
      lang: java
      version: "2.1.6"
  jakarta.xml.bind:
    jakarta.xml.bind-api:jar:
      lang: java
      version: "2.3.3"
  javax.cache:
    cache-api:jar:
      lang: java
      version: "1.1.1"
  javax.servlet:
    javax.servlet-api:jar:
      lang: java
      version: "4.0.1"
    servlet-api:jar:
      lang: java
      version: "3.1"
  javax.validation:
    validation-api:jar:
      lang: java
      version: "2.0.1.Final"
  joda-time:
    joda-time:jar:
      lang: java
      version: "2.10"
  junit:
    junit:jar:
      lang: java
      version: "4.13.1"
  net.bytebuddy:
    byte-buddy:jar:
      lang: java
      version: "1.10.14"
    byte-buddy-agent:jar:
      lang: java
      version: "1.10.19"
  net.minidev:
    accessors-smart:jar:
      lang: java
      version: "2.4.9"
    json-smart:jar:
      lang: java
      version: "2.4.10"
  net.sf.ezmorph:
    ezmorph:jar:
      lang: java
      version: "1.0.6"
  net.sf.jopt-simple:
    jopt-simple:jar:
      lang: java
      version: "5.0.4"
  net.sf.json-lib:
    json-lib:jar:jdk15:
      lang: java
      version: "2.4"
  net.sourceforge.argparse4j:
    argparse4j:jar:
      lang: java
      version: "0.8.1"
  org.apache-extras.beanshell:
    bsh:jar:
      lang: java
      version: "2.0b6"
  org.apache.commons:
    commons-collections4:jar:
      lang: java
      version: "4.4"
    commons-lang3:jar:
      lang: java
      version: "3.12.0"
    commons-text:jar:
      lang: java
      version: "1.10.0"
  org.apache.httpcomponents:
    httpasyncclient:jar:
      lang: java
      version: "4.1.4"
    httpclient:jar:
      lang: java
      version: "4.5"
    httpcore:jar:
      lang: java
      version: "4.4.15"
    httpcore-nio:jar:
      lang: java
      version: "4.4.14"
  org.apache.httpcomponents.client5:
    httpclient5:jar:
      lang: java
      version: "5.2.1"
  org.apache.httpcomponents.core5:
    httpcore5:jar:
      lang: java
      version: "5.2.3"
    httpcore5-h2:jar:
      lang: java
      version: "5.2"
  org.apache.kafka:
    kafka-clients:jar:
      lang: java
      version: "2.1.1"
    kafka_2.12:jar:
      lang: java
      version: "2.1.1"
  org.apache.logging.log4j:
    log4j-api:jar:
      lang: java
      version: "2.17.1"
    log4j-to-slf4j:jar:
      lang: java
      version: "2.17.1"
  org.apache.sshd:
    sshd-common:jar:
      lang: java
      version: "2.9.2"
    sshd-core:jar:
      lang: java
      version: "2.9.2"
    sshd-scp:jar:
      lang: java
      version: "2.9.2"
  org.apache.tomcat.embed:
    tomcat-embed-core:jar:
      lang: java
      version: "9.0.41"
    tomcat-embed-websocket:jar:
      lang: java
      version: "9.0.41"
  org.apache.xmlgraphics:
    batik-constants:jar:
      lang: java
      version: "1.17"
    batik-css:jar:
      lang: java
      version: "1.17"
    batik-i18n:jar:
      lang: java
      version: "1.17"
    batik-shared-resources:jar:
      lang: java
      version: "1.17"
    batik-util:jar:
      lang: java
      version: "1.17"
    xmlgraphics-commons:jar:
      lang: java
      version: "2.9"
  org.apache.yetus:
    audience-annotations:jar:
      lang: java
      version: "0.5.0"
  org.apache.zookeeper:
    zookeeper:jar:
      lang: java
      version: "3.5.8"
    zookeeper-jute:jar:
      lang: java
      version: "3.5.8"
  org.apiguardian:
    apiguardian-api:jar:
      lang: java
      version: "1.1.0"
  org.aspectj:
    aspectjrt:jar:
      lang: java
      version: "1.9.6"
    aspectjweaver:jar:
      lang: java
      version: "1.9.6"
  org.assertj:
    assertj-core:jar:
      lang: java
      version: "3.16.1"
  org.bouncycastle:
    bcpkix-jdk15on:jar:
      lang: java
      version: "1.68"
    bcprov-jdk15on:jar:
      lang: java
      version: "1.68"
  org.checkerframework:
    checker-qual:jar:
      lang: java
      version: "3.5.0"
  org.conscrypt:
    conscrypt-openjdk-uber:jar:
      lang: java
      version: "2.5.1"
  org.dom4j:
    dom4j:jar:
      lang: java
      version: "2.1.4"
  org.eclipse.jetty:
    jetty-alpn-conscrypt-server:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-alpn-server:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-annotations:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-client:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-continuation:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-http:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-io:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-plus:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-security:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-server:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-servlet:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-servlets:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-util:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-util-ajax:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-webapp:jar:
      lang: java
      version: "9.4.40.v20210413"
    jetty-xml:jar:
      lang: java
      version: "9.4.40.v20210413"
  org.eclipse.jetty.http2:
    http2-common:jar:
      lang: java
      version: "9.4.40.v20210413"
    http2-hpack:jar:
      lang: java
      version: "9.4.40.v20210413"
    http2-server:jar:
      lang: java
      version: "9.4.40.v20210413"
  org.eclipse.jetty.toolchain.setuid:
    jetty-setuid-java:jar:
      lang: java
      version: "1.0.4"
  org.eclipse.jetty.websocket:
    javax-websocket-client-impl:jar:
      lang: java
      version: "9.4.40.v20210413"
    javax-websocket-server-impl:jar:
      lang: java
      version: "9.4.40.v20210413"
    websocket-api:jar:
      lang: java
      version: "9.4.40.v20210413"
    websocket-client:jar:
      lang: java
      version: "9.4.40.v20210413"
    websocket-common:jar:
      lang: java
      version: "9.4.40.v20210413"
    websocket-server:jar:
      lang: java
      version: "9.4.40.v20210413"
    websocket-servlet:jar:
      lang: java
      version: "9.4.40.v20210413"
  org.glassfish:
    jakarta.el:jar:
      lang: java
      version: "3.0.4"
  org.glassfish.hk2:
    class-model:jar:
      lang: java
      version: "2.6.1"
    hk2:jar:
      lang: java
      version: "2.6.1"
    hk2-api:jar:
      lang: java
      version: "2.6.1"
    hk2-core:jar:
      lang: java
      version: "2.6.1"
    hk2-locator:jar:
      lang: java
      version: "2.6.1"
    hk2-runlevel:jar:
      lang: java
      version: "2.6.1"
    hk2-utils:jar:
      lang: java
      version: "2.6.1"
    osgi-resource-locator:jar:
      lang: java
      version: "1.0.3"
    spring-bridge:jar:
      lang: java
      version: "2.6.1"
  org.glassfish.hk2.external:
    aopalliance-repackaged:jar:
      lang: java
      version: "2.6.1"
    jakarta.inject:jar:
      lang: java
      version: "2.6.1"
  org.glassfish.jaxb:
    jaxb-runtime:jar:
      lang: java
      version: "2.3.3"
    txw2:jar:
      lang: java
      version: "2.3.3"
  org.glassfish.jersey.bundles.repackaged:
    jersey-guava:jar:
      lang: java
      version: "2.16"
  org.glassfish.jersey.containers:
    jersey-container-servlet:jar:
      lang: java
      version: "2.30.1"
    jersey-container-servlet-core:jar:
      lang: java
      version: "2.30.1"
  org.glassfish.jersey.core:
    jersey-client:jar:
      lang: java
      version: "2.30.1"
    jersey-common:jar:
      lang: java
      version: "2.30.1"
    jersey-server:jar:
      lang: java
      version: "2.30.1"
  org.glassfish.jersey.ext:
    jersey-bean-validation:jar:
      lang: java
      version: "2.30.1"
    jersey-entity-filtering:jar:
      lang: java
      version: "2.30.1"
    jersey-metainf-services:jar:
      lang: java
      version: "2.30.1"
    jersey-spring5:jar:
      lang: java
      version: "2.30.1"
  org.glassfish.jersey.inject:
    jersey-hk2:jar:
      lang: java
      version: "2.30.1"
  org.glassfish.jersey.media:
    jersey-media-jaxb:jar:
      lang: java
      version: "2.30.1"
    jersey-media-json-jackson:jar:
      lang: java
      version: "2.30.1"
    jersey-media-multipart:jar:
      lang: java
      version: "2.30.1"
  org.hamcrest:
    hamcrest:jar:
      lang: java
      version: "2.2"
    hamcrest-core:jar:
      lang: java
      version: "2.2"
  org.hdrhistogram:
    HdrHistogram:jar:
      lang: java
      version: "2.1.12"
  org.hibernate:
    hibernate-core:jar:
      lang: java
      version: "5.4.27.Final"
  org.hibernate.common:
    hibernate-commons-annotations:jar:
      lang: java
      version: "5.1.2.Final"
  org.hibernate.javax.persistence:
    hibernate-jpa-2.1-api:jar:
      lang: java
      version: "1.0.0.Final"
  org.hibernate.validator:
    hibernate-validator:jar:
      lang: java
      version: "6.2.5.Final"
  org.htmlunit:
    neko-htmlunit:jar:
      lang: java
      version: "3.6.0"
  org.immutables:
    value:jar:
      lang: java
      version: "2.1.12"
  org.javassist:
    javassist:jar:
      lang: java
      version: "3.21.0-GA"
  org.jboss:
    jandex:jar:
      lang: java
      version: "2.1.3.Final"
  org.jboss.logging:
    jboss-logging:jar:
      lang: java
      version: "3.4.1.Final"
  org.jboss.marshalling:
    jboss-marshalling:jar:
      lang: java
      version: "2.0.10.Final"
    jboss-marshalling-river:jar:
      lang: java
      version: "2.0.10.Final"
  org.jdom:
    jdom2:jar:
      lang: java
      version: "2.0.6.1"
  org.jodd:
    jodd-bean:jar:
      lang: java
      version: "5.1.6"
    jodd-core:jar:
      lang: java
      version: "5.1.6"
  org.junit.jupiter:
    junit-jupiter:jar:
      lang: java
      version: "5.6.3"
    junit-jupiter-api:jar:
      lang: java
      version: "5.6.3"
    junit-jupiter-engine:jar:
      lang: java
      version: "5.6.3"
    junit-jupiter-params:jar:
      lang: java
      version: "5.6.3"
  org.junit.platform:
    junit-platform-commons:jar:
      lang: java
      version: "1.6.3"
    junit-platform-engine:jar:
      lang: java
      version: "1.6.3"
  org.junit.vintage:
    junit-vintage-engine:jar:
      lang: java
      version: "5.6.3"
  org.jvnet.hudson:
    ganymed-ssh2:jar:
      lang: java
      version: "build210-hudson-1"
  org.jvnet.mimepull:
    mimepull:jar:
      lang: java
      version: "1.9.13"
  org.latencyutils:
    LatencyUtils:jar:
      lang: java
      version: "2.0.3"
  org.lz4:
    lz4-java:jar:
      lang: java
      version: "1.5.0"
  org.mapstruct:
    mapstruct:jar:
      lang: java
      version: "1.4.2.Final"
  org.mockito:
    mockito-core:jar:
      lang: java
      version: "3.3.3"
    mockito-junit-jupiter:jar:
      lang: java
      version: "3.3.3"
  org.objenesis:
    objenesis:jar:
      lang: java
      version: "2.5.1"
  org.opentest4j:
    opentest4j:jar:
      lang: java
      version: "1.2.0"
  org.ow2.asm:
    asm:jar:
      lang: java
      version: "9.0"
    asm-analysis:jar:
      lang: java
      version: "9.0"
    asm-commons:jar:
      lang: java
      version: "9.0"
    asm-tree:jar:
      lang: java
      version: "9.0"
    asm-util:jar:
      lang: java
      version: "7.1"
  org.owasp.antisamy:
    antisamy:jar:
      lang: java
      version: "1.7.4"
  org.owasp.esapi:
    esapi:jar:
      lang: java
      version: "2.3.0.0"
  org.projectlombok:
    lombok:jar:
      lang: java
      version: "1.18.32"
  org.reactivestreams:
    reactive-streams:jar:
      lang: java
      version: "1.0.3"
  org.redisson:
    redisson:jar:
      lang: java
      version: "3.14.0"
  org.reflections:
    reflections:jar:
      lang: java
      version: "0.9.11"
  org.scala-lang:
    scala-library:jar:
      lang: java
      version: "2.12.7"
    scala-reflect:jar:
      lang: java
      version: "2.12.7"
  org.skyscreamer:
    jsonassert:jar:
      lang: java
      version: "1.5.0"
  org.slf4j:
    jcl-over-slf4j:jar:
      lang: java
      version: "1.7.30"
    jul-to-slf4j:jar:
      lang: java
      version: "1.7.30"
    log4j-over-slf4j:jar:
      lang: java
      version: "1.7.30"
    slf4j-api:jar:
      lang: java
      version: "1.7.30"
  org.springframework:
    spring-aop:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-aspects:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-beans:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-context:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-context-support:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-core:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-expression:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-jcl:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-jdbc:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-messaging:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-orm:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-test:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-tx:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-web:jar:
      lang: java
      version: "5.2.24.RELEASE"
    spring-webmvc:jar:
      lang: java
      version: "5.2.24.RELEASE"
  org.springframework.boot:
    spring-boot:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-actuator:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-actuator-autoconfigure:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-autoconfigure:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-actuator:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-aop:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-data-jpa:jar:
      lang: java
      version: "2.3.12.RELEASE"
      exclude:
        - "org.dom4j:dom4j"
    spring-boot-starter-jdbc:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-jersey:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-jetty:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-json:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-logging:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-test:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-tomcat:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-validation:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-starter-web:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-test:jar:
      lang: java
      version: "2.3.8.RELEASE"
    spring-boot-test-autoconfigure:jar:
      lang: java
      version: "2.3.8.RELEASE"
  org.springframework.cloud:
    spring-cloud-commons:jar:
      lang: java
      version: "2.2.7.RELEASE"
    spring-cloud-config-client:jar:
      lang: java
      version: "2.2.7.RELEASE"
    spring-cloud-context:jar:
      lang: java
      version: "2.2.7.RELEASE"
    spring-cloud-sleuth-core:jar:
      lang: java
      version: "2.2.7.RELEASE"
    spring-cloud-starter:jar:
      lang: java
      version: "2.2.7.RELEASE"
    spring-cloud-starter-config:jar:
      lang: java
      version: "2.2.7.RELEASE"
    spring-cloud-starter-openfeign:jar:
      lang: java
      version: "2.2.7.RELEASE"
    spring-cloud-starter-sleuth:jar:
      lang: java
      version: "2.2.7.RELEASE"
  org.springframework.data:
    spring-data-commons:jar:
      lang: java
      version: "2.3.6.RELEASE"
    spring-data-jpa:jar:
      lang: java
      version: "2.3.6.RELEASE"
  org.springframework.plugin:
    spring-plugin-core:jar:
      lang: java
      version: "1.2.0.RELEASE"
    spring-plugin-metadata:jar:
      lang: java
      version: "1.2.0.RELEASE"
  org.springframework.retry:
    spring-retry:jar:
      lang: java
      version: "1.3.4"
  org.springframework.security:
    spring-security-crypto:jar:
      lang: java
      version: "5.4.8"
    spring-security-rsa:jar:
      lang: java
      version: "1.0.9.RELEASE"
  org.springframework.session:
    spring-session-core:jar:
      lang: java
      version: "2.3.1.RELEASE"
  org.springframework.statemachine:
    spring-statemachine-core:jar:
      lang: java
      version: "2.5.1"
  org.xerial.snappy:
    snappy-java:jar:
      lang: java
      version: "*******"
  org.xmlunit:
    xmlunit-core:jar:
      lang: java
      version: "2.7.0"
  org.yaml:
    snakeyaml:jar:
      lang: java
      version: "1.26"
  org.zeroturnaround:
    zt-zip:jar:
      lang: java
      version: "1.15"
  xerces:
    xercesImpl:jar:
      lang: java
      version: "2.12.2"
  xml-apis:
    xml-apis:jar:
      lang: java
      version: "1.4.01"
    xml-apis-ext:jar:
      lang: java
      version: "1.3.04"
