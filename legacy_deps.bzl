load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive", "http_file", "http_jar")

def _legacy_deps():
    # fetch dependencies of run_gradle defined in defs.bzl
    http_archive(
        name = "gradle",
        build_file_content =
            """
filegroup(
   name = "bin_gradle",
   srcs = [
       "bin/gradle",
   ],
   visibility = ["//visibility:public"],
   )
filegroup(
  name = "gradle",
  srcs = glob([
      "**",
  ]),
  visibility = ["//visibility:public"],
  )""",
        sha256 = "1433372d903ffba27496f8d5af24265310d2da0d78bf6b4e5138831d4fe066e9",
        strip_prefix = "gradle-6.8.2",
        url = "https://artnj.zte.com.cn/artifactory/zxccds-release-generic/swap/tools/gradle/gradle-6.8.2-all.zip",
    )

    # fetch dependencies of run_maven defined in defs.bzl
    http_archive(
        name = "maven_tool",
        build_file_content =
            """
filegroup(
   name = "bin_mvn",
   srcs = ["bin/mvn"],
   visibility = ["//visibility:public"],
   )
filegroup(
  name = "maven_tool",
  srcs = glob(["**"]),
  visibility = ["//visibility:public"],
  )""",
        sha256 = "b118e624ec6f7abd8fc49e6cb23f134dbbab1119d88718fc09d798d33756dd72",
        strip_prefix = "apache-maven-3.9.0",
        url = "https://artnj.zte.com.cn/artifactory/zxccds-release-generic/swap/tools/maven/apache-maven-3.9.0-bin.tar.gz",
    )

    http_file(
        name = "maven_settings",
        sha256 = "899b9ffd177afcb61d5221a2907997dab4c71c004f5c09ed2a528a8baa419a7a",
        url = "https://artnj.zte.com.cn/artifactory/zxccds-release-generic/swap/settings/maven/latest/settings.xml",
    )

legacy_deps = module_extension(implementation = lambda ctx: _legacy_deps())
