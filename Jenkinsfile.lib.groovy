def clean() {
    sh "rm -rf ${WORKSPACE}/daip-${service_name}-${product_release}${SNAPSHOT_tag}.bp ${WORKSPACE}/resource_daip_template.json"
}

def obfuscated(){
    sh "chmod 755 run-node.sh"
    sh "sh run-node.sh"
}

def build() {
    sh 'chmod 755 start-build-env.sh'
    sh 'sh start-build-env.sh mvn -U package -DskipTests -Dmaven.repo.local=/home/<USER>/.m2/repository.manager'
    if(JOB_NAME.substring(JOB_NAME.lastIndexOf("-") + 1, JOB_NAME.length()).toLowerCase().equals("verify")) {
        sh "sh start-build-env.sh  mvn -DskipTests -Ppaas -Pcpaas clean package"
    } else {
        sh "sh start-build-env.sh  mvn -DskipTests -Ppaas -Pcpaas clean install"
    }
}

def htmlhint(){
    sh "chmod 755 run-node-eslint.sh"
    sh "sh run-node-eslint.sh 'cd daip-patcher-iui/src/main/iui&&npm install &&yarn replace-temp && yarn htmlhint-o'"
}

def eslint(){
    sh "sh run-node-eslint.sh 'cd daip-patcher-iui/src/main/iui&&npm install && yarn lint-o'"
}

def clean_node_modules(){
    sh "sh run-node-eslint.sh 'cd daip-patcher-iui/src/main/iui&&rm -rf node_modules/'"
}

def build_tcf() {
    if(JOB_NAME.substring(JOB_NAME.lastIndexOf("-") + 1, JOB_NAME.length()).toLowerCase().equals("verify")) {
        sh "sh start-build-env.sh  mvn -U -DskipTests -Ppaas -Ptcf  package"
    } else {
        sh "sh start-build-env.sh  mvn -U -DskipTests -Ppaas -Ptcf  install"
    }
}

def sonar_code_review() {
    withSonarQubeEnv(installationName: 'sonar_server') {
        sh 'sh sonar-env.sh mvn  org.jacoco:jacoco-maven-plugin:prepare-agent -Ppaas -Ptcf install org.jacoco:jacoco-maven-plugin:report sonar:sonar -Dsonar.login=${sonar_token} -Dsonar.host.url=http://${SONAR_SERVER}:${SONAR_SERVER_PORT} -Dsonar.report.export.path=sonar-report.json -Dproject.assembly.outputdir=/src/patcher_product'
    }
    timeout(time: 30, unit: 'SECONDS') {
        def res = waitForQualityGate()
        if (res.status != 'OK') {
            showQualityGateResult()
            showNewIssues()
            sh 'cp sonar-report.json $(pwd)/target/sonar/'
            sonarToGerrit inspectionConfig: [autoMatch: true, baseConfig: [autoMatch: true], serverURL: "http://${SONAR_SERVER}:${SONAR_SERVER_PORT}"], reviewConfig: [issueFilterConfig: [changedLinesOnly: true]], scoreConfig: [issueFilterConfig: [changedLinesOnly: true, newIssuesOnly: true], issuesScore: -1, noIssuesScore: 0]
            echo "status: ${res.status}"
            error "Pipeline aborted due to quality gate failure: ${res.status}"
        }
    }
}

def hadolint() {
    sh 'rm -rf hadolint;mkdir hadolint'
    dir('hadolint'){
        checkout([$class: 'GitSCM', branches: [[name: 'master']], doGenerateSubmoduleConfigurations: false, submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'd02452de-f297-4491-a8db-40be6ce8474d', url: 'http://<EMAIL>/a/zycommon/devsecops/pipeline_lib']]])
    }
    sh "chmod +x ${env.WORKSPACE}/hadolint/resources/hadolint/hadolint-Linux-x86_64"
    hadolint("${env.WORKSPACE}/hadolint/resources/hadolint/hadolint-Linux-x86_64","${env.WORKSPACE}","${GERRIT_CHANGE_OWNER_EMAIL}")
}

def deploy_jars() {
    sh 'sh start-build-env.sh mvn deploy -DskipTests -U'
}

def sonar_analysis() {
    sh "sh start-build-env.sh mvn sonar:sonar -Dsonar.host.url=http://${SONAR_SERVER}:${SONAR_SERVER_PORT} -Dsonar.login=${sonar_token}"
}

def upload_artifacts() {
    uploadArtifact group: "DAIP.MIX.app", artifact: "daip-${service_name}",
            version: "${product_release}${nexus_SNAPSHOT_tag}",
            type: "tar.gz",
            localfile: "daip-${service_name}-service/daip-${service_name}-impl/daip-${service_name}-impl-paas/target/cpaas/${product_id}_PATCHER_${product_release}${SNAPSHOT_tag}.tar.gz"
}

def upload_tcf() {
    uploadArtifact group: "DAIP.TCF.app", artifact: "daip-${service_name}",
            version: "${product_release}${nexus_SNAPSHOT_tag}",
            type: "tar.gz",
            localfile: "daip-${service_name}-service/daip-${service_name}-impl/daip-${service_name}-impl-paas/target/tcf/${product_id}_PATCHER_${product_release}${SNAPSHOT_tag}.tar.gz"
}

def transfer_xlsx2txt(){
    sh "rm -rf  ${WORKSPACE}/TestCase"
    checkout([$class: 'GitSCM', branches: [[name: "${testcase_branch}"]], doGenerateSubmoduleConfigurations: false, extensions: [[$class: 'CleanBeforeCheckout'], [$class: 'RelativeTargetDirectory', relativeTargetDir: "TestCase"]], submoduleCfg: [], userRemoteConfigs: [[credentialsId: '38ec6fda-defd-4ad4-adb0-6b90e343554a', url: "http://10133275:<EMAIL>/a/DAP/Libary/TestCase"]]])
    sh "cd ${WORKSPACE}/TestCase; git checkout ${testcase_branch};  git pull --rebase"
    sh "cd ${WORKSPACE}/daip-${service_name}-testcase; python xlsx2txt.py ${WORKSPACE}/daip-${service_name}-testcase daip-${service_name}-testcase.xlsx ${WORKSPACE}/TestCase"
    sh "cd ${WORKSPACE}/TestCase;git config user.email '<EMAIL>'"
    sh "cd ${WORKSPACE}/TestCase;git add ."
    try{
        sh "cd ${WORKSPACE}/TestCase;git commit -m '从${service_name}'模块增加手工用例到testcase库"
        sh "cd ${WORKSPACE}/TestCase;  git push -f origin ${testcase_branch}"
    }
    catch (Exception e){
    }
}

def showQualityGateResult() {
    targetUrl = "http://${SONAR_SERVER}:${SONAR_SERVER_PORT}/api/qualitygates/project_status?projectKey=com.zte.daip.manager.patcher:daip-patcher"
    def jsonObject = fetch_report(targetUrl, 'summary.json')
    showJson(jsonObject)
    echo ("Quality Gate not passed")
}

def showNewIssues() {
    String targetUrl = "http://${SONAR_SERVER}:${SONAR_SERVER_PORT}/api/issues/search?componentKeys=com.zte.daip.manager.patcher:daip-patcher&resolved=false&sinceLeakPeriod=true"
    def jsonObject = fetch_report(targetUrl, 'sonar-report.json')
    showJson(jsonObject)
}

def fetch_report(targetUrl, filename) {
    jsonObject = sh returnStdout: true, script: "curl -u ${sonar_token}: '$targetUrl' -o $filename"
    def jsonObject = readJSON file: filename
    return jsonObject
}

def showJson(jsonObject) {
    def jsonFormat = JsonOutput.toJson(jsonObject)
    prettyJSON = JsonOutput.prettyPrint(jsonFormat)
    echo "${prettyJSON}"
}

def upload_patcher_swagger_doc() {
    uploadArtifact group: "DAIP.TCF.DOC", artifact: "daip-${service_name}",
            version: "${product_release}${nexus_SNAPSHOT_tag}",
            type: "html",
            localfile: "daip-event/daip-patcher-swagger/target/daip-patcher-svr-api.html"
    uploadArtifact group: "DAIP.TCF.DOC", artifact: "daip-${service_name}",
            version: "${product_release}${nexus_SNAPSHOT_tag}",
            type: "html",
            localfile: "daip-event/daip-patcher-swagger/target/daip-patcher-web-api.html"
}
return this
