load("//:defs.bzl", "run_tests")
load("@rules_pkg//:pkg.bzl", "pkg_tar")

load("//:variables.bzl", "product_id", "product_version", "project_id", "project_version", "snapshot", "time_stamp", "version")


java_library(
    name = "PATCHER",
    srcs = [],
    resources = glob(
        include = ["src/main/resources/**/*"],
        exclude = [],
    ),
    visibility = [
    ],
    deps = [
    ],
)

java_library(
    name = "PATCHER-test-classes",
    srcs = [],
    resources = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
    ],
)

run_tests(
    name = "AllTests",
    size = "small",
    srcs = [],
    data = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
    ],
)
##init
## daip-patcher-init.tar.gz/daip-patcher-init
pkg_tar(
    name = "daip-patcher-init",
    srcs = [
        "//daip-patcher-init:daip-patcher-init-resource",
    ],
    extension = "tar.gz",
    package_dir = "/daip-patcher-init",
    remap_paths = {"daip-patcher-init/libdaip-patcher-init.jar": "daip-patcher-init-%s.jar" % project_version},
    strip_prefix = "daip-patcher-init/src/main/resources",
    visibility = [
        "//:__pkg__",
    ],
    deps = [
        "//daip-patcher-init:daip-patcher-init-lib",
    ],
)
## /daip-patcher/daip-patcher-init/
filegroup(
    name = "daip-patcher-init-group",
    srcs = [
        ":daip-patcher-init",
        "//daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas:daip-patcher-init-image",
    ],
    visibility = [
        "//:__pkg__",
    ],
)
pkg_tar(
    name = "daip-patcher-init-outer",
    srcs = [
        ":daip-patcher-init-group",
    ],
    package_dir = "/daip-patcher-init",
)

##iui
## daip-patcher-iui.tar.gz/daip-patcher-iui
filegroup(
    name = "daip-patcher-iui-group",
    srcs = [
        "//daip-patcher-iui",
        "//daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas:daip-patcher-iui-image",
    ],
    visibility = [
        "//:__pkg__",
    ],
)

pkg_tar(
    name = "daip-patcher-iui-outer",
    srcs = [
        ":daip-patcher-iui-group",
    ],
    package_dir = "/daip-patcher-iui",
)

##handler
## daip-patcher-handler.tar.gz/daip-patcher-handler
pkg_tar(
    name = "daip-patcher-handler",
    srcs = [
        "//daip-patcher-handler/daip-patcher-handler-impl/daip-patcher-handler-impl-paas:daip-patcher-handler-resource",
    ],
    extension = "tar.gz",
    package_dir = "/daip-patcher-handler",
    remap_paths = {"daip-patcher-handler/daip-patcher-handler-impl/daip-patcher-handler-impl-paas/libdaip-patcher-handler-impl-paas.jar": "daip-patcher-handler.jar"},
    strip_prefix = "daip-patcher-handler/daip-patcher-handler-impl/daip-patcher-handler-impl-paas/src/main/resources/version",
    visibility = [
        "//:__pkg__",
    ],
    deps = [
        "//daip-patcher-handler/daip-patcher-handler-impl/daip-patcher-handler-impl-paas:daip-patcher-handler-lib",
    ],
)

## /daip-patcher/daip-patcher-handler/
filegroup(
    name = "daip-patcher-handler-group",
    srcs = [
        ":daip-patcher-handler",
        "//daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas:daip-patcher-handler-image",
    ],
    visibility = [
        "//:__pkg__",
    ],
)

pkg_tar(
    name = "daip-patcher-handler-outer",
    srcs = [
        ":daip-patcher-handler-group",
    ],
    package_dir = "/daip-patcher-handler",
)

##svr
## daip-patcher-svr.tar.gz/daip-patcher-svr
pkg_tar(
    name = "daip-patcher-svr",
    srcs = [
        "//daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas:daip-patcher-svr-resource",
    ],
    extension = "tar.gz",
    package_dir = "/daip-patcher-svr",
    remap_paths = {"daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas/libdaip-patcher-impl-paas.jar": "daip-patcher-impl-paas-%s.jar" % project_version},
    strip_prefix = "daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas/src/main/resources/microserver",
    visibility = [
        "//:__pkg__",
    ],
    deps = [
        "//daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas:daip-patcher-svr-lib",
    ],
)

## /daip-patcher/daip-patcher-init/
filegroup(
    name = "daip-patcher-svr-group",
    srcs = [
        ":daip-patcher-svr",
        "//daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas:daip-patcher-svr-image",
    ],
    visibility = [
        "//:__pkg__",
    ],
)

pkg_tar(
    name = "daip-patcher-svr-outer",
    srcs = [
        ":daip-patcher-svr-group",
    ],
    package_dir = "/daip-patcher-svr",
)


## daip-patcher-V20.24.40.04.tar.gz/daip-patcher/
pkg_tar(
    name = "daip-patcher-V20.24.40.04",
    srcs = [
        "//daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas:daip-patcher-svr-blueprint",
    ],
    extension = "tar.gz",
    package_dir = "/daip-patcher",
    strip_prefix = "daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas/src/main/resources/image",
    deps = [
        ":daip-patcher-init-outer",
        ":daip-patcher-handler-outer",
        ":daip-patcher-svr-outer",
        ":daip-patcher-iui-outer",
    ],
)


## /DAIP_PATCHER_V20.24.40.01-SNAPSHOT/business-images/daip-patcher/
filegroup(
    name = "daip-patcher-inner",
    srcs = [
        ":daip-patcher-V20.24.40.04",
        "//daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas:daip-patcher-svr-install",
    ],
    visibility = [
        "//:__pkg__",
    ],
)

pkg_tar(
    name = "business-images",
    srcs = [
        ":daip-patcher-inner",
    ],
    package_dir = "/business-images/daip-patcher",
)

## DAIP_PATCHER_V20.24.40.04-SNAPSHOT.tar.gz
pkg_tar(
    name = "DAIP_PATCHER_V20.24.40.04-SNAPSHOT",
    srcs = [
        "//daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas:daip-patcher",
    ],
    extension = "tar.gz",
    package_dir = "/DAIP_PATCHER_%s" % product_version,
    strip_prefix = "daip-patcher-service/daip-patcher-impl/daip-patcher-impl-paas/src/main/resources/install",
    deps = [
        ":business-images",
    ],
)
