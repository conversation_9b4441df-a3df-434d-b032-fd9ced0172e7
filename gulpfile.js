"use strict";

const gulp = require("gulp"),
    uglify = require("gulp-uglify"),
    plumber = require('gulp-plumber');

const basePath = "./",
      exceptPath = '!' + basePath + "**/*min.js";
let inPath = [];
inPath.push('*-iui/**/*.js');
inPath.push(exceptPath);

gulp.task("compress", function (done) {
    gulp.src(inPath)
        .pipe(plumber())
        .pipe(uglify({mangle :false}))
        .pipe(plumber.stop())
        .pipe(gulp.dest(outPathFun));
    done();
});

function outPathFun (file) {
    return file.base;
}
