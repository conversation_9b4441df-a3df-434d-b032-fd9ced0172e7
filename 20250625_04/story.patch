diff --git a/daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchDeleteService.java b/daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchDeleteService.java
new file mode 100644
index 00000000..08f55bca
--- /dev/null
+++ b/daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchDeleteService.java
@@ -0,0 +1,41 @@
+package com.zte.daip.manager.patcher.domain.common;
+
+import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
+import com.zte.daip.manager.patcher.infrastructure.repository.PatchHistoryRepository;
+import com.zte.daip.manager.patcher.infrastructure.repository.PatchInfoRepository;
+import com.zte.daip.manager.patcher.infrastructure.repository.PatchDispatchRepository;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.stereotype.Service;
+import org.springframework.transaction.annotation.Transactional;
+
+import java.util.List;
+
+@Service
+public class PatchDeleteService {
+
+    @Autowired
+    private PatchHistoryRepository patchHistoryRepository;
+
+    @Autowired
+    private PatchInfoRepository patchInfoRepository;
+
+    @Autowired
+    private PatchDispatchRepository patchDispatchRepository;
+
+    @Transactional
+    public void deleteSinglePatch(String patchName) throws PatchException {
+        // 查询补丁历史记录
+        List<PatchHistoryDto> historyRecords = patchHistoryRepository.findByPatchName(patchName);
+        
+        // 如果补丁已应用则抛出异常
+        if (!historyRecords.isEmpty()) {
+            throw new PatchException("patch has updated,patchName=" + patchName);
+        }
+        
+        // 删除补丁信息记录
+        patchInfoRepository.deleteByPatchName(patchName);
+        
+        // 删除补丁分发记录
+        patchDispatchRepository.deleteByPatchName(patchName);
+    }
+}
diff --git a/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchDispatchRepository.java b/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchDispatchRepository.java
index 00a2550c..d45ca65e 100644
--- a/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchDispatchRepository.java
+++ b/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchDispatchRepository.java
@@ -80,6 +80,10 @@ import java.util.List;
 @Transactional(rollbackFor = Exception.class)
 public interface PatchDispatchRepository extends JpaRepository<PatchDispatch, PatchDispatchKey> {
 
+    @Modifying(clearAutomatically = true)
+    @Query("delete from PatchDispatch p where p.id.patchName = :patchName")
+    int deleteByPatchName(@Param("patchName") String patchName);
+
     @Modifying(clearAutomatically = true)
     @Query(value = "select p from  PatchDispatch p where p.id.patchName = :patchName")
     List<PatchDispatch> queryPatchDispatchByPatchKey(@Param("patchName") String patchName);
@@ -96,4 +100,4 @@ public interface PatchDispatchRepository extends JpaRepository<PatchDispatch, Pa
     @Modifying(clearAutomatically = true)
     @Query(value = "select p from  PatchDispatch p where p.id.patchName = :patchName and p.success = :success")
     List<PatchDispatch> findByPatchNameAndSuccess(String patchName, boolean success);
-}
\ No newline at end of file
+}
diff --git a/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchHistoryRepository.java b/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchHistoryRepository.java
index 312750be..a67130ac 100644
--- a/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchHistoryRepository.java
+++ b/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchHistoryRepository.java
@@ -83,6 +83,9 @@ public interface PatchHistoryRepository extends JpaRepository<PatchHistory, Patc
     @Modifying(clearAutomatically = true)
     @Query(value = "select p.id.patchName from  PatchHistory p")
     List<String> queryPatchName();
+    
+    @Query(value = "select p from PatchHistory p where p.id.patchName = :patchName")
+    List<PatchHistory> findByPatchName(@Param("patchName") String patchName);
 
     @Modifying(clearAutomatically = true)
     @Query(
@@ -99,4 +102,4 @@ public interface PatchHistoryRepository extends JpaRepository<PatchHistory, Patc
     @Query(value = "select p from  PatchHistory p where p.id.serviceName = :serviceName")
     List<PatchHistory> queryPatchHistoryByServiceName(@Param("serviceName") String serviceName);
 
-}
\ No newline at end of file
+}
diff --git a/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchInfoRepository.java b/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchInfoRepository.java
index c580210a..1f797386 100644
--- a/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchInfoRepository.java
+++ b/daip-patcher-service/daip-patcher-infrastructure-api/src/main/java/com/zte/daip/manager/patcher/infrastructure/repository/PatchInfoRepository.java
@@ -81,6 +81,10 @@ import java.util.List;
 public interface PatchInfoRepository
     extends JpaRepository<PatchDetailPo, Long>, JpaSpecificationExecutor<PatchDetailPo> {
 
+    @Modifying(clearAutomatically = true)
+    @Query("delete from PatchDetailPo p where p.patchName = :patchName")
+    int deleteByPatchName(@Param("patchName") String patchName);
+
     List<PatchDetailPo> findByIsFullPatch(int isFullPatch);
 
     @Modifying(clearAutomatically = true)
diff --git a/daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDeleteController.java b/daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDeleteController.java
new file mode 100644
index 00000000..d53901a7
--- /dev/null
+++ b/daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDeleteController.java
@@ -0,0 +1,24 @@
+package com.zte.daip.manager.patcher.interfaces.controller;
+
+import com.zte.daip.manager.patcher.domain.common.PatchDeleteService;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.web.bind.annotation.*;
+
+@RestController
+@RequestMapping("/patches/delete")
+public class PatchDeleteController {
+    @Autowired
+    private PatchDeleteService patchDeleteService;
+
+    @PostMapping("/singlePatch")
+    public String deleteSinglePatch(@RequestParam String patchName) {
+        try {
+            patchDeleteService.deleteSinglePatch(patchName);
+            return "success";
+        } catch (PatchException e) {
+            return "patch has updated,patchName=" + patchName;
+        } catch (Exception e) {
+            return "服务器内部错误";
+        }
+    }
+}
