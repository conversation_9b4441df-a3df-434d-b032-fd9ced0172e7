module(
    name = "test",
    version = "1.0",
)

# =========================================
# Bazel module dependencies
# =========================================
bazel_dep(
    name = "rules_jvm_external",
    version = "4.5",
)

bazel_dep(
    name = "rules_pkg",
    version = "0.9.1",
)

bazel_dep(
    name = "rules_oci",
    version = "1.4.0",
)

bazel_dep(
    name = "rules_java",
    version = "7.3.0",
)

# =========================================
# rules_oci images
# =========================================
oci = use_extension("@rules_oci//oci:extensions.bzl", "oci")

oci.pull(
    name = "my_image",
    digest = "sha256:5642c3565fe72bb5da21f2432c263e2f2bc6952e4b52f9c10ac5a58fb3af49e4",
    image = "zxccds-release-docker.artnj.zte.com.cn/swap/sonarqube",
)

use_repo(oci, "my_image")

# =========================================
# use the legacy repos in deps.bzl
# =========================================
legacy_deps = use_extension("//:legacy_deps.bzl", "legacy_deps")

use_repo(legacy_deps, "gradle", "maven_tool", "maven_settings")
