完成用户故事

标题：作为运维人员，我希望能够删除单个补丁
场景1：成功删除单个补丁。
场景2：如果补丁已应用，则删除失败。

相关文件：
daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/
daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/
daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchHistoryService.java # 补丁历史信息服务
daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchInfoService.java # 补丁信息服务
daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchDispatchService.java # 补丁分发服务


# 方案设计


 ```
@startuml

participant "com.zte.daip.manager.patcher.interfaces.controller.PatchDeleteController" as AppService
participant "com.zte.daip.manager.patcher.domain.common.PatchDeleteService" as DomainService
participant "数据库" as Database

AppService -> DomainService: deleteSinglePatch(patchName)

group 前置校验
    DomainService -> Database: 查询补丁历史记录
    Database --> DomainService: 返回历史记录
    alt 补丁已应用
        DomainService --> AppService: 抛出异常("补丁已升级")
    else 未升级
         DomainService -> Database: 删除补丁信息记录
         DomainService -> Database: 删除补丁分发记录
    end
end

DomainService --> AppService: 返回操作结果
@enduml
 ```

# 接口定义

```json
{
  "swagger": "2.0",
  "info": {
    "version": "1.0.0",
    "title": "补丁删除API",
    "description": "提供补丁删除操作的接口"
  },
  "host": "api.example.com",
  "basePath": "/",
  "schemes": [
    "https"
  ],
  "consumes": [
    "application/json"
  ],
  "produces": [
    "application/json"
  ],
  "paths": {
    "/patches/delete/singlePatch": {
      "post": {
        "tags": [
          "补丁删除"
        ],
        "summary": "删除单个补丁",
        "description": "删除单个补丁",
        "operationId": "deleteSinglePatch",
        "parameters": [
          {
            "name": "patchName",
            "in": "query",
            "description": "补丁名称",
            "required": true,
            "type": "string",
            "example": "patch-1.0.0"
          }
        ],
        "responses": {
          "200": {
            "description": "删除成功",
            "schema": {
              "type": "string",
              "example": "success"
            }
          },
          "400": {
            "description": "参数错误",
            "schema": {
              "type": "string",
              "example": "参数错误"
            }
          },
          "500": {
            "description": "服务器内部错误",
            "schema": {
              "type": "string",
              "example": "服务器内部错误"
            }
          }
        }
      }
    }
  }
}
```

## 验收准则

**Scenario 1: 成功删除未应用的补丁**

- Given: 存在一个名为 "patch_v1.0" 的补丁
- And: 该补丁未出现在补丁历史记录中 (未应用过)
- When: 调用删除单个补丁接口
- Then: 补丁信息从 补丁信息表 中被删除
- And: 补丁信息从 补丁分发表 中被删除

**Scenario 2: 尝试删除已升级的补丁**

- Given: 存在一个名为 "patch_v2.0" 的补丁
- And: 该补丁已出现在补丁历史记录中 (已应用过)
- When: 调用删除单个补丁接口
- Then: 抛出 PatchException 异常，提示 "patch has updated,patchName=patch_v2.0"
- And: 所有服务中的补丁信息保持不变

