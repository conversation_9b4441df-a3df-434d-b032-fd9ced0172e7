diff --git a/daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchDeleteService.java b/daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchDeleteService.java
new file mode 100644
index 00000000..96af6f73
--- /dev/null
+++ b/daip-patcher-service/daip-patcher-domain/src/main/java/com/zte/daip/manager/patcher/domain/common/PatchDeleteService.java
@@ -0,0 +1,33 @@
+package com.zte.daip.manager.patcher.domain.common;
+
+import com.zte.daip.manager.patcher.infrastructure.api.PatchHistoryRepository;
+import com.zte.daip.manager.patcher.infrastructure.api.PatchInfoRepository;
+import com.zte.daip.manager.patcher.infrastructure.api.PatchDispatchRepository;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.stereotype.Service;
+
+@Service
+public class PatchDeleteService {
+
+    @Autowired
+    private PatchHistoryRepository patchHistoryRepository;
+
+    @Autowired
+    private PatchInfoRepository patchInfoRepository;
+
+    @Autowired
+    private PatchDispatchRepository patchDispatchRepository;
+
+    public void deleteSinglePatch(String patchName) throws PatchException {
+        // 前置校验
+        if (patchHistoryRepository.existsByPatchName(patchName)) {
+            throw new PatchException("补丁已升级,patchName=" + patchName);
+        }
+
+        // 删除补丁信息
+        patchInfoRepository.deleteByPatchName(patchName);
+        
+        // 删除补丁分发记录
+        patchDispatchRepository.deleteByPatchName(patchName);
+    }
+}
diff --git a/daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDeleteController.java b/daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDeleteController.java
new file mode 100644
index 00000000..d56ece66
--- /dev/null
+++ b/daip-patcher-service/daip-patcher-interfaces/src/main/java/com/zte/daip/manager/patcher/interfaces/controller/PatchDeleteController.java
@@ -0,0 +1,26 @@
+package com.zte.daip.manager.patcher.interfaces.controller;
+
+import com.zte.daip.manager.patcher.domain.common.PatchDeleteService;
+import com.zte.daip.manager.patcher.domain.common.PatchException;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.web.bind.annotation.*;
+
+@RestController
+@RequestMapping("/patches/delete")
+public class PatchDeleteController {
+
+    @Autowired
+    private PatchDeleteService patchDeleteService;
+
+    @PostMapping("/singlePatch")
+    public String deleteSinglePatch(@RequestParam String patchName) {
+        try {
+            patchDeleteService.deleteSinglePatch(patchName);
+            return "success";
+        } catch (PatchException e) {
+            return "patch has updated,patchName=" + patchName;
+        } catch (Exception e) {
+            return "服务器内部错误";
+        }
+    }
+}
