def run_tests(name, srcs, **kwargs):
    for src in srcs:
        src_name = src[:-5]
        native.java_test(name = src_name, test_class = src_name.replace("src/test/java/", "").replace("/", "."), srcs = srcs, **kwargs)

def run_gradle(name, srcs, outs, project_dir, arguments, java_tool = "@bazel_tools//tools/jdk:current_java_runtime", **kwargs):
    native.genrule(
        name = name,
        srcs = srcs,
        outs = [out.rpartition("/")[-1] for out in outs],
        cmd = """
            JAVA_BIN=$$(echo $(locations %s) | awk '{print $$1}')
            export JAVA_HOME=$${JAVA_BIN%%bin*}
            export GRADLE_USER_HOME=.
            ./$(location @gradle//:bin_gradle) -p %s %s &&
            cp %s $(RULEDIR)
            """ % (java_tool, project_dir, " ".join(arguments), " ".join(["%s/%s" % (project_dir, out) for out in outs])),
        tools = [
            java_tool,
            "@gradle",
            "@gradle//:bin_gradle",
        ],
        **kwargs
    )

def run_maven(name, srcs, outs, project_dir, arguments, java_tool = "@bazel_tools//tools/jdk:current_java_runtime", **kwargs):
    native.genrule(
        name = name,
        srcs = srcs,
        outs = [out.rpartition("/")[-1] for out in outs],
        cmd = """
            JAVA_BIN=$$(echo $(locations %s) | awk '{print $$1}')
            export JAVA_HOME=$${JAVA_BIN%%bin*}
            ./$(location @maven_tool//:bin_mvn) -s $(location @maven_settings//file) -Dmaven.repo.local=. -B -f %s/pom.xml %s &&
            cp %s $(RULEDIR)
            """ % (java_tool, project_dir, " ".join(arguments), " ".join(["%s/%s" % (project_dir, out) for out in outs])),
        tools = [
            java_tool,
            "@maven_tool",
            "@maven_tool//:bin_mvn",
            "@maven_settings//file",
        ],
        **kwargs
    )
