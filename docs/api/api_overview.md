# API概览 API Overview

## 1. API 简介

DAIP Patcher 提供以下核心API功能：

- 补丁全生命周期管理（上传、删除、更新、回退）
- 分发记录与历史操作查询
- 基于RESTful规范设计

## 2. 模块划分 API Modules

| 模块         | 路径前缀                  | 功能描述                     |
| ------------ | ------------------------- | --------------------------- |
| 补丁管理     | /patches                 | 补丁基础操作                |
| 分发管理     | /patches/dispatch        | 补丁分发相关操作            |
| 更新历史     | /patches/update/history  | 补丁更新历史管理            |

## 3. 主要接口概览 Main Endpoints

### 3.1 补丁管理模块

| 接口路径                          | HTTP方法 | 功能描述                         |
|----------------------------------|---------|--------------------------------|
| /api/daip-patcher-handler/v1/patches                         | GET     | 根据条件查询补丁               |
| /api/daip-patcher-handler/v1/patches/uploading               | POST    | 上传新补丁                      |
| /api/daip-patcher-handler/v1/patches/deletion                | POST    | 删除指定补丁                   |
| /api/daip-patcher-handler/v1/patches/update                  | POST    | 对目标服务执行补丁更新          |

### 3.2 分发管理模块

| 接口路径                          | HTTP方法 | 功能描述                         |
|----------------------------------|---------|--------------------------------|
| /api/daip-patcher-handler/v1/patches/dispatch                | POST    | 分发补丁到指定节点              |
| /api/daip-patcher-handler/v1/patches/dispatch/history        | GET     | 查询补丁分发历史记录            |

### 3.3 更新历史模块

| 接口路径                          | HTTP方法 | 功能描述                         |
|----------------------------------|---------|--------------------------------|
| /api/daip-patcher-handler/v1/patches/update/history          | GET     | 查询服务打补丁的历史记录        |
| /api/daip-patcher-handler/v1/patches/update/history/deletion | POST    | 删除指定补丁的历史记录          |
| /api/daip-patcher-handler/v1/patches/update/history/rollback | GET     | 获取可回退的补丁列表            |
| /api/daip-patcher-handler/v1/patches/update/history/rollback | POST    | 执行补丁回退操作                |
