# 术语表 Glossary

本文件收录项目中常见的专业术语、缩写词及其标准解释。

## 1. 业务领域术语

| 术语             | 英文/缩写             | 解释说明                                                                |
| ---------------- | --------------------- | ----------------------------------------------------------------------- |
| 补丁包           | PatchPackage          | 用户上传可识别的补丁文件或补丁集                                        |
| 补丁文件         | PatchFile             | 包含patch-update-config.xml的补丁包                                     |
| 补丁集           | PatchSet              | 包含patches的补丁包                                                     |
| 补丁文件上传记录 | PatchUploadRecord     | 补丁名称,上传时间,文件大小,上传结果,校验信息,eventId                    |
| 补丁文件删除记录 | PatchDeleteRecord     | 补丁名称,删除时间,删除结果,失败原因,eventId                             |
| 补丁文件分发记录 | PatchDistributeRecord | 补丁名称,分发时间,主机IP,分发结果,失败原因,eventId                      |
| 补丁描述文件     | PatchDescFile         | 补丁描述文件                                                            |
| 补丁名称         | PatchName             | 唯一描述该补丁的名称，持久化                                            |
| 补丁类型         | PatchType             | 表达补丁类型，有scheme、服务、container等类型                           |
| 基线版本         | BaseVersion           | 补丁制作的基础版本号，持久化                                            |
| 发布时间         | ReleaseTime           | 补丁制作的时间，持久化                                                  |
| 补丁描述         | PatchDescription      | 补丁描述，持久化                                                        |
| 补丁自定义命令   | CustomCommand         | 补丁自定义脚本，包含命令，顺序，描述                                    |
| 服务             | Service               | 服务，持久化                                                            |
| 角色             | Role                  | 角色，持久化                                                            |
| 全量补丁         | FullPatch             | fullpatch为true的补丁，持久化                                           |
| 容器补丁         | ContainerPatch        | isContainPatch为true的补丁，涉及补丁容器的修改                          |
| 补丁升级         | PatchUpgrade          | 补丁名称,服务名,基线版本,角色,服务实例,补丁类型,补丁级别,补丁容器路径   |
| 补丁级别         | PatchLevel            | 服务级，实例级，角色级，zdh(服务级集合)                                 |
| 补丁容器路径     | ContainerPath         | service_model 定义路径                                                  |
| 补丁升级记录     | UpgradeRecord         | 升级时间,升级结果,失败原因,主机IP,补丁名称,服务名,角色名,实例名,eventId |
