# DAIP Patcher 架构设计

## 1. 简介 Introduction

DAIP Patcher 是用于管理分布式系统补丁的工具，支持补丁上传、删除、更新及回退操作。系统采用分层架构设计，包含用户界面、处理逻辑、服务层和持久化存储。

## 2. 设计目标 Design Goals

- **高可用性**：支持大规模集群环境下的稳定运行
- **可扩展性**：通过微服务架构实现功能模块的灵活扩展
- **易用性**：提供直观的 Web UI 和清晰的 API 文档

## 3. 总体架构 Overview

- 补丁上传
- 补丁更新
- 补丁删除

## 4. 核心功能模块

### 4.1 补丁上传

- **功能描述**：
  - 支持 scheme、container、service 和补丁集上传
  - 文件大小限制为 2G，支持最多同时上传 9 个补丁
  - 包含文件合法性校验和安全检查
  - 上传成功后记录元数据至 PostgreSQL 并持久化文件到 PVC 存储卷

### 4.2 补丁更新

- **操作流程**：
  1. 用户选择目标集群、服务及主机
  2. 前端触发更新请求，后端生成更新任务
  3. Agent 节点执行容器停止 → 补丁更新 → 服务重启流程
  4. 更新进度通过 Kafka 消息实时上报至前端

### 4.3 补丁删除

- **删除条件**：
  - 仅当补丁未在 `dapmanager_patch_history` 中存在时允许删除
  - 同时删除数据库记录和 PVC 存储中的文件
- **技术实现**：
  ```java
  // 删除操作伪代码示例
  public void deletePatch(String patchName) {
      if (!patchHistoryRepository.existsByPatchName(patchName)) {
          patchRepository.deleteById(patchName);
          fileStorageService.deleteFile(patchName);
      } else {
          throw new PatchInUseException();
      }
  }
  ```

## 5. 技术细节

- **API 调用**：通过 `daip-patcher-handler` 接口实现前后端通信
- **Agent 通信**：使用 Kafka 消息队列异步执行补丁更新
- **事务管理**：数据库操作与文件存储原子性保证
