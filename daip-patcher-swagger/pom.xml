<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zte.daip.manager.patcher</groupId>
        <artifactId>daip-patcher</artifactId>
        <version>deletePatch</version>
    </parent>

    <artifactId>daip-patcher-swagger</artifactId>
    <packaging>pom</packaging>

    <properties>
        <swagger2markup.version>1.3.3</swagger2markup.version>
        <asciidoctor.input.directory>${project.basedir}/src/docs/asciidoc</asciidoctor.input.directory>
        <swagger.output.dir>${project.build.directory}/generated-sources/swagger</swagger.output.dir>
        <swagger.input>${swagger.output.dir}/swagger.json</swagger.input>
        <generated.asciidoc.directory>${project.build.directory}/asciidoc/generated</generated.asciidoc.directory>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-handler-impl-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-interfaces</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>
    </dependencies>

    <build>
        <!--<plugins>
            <plugin>
                <groupId>com.github.kongchen</groupId>
                <artifactId>swagger-maven-plugin</artifactId>
                <version>3.1.8</version>
                <executions>
                    <execution>
                        <phase>test</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <apiSources>
                                <apiSource>
                                    <springmvc>true</springmvc>
                                    <locations>
                                        <location>com.zte.daip.manager.patcher.interfaces.controller</location>
                                    </locations>
                                    <outputFormats>json</outputFormats>
                                    <swaggerDirectory>${swagger.output.dir}/svr</swaggerDirectory>
                                    <info>
                                        <title>DAIP PATCHER SVR API</title>
                                        <version>v1.0</version>
                                        <description>补丁svr接口文档</description>
                                    </info>
                                    <basePath>api/daip-patcher-svr/v1</basePath>
                                    <schemes>
                                        <scheme>http</scheme>
                                    </schemes>
                                    <host>daip-patcher-svr-{NAMESPACE}:56210</host>
                                </apiSource>
                                <apiSource>
                                    <springmvc>true</springmvc>
                                    <locations>
                                        <location>com.zte.daip.manager.patcher.handler.impl.paas.impl</location>
                                    </locations>
                                    <outputFormats>json</outputFormats>
                                    <swaggerDirectory>${swagger.output.dir}/web</swaggerDirectory>
                                    <info>
                                        <title>DAIP PATCHER WEB API</title>
                                        <version>v1.0</version>
                                        <description>补丁web接口文档</description>
                                    </info>
                                    <basePath>api/daip-patcher-handler/v1</basePath>
                                    <schemes>
                                        <scheme>http</scheme>
                                    </schemes>
                                    <host>daip-patcher-handler-{NAMESPACE}:13001</host>
                                </apiSource>
                            </apiSources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>io.github.swagger2markup</groupId>
                <artifactId>swagger2markup-maven-plugin</artifactId>
                <version>${swagger2markup.version}</version>
                <configuration>
                    <swaggerInput>${swagger.output.dir}/swagger.json</swaggerInput>
                    <outputDir>${generated.asciidoc.directory}</outputDir>
                    <config>
                        <swagger2markup.operationOrderBy>CUSTOM</swagger2markup.operationOrderBy>
                        <swagger2markup.outputLanguage>ZH</swagger2markup.outputLanguage>
                        <swagger2markup.generatedExamplesEnabled>true</swagger2markup.generatedExamplesEnabled>
                        <swagger2markup.inlineSchemaEnabled>false</swagger2markup.inlineSchemaEnabled>
                        &lt;!&ndash;<swagger2markup.pathsGroupedBy>TAGS</swagger2markup.pathsGroupedBy>&ndash;&gt;
                        <swagger2markup.markupLanguage>ASCIIDOC</swagger2markup.markupLanguage>
                    </config>
                </configuration>
                <executions>
                    <execution>
                        <id>svr-docs</id>
                        <phase>test</phase>
                        <goals>
                            <goal>convertSwagger2markup</goal>
                        </goals>
                        <configuration>
                            <swaggerInput>${swagger.output.dir}/svr/swagger.json</swaggerInput>
                            <outputDir>${generated.asciidoc.directory}/svr</outputDir>
                        </configuration>
                    </execution>
                    <execution>
                        <id>web-docs</id>
                        <phase>test</phase>
                        <goals>
                            <goal>convertSwagger2markup</goal>
                        </goals>
                        <configuration>
                            <swaggerInput>${swagger.output.dir}/web/swagger.json</swaggerInput>
                            <outputDir>${generated.asciidoc.directory}/web</outputDir>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.asciidoctor</groupId>
                <artifactId>asciidoctor-maven-plugin</artifactId>
                <version>1.5.6</version>
                <configuration>
                    <sourceDirectory>${asciidoctor.input.directory}</sourceDirectory>
                    <sourceDocumentName>index.adoc</sourceDocumentName>
                    <attributes>
                        <doctype>book</doctype>
                        <toc>left</toc>
                        <toclevels>3</toclevels>
                        <numbered></numbered>
                        <hardbreaks></hardbreaks>
                        <sectlinks></sectlinks>
                        <sectnums>true</sectnums>
                        <sectanchors></sectanchors>
                    </attributes>
                    <backend>html5</backend>
                </configuration>
                <executions>
                    <execution>
                        <id>svr-output-html</id>
                        <phase>test</phase>
                        <goals>
                            <goal>process-asciidoc</goal>
                        </goals>
                        <configuration>
                            <attributes>
                                <generated>${generated.asciidoc.directory}/svr/</generated>
                            </attributes>
                            <outputFile>${project.build.directory}/html/daip-patcher-svr-api.html</outputFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>web-output-html</id>
                        <phase>test</phase>
                        <goals>
                            <goal>process-asciidoc</goal>
                        </goals>
                        <configuration>
                            <attributes>
                                <generated>${generated.asciidoc.directory}/web/</generated>
                            </attributes>
                            <outputFile>${project.build.directory}/html/daip-patcher-web-api.html</outputFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>-->
    </build>

</project>
