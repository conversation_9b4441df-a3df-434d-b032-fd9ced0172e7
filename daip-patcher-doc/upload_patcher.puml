@startuml


left to right direction


node  pod {

package IUI {
   [ daip_patcher_iui ]
}

package HANDLER {
   [ daip_patcher_handler ]
}

package SERVICE {
  [ daip_patcher_service ]
}
}

frame USER {
   :User:
   [ daip_openapi ]
}

database "postgres" {
  folder "daip_patcher" {
    [ dapmanager_patch_detail_info ]
    [ dapmanager_patch_history  ]
    [ dapmanager_patches_rollback ]
    [ dapmanager_upgrade_patch_info ]
  }
}



User--> [ daip_patcher_iui ] : "上传补丁:\n1、支持scheme、container、service和补丁集;\n2、补丁上传失败有详细说明; \n 3、最多支持同时上传9个补丁; \n4、文件上传增加安全性校验;\n5、补丁大小不超过2G;"
[ daip_openapi ] --> [ daip_patcher_service ] : ""
[ daip_patcher_iui ]  --> [ daip_patcher_handler ]
[ daip_patcher_handler ]  --> [ daip_patcher_service ] : ""
[ daip_patcher_service ]  --> [ dapmanager_patch_detail_info ]:"检查补丁合法性，合法补丁记录持久化"
[ daip_patcher_service ]  --> [ pvc ]:补丁更名后持久化到pvc中, pvc不小于20G





@enduml