@startuml

/'
942:
Select Cluster
Service Select
Choose Host
Confirm
Service Stop
Update
Service Start
Upgrade Report
'/

title ui update patches

actor user
participant patcher_iui as iui
participant patcher
participant deployer
collections agent_patchClient as agent

user->iui : select cluster in patcher ui
iui -> patcher : query cluster need update patch services
patcher --> iui : service list

user->iui : select services

iui -> patcher : service's need update patch hosts
patcher --> iui : service to hosts lists

user -> iui : select every service's hosts individual

iui ->> patcher : update patches

patcher ->> agent : update patches

iui -> patcher : query progress periodical util over
patcher --> iui : progress

activate agent

loop patch container counts(kafka message)

    agent -> agent : stop selected role instance
    agent -> agent : update container patches
    agent -> agent : update patches
    agent -> agent : start role instance which be stopped before

    patcher <<-- agent : one container one node over

    patcher -> patcher : update progress
    deactivate agent
end


@enduml