@startuml

left to right direction

actor User

package user {
  usecase "上传补丁文件" as daip.patcher.user.001
  usecase "上传补丁集" as daip.patcher.user.002
  usecase "删除补丁文件" as daip.patcher.user.003
  usecase "查询补丁文件" as daip.patcher.user.004
  usecase "分发补丁文件" as daip.patcher.user.005

  usecase "升级补丁" as daip.patcher.user.006
  usecase "回退补丁" as daip.patcher.user.007

  usecase "查询补丁文件分发记录" as daip.patcher.user.008
  usecase "查询补丁升级记录" as daip.patcher.user.009

  User --> daip.patcher.user.001
  User --> daip.patcher.user.002
  User --> daip.patcher.user.003
  User --> daip.patcher.user.004
  User --> daip.patcher.user.005
  User --> daip.patcher.user.006
  User --> daip.patcher.user.007
  User --> daip.patcher.user.008
  User --> daip.patcher.user.009

}


@enduml