@startuml

left to right direction


node  pod {

package IUI {
   [ daip_patcher_iui ]
}

package HANDLER {
   [ daip_patcher_handler ]
}

package SERVICE {
  [ daip_patcher_service ]
  }
}

frame USER {
   :User:
   [ daip_openapi ]
}

database "postgres" {
  folder "daip_patcher" {
    [ dapmanager_patch_detail_info ]
    [ dapmanager_patch_history  ]
    [ dapmanager_patches_rollback ]
    [ dapmanager_upgrade_patch_info ]
  }
}


User--> [ daip_patcher_iui ] : "单个/批量删除补丁"
[ daip_openapi ] --> [ daip_patcher_service ] : "单个/批量删除补丁"
[ daip_patcher_iui ]  --> [ daip_patcher_handler ]
[ daip_patcher_handler ]  --> [ daip_patcher_service ]
[ daip_patcher_service ]  --> [ dapmanager_patch_detail_info ]:"当补丁不在dapmanager_patch_history，可删除"
[ daip_patcher_service ]  --> [ pvc ]:从pvc中删除补丁



@enduml