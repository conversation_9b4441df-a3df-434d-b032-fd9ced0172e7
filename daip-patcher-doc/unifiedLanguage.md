# DAIP patcher

|  术语   | 描述  | 定义 |  属性|
|  ----  | ----  |---- |---- |
|补丁包|用户上传可识别的补丁文件或补丁集|
|补丁文件|包含patch-update-config.xml的补丁包|
|补丁集|包含patches的补丁包|
|补丁文件上传记录|补丁名称,上传时间,文件大小,上传结果,校验信息,eventId|
|补丁文件删除记录|补丁名称,删除时间,删除结果,失败原因,eventId|
|补丁文件分发记录|补丁名称,分发时间,主机IP,分发结果,失败原因,eventId|
|补丁描述文件||
|补丁名称|唯一描述该补丁的名称|持久化|
|补丁类型|表达补丁类型，有scheme、服务、container等类型|
|基线版本|补丁制作的基础版本号|持久化|
|发布时间|补丁制作的时间|持久化|
|补丁描述|补丁描述|持久化|
|补丁自定义命令|补丁自定义脚本|命令，顺序，描述|
|服务|服务|持久化|
|角色|角色|持久化|
|全量补丁|fullpatch为true的补丁|持久化|
|容器补丁|isContainPatch为true的补丁,补丁容器的补丁，涉及补丁容器的修改|
|补丁升级|补丁名称,服务名,基线版本,角色,服务实例,补丁类型,补丁级别,补丁容器路径|
|补丁级别|服务级，实例级，角色级，zdh(服务级集合)|
|补丁容器路径|service_model 定义路径|
|补丁升级记录|升级时间,升级结果,失败原因,主机IP,补丁名称,服务名,角色名,实例名,eventId|

**Thank you for using DAIP patcher.**