@startuml


left to right direction


node  pod {

package IUI {
   [ daip_patcher_iui ]
}

package HANDLER {
   [ daip_patcher_handler ]
}

package SERVICE {
  [ daip_patcher_service ]
}
}

frame USER {
   :User:
   [ daip_openapi ]
}

database "postgres" {
  folder "daip_patcher" {
    [ dapmanager_patch_detail_info ]
    [ dapmanager_patch_history ]
    [ dapmanager_patches_rollback ]
    [ dapmanager_upgrade_patch_info ]
  }
}


User--> [ daip_patcher_iui ] : "升级补丁:\n1、可升级container、service补丁，成功率大于于99%;\n2、补丁升级失败有详细说明;\n3、2000台机器升级时间不大于10分钟"
[ daip_openapi ] --> [ daip_patcher_service ] : ""
[ daip_patcher_iui ]  --> [ daip_patcher_handler ]
[ daip_patcher_handler ]  --> [ daip_patcher_service ] : ""
[ daip_patcher_service ]  --> [ dapmanager_patch_history ]:"升级完成后，升级记录入库"


@enduml