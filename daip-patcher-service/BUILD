load("//:defs.bzl", "run_tests")

java_library(
    name = "daip-patcher-service",
    srcs = [],
    resources = glob(
        include = ["src/main/resources/**/*"],
        exclude = [],
    ),
    visibility = [
    ],
    deps = [
    ],
)

java_library(
    name = "daip-patcher-service-test-classes",
    srcs = [],
    resources = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
    ],
)

run_tests(
    name = "AllTests",
    size = "small",
    srcs = [],
    data = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
    ],
)
