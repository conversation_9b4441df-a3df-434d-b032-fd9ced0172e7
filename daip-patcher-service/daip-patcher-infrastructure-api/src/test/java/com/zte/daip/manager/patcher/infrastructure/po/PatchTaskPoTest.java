package com.zte.daip.manager.patcher.infrastructure.po;

import org.junit.Assert;
import org.junit.Test;

import java.util.Date;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchTaskPoTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/10/11</p>
 * <p>完成日期：2023/10/11</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class PatchTaskPoTest
{
    @Test
    public void setTaskId()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setTaskId(1L);
        assertThat(1L, is(patchTaskPo.getTaskId()));
    }

    @Test
    public void setTaskName()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setTaskName("1");
        assertThat("1", is(patchTaskPo.getTaskName()));
    }

    @Test
    public void setTaskType()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setTaskType(1);
        assertThat(1, is(patchTaskPo.getTaskType()));
    }

    @Test
    public void setClusterId()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setClusterId("1");
        assertThat("1", is(patchTaskPo.getClusterId()));
    }

    @Test
    public void setOperateType()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setOperateType(1);
        assertThat(1, is(patchTaskPo.getOperateType()));
    }

    @Test
    public void setCreateTime()
    {
        Date date = new Date();
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setCreateTime(date);
        assertThat(date, is(patchTaskPo.getCreateTime()));
    }

    @Test
    public void setAllowModify()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setAllowModify(1);
        assertThat(1, is(patchTaskPo.getAllowModify()));
    }

    @Test
    public void setPatchType()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setPatchCategory("schema");
        assertThat("schema", is(patchTaskPo.getPatchCategory()));
    }

    @Test
    public void setClusterName()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setClusterName("1");
        assertThat("1", is(patchTaskPo.getClusterName()));
    }

    @Test
    public void setContext()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setContext("1");
        assertThat("1", is(patchTaskPo.getContext()));
    }

    @Test
    public void setRelationTaskId()
    {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setRelationTaskId(1L);
        assertThat(1L, is(patchTaskPo.getRelationTaskId()));
    }

    /* Started by AICoder, pid:o8859002c5b16b4141b20a08907a8b4a8341d672 */
    @Test
    public void setNeedRestartServices() {
        // 创建一个PatchTaskPo对象
        PatchTaskPo patchTaskPo = new PatchTaskPo();

        // 设置需要重启的服务为"hdfs"
        patchTaskPo.setNeedRestartServices("hdfs");

        // 验证getNeedRestartServices方法返回的值是否为"hdfs"
        assertThat("hdfs", is(patchTaskPo.getNeedRestartServices()));
    }

    @Test
    public void setRelationServices() {
        // 创建一个PatchTaskPo对象
        PatchTaskPo patchTaskPo = new PatchTaskPo();

        // 设置关联的服务为"1"
        patchTaskPo.setRelationServices("1");

        // 验证getRelationServices方法返回的值是否为"1"
        assertThat("1", is(patchTaskPo.getRelationServices()));
    }

    @Test
    public void testEqualsAndHashCode() {
        // 创建两个PatchTaskPo对象，它们具有相同的needRestartServices和taskId
        PatchTaskPo patchTaskPo1 = new PatchTaskPo();
        patchTaskPo1.setNeedRestartServices("test");
        patchTaskPo1.setTaskId(1L);

        PatchTaskPo patchTaskPo2 = new PatchTaskPo();
        patchTaskPo2.setNeedRestartServices("test");
        patchTaskPo2.setTaskId(1L);

        // 验证两个对象的hashCode是否相等
        Assert.assertEquals(patchTaskPo1.hashCode(), patchTaskPo2.hashCode());

        // 验证两个对象是否相等
        Assert.assertEquals(patchTaskPo1, patchTaskPo2);
    }

    /* Ended by AICoder, pid:o8859002c5b16b4141b20a08907a8b4a8341d672 */
}