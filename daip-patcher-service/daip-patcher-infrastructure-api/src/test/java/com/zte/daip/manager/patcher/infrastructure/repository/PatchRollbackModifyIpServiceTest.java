package com.zte.daip.manager.patcher.infrastructure.repository;

import com.zte.daip.manager.patcher.infrastructure.bean.IpMigrationBean;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ParameterizedPreparedStatementSetter;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PatchRollbackModifyIpServiceTest {

    @InjectMocks
    private PatchRollbackModifyIpService patchRollbackModifyIpService;
    @Mock
    private JdbcTemplate jdbcTemplate;

    @Test
    public void test_patchModifyHostIps() {
        IpMigrationBean ipMigrationBean = new IpMigrationBean();
        ipMigrationBean.setOldIp("***********");
        ipMigrationBean.setNewIp("***********");
        when(jdbcTemplate.batchUpdate(anyString(), anyList(), anyInt(), any(ParameterizedPreparedStatementSetter.class))).thenReturn(null);
        patchRollbackModifyIpService.batchModifyHostIps(com.google.common.collect.Lists.newArrayList(ipMigrationBean));
        verify(jdbcTemplate).batchUpdate(anyString(), anyList(), anyInt(), any(ParameterizedPreparedStatementSetter.class));
    }
}
