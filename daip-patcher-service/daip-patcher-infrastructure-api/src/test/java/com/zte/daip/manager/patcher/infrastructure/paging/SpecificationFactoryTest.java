package com.zte.daip.manager.patcher.infrastructure.paging;

import com.zte.daip.manager.common.utils.paging.jpa.Predication;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import jakarta.persistence.criteria.*;

import java.util.*;

import static junit.framework.TestCase.assertTrue;

public class SpecificationFactoryTest {
    @Mock
    private Root root;
    @Mock
    private CriteriaBuilder builder;

    @Before
    public void before() {
        MockitoAnnotations.initMocks(this);

        Mockito.when(root.get("name")).thenReturn(Mockito.any());
    }

    @Test
    public void testGetPredicateList() {
        // Setup
        final List<Predication> ps = Arrays.asList(new Predication<>("like", "name", "value"),
            new Predication<>("likeStart", "name", "value"), new Predication<>("likeEnd", "name", "value"),
            new Predication<>("dateLike", "name", "value"), new Predication<>("equal", "name", "value"));

        // Run the test
        final Predicate[] result = SpecificationFactory.getPredicateList(root, builder, ps);

        // Verify the results

        assertTrue(result.length == 5);
    }
}