/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchRegisterPoTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2024/1/16
 * </p>
 * <p>
 * 完成日期：2024/1/16
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.infrastructure.po;

import org.junit.Assert;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
public class PatchRegisterPoTest {
    @Test
    public void setPatchRegisterId() {
        PatchRegisterPo patchRegisterPo = new PatchRegisterPo();
        PatchRegisterKey patchRegisterKey = new PatchRegisterKey();
        patchRegisterKey.setUrl("/v1/download/schema");
        patchRegisterKey.setServiceName("patcher");

        patchRegisterPo.setId(patchRegisterKey);
        assertThat(patchRegisterKey, is(patchRegisterPo.getId()));
    }

    @Test
    public void setPatchRegisterPort() {
        PatchRegisterPo patchRegisterPo = new PatchRegisterPo();
        patchRegisterPo.setPort("5432");
        assertThat("5432", is(patchRegisterPo.getPort()));
    }

    @Test
    public void setSchemaFiles() {
        PatchRegisterPo patchRegisterPo = new PatchRegisterPo();
        patchRegisterPo.setSchemaFiles("file1");
        assertThat("file1", is(patchRegisterPo.getSchemaFiles()));
    }

    @Test
    public void testEqualsAndHashCode() {
        PatchRegisterPo patchRegisterPo1 = new PatchRegisterPo();
        patchRegisterPo1.setPort("5432");
        PatchRegisterPo patchRegisterPo2 = new PatchRegisterPo();
        patchRegisterPo2.setPort("5432");
        Assert.assertEquals(patchRegisterPo1.hashCode(), patchRegisterPo2.hashCode());
        Assert.assertEquals(patchRegisterPo1, patchRegisterPo2);
    }
}