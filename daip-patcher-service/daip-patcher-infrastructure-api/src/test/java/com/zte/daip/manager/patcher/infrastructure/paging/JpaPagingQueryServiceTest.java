package com.zte.daip.manager.patcher.infrastructure.paging;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.paging.jpa.*;
import org.junit.Test;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import static junit.framework.TestCase.assertEquals;
import static junit.framework.TestCase.assertTrue;

public class JpaPagingQueryServiceTest {

    private JpaPagingQueryService jpaPagingQueryServiceUnderTest = new JpaPagingQueryService();

    @Test
    public void testGetPageRequestWithoutSort() {
        // Setup
        final QueryParam query = QueryParam.builder().pageSize(10).pageNum(1).build();

        // Run the test
        final PageRequest result = jpaPagingQueryServiceUnderTest.getPageRequest(query);

        // Verify the results
        assertEquals(PageRequest.of(0, 10), result);
    }

    @Test
    public void testGetPageRequestWithSort() {
        // Setup
        final QueryParam query = QueryParam.builder().pageSize(10).pageNum(1).sortKey("name").build();

        // Run the test
        final PageRequest result = jpaPagingQueryServiceUnderTest.getPageRequest(query);

        // Verify the results
        assertEquals(PageRequest.of(0, 10, Sort.by(query.getSortKey()).ascending()), result);
    }

    @Test
    public void testGetSpecification() {
        // Setup
        final QueryParam query = QueryParam.builder()
            .fuzzyQuery(new FuzzyQuery("SP001", Lists.newArrayList(new Predication("patchName"))))
            .fieldQuery(
                new FieldQuery(Lists.newArrayList(new Predication(PagingConstants.EQUAL_OPERATE, "service", "hdfs"))))
            .build();

        // Run the test
        final Specification fieldQuerySpecification =
            jpaPagingQueryServiceUnderTest.getSpecificationByQueryParam(query);

        // Verify the results
        // fieldQuerySpecification.

        assertTrue(fieldQuerySpecification.toString().contains("Specification"));
    }
}
