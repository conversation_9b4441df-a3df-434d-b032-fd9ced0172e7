package com.zte.daip.manager.patcher.infrastructure.bean;

import org.junit.Assert;
import org.junit.Test;

public class IpMigrationBeanTest {

    @Test
    public void testIpMigrationBean(){
        IpMigrationBean ipMigrationBean = new IpMigrationBean("127.0.0.1", "*********");
        Assert.assertNotNull(ipMigrationBean.toString());
    }

    @Test
    public void equals(){
        IpMigrationBean ipMigrationBean = new IpMigrationBean();
        boolean ret = ipMigrationBean.equals(ipMigrationBean);
        Assert.assertTrue(ret);

        ret = ipMigrationBean.equals(null);
        Assert.assertFalse(ret);

        IpMigrationBean ipMigrationBean1 = new IpMigrationBean();
        ret = ipMigrationBean.equals(ipMigrationBean1);
        Assert.assertTrue(ret);
    }
}
