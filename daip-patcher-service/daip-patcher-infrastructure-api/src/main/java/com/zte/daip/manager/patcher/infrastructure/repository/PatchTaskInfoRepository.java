package com.zte.daip.manager.patcher.infrastructure.repository;

import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskInfoKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskInfoPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;

import java.util.List;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Transactional(rollbackFor = Exception.class)
public interface PatchTaskInfoRepository
    extends JpaRepository<PatchTaskInfoPo, PatchTaskInfoKey>, JpaSpecificationExecutor<PatchTaskInfoPo> {

    @Modifying(clearAutomatically = true)
    @Query(
        value = "update PatchTaskInfoPo p set p.id.rollbackTaskId = :rollbackTaskId where p.id.updateTaskId = :updateTaskId")
    void updateByUpdateTaskId(@Param("updateTaskId") long updateTaskId, @Param("rollbackTaskId") long rollbackTaskId);

    @Query(value = "select p from PatchTaskInfoPo p where p.id.updateTaskId = :updateTaskId")
    PatchTaskInfoPo findByUpdateTaskId(long updateTaskId);

    @Query(value = "select p from PatchTaskInfoPo p where p.id.rollbackTaskId = :rollbackTaskId")
    PatchTaskInfoPo findByRollbackTaskId(long rollbackTaskId);

    @Modifying(clearAutomatically = true)
    @Query(value = "update PatchTaskInfoPo p set p.id.updateTaskId = 0 where p.id.updateTaskId in (:updateTaskIds)")
    int modifyUpdateTaskIdToZero(List<Long> updateTaskIds);

    @Modifying(clearAutomatically = true)
    @Query(
        value = "update PatchTaskInfoPo p set p.id.rollbackTaskId = 0 where p.id.rollbackTaskId in (:rollbackTaskIds)")
    int modifyRollbackTaskIdToZero(List<Long> rollbackTaskIds);

    @Modifying(clearAutomatically = true)
    @Query(value = "delete from PatchTaskInfoPo p where p.id.rollbackTaskId in (:rollbackTaskIds)")
    int deleteRollbackTaskId(List<Long> rollbackTaskIds);

    @Modifying(clearAutomatically = true)
    @Query(value = "delete from PatchTaskInfoPo p where p.id.updateTaskId in (:updateTaskIds)")
    int deleteUpdateTaskId(List<Long> updateTaskIds);
}
