package com.zte.daip.manager.patcher.infrastructure.repository;

import com.zte.daip.manager.patcher.infrastructure.bean.IpMigrationBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.zte.daip.manager.patcher.infrastructure.constants.PatchUpdateConstants.INSERT_BATCH_SIZE;

@Service
public class PatchDispatchModifyIpService {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Transactional(rollbackFor = Exception.class)
    public void batchModifyHostIps(List<IpMigrationBean> ipMigrationBeans) {
        String sql = "update dapmanager_patch_dispatch set ip=? where ip=?";

        jdbcTemplate.batchUpdate(sql, ipMigrationBeans, INSERT_BATCH_SIZE, (preparedStatement, ipMigrationBean) -> {
            preparedStatement.setString(1, ipMigrationBean.getNewIp());
            preparedStatement.setString(2, ipMigrationBean.getOldIp());
        });
    }

}
