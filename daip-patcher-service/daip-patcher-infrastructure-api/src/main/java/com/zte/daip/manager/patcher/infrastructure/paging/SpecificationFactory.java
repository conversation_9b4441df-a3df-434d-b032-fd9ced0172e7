/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: SpecificationFactory.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2022/3/1
 * </p>
 * <p>
 * 完成日期：2022/3/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.infrastructure.paging;

import com.zte.daip.manager.common.utils.paging.jpa.OP;
import com.zte.daip.manager.common.utils.paging.jpa.Predication;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public class SpecificationFactory {

    private SpecificationFactory() {}

    /**
     * 多where条件and连接
     *
     * @param ps
     * @param <T>
     * @return
     */
    public static <T> Specification<T> and(List<Predication> ps) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> builder
            .and(getPredicateList(root, builder, ps));
    }

    /**
     * 多where条件or连接
     *
     * @param ps
     * @param <T>
     * @return
     */
    public static <T> Specification<T> or(List<Predication> ps) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> builder
            .or(getPredicateList(root, builder, ps));
    }

    public static Predicate[] getPredicateList(Root<?> root, CriteriaBuilder builder, List<Predication> ps) {
        List<Predicate> predicateList = new ArrayList<>();
        ps.forEach(p -> {
            Predicate predicate = buildPredicate(builder, root.get(p.getName()), p);
            predicateList.add(predicate);
        });
        return predicateList.toArray(new Predicate[predicateList.size()]);
    }

    private static Predicate buildPredicate(CriteriaBuilder cb, Path path, Predication p) {

        Predicate predicate;
        switch (OP.queryOPType(p.getOperator())) {
            case LIKE:
                predicate = cb.like(path, "%" + p.getValue().toString() + "%");
                break;
            case LIKE_START:
                predicate = cb.like(path, p.getValue().toString() + "%");
                break;
            case LIKE_END:
                predicate = cb.like(path, "%" + p.getValue().toString());
                break;
            case EQ:
                predicate = cb.equal(path, p.getValue());
                break;
            case DATE_LIKE:
                predicate = cb.like(cb.concat(path, ""), "%" + p.getValue().toString() + "%");
                break;
            default:
                throw new IllegalArgumentException("Illegal operator");
        }
        return predicate;
    }

}