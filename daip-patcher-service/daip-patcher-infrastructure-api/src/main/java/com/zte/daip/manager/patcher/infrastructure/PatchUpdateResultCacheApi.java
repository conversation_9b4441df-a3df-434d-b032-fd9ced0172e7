package com.zte.daip.manager.patcher.infrastructure;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public interface PatchUpdateResultCacheApi {
    void updateSuccessPatchResult(String clusterId, String patchResultJson);

    void updateFailedPatchResult(String clusterId, String patchUpdateResultJson);

    List<String> pollSuccessPatchUpdateResult(String clusterId);

    List<String> pollFailedPatchUpdateResult(String clusterId);

    int querySuccessPatchUpdateResultSize(String clusterId);

    int queryFailedPatchUpdateResultSize(String clusterId);

    void clearCache(String clusterId);
}