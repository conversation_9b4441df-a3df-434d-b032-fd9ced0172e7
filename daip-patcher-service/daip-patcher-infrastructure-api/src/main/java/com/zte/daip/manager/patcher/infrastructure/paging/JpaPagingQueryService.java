/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: JpaPagingQueryService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2022/3/1
 * </p>
 * <p>
 * 完成日期：2022/3/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.infrastructure.paging;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.paging.jpa.FieldQuery;
import com.zte.daip.manager.common.utils.paging.jpa.FuzzyQuery;
import com.zte.daip.manager.common.utils.paging.jpa.PagingConstants;
import com.zte.daip.manager.common.utils.paging.jpa.QueryParam;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class JpaPagingQueryService {
    public Specification getSpecificationByQueryParam(QueryParam query) {
        Specification specification = getFieldQuerySpecification(query);

        return specification.and(getFuzzyQuerySpecification(query));
    }

    private Specification getFieldQuerySpecification(QueryParam query) {
        final FieldQuery fieldQuery = query.getFieldQuery();
        if (null == fieldQuery || CollectionUtils.isEmpty(fieldQuery.getPredications())) {
            return SpecificationFactory.and(Lists.newArrayList());
        }
        return SpecificationFactory.and(fieldQuery.getPredications());
    }

    private Specification getFuzzyQuerySpecification(QueryParam query) {
        final FuzzyQuery fuzzyQuery = query.getFuzzyQuery();
        if (null == fuzzyQuery || StringUtils.isEmpty(fuzzyQuery.getKeyWord())
            || CollectionUtils.isEmpty(fuzzyQuery.getFieldNames())) {
            return SpecificationFactory.and(Lists.newArrayList());
        }

        fuzzyQuery.getFieldNames().forEach(f -> {
            f.setOperator(StringUtils.defaultString(f.getOperator(), PagingConstants.LIKE_OPERATE));
            f.setValue(fuzzyQuery.getKeyWord());
        });

        return SpecificationFactory.or(fuzzyQuery.getFieldNames());
    }

    public PageRequest getPageRequest(QueryParam query) {

        if (StringUtils.isEmpty(query.getSortKey())) {
            return PageRequest.of(query.getPageNum() - 1, query.getPageSize());
        }
        final Sort sort = StringUtils.equalsIgnoreCase(query.getSortOrder(), PagingConstants.SORT_DESC)
            ? Sort.by(query.getSortKey()).descending() : Sort.by(query.getSortKey()).ascending();

        return PageRequest.of(query.getPageNum() - 1, query.getPageSize(), sort);
    }
}