package com.zte.daip.manager.patcher.infrastructure.repository;

import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Transactional(rollbackFor = Exception.class)
public interface PatchTaskRepository extends JpaRepository<PatchTaskPo, Long>, JpaSpecificationExecutor<PatchTaskPo> {
    @Modifying(clearAutomatically = true)
    @Query(value = "delete from PatchTaskPo p where p.taskId = :taskId")
    void deleteByTaskId(@Param("taskId") long taskIds);

    @Modifying(clearAutomatically = true)
    @Query(value = "delete from PatchTaskPo p where p.taskId in (:taskIds)")
    void deleteByTaskIds(@Param("taskIds") List<Long> taskIds);

    @Modifying(clearAutomatically = true)
    @Query(value = "update PatchTaskPo p set p.allowModify = :allowModify where p.taskId = :taskId")
    int updateAllowModifyByTaskId(long taskId, int allowModify);

    @Modifying(clearAutomatically = true)
    @Query(value = "update PatchTaskPo p set p.relationTaskId = :relationTaskId where p.taskId = :taskId")
    int updateRelationTaskIdTaskId(long taskId, long relationTaskId);

    @Modifying(clearAutomatically = true)
    @Query(value = "update PatchTaskPo p set p.relationTaskId = 0 where p.relationTaskId in (:relationTaskIds)")
    int updateRelationTaskIdsToZero(List<Long> relationTaskIds);

    PatchTaskPo findByTaskId(long taskId);

    @Query(value = "from PatchTaskPo p where p.taskId in (:taskIds)")
    List<PatchTaskPo> findByTaskIds(@Param("taskIds") List<Long> taskIds);
}
