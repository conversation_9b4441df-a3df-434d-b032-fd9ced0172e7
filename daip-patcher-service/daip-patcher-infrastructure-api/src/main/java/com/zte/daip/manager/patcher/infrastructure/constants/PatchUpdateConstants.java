package com.zte.daip.manager.patcher.infrastructure.constants;

import org.apache.commons.lang3.time.DateUtils;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public class PatchUpdateConstants
{
    private PatchUpdateConstants(){}

    public  static  final String PATCH_UPDATE_PROGRESS_CACHE_KEYNAME = "UPDATE_PATCH_CLUSTER_%s";

    public  static  final String PATCH_UPDATE_PROGRESS_FIELD_NAME = "UPDATE_PATCH_RESULT";

    public  static  final long DEFAULT_CACHE_EXPIRE_TIME = DateUtils.MILLIS_PER_MINUTE * 40 / DateUtils.MILLIS_PER_SECOND;

    public  static  final String PATCH_UPDATE_SUCCESS_RESULT_CACHE_KEYNAME = "SUCCESS_UPDATE_PATCH_CLUSTER_%s";

    public  static  final String PATCH_UPDATE_FAILED_RESULT_CACHE_KEYNAME = "FAILED_UPDATE_PATCH_CLUSTER_%s";

    public  static  final String PATCH_UPDATE_FINISHED_CACHE_KEYNAME = "UPDATE_PATCH_FINISHED_%s";

    public  static  final String PATCH_UPDATE_FINISHED_FIELD_NAME = "UPDATE_PATCH_RESULT";

    public static final int INSERT_BATCH_SIZE = 200;
}