package com.zte.daip.manager.patcher.infrastructure.po;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Data
@EqualsAndHashCode
public class PatchUpdateResultKey {
    private String clusterId;

    private String patchHome;

    private String patchType;

    public PatchUpdateResultKey() {}

    public PatchUpdateResultKey(String clusterId, String patchHome, String patchType) {
        this.clusterId = clusterId;
        this.patchHome = patchHome;
        this.patchType = patchType;
    }

    public String generateUId() {
        String pHome = this.patchHome == null ? "" : this.patchHome;
        return String.format("%s:%d:%s", this.clusterId, pHome.hashCode(), this.patchType);
    }
}