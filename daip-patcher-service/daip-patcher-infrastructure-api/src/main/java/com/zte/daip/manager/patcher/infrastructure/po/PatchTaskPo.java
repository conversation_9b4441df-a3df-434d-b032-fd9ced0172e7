package com.zte.daip.manager.patcher.infrastructure.po;

import java.io.IOException;
import java.io.Serializable;
import java.util.Date;

import jakarta.persistence.*;

import org.springframework.context.annotation.Primary;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zte.daip.manager.common.security.injection.annotations.InjectionDef;
import com.zte.daip.manager.common.security.injection.annotations.InjectionType;
import com.zte.daip.manager.common.security.injection.annotations.SecurityDef;

import lombok.Data;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Table(name = "dapmanager_patch_task")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Primary
public class PatchTaskPo implements Serializable {
    private static final long serialVersionUID = 5561121397966022453L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "task_id")
    private long taskId;

    @Column(name = "task_name")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String taskName;

    @Column(name = "task_type")
    private int taskType;

    @Column(name = "cluster_id")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String clusterId;

    @Column(name = "operate_type")
    private int operateType;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "allow_modify")
    private int allowModify;

    @Column(name = "patch_category")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String patchCategory;

    @Column(name = "relation_task_id")
    private long relationTaskId;

    @Transient
    private String clusterName;

    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String context;

    @Column(name = "relation_services")
    private String relationServices;

    @Column(name = "need_restart_services")
    private String needRestartServices;

    private void writeObject(java.io.ObjectOutputStream out) throws IOException {
        out.defaultWriteObject();
    }

    private void readObject(java.io.ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
    }
}