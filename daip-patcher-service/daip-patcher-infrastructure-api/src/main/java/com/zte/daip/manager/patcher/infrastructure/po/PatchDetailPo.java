/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDetail.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/10
 * </p>
 * <p>
 * 完成日期：2021/3/10
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.infrastructure.po;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zte.daip.manager.common.security.injection.annotations.InjectionDef;
import com.zte.daip.manager.common.security.injection.annotations.InjectionType;
import com.zte.daip.manager.common.security.injection.annotations.SecurityDef;
import lombok.Data;
import org.springframework.context.annotation.Primary;

import jakarta.persistence.*;
import java.io.IOException;
import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Table(name = "dapmanager_patch_detail_info")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Primary
public class PatchDetailPo implements Serializable {
    private static final long serialVersionUID = 5561121397966022453L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column
    private long id;

    @Column(name = "patch_name")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String patchName;

    @Column(name = "patch_display_name_zh")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String patchDisplayNameZh;

    @Column(name = "patch_display_name_en")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String patchDisplayNameEn;

    @Column(name = "depend_patch")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String dependPatch;

    @Column(name = "base_version")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String baseVersion;

    @Column(name = "patch_create_date")
    private Date patchCreateDate;

    @Column(name = "patch_size")
    private long patchSize;


    @Column
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String service;

    @Column(name = "hot_patch")
    private int hotPatch;

    @Column
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String roles;

    @Column(name = "patch_upload_time")
    private Timestamp patchUploadTime;

    @Transient
    private String patchUpgradeTime;

    @Column(name = "description_zh")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String descriptionZh;

    @Column(name = "description_en")
    @SecurityDef(injectionCheck = {@InjectionDef(injectionType = InjectionType.XSS)})
    private String descriptionEn;

    @Transient
    private String servicePatchHomes = "";

    @Transient
    private int applyHostNum;

    @Transient
    private boolean canDel;

    @Transient
    private String patchType;

    @Transient
    private String serviceInstanceId;

    @Transient
    private Map<String, String> role2PatchHome = new HashMap<>();

    @Column(name = "is_container_patch")
    private int isContainerPatch;

    @Column(name = "is_full_patch")
    private int isFullPatch;

    private void writeObject(java.io.ObjectOutputStream out) throws IOException {
        out.defaultWriteObject();
    }

    private void readObject(java.io.ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
    }
}