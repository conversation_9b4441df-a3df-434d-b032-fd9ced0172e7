/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchRollbackRepository.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/21
 * </p>
 * <p>
 * 完成日期：2023/3/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.infrastructure.repository;

import com.zte.daip.manager.patcher.infrastructure.po.PatchRollbackKey;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRollbackPo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
@Transactional(rollbackFor = Exception.class)
public interface PatchRollbackRepository extends JpaRepository<PatchRollbackPo, PatchRollbackKey> {
    @Modifying(clearAutomatically = true)
    @Query(value = "select p from PatchRollbackPo p where p.id.ip in (:ips)")
    List<PatchRollbackPo> queryByIps(@Param("ips") List<String> ips);

    @Modifying(clearAutomatically = true)
    @Query(
        value = "select p from PatchRollbackPo p where p.id.serviceName = :serviceName and p.id.serviceInstanceId = :serviceInstanceId and p.id.roleName = :roleName and p.id.patchName = :patchName")
    List<PatchRollbackPo> queryIpsByServiceAndInstanceAndRole(@Param("serviceName") String serviceName,
        @Param("serviceInstanceId") String serviceInstanceId, @Param("roleName") String roleName,
        @Param("patchName") String patchName);

    @Modifying(clearAutomatically = true)
    @Query(
        value = "delete from PatchRollbackPo p where p.id.serviceName = :serviceName and p.id.serviceInstanceId = :serviceInstanceId and p.id.roleName = :roleName and p.id.ip = :ip and p.id.patchName not in (:patchNames)")
    void deleteByRollbackKey(@Param("serviceName") String serviceName,
        @Param("serviceInstanceId") String serviceInstanceId, @Param("roleName") String roleName,
        @Param("ip") String ip, @Param("patchNames") List<String> patchNames);

}
