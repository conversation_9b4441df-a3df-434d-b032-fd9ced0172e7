<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>daip-patcher-service</artifactId>
        <groupId>com.zte.daip.manager.patcher</groupId>
        <version>deletePatch</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>daip-patcher-infrastructure-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.javax.persistence</groupId>
            <artifactId>hibernate-jpa-2.1-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.miniagent</groupId>
            <artifactId>daip-miniagent-seed</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-event-reporter-client</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.3.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>


</project>