package com.zte.daip.manager.patcher.domain.rollback.entity;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.assertj.core.util.Lists;
import org.junit.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchRollBackResultTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/23</p>
 * <p>完成日期：2023/3/23</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class PatchRollBackResultTest {
    @Test
    public void testBean() {
        PatchRollBackResult result = new PatchRollBackResult("1", "host1", "kafka", "a", Lists.newArrayList());
        assertTrue(StringUtils.isNotBlank(result.getRoleName()) && StringUtils.isNotBlank(result.getIp())
            && StringUtils.isNotBlank(result.getServiceInstanceId())
            && StringUtils.isNotBlank(result.getServiceName()));
    }
}