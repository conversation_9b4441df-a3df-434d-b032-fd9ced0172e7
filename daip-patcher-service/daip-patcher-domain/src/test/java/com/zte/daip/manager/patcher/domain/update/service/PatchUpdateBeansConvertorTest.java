package com.zte.daip.manager.patcher.domain.update.service;

import com.google.common.collect.Maps;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateRequest;
import com.zte.daip.manager.patcher.api.update.request.ServiceInstance;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchUpdateBeansConvertorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/20</p>
 * <p>完成日期：2021/4/20</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class PatchUpdateBeansConvertorTest {
    @InjectMocks
    private PatchUpdateBeansConvertor patchUpdateBeansConvertor;

    private static final String clusterId = "1";

    @Test
    public void convertUpdateRequest2CacheDtoForService() {
        PatchUpdateRequest p1 = mockPatchUpdateRequest("/home/<USER>", "rdk", "SERVICE");
        PatchUpdateRequest p2 = mockPatchUpdateRequest("/home/<USER>", "rdk_1", "SERVICE");

        PatchUpdateCacheDto patchUpdateCacheDto =
            patchUpdateBeansConvertor.convertUpdateRequest2CacheDto(clusterId, Lists.newArrayList(p1, p2));

        assertThat(patchUpdateCacheDto.getPatchResults().size(), is(5));
    }

    @Test
    public void convertUpdateRequest2CacheDtoForInstance() {
        PatchUpdateRequest p1 = mockPatchUpdateRequest("/home/<USER>", "rdk", "INSTANCE");
        PatchUpdateRequest p2 = mockPatchUpdateRequest("/home/<USER>", "rdk_1", "INSTANCE");

        PatchUpdateCacheDto patchUpdateCacheDto =
            patchUpdateBeansConvertor.convertUpdateRequest2CacheDto(clusterId, Lists.newArrayList(p1, p2));

        assertThat(patchUpdateCacheDto.getPatchResults().size(), is(5));
    }

    @Test
    public void convertUpdateRequest2CacheDtoForRoleType() {
        PatchUpdateRequest p1 = mockPatchUpdateRequest("/home/<USER>", "rdk", "role");
        PatchUpdateRequest p2 = mockPatchUpdateRequest("/home/<USER>", "rdk_1", "role");

        PatchUpdateCacheDto patchUpdateCacheDto =
            patchUpdateBeansConvertor.convertUpdateRequest2CacheDto(clusterId, Lists.newArrayList(p1, p2));

        assertThat(patchUpdateCacheDto.getPatchResults().size(), is(5));
    }

    private PatchUpdateRequest mockPatchUpdateRequest(String patchHome, String instanceId, String patchType) {
        PatchUpdateRequest p1 = new PatchUpdateRequest();
        p1.setPatchType("role");
        p1.setClusterId(clusterId);
        p1.setPatchHome(patchHome);
        Map<String, List<ServicePatchInfo>> m1 = Maps.newHashMap();
        for (int i = 0; i < 5; i++) {
            ServicePatchInfo servicePatchInfo = new ServicePatchInfo();
            List<SimplePatchInfo> s1 = Lists.newArrayList();
            for (int j = 0; j < 5; j++) {
                SimplePatchInfo simplePatchInfo = new SimplePatchInfo();
                simplePatchInfo.setPatchName("rdk-SP00" + j);
                s1.add(simplePatchInfo);
            }
            servicePatchInfo.setPatches(s1);
            ServiceInstance serviceInstance = new ServiceInstance();
            serviceInstance.setServiceInstanceId(instanceId);
            serviceInstance.setRoles(Lists.newArrayList("master", "slave"));
            servicePatchInfo.setServiceInstances(Lists.newArrayList(serviceInstance));
            m1.put("127.0.0." + i, Lists.newArrayList(servicePatchInfo));
        }
        p1.setIp2Patches(m1);
        return p1;
    }
}