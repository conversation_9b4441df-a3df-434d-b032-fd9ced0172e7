package com.zte.daip.manager.patcher.domain.rollback.executor;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.model.controller.api.RoleModelControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchHostInfo;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: RoleTypeRollbackExecutorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/4/14</p>
 * <p>完成日期：2023/4/14</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class RoleTypeRollbackExecutorTest {

    @InjectMocks
    private RoleTypeRollbackExecutor roleTypeRollbackExecutor;
    @Mock
    private PatchHomeQueryService patchHomeQueryService;
    @Mock
    private ServiceResourceControllerApi serviceResourceControllerApi;
    @Mock
    private PublishRollbackMsg publishRollbackMsg;
    @Mock
    private RoleModelControllerApi roleModelControllerApi;

    @Before
    public void setUp() throws Exception {

        List<ServiceRoleInfo> serviceRoles = Lists.newArrayList();
        ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setRoleName("HQ");
        serviceRoleInfo.setRoleId("dap.zdh.zk.hq");
        serviceRoleInfo.setServiceInstanceId("zk01");

        serviceRoles.add(serviceRoleInfo);
        Mockito.when(serviceResourceControllerApi.queryByClusterId(anyString())).thenReturn(serviceRoles);
        Mockito.when(patchHomeQueryService.queryDefaultZdhPatchHome()).thenReturn("/opt/ZDH/patch/");
        doNothing().when(publishRollbackMsg).publishRollbackMsg(anyString(), any(), anyString(), anyString(),
            anyBoolean());

    }

    @Test
    public void executeRollback() throws Exception {
        PatchRollbackHostDto rollbackHostDto = new PatchRollbackHostDto();
        PatchHostInfo patchHostInfo = new PatchHostInfo("*********", "host2");
        rollbackHostDto.setServiceName("zk");
        rollbackHostDto.setRoleName("HQ");
        rollbackHostDto.setServiceInstanceId("zk01");
        rollbackHostDto.setPatchName("Sp001");
        rollbackHostDto.setPatchHostInfoList(org.assertj.core.util.Lists.newArrayList(patchHostInfo));
        roleTypeRollbackExecutor.executeRollback("1", rollbackHostDto);
        verify(publishRollbackMsg, times(1)).publishRollbackMsg(anyString(), any(), anyString(), anyString(),
            anyBoolean());
    }
}