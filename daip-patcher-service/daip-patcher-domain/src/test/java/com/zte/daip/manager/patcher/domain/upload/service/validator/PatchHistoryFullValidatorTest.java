package com.zte.daip.manager.patcher.domain.upload.service.validator;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.operate.NormalPatchOperator;
import com.zte.daip.manager.patcher.domain.upload.operate.PatchTypeOperateFactory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class PatchHistoryFullValidatorTest {

    @Mock
    private PatchInfoService patchInfoService;

    @Mock
    private PatchTypeOperateFactory patchTypeOperateFactory;

    @Mock
    private NormalPatchOperator normalPatchOperator;

    @InjectMocks
    private PatchHistoryFullValidator patchHistoryFullValidator;

    @Test
    public void test() {
        ReflectionTestUtils.setField(patchHistoryFullValidator, "ignoreList", "VMAX");
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("DAP-SPARK-V20.19.40.R4.B2-SP030-20201215");
        patchDetailPo.setIsFullPatch(1);
        patchDetailPo.setBaseVersion("v20");
        patchDetailPo.setService("spark");
        patchDetailPo.setIsContainerPatch(0);
        when(patchInfoService.findByServiceAndBaseVersion(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchDetailPo));
        when(patchTypeOperateFactory.getPatchType(any())).thenReturn(normalPatchOperator);

        when(normalPatchOperator.filterHistoryFullPatch(any())).thenReturn(Lists.newArrayList(patchDetailPo));

        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName("DAP-SPARK-V20.19.40.R4.B2-SP029-20201212");
        patchBean.setSrcVersion("v20");
        patchBean.setService("spark");
        PatchUploadResult patchUploadResult = patchHistoryFullValidator.checkPatch(patchBean,
            new File("DAP-SPARK-V20.19.40.R4.B2-SP029-20201212.zip"), Lists.newArrayList(patchDetailPo));
        assertFalse(patchUploadResult.isSuccess());

    }

}