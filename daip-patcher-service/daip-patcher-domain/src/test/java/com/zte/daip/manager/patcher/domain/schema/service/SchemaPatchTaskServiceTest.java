/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: SchemaPatchTaskServiceTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2024/1/19
 * </p>
 * <p>
 * 完成日期：2024/1/19
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.schema.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.ClusterBean;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.common.deployer.model.controller.api.ClusterVersionControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchFlagApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.domain.schema.service.SchemaPatchTaskService;
import com.zte.daip.manager.patcher.domain.schema.utils.SchemaPatchUtils;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class SchemaPatchTaskServiceTest {

    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Mock
    private PatchInfoService patchInfoService;

    @Mock
    private PatchHistoryService patchHistoryService;

    @Mock
    private SchemaPatchFlagApi schemaPatchFlagApi;

    @Mock
    private SchemaPatchApi schemaPatchApi;

    @Mock
    private SchemaPatchUtils schemaPatchUtils;

    @Mock
    private PatchTaskOperateService patchTaskOperateService;

    @Mock
    private ClusterInfoControllerApi clusterInfoControllerApi;

    @InjectMocks
    private SchemaPatchTaskService schemaPatchTaskService;

    @Test
    public void 升级scheme补丁() throws Exception {
        schemaPatchTaskService.executeSchemaPatchTask();
        assertNull(null);
    }

    @Test
    public void queryCanCopyPatchesTest() throws Exception {
        List<PatchTaskInfo> patchTaskInfos = schemaPatchTaskService.queryCanCopyPatches(1L);
        assertFalse(patchTaskInfos.isEmpty());
    }

    @Before
    public void init() {
        Mockito.when(schemaPatchFlagApi.isFinished()).thenReturn(true);
        Mockito.when(productModelInfoControllerApi.query(ArgumentMatchers.any(), ArgumentMatchers.any()))
            .thenReturn(new ServiceModel("deployer", "deployer"));
        Mockito.when(patchInfoService.queryAllPatchInfos()).thenReturn(initPatchDetail());
        Mockito.when(schemaPatchUtils.isProductLoadSuccess(ArgumentMatchers.any(), ArgumentMatchers.any()))
            .thenReturn(true);
        Mockito.when(schemaPatchUtils.checkTaskPatchDependency(ArgumentMatchers.any(), ArgumentMatchers.any(),
            ArgumentMatchers.any())).thenReturn(true);
        Mockito.when(schemaPatchApi.queryPatchInfosByService(ArgumentMatchers.any(), ArgumentMatchers.any()))
            .thenReturn(Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP032-20201215"));
        Mockito.when(patchHistoryService.querySchemaPatchHistory())
            .thenReturn(Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
        Mockito.when(clusterInfoControllerApi.queryAll()).thenReturn(Lists.newArrayList(new ClusterBean()));
        PatchTaskInfo patchTaskInfo = new PatchTaskInfo();
        patchTaskInfo.setServiceName("zookeeper");
        patchTaskInfo.setVersion("V1");
        patchTaskInfo.setUpdatePatchNames(Lists.newArrayList("DAP-zookeeper-V20.19.40.R4.B2-schema-SP030-20201215"));
        Mockito.when(schemaPatchApi.queryByUpdateTaskId(1L)).thenReturn(Lists.newArrayList(patchTaskInfo));
        Mockito.when(patchInfoService.queryPatchName())
            .thenReturn(Lists.newArrayList("DAP-zookeeper-V20.19.40.R4.B2-schema-SP030-20201215"));
    }

    private List<PatchDetailPo> initPatchDetail() {
        PatchDetailPo patchDetail1 = new PatchDetailPo();
        patchDetail1.setPatchName("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215");
        patchDetail1.setBaseVersion("V20.19.40.R4.B2");
        patchDetail1.setService("deployer");
        patchDetail1.setDependPatch("DAP-deployer-V20.19.40.R4.B2-schema-SP029-20201215");

        PatchDetailPo patchDetail2 = new PatchDetailPo();
        patchDetail2.setPatchName("DAP-deployer-V20.19.40.R4.B2-schema-SP032-20201215");
        patchDetail2.setBaseVersion("V20.19.40.R4.B2");
        patchDetail2.setService("deployer");
        patchDetail2.setDependPatch("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215");

        PatchDetailPo patchDetail3 = new PatchDetailPo();
        patchDetail3.setPatchName("DAP-deployer-V20.19.40.R4.B2-schema-SP035-20201215");
        patchDetail3.setBaseVersion("V20.19.40.R4.B2");
        patchDetail3.setService("deployer");
        patchDetail3.setDependPatch("DAP-deployer-V20.19.40.R4.B2-schema-SP032-20201215");
        return Lists.newArrayList(patchDetail2, patchDetail1, patchDetail3);
    }
}