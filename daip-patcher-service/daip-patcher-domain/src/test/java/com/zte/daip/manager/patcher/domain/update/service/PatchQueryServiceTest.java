package com.zte.daip.manager.patcher.domain.update.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.InstancePatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.RolePatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.ServicePatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.ZDHPatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;

import static org.mockito.ArgumentMatchers.*;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchQueryServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/5/17</p>
 * <p>完成日期：2021/5/17</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchQueryServiceTest {

    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchHistoryService patchHistoryService;
    @Mock
    private ServicePatchSourceGenerator servicePatchSourceGenerator;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private ServiceModelInfoCache serviceModelInfoCache;
    @Mock
    private PatchTypeQueryService patchTypeQueryService;
    @Mock
    private PatchHomeQueryService patchHomeQueryService;
    @Mock
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;
    @InjectMocks
    private PatchQueryService patchQueryService;

    @Test
    public void queryUpdatedContainerPatches() {
        String serviceName = "zdh";
        String version = "V20.19.40.R4.B2";

        Mockito.when(patchInfoService.findByServiceAndBaseVersion("zdh", "V20.19.40.R4.B2")).thenReturn(mockzdhPatch());
        Mockito.when(patchHistoryService.queryPatchHistoryInfoByServiceName(serviceName))
            .thenReturn(mockPatchHistory());

        Map<String, List<String>> ip2ContainedPatches =
            patchQueryService.queryUpdatedContainerPatches(serviceName, version);

        Assert.assertEquals(2, ip2ContainedPatches.keySet().size());
    }

    @Test
    public void queryUpdatePatchInfo() throws DaipBaseException {
        boolean result = true;
        try {
            ServiceModel serviceModel = new ServiceModel();
            serviceModel.setVersion("V20.19.40.R4.B2");

            Mockito.when(serviceModelInfoCache.queryByClusterIdAndServiceName(any(), any())).thenReturn(serviceModel);
            Mockito.when(serviceModelInfoCache.queryByClusterIdAndServiceName(anyString(), anyString()))
                .thenReturn(serviceModel);
            List<DeploymentServiceInstance> serviceInstances = Lists.newArrayList(new DeploymentServiceInstance());
            Mockito.when(deploymentInstanceServiceControllerApi.queryByClusterIdAndServiceId(anyString(), any()))
                .thenReturn(serviceInstances);
            ConfigInstance configInstance = new ConfigInstance();
            configInstance.setConfigValue("/opt/ZDH");
            Mockito.when(patchHomeQueryService.queryPatchHome(anyString(), any()))
                .thenReturn(Lists.newArrayList(configInstance));

            Mockito.when(patchTypeQueryService.queryPatchTypeByServiceName(anyString(), anyString()))
                .thenReturn("service");
            RollAutoPatchUpdateInfo rollAutoPatchUpdateInfo = new RollAutoPatchUpdateInfo();
            Mockito.when(servicePatchSourceGenerator.obtainPatchUpdateInfos(any(), anyString(), anyList()))
                .thenReturn(Lists.newArrayList(rollAutoPatchUpdateInfo));
            Mockito.when(applicationContext.getBean(PatchTypeEnum.SERVICE.getServiceName()))
                .thenReturn(new ServicePatchSourceGenerator());
            Mockito.when(applicationContext.getBean(PatchTypeEnum.ROLE.getServiceName()))
                .thenReturn(new RolePatchSourceGenerator());
            Mockito.when(applicationContext.getBean(PatchTypeEnum.INSTANCE.getServiceName()))
                .thenReturn(new InstancePatchSourceGenerator());
            Mockito.when(applicationContext.getBean(PatchTypeEnum.ZDH.getServiceName()))
                .thenReturn(new ZDHPatchSourceGenerator());
            List<RollAutoPatchUpdateInfo> rollAutoPatchUpdateInfos =
                patchQueryService.queryUpdatePatchInfo(new RollAutoPatchUpdateParam());
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(!result);

    }

    private List<PatchHistory> mockPatchHistory() {

        List<PatchHistory> patchHistories = new ArrayList<>();

        PatchHistory patchHistory1 = new PatchHistory();
        PatchHistoryKey patchHistoryKey1 = new PatchHistoryKey();
        patchHistoryKey1.setPatchName("DAP-zdh-V20.19.40.R4.B2-SP001-container");
        patchHistoryKey1.setIp("*************");
        patchHistoryKey1.setServiceName("zdh");
        patchHistory1.setId(patchHistoryKey1);
        patchHistories.add(patchHistory1);

        PatchHistory patchHistory2 = new PatchHistory();
        PatchHistoryKey patchHistoryKey2 = new PatchHistoryKey();
        patchHistoryKey2.setPatchName("DAP-zdh-V20.19.40.R4.B2-SP002");
        patchHistoryKey2.setIp("*************");
        patchHistoryKey2.setServiceName("zdh");
        patchHistory2.setId(patchHistoryKey2);
        patchHistories.add(patchHistory2);

        PatchHistory patchHistory3 = new PatchHistory();
        PatchHistoryKey patchHistoryKey3 = new PatchHistoryKey();
        patchHistoryKey3.setPatchName("DAP-zdh-V20.19.40.R4.B2-SP001-container");
        patchHistoryKey3.setIp("*************");
        patchHistoryKey3.setServiceName("zdh");
        patchHistory3.setId(patchHistoryKey3);
        patchHistories.add(patchHistory3);

        PatchHistory patchHistory4 = new PatchHistory();
        PatchHistoryKey patchHistoryKey4 = new PatchHistoryKey();
        patchHistoryKey4.setPatchName("DAP-zdh-V20.19.40.R4.B2-SP003-container");
        patchHistoryKey4.setIp("*************");
        patchHistoryKey4.setServiceName("zdh");
        patchHistory4.setId(patchHistoryKey4);
        patchHistories.add(patchHistory4);

        return patchHistories;
    }

    private List<PatchDetailPo> mockzdhPatch() {

        int count = 5;
        List<PatchDetailPo> patchDetailPos = Lists.newArrayList();
        for (int i = 0; i < count; i++) {

            PatchDetailPo patchDetailPo1 = new PatchDetailPo();
            patchDetailPo1.setPatchName("DAP-zdh-V20.19.40.R4.B2-SP00" + i + "-container");
            patchDetailPo1.setBaseVersion("V20.19.40.R4.B2");
            patchDetailPo1.setService("zdh");
            patchDetailPo1.setDependPatch("DAP-zdh-V20.19.40.R4.B2-SP00" + (i - 1) + "-container");
            patchDetailPo1.setIsContainerPatch(1);
            patchDetailPos.add(patchDetailPo1);

        }
        return patchDetailPos;
    }
}