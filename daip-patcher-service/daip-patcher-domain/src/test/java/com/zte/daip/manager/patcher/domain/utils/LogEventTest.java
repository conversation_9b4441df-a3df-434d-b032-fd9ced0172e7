package com.zte.daip.manager.patcher.domain.utils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.zte.daip.manager.event.beans.OperationDesc;
import com.zte.daip.manager.event.beans.OperationResult;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;

/**
 * <p><owner>10231295</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: LogEventTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2024/1/12</p>
 * <p>完成日期：2024/1/12</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(MockitoJUnitRunner.class)
public class LogEventTest {

    @InjectMocks
    private LogEvent logEvent;
    @Mock
    private DaipEventReporter daipEventReporter;

    @Test
    public void logOpt() {
        String operationNameI18nKey = "";
        String operationObjI18nKey = "";
        String failReason = "";

        logEvent.logOpt(operationNameI18nKey, operationObjI18nKey, failReason);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void testLogOpt() {
        String operationNameI18nKey = "";
        String operationObjI18nKey = "";
        String extraI18nKey = "";
        String[] extraI18nArgs = new String[0];

        logEvent.logOpt(operationNameI18nKey, operationObjI18nKey, extraI18nKey, extraI18nArgs);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void testLogOpt1() {
        String operationNameI18nKey = "";
        String[] operationNameI18nArgs = new String[0];
        String operationObjI18nKey = "";
        OperationDesc optDesc = null;

        logEvent.logOpt(operationNameI18nKey, operationNameI18nArgs, operationObjI18nKey, optDesc);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void testLogOpt2() {
        String operationNameI18nKey = "";
        String[] operationNameI18nArgs = new String[0];
        String operationObjI18nKey = "";
        OperationDesc optDesc = null;
        String failReason = "";

        logEvent.logOpt(operationNameI18nKey, operationNameI18nArgs, operationObjI18nKey, optDesc, failReason);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void testLogOpt3() {
        String operationNameI18nKey = "";
        String[] operationNameI18nArgs = new String[0];
        String operationObjI18nKey = "";
        OperationDesc optDesc = null;
        OperationResult operationResult = OperationResult.succeed();

        logEvent.logOpt(operationNameI18nKey, operationNameI18nArgs, operationObjI18nKey, optDesc, operationResult);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void logEvent() {
        String eventName = "";
        String[] i18nArgs = new String[0];
        String extraInfo = "";
        String[] extraInfoI18nArgs = new String[0];

        logEvent.logEvent(eventName, i18nArgs, extraInfo, extraInfoI18nArgs);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void testLogEvent() {
        String eventName = "";
        String[] i18nArgs = new String[0];
        String extraInfo = "";
        String[] extraInfoI18nArgs = new String[0];
        String failReason = "";

        logEvent.logEvent(eventName, i18nArgs, extraInfo, extraInfoI18nArgs, failReason);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void testLogEvent1() {
        String eventName = "";
        String[] i18nArgs = new String[0];
        String extraInfo = "";
        String[] extraInfoI18nArgs = new String[0];
        OperationResult operationResult = OperationResult.succeed();

        logEvent.logEvent(eventName, i18nArgs, extraInfo, extraInfoI18nArgs, operationResult);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void testLogEvent2() {
        String eventName = "";
        String[] i18nArgs = new String[0];
        String extraInfo = "";
        OperationResult operationResult = OperationResult.succeed();

        logEvent.logEvent(eventName, i18nArgs, extraInfo, operationResult);
        verify(daipEventReporter, times(1)).report(any());
    }

}