package com.zte.daip.manager.patcher.domain.upload.service.listener;

import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatingQueue;
import com.zte.daip.manager.patcher.domain.upload.event.PatchValidateEvent;
import com.zte.daip.manager.patcher.domain.upload.pool.PatchFileValidateExecutor;
import com.zte.daip.manager.patcher.domain.upload.service.PatchValidateService;
import io.jsonwebtoken.JwsHeader;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
public class PatchValidateEventListenerTest {

    @Mock
    private PatchFileValidateExecutor patchFileValidateExecutor;

    @Mock
    private PatchValidatingQueue patchValidatingQueue;

    @Mock
    private PatchInfoService patchInfoService;

    @InjectMocks
    private PatchValidateEventListener patchValidateEventListener;

    @Test
    public void onApplicationEvent() {
        Mockito.when(patchValidatingQueue.isEmpty()).thenReturn(false);
        Mockito.when(patchFileValidateExecutor.checkThread()).thenReturn(true);
        Mockito.when(patchInfoService.queryAllPatchInfos()).thenReturn(Lists.newArrayList());
        Mockito.when(patchValidatingQueue.pollOnePatch()).thenReturn(null);
        patchValidateEventListener.onApplicationEvent(new PatchValidateEvent("aaa"));
    }
}