package com.zte.daip.manager.patcher.domain.update.listener;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.communication.producer.EventPublisher;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchInfoCleanLocalCacheSenderTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2022/10/13</p>
 * <p>完成日期：2022/10/13</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class PatchInfoCleanLocalCacheSenderTest {
    @Mock
    private EventPublisher eventPublisher;
    @InjectMocks
    private PatchInfoCleanLocalCacheSender patchInfoCleanLocalCacheSender;

    @Test
    public void sendMsgToClearLocalCache() {
        Mockito.doNothing().when(eventPublisher).send(any(), any());

        String key = "1_ZDH_/opt/patch";
        patchInfoCleanLocalCacheSender.sendMsgToClearLocalCache(key);

        verify(eventPublisher, times(1)).send(any(), any());
    }
}