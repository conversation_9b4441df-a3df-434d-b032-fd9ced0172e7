package com.zte.daip.manager.patcher.domain.rollback.executor;

import com.zte.daip.communication.reply.producer.ReplyingEventPublisher;
import com.zte.daip.manager.patcher.api.dto.PatchHostInfo;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PublishRollbackMsgTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/4/17</p>
 * <p>完成日期：2023/4/17</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PublishRollbackMsgTest {

    @InjectMocks
    private PublishRollbackMsg publishRollbackMsg;

    @Mock
    private ReplyingEventPublisher replyingEventPublisher;

    @Test
    public void publishRollbackMsg() {
        doNothing().when(replyingEventPublisher).send(anyString(), any(), anyString());
        PatchRollbackHostDto rollbackHostDto = new PatchRollbackHostDto();
        PatchHostInfo patchHostInfo = new PatchHostInfo("*********", "host2");
        rollbackHostDto.setServiceName("kafka");
        rollbackHostDto.setPatchName("Sp001");
        rollbackHostDto.setPatchHostInfoList(Lists.newArrayList(patchHostInfo));
        publishRollbackMsg.publishRollbackMsg("1", rollbackHostDto, "/opt/ZDH/patch", "ls", true);
        verify(replyingEventPublisher, times(1)).send(anyString(), any(), anyString());
    }
}