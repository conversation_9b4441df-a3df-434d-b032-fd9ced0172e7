/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchUpdateServiceTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/9/30
 * </p>
 * <p>
 * 完成日期：2021/9/30
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.service;
/* Started by AICoder, pid:25ac5vc5d96dc00148e90b0481a6c861bae728e6 */
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.domain.update.PatchUpdateService;
import com.zte.daip.manager.patcher.domain.update.bean.ServiceNeedUpdatePatchInfo;
import com.zte.daip.manager.patcher.domain.update.executor.InstanceTypePatchParamRequester;
import com.zte.daip.manager.patcher.domain.update.executor.depend.DependResourceLocalUtil;
import com.zte.daip.manager.patcher.domain.update.sender.PatchUpdateMessageSender;
import com.zte.daip.manager.patcher.inner.api.dto.PatchRollbackHostInfo;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static junit.framework.TestCase.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchUpdateServiceTest {
    @Mock
    private PatchQueryService patchQueryService;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private PatchTypeQueryService patchTypeQueryService;

    @Mock
    private PatchUpdateMessageSender patchUpdateMessageSender;

    @Mock
    private PatchRequestCheckService patchRequestCheckService;

    @Mock
    private InstanceTypePatchParamRequester instanceTypePatchParamRequester;

    @Mock
    private DaipEventReporter daipEventReporter;
    @Mock
    private DependResourceLocalUtil dependResourceLocalUtil;

    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @InjectMocks
    private PatchUpdateService patchUpdateService;

    @Before
    public void init() {
        Mockito.when(patchTypeQueryService.queryPatchTypeByServiceName(any(), any())).thenReturn("role");
        Mockito.when(applicationContext.getBean(any(), (Class<Object>)any()))
                .thenReturn(instanceTypePatchParamRequester);

        Mockito.when(instanceTypePatchParamRequester.organizeRequest(any(), any(), any()))
                .thenReturn(Lists.newArrayList());
        ServiceNeedUpdatePatchInfo serviceNeedUpdatePatchInfo = new ServiceNeedUpdatePatchInfo();
        serviceNeedUpdatePatchInfo.setPatchInfoList(new ArrayList<>());
        serviceNeedUpdatePatchInfo.setServiceInstanceId("test");
        serviceNeedUpdatePatchInfo.setNeedUpdatePatchList(new ArrayList<>());
        Mockito.when(instanceTypePatchParamRequester.queryRollbackPoints(any(), any()))
                .thenReturn(serviceNeedUpdatePatchInfo);
        doNothing().when(daipEventReporter).error(anyString(), anyString());
        doNothing().when(daipEventReporter).info(anyString(), anyString());
        doNothing().when(dependResourceLocalUtil).initDependResource(anyString());
    }

    @Test
    public void testUpdate() {
        // 测试更新方法，并验证异常处理
        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest.setClusterId("0");
        ServiceInstanceInfo serviceInstanceInfo = new ServiceInstanceInfo();
        serviceInstanceInfo.setServiceInstanceId("hdfs");
        serviceInstanceInfo.setServiceName("hdfs");
        serviceInstanceInfo.setServiceInstanceId("hdfs");
        serviceInstanceInfo.setVersion("V1");
        updateRequest.setServiceInstanceInfos(Lists.newArrayList(serviceInstanceInfo));
        patchUpdateService.update(updateRequest);

        Assert.assertTrue(true);
    }

    @Test
    public void queryRollbackPoints() {
        // 测试查询回滚点的方法，并验证异常处理
        List<ServiceInstancePatchInfo> patchRollbackHostInfos =
                patchUpdateService.queryRollbackPoints("0", Lists.newArrayList());
        Assert.assertEquals(0, patchRollbackHostInfos.size());

        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceId("dap.manager.hdfs");
        serviceModel.setServiceName("hdfs");
        List<ServiceModel> serviceModels = Lists.newArrayList(serviceModel);
        Mockito.when(productModelInfoControllerApi.queryByClusterId(any())).thenReturn(serviceModels);
        Mockito.when(patchTypeQueryService.queryBigDataServiceIds(any())).thenReturn(Sets.newHashSet("hdfs"));
        ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setServiceId("dap.manager.hdfs");
        serviceRoleInfo.setServiceInstanceId("hdfs");
        List<ServiceRoleInfo> serviceRoles = Lists.newArrayList(serviceRoleInfo);
        Mockito.when(dependResourceLocalUtil.getClusterServiceRoleInfo(any())).thenReturn(serviceRoles);

        boolean result = true;
        try {
            patchUpdateService.queryRollbackPoints("0", Collections.singletonList("hdfs"));
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryNeedUpdatePatchs() {
        // 测试查询需要更新的补丁的方法，并验证异常处理
        List<ServiceInstancePatchInfo> patchRollbackHostInfos =
                patchUpdateService.queryRollbackPoints("0", Lists.newArrayList());
        Assert.assertEquals(0, patchRollbackHostInfos.size());

        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceId("dap.manager.hdfs");
        serviceModel.setServiceName("hdfs");
        List<ServiceModel> serviceModels = Lists.newArrayList(serviceModel);
        Mockito.when(productModelInfoControllerApi.queryByClusterId(any())).thenReturn(serviceModels);
        Mockito.when(patchTypeQueryService.queryBigDataServiceIds(any())).thenReturn(Sets.newHashSet("hdfs"));
        ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setServiceId("dap.manager.hdfs");
        serviceRoleInfo.setServiceInstanceId("hdfs");
        List<ServiceRoleInfo> serviceRoles = Lists.newArrayList(serviceRoleInfo);
        Mockito.when(dependResourceLocalUtil.getClusterServiceRoleInfo(any())).thenReturn(serviceRoles);

        boolean result = true;
        try {
            patchUpdateService.queryNeedUpdatePatchs("0", Collections.singletonList("hdfs"));
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }
}

/* Ended by AICoder, pid:25ac5vc5d96dc00148e90b0481a6c861bae728e6 */
