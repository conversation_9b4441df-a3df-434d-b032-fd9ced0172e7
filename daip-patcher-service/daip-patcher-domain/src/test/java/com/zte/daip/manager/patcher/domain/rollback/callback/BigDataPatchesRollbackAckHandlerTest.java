package com.zte.daip.manager.patcher.domain.rollback.callback;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

import java.util.*;

import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.bean.ResponseMessageBody;
import com.zte.daip.manager.patcher.api.dto.BigDataPatchRollbackMsg;
import com.zte.daip.manager.patcher.api.update.response.PatchRecordAndRecoverInfos;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.rollback.cache.PatchRollbackProgressQueue;
import com.zte.daip.manager.patcher.domain.rollback.cache.PatchRollbackResultQueue;
import com.zte.daip.manager.patcher.domain.rollback.entity.RollbackProgressDo;

/* Started by AICoder, pid:e92be22672wd3f514efb0b62d0cfc2874ba5946c */
@RunWith(SpringRunner.class)
public class BigDataPatchesRollbackAckHandlerTest {

    private ResponseMessageBody<PatchResult> responseMessageBody;

    private PatchResult patchResult;

    @Mock
    private PatchRollbackProgressQueue patchRollbackProgressQueue;

    @Mock
    private PatchRollbackResultQueue patchRollbackResultQueue;

    private RequestMessageBody<List<BigDataPatchRollbackMsg>> requestMessageBody;

    @InjectMocks
    private BigDataPatchesRollbackAckHandler bigDataPatchesRollbackAckHandler;

    private List<BigDataPatchRollbackMsg> bigDataPatchRollbackMsgs;

    @Test
    public void testResponseWithSuccess() {
        // given
        String body = "{\"result\":" + JSON.toJSONString(patchResult) + "}";
        String hostName = "host1";

        // when
        String result = bigDataPatchesRollbackAckHandler.response(hostName, body, requestMessageBody);

        // then
        assertEquals("success", result);
    }

    @Test
    public void testResponseWithFailure() {
        // given
        patchResult.setUpdateSuccess(false);
        String body = "{\"result\":" + JSON.toJSONString(patchResult) + "}";
        String hostName = "host1";

        // when
        String result = bigDataPatchesRollbackAckHandler.response(hostName, body, requestMessageBody);

        // then
        assertEquals("failed", result);
    }

    @Test
    public void testOnFailureWithNonEmptyHostNamesAndRequestMessageBody() {
        // Given
        Set<String> hostNames = new HashSet<>(Arrays.asList("host1", "host2"));
        Throwable ex = new Exception("Error message");
        // When
        bigDataPatchesRollbackAckHandler.onFailure(hostNames, ex, requestMessageBody);

        // Then
        verify(patchRollbackProgressQueue, times(4)).add(any(RollbackProgressDo.class));
    }

    @Before
    public void setUp() {
        bigDataPatchRollbackMsgs = new ArrayList<>();
        BigDataPatchRollbackMsg msg1 = new BigDataPatchRollbackMsg();
        msg1.setHostNames(Lists.newArrayList("host1"));
        msg1.setServiceNames(Lists.newArrayList("zookeeper", "hdfs"));
        msg1.setClusterId("1");
        bigDataPatchRollbackMsgs.add(msg1);
        BigDataPatchRollbackMsg msg2 = new BigDataPatchRollbackMsg();
        msg2.setHostNames(Lists.newArrayList("host2"));
        msg2.setServiceNames(Lists.newArrayList("kafka", "yarn"));
        msg2.setClusterId("2");
        bigDataPatchRollbackMsgs.add(msg2);
        requestMessageBody = new RequestMessageBody();
        requestMessageBody.setBody(bigDataPatchRollbackMsgs);

        patchResult = new PatchResult();
        patchResult.setUpdateSuccess(true);
        patchResult.setHostIp("host1");
        PatchRecordAndRecoverInfos patchRecordAndRecoverInfos = new PatchRecordAndRecoverInfos();
        patchRecordAndRecoverInfos.setService("zookeeper");
        patchResult.setPatchRecordAndRecoverInfos(Lists.newArrayList(patchRecordAndRecoverInfos));
        responseMessageBody = new ResponseMessageBody<>();
        responseMessageBody.setResult(patchResult);
    }
}

/* Ended by AICoder, pid:e92be22672wd3f514efb0b62d0cfc2874ba5946c */