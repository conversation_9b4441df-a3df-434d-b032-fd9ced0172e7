package com.zte.daip.manager.patcher.domain.task.service;

import static com.zte.daip.communication.bean.MessageType.MULICAST;
import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import com.zte.daip.manager.common.deployer.bean.cluster.ClusterBean;
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSONObject;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.exception.MessageCenterException;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class CleanPatchTaskByClusterDeletedTest {

    @Mock
    private PatchTaskService patchTaskService;

    @InjectMocks
    private CleanPatchTaskByClusterDeleted cleanPatchTaskByClusterDeleted;

    /* Started by AICoder, pid:ga1c0y9024pe48a147e1092240f0cc1492e82c59 */
    @Test
    public void testCleanPatchTaskByClusterDeleted() throws MessageCenterException {
        List<PatchTaskPo> patchTaskPos = new ArrayList<>();
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setClusterId("1");
        patchTaskPos.add(patchTaskPo);
        Mockito.when(patchTaskService.queryAllPatchTasks()).thenReturn(patchTaskPos);

        String result = cleanPatchTaskByClusterDeleted.handle(generateRequestMsg());
        assertTrue(StringUtils.equals(result, "success"));
    }

    private String generateRequestMsg() {
        RequestMessageBody requestMessageBody = new RequestMessageBody();
        requestMessageBody.setType(MULICAST.getType());
        ClusterBean clusterBean1 = new ClusterBean();
        clusterBean1.setClusterId(1);
        ClusterBean clusterBean2 = new ClusterBean();
        clusterBean2.setClusterId(2);
        requestMessageBody.setBody(Arrays.asList(clusterBean1, clusterBean2));
        return JSONObject.toJSONString(requestMessageBody);
    }
    /* Ended by AICoder, pid:ga1c0y9024pe48a147e1092240f0cc1492e82c59 */
}