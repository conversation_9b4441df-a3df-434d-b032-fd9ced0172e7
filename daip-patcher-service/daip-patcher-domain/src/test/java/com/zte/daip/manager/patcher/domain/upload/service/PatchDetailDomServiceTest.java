package com.zte.daip.manager.patcher.domain.upload.service;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchDispatchService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.assembler.PatchDetailAssembler;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchDetailDomServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2022/6/7</p>
 * <p>完成日期：2022/6/7</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
@Slf4j
public class PatchDetailDomServiceTest {

    @Mock
    private PatchDetailAssembler patchDetailAssembler;
    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchDispatchService patchDispatchService;
    @InjectMocks
    private PatchDetailDomService patchDetailDomService;

    @Test
    public void saveValidPatches() {



        Mockito.doAnswer(invocation -> {
            return 1;
        }).when(patchInfoService).saveAllPatchDetail(any());



        Mockito.when(patchDetailAssembler.patchBean2DbBean(any())).thenReturn(generatePatchDetailPo());

        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP001-20220607");
        List<PatchBean> patchBeans = Lists.newArrayList();
        patchBeans.add(patchBean);

        patchDetailDomService.saveValidPatches(patchBeans);
        assertNull(null);
    }

    private PatchDetailPo generatePatchDetailPo() {
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP001-20220607");
        return patchDetailPo;
    }
}