package com.zte.daip.manager.patcher.domain.task.assembler;
/* Started by AICoder, pid:d694dbab47i914b1446109693102d253154063c9 */
import com.alibaba.fastjson.JSONObject;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchCategoryEnum;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowRequest;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowSummaryRequest;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
public class PatchTaskAssemblerTest {
    @InjectMocks
    private PatchTaskAssembler patchTaskAssembler;
    @Mock
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    private PatchTaskDto patchTaskDto = new PatchTaskDto();

    private PatchTaskPo patchTaskPo = new PatchTaskPo();

    private long taskId = 1L;

    private String taskName = "1";

    private String serviceId = "dap.amanager.hdfs";

    private String service = "hdfs";

    @Before
    public void init() throws DaipBaseException {
        // serviceInstancePatchInfo
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceId(serviceId);
        serviceInstance.setServiceName(service);
        serviceInstance.setServiceInstanceId(service);
        serviceInstance.setServiceInstanceName(serviceId);
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);

        // patchTaskDto
        patchTaskDto.setTaskId(taskId);
        patchTaskDto.setTaskName(taskName);
        patchTaskDto.setAllowModify(true);
        patchTaskDto.setCreateTime("2023-10-18 14:55:20");
        patchTaskDto.setContext(Lists.newArrayList(serviceInstancePatchInfo));
        patchTaskDto.setRelationServices(Lists.newArrayList(serviceInstance));
        patchTaskDto.setNeedRestartServices(Lists.newArrayList(serviceInstance));

        // patchTaskPo
        patchTaskPo.setTaskId(taskId);
        patchTaskPo.setTaskName(taskName);
        patchTaskPo.setCreateTime(new Date());
        patchTaskPo.setPatchCategory(PatchCategoryEnum.ORDINARY.getPatchCategory());
        patchTaskPo.setContext(JSONObject.toJSONString(patchTaskDto.getContext()));
        patchTaskPo.setRelationServices(JSONObject.toJSONString(patchTaskDto.getRelationServices()));
        patchTaskPo.setNeedRestartServices(JSONObject.toJSONString(patchTaskDto.getNeedRestartServices()));
    }

    @Test
    public void patchTaskDto2Po() {
        // 测试方法，将PatchTaskDto转换为PatchTaskPo
        PatchTaskPo taskPo = patchTaskAssembler.convertPatchTaskDto2Po(patchTaskDto);
        Assert.assertEquals(taskPo.getTaskName(), patchTaskDto.getTaskName());
        Assert.assertEquals(taskPo.getContext(), patchTaskPo.getContext());
    }

    @Test
    public void patchTaskPo2Dto() throws DaipBaseException {
        // 测试方法，将PatchTaskPo转换为PatchTaskDto
        PatchTaskDto taskDto = patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo);
        Assert.assertEquals(patchTaskPo.getTaskName(), taskDto.getTaskName());
        String serviceId1 = patchTaskDto.getContext().get(0).getServiceInstance().getServiceId();
        String serviceId2 = taskDto.getContext().get(0).getServiceInstance().getServiceId();
        Assert.assertEquals(serviceId1, serviceId2);
    }

    @Test
    public void patchTaskPo2NestWorkRequest() throws DaipBaseException {
        // 测试方法，将PatchTaskPo转换为NestWorkFlowRequest，并验证异常处理
        Assert.assertThrows(DaipBaseException.class,
                () -> patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTaskPo, null));

        List<ServiceModel> serviceModels = Lists.newArrayList();
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceId(serviceId);
        serviceModel.setServiceName(service);
        serviceModels.add(serviceModel);
        Mockito.when(productModelInfoControllerApi.queryByClusterId(patchTaskPo.getClusterId()))
                .thenReturn(serviceModels);

        NestWorkFlowRequest request = patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTaskPo);
        Assert.assertEquals(patchTaskPo.getTaskName(), request.getParentName());

        patchTaskPo.setPatchCategory(PatchCategoryEnum.SCHEMA.getPatchCategory());
        NestWorkFlowRequest request1 = patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTaskPo);
        Assert.assertEquals(patchTaskPo.getTaskName(), request1.getParentName());

        patchTaskPo.setContext("");
        Assert.assertThrows(DaipBaseException.class,
                () -> patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTaskPo, null));
    }

    @Test
    public void patchTaskPo2NestWorkFlowSummaryRequest() throws DaipBaseException {
        // 测试方法，将PatchTaskPo转换为NestWorkFlowSummaryRequest
        NestWorkFlowSummaryRequest request = patchTaskAssembler.patchTaskPo2NestWorkFlowSummaryRequest(patchTaskPo);
        Assert.assertEquals(patchTaskPo.getTaskName(), request.getParentName());
    }

    @Test
    public void assemblePatchTaskPo2AccessType() throws DaipBaseException {
        // 测试方法，根据PatchTaskPo的任务类型和操作类型来组装访问类型，并验证异常处理
        PatchTaskPo patchTaskPo1 = new PatchTaskPo();
        patchTaskPo1.setTaskType(2);
        patchTaskPo1.setOperateType(1);
        patchTaskPo1.setTaskName(taskName);
        Assert.assertThrows(DaipBaseException.class,
                () -> patchTaskAssembler.assemblePatchTaskPo2AccessType(patchTaskPo1));

        PatchTaskPo patchTaskPo2 = new PatchTaskPo();
        patchTaskPo2.setTaskType(1);
        patchTaskPo2.setOperateType(2);
        patchTaskPo2.setTaskName(taskName);
        Assert.assertThrows(DaipBaseException.class,
                () -> patchTaskAssembler.assemblePatchTaskPo2AccessType(patchTaskPo2));

        String accessType = patchTaskAssembler.assemblePatchTaskPo2AccessType(patchTaskPo);
        Assert.assertEquals(accessType, "offline_update_patch");
    }
}

/* Ended by AICoder, pid:d694dbab47i914b1446109693102d253154063c9 */