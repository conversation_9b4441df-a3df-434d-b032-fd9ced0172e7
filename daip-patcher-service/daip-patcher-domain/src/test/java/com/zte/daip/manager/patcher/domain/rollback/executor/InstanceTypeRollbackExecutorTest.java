package com.zte.daip.manager.patcher.domain.rollback.executor;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.patcher.api.dto.PatchHostInfo;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: InstanceTypeRollbackExecutorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/4/14</p>
 * <p>完成日期：2023/4/14</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class InstanceTypeRollbackExecutorTest
{

    @InjectMocks
    private InstanceTypeRollbackExecutor instanceTypeRollbackExecutor;
    @Mock
    private ServiceModelInfoCache serviceModelInfoCache;
    @Mock
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;
    @Mock
    private PatchHomeQueryService patchHomeQueryService;
    @Mock
    private PublishRollbackMsg publishRollbackMsg;

    @Before
    public void setUp() throws Exception {
        Mockito.when(serviceModelInfoCache.queryByClusterIdAndServiceName(anyString(), anyString()))
            .thenReturn(new ServiceModel());
        DeploymentServiceInstance deploymentServiceInstance = new DeploymentServiceInstance();
        deploymentServiceInstance.setServiceInstanceId("zookeeper_1");
        Mockito.when(deploymentInstanceServiceControllerApi.queryByClusterIdAndServiceId(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(deploymentServiceInstance));
        doNothing().when(publishRollbackMsg).publishRollbackMsg(anyString(), any(), anyString(), anyString(),
            anyBoolean());
        ConfigInstance configInstance = new ConfigInstance();
        configInstance.setConfigValue("/home/<USER>");
        Mockito.when(patchHomeQueryService.queryPatchHome(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(configInstance));
    }

    @Test
    public void executeRollback() throws Exception {
        PatchHostInfo patchHostInfo = new PatchHostInfo("127.0.0.1", "host1");
        PatchHostInfo patchHostInfo2 = new PatchHostInfo("*********", "host2");
        PatchRollbackHostDto patchRollbackDo =
            new PatchRollbackHostDto("SP01", "zookeeper", "", "", Lists.newArrayList(patchHostInfo, patchHostInfo2));
        instanceTypeRollbackExecutor.executeRollback("1", patchRollbackDo);
        verify(publishRollbackMsg, times(1)).publishRollbackMsg(anyString(), any(), anyString(), anyString(),
            anyBoolean());
    }
}