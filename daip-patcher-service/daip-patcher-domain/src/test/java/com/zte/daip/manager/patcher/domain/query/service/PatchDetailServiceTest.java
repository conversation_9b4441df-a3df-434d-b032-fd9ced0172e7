/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDetailServiceTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/30
 * </p>
 * <p>
 * 完成日期：2021/3/30
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service;

import com.zte.daip.manager.common.utils.paging.jpa.QueryParam;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.api.dto.PendingUpdatePatchPageInfo;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchDispatchService;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchDetailDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchHistoryDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.domain.query.valobj.UnpatchedParam;
import com.zte.daip.manager.patcher.infrastructure.po.*;
import lombok.val;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchDetailServiceTest {

    @Mock
    private PatchInfoService patchInfoService;

    @Mock
    private PatchHistoryService patchHistoryService;

    @Mock
    private PatchDispatchService patchDispatchService;

    @Mock
    private UnpatchedService unpatchedService;

    @Spy
    private PatchDetailDtoAssembler patchDetailDtoAssembler = new PatchDetailDtoAssembler();
    @Spy

    private PatchHistoryDtoAssembler patchHistoryDtoAssembler = new PatchHistoryDtoAssembler();

    @Mock
    private PatchDetailAsyncQueryService patchDetailAsyncQueryService;

    @InjectMocks
    private PatchDetailService patchDetailService;

    @Before
    public void setUp() {

        Mockito.when(patchInfoService.queryAllPatchInfos()).thenReturn(mockPatchDetailPos());

        Mockito.when(patchInfoService.queryAllPatchExceptScheme()).thenReturn(Lists.newArrayList(new PatchDetailPo()));

        Mockito.when(patchHistoryService.queryPatchHistoryName()).thenReturn(Lists.newArrayList());

        Mockito.when(patchHistoryService.queryAllPatchHistoryInfo()).thenReturn(mockHistoryInfo());

        Mockito.when(unpatchedService.getUnpatchedParam()).thenReturn(new UnpatchedParam());

        Mockito.when(patchDetailAsyncQueryService.calcOnePatchPatchDetail(any(), any(), any()))
            .thenReturn(new AsyncResult<>(new PatchDetailDto()));

        Mockito.when(patchInfoService.queryPatchInfoByPaging(any()))
            .thenReturn(new PageImpl<>(mockPatchDetailPos(), new Pageable() {
                @Override
                public int getPageNumber() {
                    return 0;
                }

                @Override
                public int getPageSize() {
                    return 0;
                }

                @Override
                public long getOffset() {
                    return 0;
                }

                @Override
                public Sort getSort() {
                    return null;
                }

                @Override
                public Pageable next() {
                    return null;
                }

                @Override
                public Pageable previousOrFirst() {
                    return null;
                }

                @Override
                public Pageable first() {
                    return null;
                }

                @Override
                public Pageable withPage(int pageNumber) {
                    return null;
                }

                @Override
                public boolean hasPrevious() {
                    return false;
                }
            }, 100));

    }

    private List<PatchHistory> mockHistoryInfo() {
        List<PatchHistory> patchHistories = Lists.newArrayList();
        for (int i = 0; i < 5; i++) {
            PatchHistoryKey patchHistoryKey = new PatchHistoryKey();
            patchHistoryKey.setServiceName("hdfs");
            patchHistoryKey.setIp("********");
            patchHistoryKey.setServiceInstanceId("hdfs");
            patchHistoryKey.setPatchName("DAP--V20.19.40.R4.B2-SP00" + i);
            PatchHistory patchHistory = new PatchHistory();
            patchHistory.setId(patchHistoryKey);
            patchHistory.setPatchUptime(System.currentTimeMillis());

            patchHistories.add(patchHistory);
        }

        return patchHistories;
    }

    private List<PatchHistory> mockRoleHistoryInfo() {
        List<PatchHistory> patchHistories = Lists.newArrayList();
        long currentTimeMillis = System.currentTimeMillis();
        for (int i = 0; i < 5; i++) {
            PatchHistoryKey patchHistoryKey = new PatchHistoryKey();
            patchHistoryKey.setServiceName("saturn");
            patchHistoryKey.setIp("********");
            patchHistoryKey.setServiceInstanceId("saturn");
            patchHistoryKey.setRoleName("Manager");
            patchHistoryKey.setPatchName("DAP--V6.20.10.01P02-SP00" + i);
            PatchHistory patchHistory = new PatchHistory();
            patchHistory.setId(patchHistoryKey);
            patchHistory.setPatchUptime(currentTimeMillis);

            patchHistories.add(patchHistory);
        }

        return patchHistories;
    }

    @Test
    public void 查询上传补丁信息() {
        final List<PatchDetailDto> patchDetailDtos = patchDetailService.queryAllPatch();

        assertEquals(100, patchDetailDtos.size());
    }

    @Test
    public void 查询上传补丁分页信息() {

        final List<String> queryAllPatchType = patchDetailService.queryAllPatchType();

        assertEquals(1, queryAllPatchType.size());
    }

    @Test
    public void 查询补丁类型信息() {

        final PendingUpdatePatchPageInfo pendingUpdatePatchPageInfo =
            patchDetailService.queryPatchesByPaging(QueryParam.builder().build());

        assertEquals(100, pendingUpdatePatchPageInfo.getData().size());
    }

    private List<PatchDetailPo> mockPatchDetailPos() {
        int count = 100;
        List<PatchDetailPo> patchDetailPos = Lists.newArrayList();
        for (int i = 0; i < count; i++) {
            PatchDetailPo patchDetailPo = new PatchDetailPo();
            patchDetailPo.setPatchName("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP0" + i);
            patchDetailPo.setBaseVersion("V20.19.40.R4.B2");
            patchDetailPo.setService("ZOOKEEPER");
            patchDetailPo.setDependPatch("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP0" + (i - 1));
            patchDetailPos.add(patchDetailPo);
        }
        return patchDetailPos;
    }

    private Map<PatchKeyDo, Map<Boolean, List<PatchDispatch>>> mockPatchDispatchStatistics() {
        Map<PatchKeyDo, Map<Boolean, List<PatchDispatch>>> patchKeyDoMapMap = new HashMap<>();

        patchKeyDoMapMap.put(new PatchKeyDo("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01"),
            new HashMap<Boolean, List<PatchDispatch>>() {
                {
                    put(true,
                        Lists.newArrayList(
                            new PatchDispatch(
                                new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01", "10.10.10.1")),
                            new PatchDispatch(
                                new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01", "10.10.10.2"))));
                }
            });

        patchKeyDoMapMap.put(new PatchKeyDo("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP02"),
            new HashMap<Boolean, List<PatchDispatch>>() {
                {
                    put(true, Lists.newArrayList(new PatchDispatch(
                        new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP02", "10.10.10.1"))));
                    put(false, Lists.newArrayList(new PatchDispatch(
                        new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP02", "10.10.10.2"))));
                }
            });

        return patchKeyDoMapMap;
    }

    @Test
    public void test_patch_history_info_map() {
        Map<PatchKeyDo, List<PatchHistory>> patchHistoryInfoMap =
            mockHistoryInfo().stream().collect(Collectors.groupingBy(patchHistoryDtoAssembler::patchHistory2Do));
        assertEquals(5, patchHistoryInfoMap.keySet().size());
        assertEquals(5, patchHistoryInfoMap.values().size());
    }

    @Test
    public void test_role_patch_history_info_map() {
        Map<PatchHistoryDto, List<PatchHistory>> patchHistoryInfoMap =
            mockRoleHistoryInfo().stream().collect(Collectors.groupingBy(patchHistoryDtoAssembler::patchDispatch2Dto));
        assertEquals(1, patchHistoryInfoMap.keySet().size());
        assertEquals(1, patchHistoryInfoMap.values().size());
    }

    @Test
    public void test_queryNeedDispatchExceptSchemaPatch() {
        List<PatchBean> patchBeans = patchDetailService.queryNeedDispatchExceptSchemaPatch();

        assertEquals(1, patchBeans.size());
    }
}