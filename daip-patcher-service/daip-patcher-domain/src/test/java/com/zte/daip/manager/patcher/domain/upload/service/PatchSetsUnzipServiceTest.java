package com.zte.daip.manager.patcher.domain.upload.service;

import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.UnzipFileApi;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.net.URL;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class PatchSetsUnzipServiceTest {

    /* Started by AICoder, pid:w6d52gb887b040b148ff0bb370545423a857657b */
@Mock
private PatchEnvApi patchEnvApi;

@Mock
private UnzipFileApi unzipFileApi;

@InjectMocks
private PatchSetsUnzipService patchSetsUnzipService;

@Test
public void unzipPatchSets() {
    String repositoryHome = getPatchUploadHomeEnv();
    File patchSetFile = new File(repositoryHome + File.separator + "patchset" + File.separator + "DAP-configcenter-V20.19.40.R4.B2-patches-SP014-20200227.zip");

    when(patchEnvApi.getRepositoryHomeEnv()).thenReturn(repositoryHome);
    doNothing().when(unzipFileApi).unzipFile(any(), anyString());

    patchSetsUnzipService.unzipPatchSets(patchSetFile);
}

private String getPatchUploadHomeEnv() {
    URL resourceUrl = getClass().getClassLoader().getResource("application.yml");
    if (resourceUrl == null) {
        throw new IllegalStateException("Resource 'application.yml' not found");
    }
    return new File(resourceUrl.getPath()).getParent();
}

/* Ended by AICoder, pid:w6d52gb887b040b148ff0bb370545423a857657b */
}