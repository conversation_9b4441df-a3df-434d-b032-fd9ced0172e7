/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchInfoServiceTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/30
 * </p>
 * <p>
 * 完成日期：2021/3/30
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.paging.jpa.QueryParam;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.paging.JpaPagingQueryService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchInfoRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchInfoServiceTest {

    @Mock
    private PatchInfoRepository patchInfoRepository;

    @Mock
    private JpaPagingQueryService jpaPagingQueryService;

    @InjectMocks
    private PatchInfoService patchInfoService;

    @Before
    public void setUp() {
        Mockito.when(patchInfoRepository.findAll()).thenReturn(Lists.newArrayList());
        Mockito.when(patchInfoRepository.queryPatchName()).thenReturn(Lists.newArrayList());
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("sp01");
        patchDetailPo.setIsContainerPatch(1);
        Mockito.when(patchInfoRepository.findByService(anyString())).thenReturn(Lists.newArrayList(patchDetailPo));
        Mockito.when(patchInfoRepository.findByServiceAndBaseVersion(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchDetailPo));
    }

    @Test
    public void 查询上传补丁DB信息() {
        final List<PatchDetailPo> patchDetailPos = patchInfoService.queryAllPatchInfos();

        assertEquals(0, patchDetailPos.size());
    }

    @Test
    public void 查询上传补丁类型信息() {
        final List<String> patchDetailPos = patchInfoService.queryPatchName();

        assertEquals(0, patchDetailPos.size());
    }

    @Test
    public void 查询上传补丁分页信息() {
        final QueryParam build = QueryParam.builder().build();
        final PageRequest of = PageRequest.of(0, 10);
        final Specification specification = (root, criteriaQuery, criteriaBuilder) -> null;

        Mockito.when(jpaPagingQueryService.getSpecificationByQueryParam(build)).thenReturn(specification);
        Mockito.when(jpaPagingQueryService.getPageRequest(build)).thenReturn(of);
        Mockito.when(patchInfoRepository.findAll(specification, of)).thenReturn(new PageImpl<>(Lists.newArrayList()));

        Page<PatchDetailPo> patchDetailPos = patchInfoService.queryPatchInfoByPaging(build);

        assertEquals(0, patchDetailPos.getContent().size());
    }

    @Test
    public void findContainerPatchByService() {
        List<PatchDetailPo> p = patchInfoService.findContainerPatchByService("kafka");
        assertEquals("sp01", p.get(0).getPatchName());
    }

    @Test
    public void findByServiceAndBaseVersionNotSchemeAndRepository() {
        List<PatchDetailPo> p = patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository("kafka", "942");
        assertEquals("sp01", p.get(0).getPatchName());
    }

    @Test
    public void findSchemaByServiceAndBaseVersion() {
        PatchDetailPo patchDetailPo1 = new PatchDetailPo();
        patchDetailPo1.setPatchName("sp01-schema-kafka");
        Mockito.when(patchInfoRepository.findByServiceAndBaseVersion(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchDetailPo1));

        List<PatchDetailPo> p = patchInfoService.findSchemaByServiceAndBaseVersion("kafka", "942");
        assertEquals("sp01-schema-kafka", p.get(0).getPatchName());
    }
}