/* Started by AICoder, pid:e1bc55e124f04fa6be858924d644ca38 */
package com.zte.daip.manager.patcher.domain.task.assembler;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.domain.query.service.PatchDetailService;
import com.zte.daip.manager.patcher.domain.update.service.PatchTypeQueryService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskTypeEnum;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(SpringRunner.class)
public class CurrentPatchAssemblerTest {
    @Mock
    private PatchHistoryService patchHistoryService;
    @Mock
    private PatchDetailService patchDetailService;
    @Mock
    private PatchTypeQueryService patchTypeQueryService;
    @Mock
    private PatchTaskAssembler patchTaskAssembler;
    @Mock
    private PatchTaskService patchTaskService;
    @InjectMocks
    private CurrentPatchAssembler currentPatchAssembler;
    private List<ServiceInstancePatchInfo> serviceInstancePatchInfos = new ArrayList<>();;

    @Before
    public void init() throws DaipBaseException {
        PatchHistory patchHistory = new PatchHistory();
        PatchHistoryKey patchHistoryKey = new PatchHistoryKey("************", "zookeeper-sp004", "zookeeper");
        patchHistoryKey.setRoleName("");
        patchHistoryKey.setServiceInstanceId("");
        patchHistory.setId(patchHistoryKey);

        PatchDetailDto patchDetailDto = new PatchDetailDto();
        patchDetailDto.setPatchName("zookeeper-sp004");
        patchDetailDto.setBaseVersion("V1");

        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceName("zookeeper");
        serviceInstance.setVersion("V1");
        serviceInstancePatchInfo.setPatchType("zdh");
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);
        serviceInstancePatchInfo.setTargetPatchPoint("zookeeper-sp006");
        RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
        rollBackPatchPointInfo.setPatchHostInfos(Lists.newArrayList(new PatchHostInfoDto("************", "")));
        rollBackPatchPointInfo.setRollBackPatchPoint("zookeeper-sp002");
        rollBackPatchPointInfo.setDisplayRollbackPatchPoint("zookeeper-sp005");
        serviceInstancePatchInfo.setRollBackPatchPoints(Lists.newArrayList(rollBackPatchPointInfo));
        serviceInstancePatchInfos.add(serviceInstancePatchInfo);
        Mockito.when(patchHistoryService.queryAllPatchHistoryInfo()).thenReturn(Lists.newArrayList(patchHistory));
        Mockito.when(patchDetailService.queryAllPatch()).thenReturn(Lists.newArrayList(patchDetailDto));
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setTaskId(0L);
        patchTaskPo.setPatchCategory(PatchTaskTypeEnum.UPDATE.getTaskTypeName());
        Mockito.when(patchTaskService.queryAllPatchTasks()).thenReturn(Lists.newArrayList(patchTaskPo));
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setContext(serviceInstancePatchInfos);
        Mockito.when(patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo)).thenReturn(patchTaskDto);

    }

    @Test
    public void testOrganizeCurrentPatchPoints_EmptyServiceInstancePatchInfos() {
        serviceInstancePatchInfos = Lists.newArrayList();
        currentPatchAssembler.organizeCurrentPatchPoints(serviceInstancePatchInfos);
        assertTrue(serviceInstancePatchInfos.isEmpty());
    }

    /* Started by AICoder, pid:62aa5h63d880d0614a520b14007f733775e1ece9 */
    @Test
    public void checkTaskIsUpdateSequenceTest() {
        boolean result = currentPatchAssembler.checkTaskIsSequence(
            Lists.newArrayList(organizeServiceInstancePatchInfo()), PatchTaskTypeEnum.UPDATE, new StringBuilder());
        assertFalse(result);
    }

    @Test
    public void checkTaskIsRollbackSequenceTest() {
        boolean result = currentPatchAssembler.checkTaskIsSequence(
            Lists.newArrayList(organizeServiceInstancePatchInfo()), PatchTaskTypeEnum.ROLLBACK, new StringBuilder());
        assertFalse(result);
    }

    private ServiceInstancePatchInfo organizeServiceInstancePatchInfo() {
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceName("zookeeper");
        serviceInstance.setVersion("V1");
        serviceInstancePatchInfo.setPatchType("zdh");
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);
        serviceInstancePatchInfo.setTargetPatchPoint("zookeeper-sp003");
        RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
        rollBackPatchPointInfo.setPatchHostInfos(Lists.newArrayList(new PatchHostInfoDto("************", "")));
        rollBackPatchPointInfo.setRollBackPatchPoint("zookeeper-sp002");
        rollBackPatchPointInfo.setDisplayRollbackPatchPoint("zookeeper-sp005");
        serviceInstancePatchInfo.setRollBackPatchPoints(Lists.newArrayList(rollBackPatchPointInfo));
        return serviceInstancePatchInfo;
    }

    /* Ended by AICoder, pid:62aa5h63d880d0614a520b14007f733775e1ece9 */

    @Test
    public void checkTaskIsAllUpdateTest() {
        boolean result = currentPatchAssembler.checkTaskIsAllUpdate(serviceInstancePatchInfos);
        assertFalse(result);
    }

    @Test
    public void checkTaskIsAllRollbackTest() {
        boolean result = currentPatchAssembler.checkTaskIsAllRollback(serviceInstancePatchInfos);
        assertTrue(result);
    }

    @Test
    public void testOrganizeCurrentPatchPoints_NOT_EmptyServiceInstancePatchInfos() {

        currentPatchAssembler.organizeCurrentPatchPoints(serviceInstancePatchInfos);
        assertTrue(!serviceInstancePatchInfos.isEmpty());

        currentPatchAssembler.organizeDisplayPatchPoints(serviceInstancePatchInfos);
        assertTrue(!serviceInstancePatchInfos.isEmpty());
    }
}
/*Ended by AICoder, pid:e1bc55e124f04fa6be858924d644ca38*/