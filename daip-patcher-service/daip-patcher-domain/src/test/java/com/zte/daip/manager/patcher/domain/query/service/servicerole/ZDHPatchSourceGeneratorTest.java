package com.zte.daip.manager.patcher.domain.query.service.servicerole;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;

@RunWith(SpringRunner.class)
public class ZDHPatchSourceGeneratorTest {

    @Mock
    private PatchHomeQueryService patchHomeQueryService;
    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchHistoryService patchHistoryService;
    @Mock
    private ServiceModelInfoCache serviceModelInfoCache;
    @Mock
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;
    @InjectMocks
    private ZDHPatchSourceGenerator zdhPatchSourceGenerator;

    @Test
    public void obtainPatchUpdateInfos() throws DaipBaseException {
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("zookeeper-001");
        patchDetailPo.setIsContainerPatch(1);
        Mockito.when(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchDetailPo));
        PatchHistory patchHistory = new PatchHistory();
        PatchHistoryKey patchHistoryKey = new PatchHistoryKey("1", "DAP-ZOOKEEPER-SP002", "zookeeper");
        patchHistory.setId(patchHistoryKey);
        Mockito.when(patchHistoryService.queryPatchHistoryInfoByServiceNameAndIp(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchHistory));
        RollAutoPatchUpdateParam rollingPatchUpdateParam =
            new RollAutoPatchUpdateParam("100001", "zookeeper", "localhost");
        List<String> updatedContainerPatches = Lists.newArrayList("DAP-ZOOKEEPER-SP001-container");
        List<RollAutoPatchUpdateInfo> rollAutoPatchUpdateInfos =
            zdhPatchSourceGenerator.obtainPatchUpdateInfos(rollingPatchUpdateParam, "942", updatedContainerPatches);
        Assert.assertEquals(2, rollAutoPatchUpdateInfos.size());
    }
}