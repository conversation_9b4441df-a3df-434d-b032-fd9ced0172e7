/* Started by AICoder, pid:b2a7b71706c14bdd93082e18db6130b3 */
package com.zte.daip.manager.patcher.domain.task.service;

import com.zte.daip.manager.patcher.domain.task.assembler.CurrentPatchAssembler;
import com.zte.daip.manager.patcher.inner.api.PatchUpdateInnerControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertTrue;

@RunWith(SpringRunner.class)
public class OrdinaryPatchServiceTest {
    @Mock
    private PatchUpdateInnerControllerApi patchUpdateInnerControllerApi;

    @Mock
    private CurrentPatchAssembler currentPatchAssembler;

    @InjectMocks
    private OrdinaryPatchService ordinaryPatchService;

    @Test
    public void testOrganizeServiceInstancePatchInfo_EmptyContext() {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setClusterId("cluster1");
        patchTaskDto.setContext(new ArrayList<>());

        List<ServiceInstancePatchInfo> result = ordinaryPatchService.organizeServiceInstancePatchInfo(patchTaskDto);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testOrganizeServiceInstancePatchInfo_NullInstance() {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setClusterId("cluster1");
        List<ServiceInstancePatchInfo> context = new ArrayList<>();
        context.add(new ServiceInstancePatchInfo());
        patchTaskDto.setContext(context);

        List<ServiceInstancePatchInfo> result = ordinaryPatchService.organizeServiceInstancePatchInfo(patchTaskDto);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testOrganizeServiceInstancePatchInfo_Not_NullInstance() {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setClusterId("cluster1");
        List<ServiceInstancePatchInfo> context = new ArrayList<>();
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceInstanceId("zookeeper_1");
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);
        context.add(serviceInstancePatchInfo);
        patchTaskDto.setContext(context);

        List<ServiceInstancePatchInfo> result = ordinaryPatchService.organizeServiceInstancePatchInfo(patchTaskDto);
        assertTrue(!result.isEmpty());
    }

}
/*Ended by AICoder, pid:b2a7b71706c14bdd93082e18db6130b3*/