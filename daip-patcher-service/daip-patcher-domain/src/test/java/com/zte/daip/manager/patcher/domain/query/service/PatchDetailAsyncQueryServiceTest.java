/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDetailAsyncQueryServiceTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/12
 * </p>
 * <p>
 * 完成日期：2021/4/12
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.RoleModel;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.api.dto.PatchHostDto;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchDetailDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchHistoryDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.InstancePatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.RolePatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.ServicePatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.ZDHPatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchDetailParam;
import com.zte.daip.manager.patcher.domain.query.valobj.UnpatchedParam;
import com.zte.daip.manager.patcher.infrastructure.po.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.internal.util.collections.Sets;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static junit.framework.TestCase.assertEquals;
import static junit.framework.TestCase.assertTrue;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchDetailAsyncQueryServiceTest {

    @Mock
    private ApplicationContext applicationContext;

    @Spy
    private PatchDetailDtoAssembler patchDetailDtoAssembler = new PatchDetailDtoAssembler();

    @Spy
    private PatchHistoryDtoAssembler patchHistoryDtoAssembler = new PatchHistoryDtoAssembler();

    @InjectMocks
    private PatchDetailAsyncQueryService patchDetailAsyncQueryService;

    private Map<String, Set<ServiceInstanceInfo>> serviceInstanceInfoMap;

    private Map<String, Set<String>> clusterServiceLstMap;

    private UnpatchedParam unpatchedParam;

    private PatchDetailParam patchDetailParam;

    private CountDownLatch countDownLatch;

    private String clusterId = "0";

    private Map<String, Set<PatchHistoryDto>> service2Hosts = new HashMap<String, Set<PatchHistoryDto>>() {
        {
            put("zookeeper", Sets.newSet());
        }
    };

    @Before
    public void setUp() {
        Mockito.when(applicationContext.getBean(PatchTypeEnum.SERVICE.getServiceName()))
            .thenReturn(new ServicePatchSourceGenerator());
        Mockito.when(applicationContext.getBean(PatchTypeEnum.ROLE.getServiceName()))
            .thenReturn(new RolePatchSourceGenerator());
        Mockito.when(applicationContext.getBean(PatchTypeEnum.INSTANCE.getServiceName()))
            .thenReturn(new InstancePatchSourceGenerator());
        Mockito.when(applicationContext.getBean(PatchTypeEnum.ZDH.getServiceName()))
            .thenReturn(new ZDHPatchSourceGenerator());

        serviceInstanceInfoMap = Maps.newHashMap();

        clusterServiceLstMap = Maps.newHashMap();

        countDownLatch = new CountDownLatch(1);

        unpatchedParam = new UnpatchedParam(
            mockAllPatchHistoryInfo().stream()
                .collect(Collectors.groupingBy(patchHistoryDtoAssembler::patchHistory2Do)),
            mockAllServiceModels(), mockAllHost(), mockServiceRoleInfos(), mockServiceVersion2Ip(),false);

        patchDetailParam = new PatchDetailParam(mocPatchDetailPatchNameList(), mockPatchDispatchStatistics(),
            mockPatchHistoryInfoMap());

    }

    @Test
    public void 查询服务级补丁未打角色信息() throws Exception {
        PatchDetailDto patchDetailPo = new PatchDetailDto();
        patchDetailPo.setPatchName("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01");
        patchDetailPo.setBaseVersion("V20.19.40.R4.B2");
        patchDetailPo.setService("zookeeper");

        patchDetailAsyncQueryService.calcOnePatchUnpatchedServiceInstanceInfo(serviceInstanceInfoMap, unpatchedParam,
            patchDetailPo, countDownLatch);

        patchDetailAsyncQueryService.calcOnePatchClustersAndServices(clusterServiceLstMap, unpatchedParam,
            patchDetailPo, countDownLatch);

        final List<PatchHostDto> patchHostDtoList = patchDetailAsyncQueryService
            .calcOnePatchPatchAndHost(unpatchedParam, patchDetailPo, service2Hosts, clusterId).get();

        final Set<ServiceInstanceInfo> serviceInstanceInfos = serviceInstanceInfoMap.get("0");

        assertEquals(2, serviceInstanceInfos.size());

        final Set<String> stringSet = clusterServiceLstMap.get("0");

        assertTrue(stringSet.contains("zookeeper"));

        assertEquals(1, patchHostDtoList.size());

    }

    @Test
    public void 查询角色级补丁未打角色信息() throws Exception {
        PatchDetailDto patchDetailPo = new PatchDetailDto();
        patchDetailPo.setPatchName("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01");
        patchDetailPo.setBaseVersion("V20.19.40.R4.B3");
        patchDetailPo.setService("zookeeper");
        patchDetailPo.setRoles("HQuorumPeer");

        patchDetailAsyncQueryService.calcOnePatchUnpatchedServiceInstanceInfo(serviceInstanceInfoMap, unpatchedParam,
            patchDetailPo, countDownLatch);

        patchDetailAsyncQueryService.calcOnePatchClustersAndServices(clusterServiceLstMap, unpatchedParam,
            patchDetailPo, countDownLatch);

        final List<PatchHostDto> patchHostDtoList = patchDetailAsyncQueryService
            .calcOnePatchPatchAndHost(unpatchedParam, patchDetailPo, service2Hosts, clusterId).get();

        final Set<ServiceInstanceInfo> serviceInstanceInfos = serviceInstanceInfoMap.get("0");

        assertEquals(2, serviceInstanceInfos.size());

        final Set<String> stringSet = clusterServiceLstMap.get("0");

        assertTrue(stringSet.contains("zookeeper"));

        assertEquals(1, patchHostDtoList.size());
    }

    @Test
    public void 查询Instance级补丁未打角色信息() throws Exception {
        PatchDetailDto patchDetailPo = new PatchDetailDto();
        patchDetailPo.setPatchName("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01");
        patchDetailPo.setBaseVersion("V20.19.40.R4.B4");
        patchDetailPo.setService("zookeeper");

        patchDetailAsyncQueryService.calcOnePatchUnpatchedServiceInstanceInfo(serviceInstanceInfoMap, unpatchedParam,
            patchDetailPo, countDownLatch);

        patchDetailAsyncQueryService.calcOnePatchClustersAndServices(clusterServiceLstMap, unpatchedParam,
            patchDetailPo, countDownLatch);

        final List<PatchHostDto> patchHostDtoList = patchDetailAsyncQueryService
            .calcOnePatchPatchAndHost(unpatchedParam, patchDetailPo, service2Hosts, clusterId).get();

        final Set<ServiceInstanceInfo> serviceInstanceInfos = serviceInstanceInfoMap.get("0");

        assertEquals(2, serviceInstanceInfos.size());

        final Set<String> stringSet = clusterServiceLstMap.get("0");

        assertTrue(stringSet.contains("zookeeper"));

        assertEquals(1, patchHostDtoList.size());
    }

    @Test
    public void 查询zdh级补丁未打角色信息() throws Exception {
        PatchDetailDto patchDetailPo = new PatchDetailDto();
        patchDetailPo.setPatchName("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01");
        patchDetailPo.setBaseVersion("V20.19.40.R4.B5");
        patchDetailPo.setService("zdh");

        patchDetailAsyncQueryService.calcOnePatchUnpatchedServiceInstanceInfo(serviceInstanceInfoMap, unpatchedParam,
            patchDetailPo, countDownLatch);

        patchDetailAsyncQueryService.calcOnePatchClustersAndServices(clusterServiceLstMap, unpatchedParam,
            patchDetailPo, countDownLatch);

        final List<PatchHostDto> patchHostDtoList = patchDetailAsyncQueryService
            .calcOnePatchPatchAndHost(unpatchedParam, patchDetailPo, service2Hosts, clusterId).get();

        final Set<ServiceInstanceInfo> serviceInstanceInfos = serviceInstanceInfoMap.get("0");

        assertEquals(1, serviceInstanceInfos.size());

        final Set<String> stringSet = clusterServiceLstMap.get("0");

        assertTrue(stringSet.contains("zdh"));

        assertEquals(0, patchHostDtoList.size());
    }

    @Test
    public void 查询zdh级补丁未打主机信息() throws Exception {
        PatchDetailDto patchDetailPo = new PatchDetailDto();
        patchDetailPo.setPatchName("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01");
        patchDetailPo.setBaseVersion("V20.19.40.R4.B2");
        patchDetailPo.setService("zookeeper");

        final Future<PatchDetailDto> patchDetailDtoFuture =
            patchDetailAsyncQueryService.calcOnePatchPatchDetail(patchDetailParam, unpatchedParam, patchDetailPo);

        final PatchDetailDto patchDetailDto = patchDetailDtoFuture.get();

        assertEquals(2, patchDetailDto.getPatchUpdateResultDto().getPendingNum());
    }

    private List<PatchHistory> mockAllPatchHistoryInfo() {
        List<PatchHistory> patchHistories = Lists.newArrayList();

        patchHistories.add(
            new PatchHistory(new PatchHistoryKey("**********", "DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01", "zookeeper")));

        patchHistories.add(
            new PatchHistory(new PatchHistoryKey("**********", "DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02", "zookeeper")));

        return patchHistories;
    }

    private List<PatchDispatch> mockAllPatchDispatch() {
        List<PatchDispatch> patchDispatches = Lists.newArrayList();

        patchDispatches
            .add(new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01", "**********"), true));

        patchDispatches
            .add(new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01", "**********"), true));
        patchDispatches
            .add(new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01", "**********"), true));

        patchDispatches
            .add(new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02", "**********"), true));

        patchDispatches
            .add(new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02", "**********"), true));
        patchDispatches
            .add(new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02", "**********"), true));
        return patchDispatches;
    }

    private List<String> mockPatchHistoryNameList() {
        return Lists.newArrayList("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01", "DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02");
    }

    private List<String> mocPatchDetailPatchNameList() {
        return Lists.newArrayList("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01", "DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02",
            "DAP-ZOOKEEPER-V20.19.40.R4.B2.SP03");
    }

    private Map<PatchKeyDo, Map<Boolean, List<PatchDispatch>>> mockPatchDispatchStatistics() {
        Map<PatchKeyDo, Map<Boolean, List<PatchDispatch>>> patchKeyDoMapMap = new HashMap<>();

        patchKeyDoMapMap.put(new PatchKeyDo("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01"),
            new HashMap<Boolean, List<PatchDispatch>>() {
                {
                    put(true, Lists.newArrayList(
                        new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01", "**********"))));
                }
            });

        patchKeyDoMapMap.put(new PatchKeyDo("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02"),
            new HashMap<Boolean, List<PatchDispatch>>() {
                {
                    put(false, Lists.newArrayList(
                        new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02", "**********"))));
                    put(true, Lists.newArrayList(
                        new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02", "**********"))));
                }
            });

        return patchKeyDoMapMap;
    }

    private Map<PatchKeyDo, List<PatchHistory>> mockPatchHistoryInfoMap() {

        Map<PatchKeyDo, List<PatchHistory>> patchKeyDoMapMap = new HashMap<>();
        patchKeyDoMapMap.put(new PatchKeyDo("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01"), Lists
            .newArrayList(new PatchHistory(new PatchHistoryKey("**********", "DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01"))));

        patchKeyDoMapMap.put(new PatchKeyDo("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02"), Lists
            .newArrayList(new PatchHistory(new PatchHistoryKey("**********", "DAP-ZOOKEEPER-V20.19.40.R4.B2.SP02"))));

        return patchKeyDoMapMap;
    }

    private List<ServiceModel> mockAllServiceModels() {

        List<ServiceModel> serviceModels = Lists.newArrayList();

        ServiceModel serviceModel1 = new ServiceModel("dap.manager.zookeeper", "zookeeper");

        serviceModel1.setVersion("V20.19.40.R4.B2");
        serviceModel1.setComponentType("dap.manager.common.bigdata");

        ServiceModel serviceModel2 = new ServiceModel("dap.manager.zookeeper", "zookeeper");

        serviceModel2.setVersion("V20.19.40.R4.B3");
        serviceModel2.setComponentType("dap.manager.common.bigdata");
        serviceModel2.setPatchType("role");
        serviceModel2.setRoles(Lists.newArrayList(new RoleModel("dap.manager.zookeeper.HQuorumPeer", "HQuorumPeer")));

        ServiceModel serviceModel3 = new ServiceModel("dap.manager.zookeeper", "zookeeper");

        serviceModel3.setVersion("V20.19.40.R4.B4");
        serviceModel3.setComponentType("dap.manager.common.bigdata");
        serviceModel3.setPatchType("instance");

        ServiceModel serviceModel4 = new ServiceModel("dap.manager.zdh", "zdh");

        serviceModel4.setVersion("V20.19.40.R4.B5");
        serviceModel4.setComponentType("dap.manager.common.bigdata");

        serviceModels.add(serviceModel1);
        serviceModels.add(serviceModel2);
        serviceModels.add(serviceModel3);
        serviceModels.add(serviceModel4);
        return serviceModels;
    }

    private List<ServiceRoleInfo> mockServiceRoleInfos() {
        List<ServiceRoleInfo> serviceRoleInfos = Lists.newArrayList();

        ServiceRoleInfo serviceRoleInfo1 = new ServiceRoleInfo("0", "**********", "dap.manager.zookeeper",
            "dap.manager.zookeeper.HQuorumPeer", "zookeeper");

        ServiceRoleInfo serviceRoleInfo2 = new ServiceRoleInfo("0", "**********", "dap.manager.zookeeper",
            "dap.manager.zookeeper.RestClient", "zookeeper");

        ServiceRoleInfo serviceRoleInfo3 = new ServiceRoleInfo("0", "**********", "dap.manager.zookeeper",
            "dap.manager.zookeeper.HQuorumPeer", "zookeeper");
        ServiceRoleInfo serviceRoleInfo4 = new ServiceRoleInfo("0", "**********", "dap.manager.zookeeper",
            "dap.manager.zookeeper.HQuorumPeer", "zookeeper");

        ServiceRoleInfo serviceRoleInfo5 = new ServiceRoleInfo("0", "**********", "dap.manager.zookeeper",
            "dap.manager.zookeeper.HQuorumPeer", "zookeeper1");

        serviceRoleInfos.add(serviceRoleInfo1);
        serviceRoleInfos.add(serviceRoleInfo2);
        serviceRoleInfos.add(serviceRoleInfo3);
        serviceRoleInfos.add(serviceRoleInfo4);
        serviceRoleInfos.add(serviceRoleInfo5);
        return serviceRoleInfos;
    }

    private Map<String, String> mockAllHost() {
        List<HostInfo> hostInfos = Lists.newArrayList();

        final HostInfo hostInfo1 = new HostInfo();
        hostInfo1.setIpAddress("**********");
        hostInfo1.setHostName("**********");
        final HostInfo hostInfo2 = new HostInfo();
        hostInfo2.setIpAddress("**********");
        hostInfo2.setHostName("**********");
        final HostInfo hostInfo3 = new HostInfo();
        hostInfo3.setIpAddress("**********");
        hostInfo3.setHostName("**********");
        final HostInfo hostInfo4 = new HostInfo();
        hostInfo4.setIpAddress("**********");
        hostInfo4.setHostName("**********");
        hostInfos.add(hostInfo1);
        hostInfos.add(hostInfo2);
        hostInfos.add(hostInfo3);
        hostInfos.add(hostInfo4);

        return hostInfos.stream().collect(Collectors.toMap(HostInfo::getIpAddress, HostInfo::getHostName));

    }

    private Map<String, List<String>> mockServiceVersion2Ip() {
        Map<String, List<String>> listMap = Maps.newHashMap();

        listMap.put("host_V20.19.40.R4.B2", Lists.newArrayList("**********", "**********", "**********"));

        listMap.put("zdh_V20.19.40.R4.B2", Lists.newArrayList("**********", "**********", "**********"));
        listMap.put("zookeeperHQuorumPeer_V20.19.40.R4.B3",
            Lists.newArrayList("**********", "**********", "**********"));

        listMap.put("zookeeper_V20.19.40.R4.B4", Lists.newArrayList("**********", "**********", "**********"));

        listMap.put("zdh_V20.19.40.R4.B5", Lists.newArrayList("**********", "**********", "**********"));
        return listMap;
    }
}