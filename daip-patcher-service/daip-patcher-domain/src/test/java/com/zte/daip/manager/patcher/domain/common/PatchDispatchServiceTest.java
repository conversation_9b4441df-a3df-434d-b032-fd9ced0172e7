package com.zte.daip.manager.patcher.domain.common;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.dto.PatchDispatchDto;
import com.zte.daip.manager.patcher.api.dto.PatchDispatchResult;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchDispatchPoAssembler;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatch;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatchKey;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchDispatchRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PatchDispatchServiceTest {
    @Mock
    private PatchDispatchRepository patchDispatchRepository;
    @Mock
    private HostResourceInfoCache hostResourceInfoCache;

    @Spy
    private PatchDispatchPoAssembler patchDispatchPoAssembler = new PatchDispatchPoAssembler();

    @InjectMocks
    private PatchDispatchService patchDispatchService;


    @Test
    public void 查询补丁分发记录DB信息() {
        when(patchDispatchRepository.findAll()).thenReturn(mockPatchDispatchInfo());
        final List<PatchDispatch> patchDispatches = patchDispatchService.queryAllPatchDispatchInfo();

        Map<PatchKeyDo, Map<Boolean, List<PatchDispatch>>> collect = patchDispatches.stream().collect(Collectors
            .groupingBy(patchDispatchPoAssembler::patchDispatch2Do, Collectors.groupingBy(PatchDispatch::isSuccess)));
        final Map<Boolean, List<PatchDispatch>> SP01DispatchInfo =
            collect.get(new PatchKeyDo("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01", "", ""));

        final List<PatchDispatch> SP01SuccessDispatchInfo = SP01DispatchInfo.get(true);

        assertEquals(2, collect.keySet().size());

        assertEquals(1, SP01SuccessDispatchInfo.size());
    }

    @Test
    public void 批量入库() {
        when(hostResourceInfoCache.queryIpAddress(anyString())).thenReturn("*******");
        when(patchDispatchRepository.saveAll(anyList())).thenReturn(Lists.newArrayList());

        PatchDispatchResult patchDispatchResult = new PatchDispatchResult();
        patchDispatchResult.setServiceName("zk");
        patchDispatchResult.setPatchName("zk_SP001");
        patchDispatchResult.setSuccess(true);
        patchDispatchResult.setHostIp("*******");
        patchDispatchResult.setReason("success");
        patchDispatchService.batchUpdate(Lists.newArrayList(patchDispatchResult));
    }

    private List<PatchDispatch> mockPatchDispatchInfo() {

        final List<PatchDispatch> patchDispatches = mockZkSP001PatchHistoryInfo();

        patchDispatches.add(
            new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP02", "**********"), true));

        patchDispatches.add(
            new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP02", "**********"), true));

        return patchDispatches;
    }

    private List<PatchDispatch> mockZkSP001PatchHistoryInfo() {
        return Lists.newArrayList(
            new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01", "**********"), true),
            new PatchDispatch(new PatchDispatchKey("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01", "**********"), false));
    }
}