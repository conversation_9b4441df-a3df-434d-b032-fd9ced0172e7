package com.zte.daip.manager.patcher.domain.upload.assembler;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.*;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchDetailAssemblerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/3/31</p>
 * <p>完成日期：2021/3/31</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchDetailAssemblerTest {

    private PatchDetailAssembler patchDetailAssembler = new PatchDetailAssembler();

    @Test
    public void assembler() {
        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName("DAP-spark-V20.19.40.R4.B2-schema-SP026-20200111");
        patchBean.setService("spark");
        patchBean.setCreateDate("20161008");
        PatchDetailPo patchDetail = patchDetailAssembler.patchBean2DbBean(patchBean);
        assertThat(patchDetail.getPatchName().equals(patchBean.getPatchName()), is(true));
    }
}