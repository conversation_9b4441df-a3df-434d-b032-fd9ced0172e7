package com.zte.daip.manager.patcher.domain.rollback.strategy;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.update.response.OnePatchUpdateInfo;
import com.zte.daip.manager.patcher.api.update.response.PatchRecordAndRecoverInfos;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.common.PatchRollbackService;
import com.zte.daip.manager.patcher.domain.rollback.cache.PatchRollbackResultQueue;
import com.zte.daip.manager.patcher.domain.rollback.entity.PatchRollBackResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: UpdateRollbackResultSchedulerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/23</p>
 * <p>完成日期：2023/3/23</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class UpdateRollbackResultSchedulerTest {

    @InjectMocks
    private UpdateRollbackResultScheduler updateRollbackResultScheduler;
    @Mock
    private PatchRollbackResultQueue patchRollbackResultQueue;
    @Mock
    private PatchRollbackService patchRollbackService;
    @Mock
    private PatchHistoryService patchHistoryService;
    @Mock
    private PatchInfoService patchInfoService;

    @Before
    public void setUp() {
        Mockito.when(patchInfoService.findContainerPatchByService(anyString())).thenReturn(Lists.newArrayList());
        doNothing().when(patchRollbackService).saveRollbackPatches(anyList());
        doNothing().when(patchRollbackService).deleteByRollbackKeyWithoutContainer(anyString(), anyString(),
            anyString(), anyString(), anyList());
        doNothing().when(patchHistoryService).deleteByHistoryKeyWithoutContainer(anyString(), anyString(), anyString(),
            anyString(), anyList());
    }

    @Test
    public void updateRollbackResult() {
        boolean result = true;
        try {
            List<PatchRollBackResult> patchRollBackResults = getPatchRollBackResults();
            updateRollbackResultScheduler.updateRollbackResult(patchRollBackResults);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    private List<PatchRollBackResult> getPatchRollBackResults() {
        List<PatchRollBackResult> patchRollBackResults = Lists.newArrayList();
        PatchRollBackResult patchRollBackResult = new PatchRollBackResult();
        patchRollBackResult.setServiceName("kafka");
        patchRollBackResult.setServiceInstanceId("");
        patchRollBackResult.setRoleName("");
        patchRollBackResult.setIp("");
        PatchRecordAndRecoverInfos patchRecordAndRecoverInfo = new PatchRecordAndRecoverInfos();
        patchRecordAndRecoverInfo.setService("kafka");
        patchRecordAndRecoverInfo.setRole("");
        patchRecordAndRecoverInfo.setHostIp("127.0.0.1");
        OnePatchUpdateInfo onePatchUpdateInfo =
            new OnePatchUpdateInfo(1, "SP01", "kafka", "", "", "127.0.0.1", "true", "");
        patchRecordAndRecoverInfo.setPatchHistory(Lists.newArrayList(onePatchUpdateInfo));
        patchRecordAndRecoverInfo.setRecoverPatch(Lists.newArrayList(onePatchUpdateInfo));
        patchRollBackResult.setPatchRecordAndRecoverInfos(Lists.newArrayList(patchRecordAndRecoverInfo));
        patchRollBackResults.add(patchRollBackResult);
        return patchRollBackResults;
    }
}