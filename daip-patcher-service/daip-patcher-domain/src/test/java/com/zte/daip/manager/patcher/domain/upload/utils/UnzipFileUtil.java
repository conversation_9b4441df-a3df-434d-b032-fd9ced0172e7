/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UnzipFileUtil.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/2
 * </p>
 * <p>
 * 完成日期：2021/4/2
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.upload.utils;

import com.zte.daip.manager.common.utils.file.cleaner.CoverityUtil;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.*;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class UnzipFileUtil {
    public void unzipFile(File zipFile, String descDir) throws IOException {
        File pathFile = new File(descDir);
        if (!pathFile.exists()) {
            pathFile.mkdirs();
        }
        try (ZipFile zip = new ZipFile(zipFile)) {
            unzipOneFile(descDir, zip);
        } catch (IOException e) {
            log.error("unzip file error", e);
        }
    }

    private void unzipOneFile(String descDir, ZipFile zip) throws IOException {
        for (Enumeration entries = zip.entries(); entries.hasMoreElements();) {
            ZipEntry entry = (ZipEntry)entries.nextElement();
            String zipEntryName = CoverityUtil.coverityValidatePath(entry.getName());
            InputStream in = zip.getInputStream(entry);
            String outPath = (descDir + File.separator + zipEntryName).replace("/", File.separator);
            File file = new File(outPath.substring(0, outPath.lastIndexOf(File.separator)));
            if (!file.exists()) {
                file.mkdirs();
            }
            if (new File(outPath).isDirectory()) {
                continue;
            }
            try (OutputStream out = new FileOutputStream(outPath)) {
                byte[] buf1 = new byte[2048];
                int len;
                while ((len = in.read(buf1)) > 0) {
                    out.write(buf1, 0, len);
                }
                in.close();
            } catch (IOException er) {
                log.error("unzip file error", er);
            }
        }
    }

    public void unzipFile(File zipFile, String destFile, String destDir) {

        try (ZipFile zf = new ZipFile(zipFile)) {
            File tempDir = FilePathCleaner.newFile(destDir);
            if (tempDir != null) {
                tempDir.mkdirs();
            }
            File desFile = FilePathCleaner.newFile(destDir, zipFile.getName());
            FileUtils.copyFile(zipFile, desFile);
            InputStream in = zf.getInputStream(zf.getEntry(destFile));
            File patchConfig = FilePathCleaner.newFile(destDir + "/" + destFile);
            inputstreamToFile(in, patchConfig);
        } catch (IOException e) {
            log.error("unzip file {} failed.", zipFile.getName());
        }
    }

    private void inputstreamToFile(InputStream ins, File file) throws IOException {
        try (OutputStream os = new FileOutputStream(file)) {
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        }
        ins.close();
    }
}