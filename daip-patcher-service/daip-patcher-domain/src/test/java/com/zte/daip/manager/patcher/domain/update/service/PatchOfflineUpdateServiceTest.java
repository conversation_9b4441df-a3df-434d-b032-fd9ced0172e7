package com.zte.daip.manager.patcher.domain.update.service;

/* Started by AICoder, pid:5d347abb84wd9801491b083bf0da779f8ab75606 */
import static org.mockito.Mockito.*;
import static org.hamcrest.MatcherAssert.*;
import static org.hamcrest.Matchers.*;

import java.util.Collections;
import java.util.List;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.api.update.response.OfflinePatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.update.service.constructor.ServicePatchSourceConstructor;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

@RunWith(MockitoJUnitRunner.class)
public class PatchOfflineUpdateServiceTest {

    @Mock
    private PatchTypeQueryService patchTypeQueryService;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private ServicePatchSourceConstructor servicePatchSourceConstructor;
    @InjectMocks
    private PatchOfflineUpdateService service;

    @Before
    public void setUp() throws DaipBaseException {
        when(applicationContext.getBean(anyString())).thenReturn(servicePatchSourceConstructor);
        when(patchTypeQueryService.queryPatchTypeByServiceName(anyString(),anyString())).thenReturn("service");
        OfflinePatchUpdateInfo offlinePatchUpdateInfo = new OfflinePatchUpdateInfo();
        ServicePatchInfo servicePatchInfo = new ServicePatchInfo();
        SimplePatchInfo simplePatchInfo = new SimplePatchInfo();
        simplePatchInfo.setPatchName("a");
        servicePatchInfo.setPatches(Lists.newArrayList(simplePatchInfo));
        offlinePatchUpdateInfo.setServicePatchInfos(Lists.newArrayList(servicePatchInfo));
        when(servicePatchSourceConstructor.obtainOfflinePatchUpdateInfo(any(),anyString(),anyString())).thenReturn(
                Lists.newArrayList(offlinePatchUpdateInfo)
        );
        
    }

    @Test
    public void should_ReturnEmptyList_When_NoServiceParams() throws DaipBaseException {
        PatchUpdateParam param = createValidParam();
        List<OfflinePatchUpdateInfo> result = service.queryUpdatePatchInfo(param);
    }

    private PatchUpdateParam createValidParam() {
        PatchUpdateParam param = new PatchUpdateParam();
        param.setClusterId("cluster1");
        param.setHostIp("127.0.0.1");
        PatchServiceParam serviceParam = new PatchServiceParam();
        serviceParam.setServiceName("service1");
        param.setServiceParams(Collections.singletonList(serviceParam));
        return param;
    }
}

/* Ended by AICoder, pid:5d347abb84wd9801491b083bf0da779f8ab75606 */