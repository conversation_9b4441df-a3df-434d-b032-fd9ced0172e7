package com.zte.daip.manager.patcher.domain.update.service.constructor;

import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.ServiceInstance;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class ZdhSourceConstructorTest {

    @Mock
    private DeploymentInstanceServiceControllerApi instanceServiceControllerApi;

    @Mock
    private PatchHomeQueryService patchHomeQueryService;
    @Mock
    private ServiceModelInfoCache serviceModelInfoCache;
    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchHistoryService patchHistoryService;

    @InjectMocks
    private ZdhSourceConstructor zdhSourceConstructor;

    @Test
    public void obtainOfflinePatchUpdateInfo() throws DaipBaseException {
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceId("serviceId");
        serviceModel.setPatchType("service");
        serviceModel.setComponentType("dap.manager.common.bigdata");
        when(serviceModelInfoCache.queryByClusterIdAndServiceName(anyString(), anyString())).thenReturn(serviceModel);
        PatchServiceParam updateParam = new PatchServiceParam();
        updateParam.setVersion("version");
        updateParam.setServiceName("serviceName");
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceInstanceId("instanceId");
        serviceInstance.setRoles(Lists.newArrayList("roleId"));
        updateParam.setServiceInstances(Lists.newArrayList(serviceInstance));
        zdhSourceConstructor.obtainOfflinePatchUpdateInfo(updateParam, "100000", "********");
    }
}