package com.zte.daip.manager.patcher.domain.update.service;

import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchTypeQueryServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/8</p>
 * <p>完成日期：2021/4/8</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchTypeQueryServiceTest {

    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;
    @InjectMocks
    private PatchTypeQueryService patchTypeQueryService;

    @Test
    public void queryPatchTypeByServiceName() {

        String clusterId = "0";
        Mockito.when(productModelInfoControllerApi.queryByClusterId(clusterId)).thenReturn(mockServiceModel());

        String patchType = patchTypeQueryService.queryPatchTypeByServiceName(clusterId, "hdfs");

        Assert.assertEquals("service", patchType);

        String zdhBaseServicePatchType = patchTypeQueryService.queryPatchTypeByServiceName(clusterId, "zdh");

        Assert.assertEquals("ZDH", zdhBaseServicePatchType);

        String newServiceNamePatchType =
            patchTypeQueryService.queryPatchTypeByServiceName(clusterId, "new serviceName");

        Assert.assertEquals("service", newServiceNamePatchType);

    }

    @Test
    public void queryBigDataServiceIds() {

        String clusterId = "0";
        Mockito.when(productModelInfoControllerApi.queryByClusterId(clusterId)).thenReturn(mockServiceModel());

        Set<String> bigDataServiceIds = patchTypeQueryService.queryBigDataServiceIds(clusterId);

        Assert.assertEquals(Sets.newHashSet("dap.manager.hdfs"), bigDataServiceIds);

    }

    private List<ServiceModel> mockServiceModel() {
        String json =
            "[{\"serviceId\":\"vmax.datagovernance\",\"serviceName\":\"datagovernance\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1,11\",\"serviceInstallType\":\"singleService\",\"displayOrder\":450,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"vmax.datagovernance.main\",\"roleName\":\"main\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/vmax-service/vmax-datagovernance/jettyDataGovernance.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"role\",\"clusterType\":\"smallCluster,defaultCluster\",\"version\":\"V20.01.16.00\",\"descriptionsZH\":\"VMAX平台-数据治理服务\",\"descriptionsEN\":\"vmax-datagovernance\"},{\"serviceId\":\"VMAX.ConfigManage\",\"serviceName\":\"ConfigManage\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"singleService\",\"displayOrder\":4,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.ConfigManage.ConfigManage\",\"roleName\":\"ConfigManage\",\"abbreviation\":\"\",\"roleType\":\"NO_LIMIT\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"0\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependServicePosition\",\"type\":\"unlimited\",\"value\":\">VMAX.ICT\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/ems/ums-server/configManage/java.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"role\",\"clusterType\":\"smallCluster,defaultCluster\",\"version\":\"V20.01.01.16\",\"descriptionsZH\":\"ConfigManage用于维护表格，提供二次开发框架\",\"descriptionsEN\":\"ConfigManage is used to maintain tables and provide a secondary development framework\"},{\"serviceId\":\"VMAX.datamanagement\",\"serviceName\":\"datamanagement\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"singleService\",\"displayOrder\":305,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.datamanagement.datamanagement\",\"roleName\":\"datamanagement\",\"abbreviation\":\"\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/jettyDataManagement.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"defaultCluster\",\"version\":\"V20.01.04.10\",\"descriptionsZH\":\"Datamanagement是系统内数据信息查看、管理的组件。\",\"descriptionsEN\":\"Datamanagement is a component for data management\"},{\"serviceId\":\"wireless.vmaxdatabuilder\",\"serviceName\":\"vmaxdatabuilder\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"0,1,5,11\",\"serviceInstallType\":\"singleService\",\"displayOrder\":4,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"wireless.vmaxdatabuilder.databuilder\",\"roleName\":\"databuilder\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/var/run/databuilder.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"role\",\"clusterType\":\"defaultCluster\",\"version\":\"V20.04.04.02\",\"descriptionsZH\":\"vmaxdatabuilder是vmax的开发套件\",\"descriptionsEN\":\"vmax workspace function\"},{\"serviceId\":\"VMAX.WEBGIS\",\"serviceName\":\"WEBGIS\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"singleService\",\"displayOrder\":4,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.WEBGIS.WEBGIS\",\"roleName\":\"WEBGIS\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependServicePosition\",\"type\":\"unlimited\",\"value\":\"VMAX.ICT\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/ems/ums-server/WEBGIS/zte_webgis1.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"role\",\"clusterType\":\"smallCluster,defaultCluster\",\"version\":\"V20.01.15.02\",\"descriptionsZH\":\"地理化信息系统\",\"descriptionsEN\":\"WEBGIS\"},{\"serviceId\":\"VMAX.postgreSQL\",\"serviceName\":\"postgreSQL\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"multiService\",\"displayOrder\":206,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.postgreSQL.postgreSQL\",\"roleName\":\"postgreSQL\",\"abbreviation\":\"\",\"roleType\":\"NO_LIMIT\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"0\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/file/bin/postgres.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"smallCluster,defaultCluster\",\"version\":\"V20.01.04.03\",\"descriptionsZH\":\"postgreSQL数据库组件。\",\"descriptionsEN\":\"postgreSQL is a database component\"},{\"serviceId\":\"VMAX.VMAXCommon\",\"serviceName\":\"VMAXCommon\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"singleService\",\"displayOrder\":4,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.VMAXCommon.VMAXCommon\",\"roleName\":\"VMAXCommon\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependServicePosition\",\"type\":\"unlimited\",\"value\":\"VMAX.ICT\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/ems/ums-server/VMAXCommon/vmaxdatacheck1.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"role\",\"clusterType\":\"smallCluster,defaultCluster\",\"version\":\"V20.01.10.01\",\"descriptionsZH\":\"VMAXCommon是vmax下公共组件\",\"descriptionsEN\":\"VMAXCommon\"},{\"serviceId\":\"dap.manager.ldap\",\"serviceName\":\"ldap\",\"componentType\":\"dap.manager.common.feagle\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"COLLECT_FROM_OS\",\"serviceShowTabs\":\"0,22\",\"serviceInstallType\":\"singleService\",\"displayOrder\":450,\"clusterRestart\":false,\"roles\":[{\"roleId\":\"dap.manager.ldap.LdapServer1\",\"roleName\":\"LdapServer1\",\"abbreviation\":\"\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS_REDHAT\",\"collectionParameters\":\"/var/run/openldap/slapd.pid\"},{\"collectionType\":\"PROCESS_SUSE\",\"collectionParameters\":\"/var/run/slapd/slapd.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false},{\"roleId\":\"dap.manager.ldap.LdapServer2\",\"roleName\":\"LdapServer2\",\"abbreviation\":\"\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependService\",\"type\":\"unlimited\",\"value\":\"dap.manager.ldap.LdapServer1\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS_REDHAT\",\"collectionParameters\":\"/var/run/openldap/slapd.pid\"},{\"collectionType\":\"PROCESS_SUSE\",\"collectionParameters\":\"/var/run/slapd/slapd.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"dapmanagerCluster\",\"version\":\"V20.19.40.R4.B2\",\"descriptionsZH\":\"ldap是一款轻量级目录访问协议,用于存储用户信息。\",\"descriptionsEN\":\"ldap is Lightweight Directory Access Protocol.Store user information\"},{\"serviceId\":\"dap.manager.whale\",\"serviceName\":\"whale\",\"componentType\":\"dap.manager.common.feagle\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"UNDEFINED\",\"serviceShowTabs\":\"0,1,11,12\",\"serviceInstallType\":\"singleService\",\"displayOrder\":105,\"clusterRestart\":true,\"webConsolePath\":\"/\",\"roles\":[{\"roleId\":\"dap.manager.whale.whale\",\"roleName\":\"whale\",\"abbreviation\":\"wh\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"3\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"number\",\"type\":\"regex\",\"value\":\"1\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"$FEAGLE_INSTALL_PATH/dapmanager-whale/dapmanager-whale.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"dapmanagerCluster\",\"version\":\"V20.19.40.R4.B2\",\"descriptionsZH\":\"Whale是优化指标采集的服务。\",\"descriptionsEN\":\"Whale is a server which make index collection better.\"},{\"serviceId\":\"dap.manager.configcenter\",\"serviceName\":\"configcenter\",\"componentType\":\"dap.manager.common.feagle\",\"operations\":\"rollingRestartService\",\"heartBeatReportType\":\"UNDEFINED\",\"serviceShowTabs\":\"0,1,11,12\",\"serviceInstallType\":\"singleService\",\"displayOrder\":10004,\"clusterRestart\":true,\"webConsolePath\":\"\",\"roles\":[{\"roleId\":\"dap.manager.configcenter.configcenter\",\"roleName\":\"configcenter\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"3\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"$FEAGLE_INSTALL_PATH/dapmanager-configcenter/dapmanager-configcenter.pid\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/jmx\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"dapmanagerCluster\",\"version\":\"V20.19.40.R4.B2\",\"descriptionsZH\":\"configcenter提供服务启动所需配置信息等功能\",\"descriptionsEN\":\"\"},{\"serviceId\":\"dap.manager.dapCache\",\"serviceName\":\"dapCache\",\"componentType\":\"dap.manager.common.feagle\",\"operations\":\"rollingRestartService\",\"heartBeatReportType\":\"UNDEFINED\",\"serviceShowTabs\":\"0,1,11,12\",\"serviceInstallType\":\"singleService\",\"displayOrder\":2,\"clusterRestart\":true,\"webConsolePath\":\"\",\"roles\":[{\"roleId\":\"dap.manager.dapCache.dapCache\",\"roleName\":\"dapCache\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"$FEAGLE_INSTALL_PATH/dapCache/dapmanager-provider-cache.pid\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/jmx\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"dapmanagerCluster\",\"version\":\"V20.19.40.R4.B2\",\"descriptionsZH\":\"dapCache提供分布式缓存服务\",\"descriptionsEN\":\"dapCache provides distributed cache service\"},{\"serviceId\":\"dap.manager.dapDiscovery\",\"serviceName\":\"dapDiscovery\",\"componentType\":\"dap.manager.common.feagle\",\"operations\":\"rollingRestartService\",\"heartBeatReportType\":\"UNDEFINED\",\"serviceShowTabs\":\"0,1,11,12\",\"serviceInstallType\":\"singleService\",\"displayOrder\":1,\"clusterRestart\":true,\"webConsolePath\":\"\",\"roles\":[{\"roleId\":\"dap.manager.dapDiscovery.dapDiscovery\",\"roleName\":\"dapDiscovery\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"3\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"$FEAGLE_INSTALL_PATH/dapDiscovery/dapmanager-provider-dapDiscovery.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"dapmanagerCluster\",\"version\":\"V20.19.40.R4.B2\",\"descriptionsZH\":\"dapDiscovery负责服务治理功能，提供服务实例的自动注册与发现\",\"descriptionsEN\":\"dapDiscovery is responsible for service management funcations,providing self registration and discovery of service instances.\"},{\"serviceId\":\"dap.manager.deployer\",\"serviceName\":\"deployer\",\"componentType\":\"dap.manager.common.feagle\",\"operations\":\"rollingRestartService\",\"heartBeatReportType\":\"UNDEFINED\",\"serviceShowTabs\":\"0,1,11,12\",\"serviceInstallType\":\"singleService\",\"displayOrder\":10002,\"clusterRestart\":true,\"webConsolePath\":\"\",\"roles\":[{\"roleId\":\"dap.manager.deployer.deployer\",\"roleName\":\"deployer\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"3\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"$FEAGLE_INSTALL_PATH/dapmanager-deployer/dapmanager-deployer.pid\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/jmx\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"dapmanagerCluster\",\"version\":\"V20.19.40.R4.B2\",\"descriptionsZH\":\"deployer提供版本管理及部署功能\",\"descriptionsEN\":\"deployer provides version management and version deployment\"},{\"serviceId\":\"dap.manager.dapLog\",\"serviceName\":\"dapLog\",\"componentType\":\"dap.manager.common.feagle\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"UNDEFINED\",\"serviceShowTabs\":\"0,1,11,12\",\"serviceInstallType\":\"singleService\",\"displayOrder\":10002,\"clusterRestart\":true,\"webConsolePath\":\"\",\"roles\":[{\"roleId\":\"dap.manager.dapLog.dapLog\",\"roleName\":\"dapLog\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"3\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"$FEAGLE_INSTALL_PATH/dapLog/dapmanager-provider-dapLog.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"dapmanagerCluster\",\"version\":\"V20.19.40.R4.B2\",\"descriptionsZH\":\"dapLog提供服务日志、审计日志配置功能\",\"descriptionsEN\":\"dapLog provides configuration for service log and audit log\"},{\"serviceId\":\"dap.manager.dapMonitor\",\"serviceName\":\"dapMonitor\",\"componentType\":\"dap.manager.common.feagle\",\"operations\":\"rollingRestartService\",\"heartBeatReportType\":\"UNDEFINED\",\"serviceShowTabs\":\"0,1,11,12\",\"serviceInstallType\":\"singleService\",\"displayOrder\":3,\"clusterRestart\":true,\"webConsolePath\":\"\",\"roles\":[{\"roleId\":\"dap.manager.dapMonitor.dapMonitor\",\"roleName\":\"dapMonitor\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"3\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"$FEAGLE_INSTALL_PATH/dapMonitor/dapmanager-provider-dapMonitor.pid\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/jmx\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"dapmanagerCluster\",\"version\":\"V20.19.40.R4.B2\",\"descriptionsZH\":\"dapMonitor负责监控主机、组件状态\",\"descriptionsEN\":\"dapMonitor monitors the status of the host,service\"},{\"serviceId\":\"VMAX.dintdataloader\",\"serviceName\":\"dintdataloader\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"1\",\"displayOrder\":205,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.dintdataloader.dintdataloader\",\"roleName\":\"dintdataloader\",\"abbreviation\":\"\",\"roleType\":\"NO_LIMIT\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[],\"needPatch\":true,\"roleStatusCalcType\":\"JMX\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"defaultCluster\",\"version\":\"V20.01.02.05\",\"descriptionsZH\":\"dintdataloader是数据下载组件。\",\"descriptionsEN\":\"dintdataloader is a data load component\"},{\"serviceId\":\"VMAX.ICT\",\"serviceName\":\"ICT\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"singleService\",\"displayOrder\":206,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.ICT.ICT\",\"roleName\":\"ICT\",\"abbreviation\":\"\",\"roleType\":\"NO_LIMIT\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"0\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependServicePosition\",\"type\":\"unlimited\",\"value\":\"VMAX.postgreSQL\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/ems/ums-server/ict/zte_webprocess1.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"smallCluster,defaultCluster\",\"version\":\"V20.01.07.03\",\"descriptionsZH\":\"ICT是一个统一网管平台,简称UEP\",\"descriptionsEN\":\"ICT is the unified elementmanagement platfor(UEP)\"},{\"serviceId\":\"VMAX.openapi\",\"serviceName\":\"openapi\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"singleService\",\"displayOrder\":440,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.openapi.apiproxy\",\"roleName\":\"APIProxy\",\"abbreviation\":\"\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/nginx.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false},{\"roleId\":\"VMAX.openapi.apimanager\",\"roleName\":\"APIManager\",\"abbreviation\":\"\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/jettyApiManager.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false},{\"roleId\":\"VMAX.openapi.apiserver\",\"roleName\":\"APIServer\",\"abbreviation\":\"\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/jettyAdvance.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false},{\"roleId\":\"VMAX.openapi.apifileserver\",\"roleName\":\"APIFileServer\",\"abbreviation\":\"\",\"roleType\":\"NO_LIMIT\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/jettyApiFileServer.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"role\",\"clusterType\":\"defaultCluster\",\"version\":\"V21.01.01.00\",\"descriptionsZH\":\"openapi是vmaxplat的数据开放服务\",\"descriptionsEN\":\"Open Data Services\"},{\"serviceId\":\"VMAX.vmaxplat\",\"serviceName\":\"vmaxplat\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"singleService\",\"displayOrder\":4,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.vmaxplat.ADMA\",\"roleName\":\"ADMA\",\"abbreviation\":\"\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependServicePosition\",\"type\":\"unlimited\",\"value\":\"VMAX.ICT\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"/home/<USER>/ems/ums-server/vmaxplat/vmax-metadata-manager.pid\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"role\",\"clusterType\":\"smallCluster,defaultCluster\",\"version\":\"V20.01.06.72\",\"descriptionsZH\":\"vmaxplat是vmax下平台功能\",\"descriptionsEN\":\"vmaxplat\"},{\"serviceId\":\"vmax.portal\",\"serviceName\":\"portal\",\"componentType\":\"\",\"operations\":\"uninstallService\",\"heartBeatReportType\":\"\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"singleService\",\"displayOrder\":7,\"clusterRestart\":false,\"roles\":[{\"roleId\":\"vmax.portal.Portal\",\"roleName\":\"Portal\",\"abbreviation\":\"\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependServicePosition\",\"type\":\"service\",\"value\":\"VMAX.RDK\"}],\"operations\":\"uninstallRole\",\"statusCollections\":[],\"needPatch\":true,\"roleStatusCalcType\":\"N/A\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"role\",\"clusterType\":\"smallCluster,defaultCluster\",\"version\":\"V20.01.08.04\",\"descriptionsZH\":\"统一门户服务\",\"descriptionsEN\":\"Unified Portal Service\"},{\"serviceId\":\"VMAX.dataintegration\",\"serviceName\":\"dataintegration\",\"componentType\":\"\",\"operations\":\"startService,stopService,uninstallService\",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"1\",\"serviceInstallType\":\"1\",\"displayOrder\":205,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"VMAX.dataintegration.dataintegration\",\"roleName\":\"dataintegration\",\"abbreviation\":\"\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"number\",\"type\":\"regex\",\"value\":\"1\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[],\"needPatch\":true,\"roleStatusCalcType\":\"JMX\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"defaultCluster\",\"version\":\"V20.01.02.11\",\"descriptionsZH\":\"dataintegration是数据集成组件。\",\"descriptionsEN\":\"Dataintegration is a data integration component\"},{\"serviceId\":\"dap.manager.hdfs\",\"serviceName\":\"hdfs\",\"componentType\":\"dap.manager.common.bigdata\",\"patchType\":\"service\",\"operations\":\"\\n        startService,stopService,uninstallService,hdfs_init,deployConfig,downloadConfig\\n    \",\"heartBeatReportType\":\"JMX\",\"serviceShowTabs\":\"0,1,2,5,11,16,18,19,20\",\"serviceInstallType\":\"singleService\",\"displayOrder\":120,\"clusterRestart\":true,\"roles\":[{\"roleId\":\"dap.manager.hdfs.NameNode\",\"roleName\":\"NameNode\",\"abbreviation\":\"NN\",\"roleType\":\"master\",\"heartBeatUrl\":\"\\n            http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\\n        \",\"identifyServiceAvailable\":false,\"pidFile\":[\"\\n        \"],\"roleShowTabs\":\"0,1,2,3,20\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"number\",\"type\":\"regex\",\"value\":\"^[12]$\"},{\"name\":\"dependRoleNameNodeNumber\",\"type\":\"dap.manager.hdfs.JournalNode,dap.manager.hdfs.SecondaryNameNode\",\"value\":\"2\"},{\"name\":\"attachRole\",\"type\":\"dap.manager.hdfs.zkfc\",\"value\":\"2\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false},{\"roleId\":\"dap.manager.hdfs.SecondaryNameNode\",\"roleName\":\"SecondaryNameNode\",\"abbreviation\":\"\",\"roleType\":\"unique\",\"heartBeatUrl\":\"\\n            http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\\n        \",\"identifyServiceAvailable\":false,\"pidFile\":[\"\\n        \"],\"roleShowTabs\":\"1,3,20\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"number\",\"type\":\"regex\",\"value\":\"^[1]$\"},{\"name\":\"dependRoleSingleNameNode\",\"type\":\"dap.manager.hdfs.NameNode\",\"value\":\"1\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false},{\"roleId\":\"dap.manager.hdfs.DataNode\",\"roleName\":\"DataNode\",\"abbreviation\":\"DN\",\"roleType\":\"slave\",\"heartBeatUrl\":\"http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\\n        \",\"identifyServiceAvailable\":false,\"pidFile\":[\"\\n        \"],\"roleShowTabs\":\"0,1,2,3,20\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"number\",\"type\":\"regex\",\"value\":\"^[1-9]\\\\d*$\"},{\"name\":\"dependService\",\"type\":\"unlimited\",\"value\":\"dap.manager.hdfs.NameNode\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false},{\"roleId\":\"dap.manager.hdfs.JournalNode\",\"roleName\":\"JournalNode\",\"abbreviation\":\"JN\",\"roleType\":\"halt3\",\"heartBeatUrl\":\"http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\\n        \",\"identifyServiceAvailable\":false,\"pidFile\":[\"\\n        \"],\"roleShowTabs\":\"0,1,2,3,20\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependServiceForJournalNodeRule\",\"type\":\"unlimited\",\"value\":\"dap.manager.hdfs.NameNode\"},{\"name\":\"number\",\"type\":\"regex\",\"value\":\"^[3579]|([1-9]+[13579])$\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false},{\"roleId\":\"dap.manager.hdfs.zkfc\",\"roleName\":\"zkfc\",\"abbreviation\":\"ZKFC\",\"roleType\":\"disable\",\"heartBeatUrl\":\"http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\\n        \",\"identifyServiceAvailable\":false,\"pidFile\":[\"\\n        \"],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"number\",\"type\":\"regex\",\"value\":\"^[2]$\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false},{\"roleId\":\"dap.manager.hdfs.httpfs\",\"roleName\":\"httpfs\",\"abbreviation\":\"HFS\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"http://${HOST}:${PORT}/webhdfs/jmx?user.name=hdfs\",\"identifyServiceAvailable\":false,\"pidFile\":[\"\\n        \"],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependService\",\"type\":\"unlimited\",\"value\":\"dap.manager.hdfs.NameNode\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/webhdfs/jmx?user.name=hdfs\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false},{\"roleId\":\"dap.manager.hdfs.kms\",\"roleName\":\"kms\",\"abbreviation\":\"KMS\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"http://${HOST}:${PORT}/kms/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat&user.name=hdfs\",\"identifyServiceAvailable\":false,\"pidFile\":[\"\\n        \"],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[{\"name\":\"dependService\",\"type\":\"unlimited\",\"value\":\"dap.manager.hdfs.NameNode\"}],\"operations\":\"startRole,stopRole,uninstallRole\",\"statusCollections\":[{\"collectionType\":\"PROCESS\",\"collectionParameters\":\"\"},{\"collectionType\":\"JMX\",\"collectionParameters\":\"http://${HOST}:${PORT}/kms/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat&user.name=hdfs\"}],\"needPatch\":true,\"roleStatusCalcType\":\"PID_JMX\",\"virtual\":false},{\"roleId\":\"dap.manager.hdfs.Gateway\",\"roleName\":\"Gateway\",\"abbreviation\":\"GW\",\"roleType\":\"no_limit\",\"heartBeatUrl\":\"\",\"identifyServiceAvailable\":false,\"pidFile\":[],\"roleShowTabs\":\"\",\"heartCollectImpl\":\"JMXCollect\",\"rules\":[],\"operations\":\"\",\"statusCollections\":[],\"needPatch\":true,\"roleStatusCalcType\":\"N/A\",\"virtual\":false}],\"roleLevelUpdate\":false,\"patchType\":\"service\",\"clusterType\":\"defaultCluster\",\"version\":\"V20.19.40.R4.B2\",\"descriptionsZH\":\"Hadoop 分布式文件系统 (HDFS) 是 Hadoop 应用程序使用的主要存储系统。HDFS创建多个数据块副本并将它们分布在整个群集的计算主机上，以启用可靠且极其快速的计算功能，当单节点部署时无需依赖Zookeeper服务，否则，依赖Zookeeper服务。\",\"descriptionsEN\":\"The Apache Hadoop software library is a framework that allows for the distributed processing of large data sets across clusters of computers using simple programming models. It is designed to scale up from single servers to thousands of machines, each offering local computation and storage(requires Zookeeper).\"}]";
        return JSON.parseArray(json, ServiceModel.class);
    }
}