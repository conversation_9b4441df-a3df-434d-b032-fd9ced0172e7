package com.zte.daip.manager.patcher.domain.common;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.patcher.api.update.response.OnePatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.rollback.entity.PatchRollbackDo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRollbackPo;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchRollbackRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static junit.framework.TestCase.assertEquals;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchRollbackModifyIpServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/23</p>
 * <p>完成日期：2023/3/23</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchRollbackServiceTest {
    @InjectMocks
    private PatchRollbackService patchRollbackService;
    @Mock
    private PatchRollbackRepository patchRollbackRepository;

    @Before
    public void setUp() {
        doNothing().when(patchRollbackRepository).deleteByRollbackKey(anyString(), anyString(), anyString(),
            anyString(), anyList());
        Mockito.when(patchRollbackRepository.saveAll(anyList())).thenReturn(Lists.newArrayList());
        PatchRollbackPo patchRollbackPo = new PatchRollbackPo();
        patchRollbackPo.setPatchUptime(1);
    }

    @Test
    public void deleteByRollbackKeyWithoutContainer() {
        boolean result = true;
        try {
            patchRollbackService.deleteByRollbackKeyWithoutContainer("kafka", "", "", "", Lists.newArrayList("Sp01"));
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void saveRollbackPatches() {
        boolean result = true;
        try {
            OnePatchUpdateInfo onePatchUpdateInfo = new OnePatchUpdateInfo();
            patchRollbackService.saveRollbackPatches(Lists.newArrayList(onePatchUpdateInfo));
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }
}