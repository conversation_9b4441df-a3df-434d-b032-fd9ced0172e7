/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchHistoryModifyIpServiceTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/30
 * </p>
 * <p>
 * 完成日期：2021/3/30
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchHistoryDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchHistoryRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchHistoryServiceTest {
    @Mock
    private PatchHistoryRepository patchHistoryRepository;
    @Spy
    private PatchHistoryDtoAssembler patchHistoryDtoAssembler = new PatchHistoryDtoAssembler();
    @InjectMocks
    private PatchHistoryService patchHistoryService;

    @Before
    public void setUp() {
        Mockito.when(patchHistoryRepository.findAll()).thenReturn(mockPatchHistoryInfo());

        Mockito.when(patchHistoryRepository.queryPatchHistoryByPatchKey(anyString(), anyString()))
            .thenReturn(mockZkSP001PatchHistoryInfo());
        Mockito.when(patchHistoryRepository.saveAll(any())).thenReturn(Lists.newArrayList());

    }

    @Test
    public void test_query_history_names() {
        Mockito.when(patchHistoryRepository.queryPatchName())
            .thenReturn(Lists.newArrayList("DAP-ZK-SP001", "DAP-HDFS-SP002"));
        List<String> patchNames = patchHistoryService.queryPatchHistoryName();
        assertEquals(2, patchNames.size());
    }

    @Test
    public void test_query_history_names_when_patch_is_0() {
        Mockito.when(patchHistoryRepository.queryPatchName()).thenReturn(Lists.newArrayList());
        List<String> patchNames = patchHistoryService.queryPatchHistoryName();
        assertEquals(0, patchNames.size());
    }

    @Test
    public void deleteByHistoryKeyWithoutContainer() {
        doNothing().when(patchHistoryRepository).deleteByHistoryKeyWithoutContainer(anyString(), anyString(),
            anyString(), anyString(), anyList());
        patchHistoryService.deleteByHistoryKeyWithoutContainer("a", "a", "a", "a", Lists.newArrayList());
        verify(patchHistoryRepository, times(1)).deleteByHistoryKeyWithoutContainer(anyString(), anyString(),
            anyString(), anyString(), anyList());
    }

    @Test
    public void test_save_batch_when_greater_2000() {
        List<PatchHistory> patchHistories = Lists.newArrayList();
        for (int i = 0; i < 3999; i++) {
            patchHistories.add(new PatchHistory());
        }
        patchHistoryService.saveBatch(patchHistories);

        verify(patchHistoryRepository, times(2)).saveAll(any());

    }

    @Test
    public void test_save_batch() {
        List<PatchHistory> patchHistories = Lists.newArrayList();
        for (int i = 0; i < 1000; i++) {
            patchHistories.add(new PatchHistory());
        }
        patchHistoryService.saveBatch(patchHistories);

        verify(patchHistoryRepository, times(1)).saveAll(any());

    }

    @Test
    public void test() {
        List<String> schemaPatchHistory = patchHistoryService.querySchemaPatchHistory();
        assertEquals(2, schemaPatchHistory.size());
    }

    @Test
    public void 查询单个补丁升级记录详情() {

        PatchKeyDo patchKeyDo = new PatchKeyDo("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01", "", "");

        final List<PatchHistoryDto> patchHistoryDtos = patchHistoryService.queryPatchDispatchInfoByPatchKey(patchKeyDo);

        assertEquals(1, patchHistoryDtos.size());
    }

    private List<PatchHistory> mockZkSP001PatchHistoryInfo() {
        List<PatchHistory> patchHistories = Lists.newArrayList();
        patchHistories
            .add(new PatchHistory(new PatchHistoryKey("10.10.10.1", "DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01")));
        return patchHistories;

    }

    private List<PatchHistory> mockPatchHistoryInfo() {
        List<PatchHistory> patchHistories = Lists.newArrayList();

        patchHistories
            .add(new PatchHistory(new PatchHistoryKey("10.10.10.1", "DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01")));

        patchHistories
            .add(new PatchHistory(new PatchHistoryKey("10.10.10.2", "DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP02")));

        return patchHistories;
    }

}