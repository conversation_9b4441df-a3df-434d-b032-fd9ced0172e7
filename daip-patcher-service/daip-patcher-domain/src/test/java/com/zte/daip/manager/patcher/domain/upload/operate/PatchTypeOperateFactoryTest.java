package com.zte.daip.manager.patcher.domain.upload.operate;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class PatchTypeOperateFactoryTest {

    @MockBean
    private ContainerPatchOperator containerPatchOperator;

    @MockBean
    private NormalPatchOperator normalPatchOperator;

    @MockBean
    private RepositoryVersionPatchOperator repositoryVersionPatchOperator;

    @MockBean
    private SchemaPatchOperator schemaPatchOperator;

    @Test
    public void getPatchType() {
        
        when(schemaPatchOperator.patchType()).thenReturn(Constants.SCHEMA_PATCH);
        when(repositoryVersionPatchOperator.patchType()).thenReturn(Constants.REPOSITORY_VERSION_PATCH);
        when(normalPatchOperator.patchType()).thenReturn(Constants.NORMAL_PATCH);
        when(containerPatchOperator.patchType()).thenReturn(Constants.CONTAINER_PATCH);

        PatchTypeOperateFactory patchTypeOperateFactory =
            new PatchTypeOperateFactory(Lists.newArrayList(containerPatchOperator, normalPatchOperator,
                repositoryVersionPatchOperator, schemaPatchOperator));
        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName("a-schema-");
        PatchTypeOperator patchType = patchTypeOperateFactory.getPatchType(patchBean);
        assertEquals(patchType.patchType(), Constants.SCHEMA_PATCH);
        PatchBean patchBean1 = new PatchBean();
        patchBean1.setPatchName("a-RepositoryVersion-");
        PatchTypeOperator patchType1 = patchTypeOperateFactory.getPatchType(patchBean1);
        assertEquals(patchType1.patchType(), Constants.REPOSITORY_VERSION_PATCH);
        PatchBean patchBean2 = new PatchBean();
        patchBean2.setPatchName("a");
        patchBean2.setContainPatch(true);
        PatchTypeOperator patchType2 = patchTypeOperateFactory.getPatchType(patchBean2);
        assertEquals(patchType2.patchType(), Constants.CONTAINER_PATCH);
        PatchBean patchBean3 = new PatchBean();
        patchBean3.setPatchName("a");
        PatchTypeOperator patchType3 = patchTypeOperateFactory.getPatchType(patchBean3);
        assertEquals(patchType3.patchType(), Constants.NORMAL_PATCH);
    }
}