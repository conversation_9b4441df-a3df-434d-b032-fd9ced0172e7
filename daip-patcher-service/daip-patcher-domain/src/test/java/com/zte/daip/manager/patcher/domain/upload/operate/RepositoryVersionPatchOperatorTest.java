package com.zte.daip.manager.patcher.domain.upload.operate;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/* Started by AICoder, pid:z88381645eb884514d9f0b50e0aae059c91565da */
import org.mockito.Mockito;
import static org.mockito.Mockito.*;

import java.util.Collections;

@RunWith(SpringRunner.class)
public class RepositoryVersionPatchOperatorTest {

    @Mock
    private PatchInfoService patchInfoService;

    @InjectMocks
    private RepositoryVersionPatchOperator repositoryVersionPatchOperator;

    @Test
    public void testGenerateFullKey() {
        PatchBean patchBean = new PatchBean();
        patchBean.setService("kafka");
        patchBean.setSrcVersion("942");

        String expectedFullKey =
            patchBean.getService() + ":" + patchBean.getSrcVersion() + ":" + Constants.REPOSITORY_VERSION_PATCH;
        String actualFullKey = repositoryVersionPatchOperator.generateFullKey(patchBean);

        assertTrue(expectedFullKey.equals(actualFullKey));
    }



    @Test
    public void testFilterHistoryFullPatch() {
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setIsContainerPatch(0);
        patchDetailPo.setPatchName("90" + Constants.REPOSITORY_VERSION_PATCH);

        List<PatchDetailPo> filteredPatches =
            repositoryVersionPatchOperator.filterHistoryFullPatch(Collections.singletonList(patchDetailPo));

        assertEquals(1, filteredPatches.size());
    }
}

/* Ended by AICoder, pid:z88381645eb884514d9f0b50e0aae059c91565da */