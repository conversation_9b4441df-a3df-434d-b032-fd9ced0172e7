package com.zte.daip.manager.patcher.domain.dispatch;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.ClusterBean;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.common.deployer.model.controller.api.ClusterVersionControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchDistributeBean;
import com.zte.daip.manager.patcher.api.dto.PatchDistributeDto;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.query.service.PatchDetailService;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchDispatchRepository;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.glassfish.jersey.internal.guava.Sets;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchDistributeServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/11</p>
 * <p>完成日期：2023/3/11</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchDistributeServiceTest {
    @InjectMocks
    private PatchDistributeService patchDistributeService;
    @Mock
    private ClusterInfoControllerApi clusterInfoControllerApi;
    @Mock
    private FileDistributeControllerApi fileDistributeControllerApi;
    @Mock
    private HostResourceControllerApi hostResourceControllerApi;
    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;
    @Mock
    private ClusterVersionControllerApi clusterVersionControllerApi;
    @Mock
    private PatchDispatchRepository patchDispatchRepository;
    @Mock
    private ServiceDeployedExecutor serviceDeployedExecutor;
    @Mock
    private PatchDetailService patchDetailService;
    @Mock
    private PatchEnvApi patchEnvApi;
    @Mock
    private DistributeProgressService distributeProgressService;

    @Before
    public void setUp() throws Exception {

        when(patchDetailService.queryAllPatch()).thenReturn(getPatchDetailDtos());
        when(patchDetailService.queryNeedDispatchExceptSchemaPatch()).thenReturn(getAllPatchExceptScheme());
        when(fileDistributeControllerApi.distributeFiles(any())).thenReturn("1111");
        when(clusterInfoControllerApi.queryAll()).thenReturn(getClusterBeans());
        when(serviceDeployedExecutor.getServiceInfo(anyString())).thenReturn(getServiceDeployMap());
        when(patchEnvApi.getRepositoryHomeEnv()).thenReturn("/data1/version/");
        when(hostResourceControllerApi.queryByClusterId(anyString())).thenReturn(Lists.newArrayList());

        when(productModelInfoControllerApi.queryAllServiceModels()).thenReturn(mockAllServiceModels());
        when(clusterVersionControllerApi.queryAllVersions()).thenReturn(mockAllClusterProjects());

        when(patchDispatchRepository.queryByIpList(anyList())).thenReturn(Lists.newArrayList());
        doNothing().when(distributeProgressService).checkAndUpdateDistributeProgress(anyString());
    }

    @Test
    public void test_auto_distribute() throws Exception {
        boolean result = true;
        try {
            patchDistributeService.autoUploadDistribute(getPatchDetailDtos());

        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }

    @Test
    public void test_distribute() throws DaipBaseException {

        boolean result = true;
        try {
            PatchDistributeDto patchDistributeDto = new PatchDistributeDto();
            PatchDistributeBean patchDistributeBean =
                new PatchDistributeBean("kafka", Lists.newArrayList("***********"));
            patchDistributeDto.setPatchDistributeBeans(Lists.newArrayList(patchDistributeBean));
            patchDistributeService.distributePatches(patchDistributeDto);
        } catch (Exception e) {
            result = false;
        }
        Assert.assertFalse(result);

    }

    @Test
    public void test_dispatchPatchesPrecheck() {
        PatchOperateResult patchOperateResult = patchDistributeService.dispatchPatchesPrecheck();
        Assert.assertFalse(patchOperateResult.isStatus());
        Assert.assertTrue(StringUtils.equals(patchOperateResult.getMessage(), "DAP-kafka-V20.23.40.01-SP004-20211125"));
    }

    private List<ServiceModel> mockAllServiceModels() {

        List<ServiceModel> serviceModels = Lists.newArrayList();
        ServiceModel serviceModel1 = new ServiceModel("dap.manager.zookeeper", "zookeeper");
        serviceModel1.setVersion("V20.23.40.01");
        serviceModel1.setComponentType("dap.manager.common.bigdata");

        ServiceModel serviceModel2 = new ServiceModel("dap.manager.kafka", "kafka");
        serviceModel2.setVersion("V20.23.40.02");
        serviceModel2.setComponentType("dap.manager.common.bigdata");

        serviceModels.add(serviceModel1);
        serviceModels.add(serviceModel2);
        return serviceModels;
    }

    private List<ClusterProject> mockAllClusterProjects() {

        List<ClusterProject> clusterProjects = Lists.newArrayList();
        ClusterProject clusterProject = new ClusterProject();
        clusterProject.setVersion("V20.23.40.01");
        clusterProject.setServiceName("zookeeper");
        clusterProject.setClusterId(100000);

        ClusterProject clusterProject1 = new ClusterProject();
        clusterProject1.setVersion("V20.23.40.02");
        clusterProject1.setServiceName("kafka");
        clusterProject1.setClusterId(100001);

        clusterProjects.add(clusterProject);
        clusterProjects.add(clusterProject1);
        return clusterProjects;
    }

    private Map<String, Map<String, Set<HostInfo>>> getServiceDeployMap() {
        Map<String, Map<String, Set<HostInfo>>> serviceInfo = new HashMap<>();
        Map<String, Set<HostInfo>> version4Kafka = new HashMap<>();
        version4Kafka.put("V20.23.40.01", getHostInfos());
        serviceInfo.put("kafka", version4Kafka);
        serviceInfo.put("zdh", version4Kafka);
        return serviceInfo;
    }

    private List<ClusterBean> getClusterBeans() {
        List<ClusterBean> clusterBeans = Lists.newArrayList();

        ClusterBean clusterBean = new ClusterBean();
        clusterBean.setClusterId(100000);
        clusterBeans.add(clusterBean);
        ClusterBean clusterBean1 = new ClusterBean();
        clusterBean1.setClusterId(100001);
        clusterBeans.add(clusterBean1);
        return clusterBeans;
    }

    private Set<HostInfo> getHostInfos() {
        Set<HostInfo> hostInfos = Sets.newHashSet();
        HostInfo hostInfo = new HostInfo();
        hostInfo.setHostName("host-10-228-66-6");
        hostInfo.setClusterId("100001");
        hostInfo.setIpAddress("***********");
        hostInfos.add(hostInfo);
        HostInfo hostInfo1 = new HostInfo();
        hostInfo1.setHostName("host-10-228-66-7");
        hostInfo1.setClusterId("100001");
        hostInfo1.setIpAddress("***********");
        hostInfos.add(hostInfo1);
        return hostInfos;
    }

    private List<PatchBean> getAllPatchExceptScheme() {
        return getPatchDetailDtos().stream().filter(e -> !StringUtils.contains(e.getPatchName(), "-schema-")).map(e -> {
            PatchBean patchBean = new PatchBean();
            patchBean.setPatchName(e.getPatchName());
            patchBean.setService(e.getService());
            patchBean.setSrcVersion(e.getBaseVersion());
            return patchBean;
        }).collect(Collectors.toList());
    }

    private List<PatchDetailDto> getPatchDetailDtos() {
        List<PatchDetailDto> patchDetailDtos = Lists.newArrayList();
        PatchDetailDto patchDetailDto = new PatchDetailDto();
        patchDetailDto.setPatchName("DAP-zdh-V20.23.40.01-SP004-20211125");
        patchDetailDto.setService("zdh");
        patchDetailDto.setBaseVersion("V20.23.40.01");
        PatchDetailDto patchDetailDto1 = new PatchDetailDto();
        patchDetailDto1.setPatchName("DAP-kafka-V20.23.40.01-SP004-20211125");
        patchDetailDto1.setService("kafka");
        patchDetailDto1.setBaseVersion("V20.23.40.01");
        PatchDetailDto patchDetailDto2 = new PatchDetailDto();
        patchDetailDto2.setPatchName("DAP-kafka-V20.23.40.01-schema-SP004-20211125");
        patchDetailDto2.setService("kafka");
        patchDetailDto2.setBaseVersion("V20.23.40.01");
        patchDetailDtos.add(patchDetailDto);
        patchDetailDtos.add(patchDetailDto1);
        patchDetailDtos.add(patchDetailDto2);
        return patchDetailDtos;
    }

}