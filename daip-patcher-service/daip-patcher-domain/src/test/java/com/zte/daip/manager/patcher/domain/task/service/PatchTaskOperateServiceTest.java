package com.zte.daip.manager.patcher.domain.task.service;

import static junit.framework.Assert.assertFalse;
import static junit.framework.TestCase.assertEquals;
import static junit.framework.TestCase.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;

import java.util.*;

import com.zte.daip.manager.common.task.common.enums.WfInstanceStatus;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceDependencyControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.ClusterBean;
import com.zte.daip.manager.common.deployer.bean.patch.ServiceDependencyBean;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.i18n.DaipI18nService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.service.SchemaPatchTaskService;
import com.zte.daip.manager.patcher.domain.task.assembler.CurrentPatchAssembler;
import com.zte.daip.manager.patcher.domain.task.assembler.PatchTaskAssembler;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.PatchUpdateInnerControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchCategoryEnum;
import com.zte.daip.manager.response.CommonResponse;
import com.zte.daip.manager.task.api.ability.api.TaskManagerPlatformApi;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowRequest;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowSummary;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchTaskOperateServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/10/7</p>
 * <p>完成日期：2023/10/7</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class PatchTaskOperateServiceTest {
    @InjectMocks
    private PatchTaskOperateService patchTaskOperateService;

    @Mock
    private PatchTaskService patchTaskService;

    @Mock
    private DaipI18nService daipI18nService;

    @Mock
    private SchemaPatchTaskService schemaPatchTaskService;

    @Mock
    private TaskManagerPlatformApi taskManagerPlatformApi;

    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Mock
    private PatchTaskAssembler patchTaskAssembler;

    @Mock
    private ClusterInfoControllerApi clusterInfoControllerApi;

    //
    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private CurrentPatchAssembler currentPatchAssembler;

    @Mock
    private SchemaPatchApi schemaPatchApi;

    @Mock
    private PatchUpdateInnerControllerApi patchUpdateInnerControllerApi;

    @Mock
    private ServiceDependencyControllerApi serviceDependencyControllerApi;

    @Mock
    private PatchInfoService patchInfoService;

    @Mock
    private OrdinaryPatchService ordinaryPatchService;

    private PatchTaskDto patchTaskDto1 = new PatchTaskDto();

    private PatchTaskDto patchTaskDto2 = new PatchTaskDto();

    private PatchTaskPo patchTaskPo = new PatchTaskPo();

    private PatchTaskPo patchTaskPo1 = new PatchTaskPo();

    private PatchTaskPo patchTaskPo2 = new PatchTaskPo();

    private PatchHostInfoDto patchHostInfoDto1 = new PatchHostInfoDto();

    private long taskId = 1L;

    private long taskId1 = 2L;

    private String taskName = "1";

    private String taskName1 = "2";

    private String hdfsServiceId = "dap.amanager.hdfs";

    private String hdfsService = "hdfs";

    private String rollbackPatchPoint1 = "V20.23.40.04-CP01";

    private String ip1 = "*******";

    /* Started by AICoder, pid:p55387b96ax67e91481008f811f3da1208942737 */
    @Before
    public void init() throws DaipBaseException {
        // 初始化一些测试数据，包括ServiceInstancePatchInfo、RollBackPatchPointInfo等
        // serviceInstancePatchInfo
        patchHostInfoDto1.setIp(ip1);
        patchHostInfoDto1.setHostName("host1");

        RollBackPatchPointInfo rollBackPatchPointInfo1 = new RollBackPatchPointInfo();
        rollBackPatchPointInfo1.setRollBackPatchPoint(rollbackPatchPoint1);
        rollBackPatchPointInfo1.setPatchHostInfos(Lists.newArrayList(patchHostInfoDto1));
        List<RollBackPatchPointInfo> rollBackPatchPoints1 = Lists.newArrayList(rollBackPatchPointInfo1);

        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceId(hdfsServiceId);
        serviceInstance.setServiceName(hdfsService);
        serviceInstance.setServiceInstanceId(hdfsService);
        serviceInstance.setServiceInstanceName(hdfsServiceId);
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);
        serviceInstancePatchInfo.setRollBackPatchPoints(rollBackPatchPoints1);

        // patchTaskDto
        patchTaskDto1.setTaskId(taskId);
        patchTaskDto1.setTaskName(taskName);
        patchTaskDto1.setAllowModify(true);
        patchTaskDto1.setClusterId("1");
        patchTaskDto1.setRelationServices(Collections.singletonList(serviceInstance));
        patchTaskDto1.setPatchCategory(PatchCategoryEnum.ORDINARY.getPatchCategory());
        patchTaskDto1.setContext(Lists.newArrayList(serviceInstancePatchInfo));

        patchTaskDto2.setTaskId(taskId1);
        patchTaskDto2.setTaskName(taskName);
        patchTaskDto2.setAllowModify(true);
        patchTaskDto2.setPatchCategory(PatchCategoryEnum.ORDINARY.getPatchCategory());
        patchTaskDto2.setContext(Lists.newArrayList(serviceInstancePatchInfo));

        // patchTaskPo
        patchTaskPo.setTaskId(taskId);
        patchTaskPo.setTaskName(taskName);
        patchTaskPo.setRelationTaskId(taskId1);
        patchTaskPo.setPatchCategory(PatchCategoryEnum.ORDINARY.getPatchCategory());

        patchTaskPo1.setTaskId(taskId1);
        patchTaskPo1.setTaskName(taskName1);
        patchTaskPo1.setPatchCategory(PatchCategoryEnum.ORDINARY.getPatchCategory());

        patchTaskPo2.setOperateType(1);
        patchTaskPo2.setPatchCategory(PatchCategoryEnum.ORDINARY.getPatchCategory());
        patchTaskPo2.setTaskId(taskId);
        patchTaskPo2.setTaskName(taskName);
        patchTaskPo2.setOperateType(1);

        mockPatchTaskDto2Po();

        Mockito.when(productModelInfoControllerApi.queryByClusterId(patchTaskPo.getClusterId()))
            .thenReturn(Lists.newArrayList());
        Mockito.when(applicationContext.getBean(any(), (Class<Object>)any())).thenReturn(ordinaryPatchService);
        Mockito.when(ordinaryPatchService.organizeServiceInstancePatchInfo(any()))
            .thenReturn(Lists.newArrayList(serviceInstancePatchInfo));
        Map<String, List<String>> needUpdatePatchs = new HashMap<>();
        needUpdatePatchs.put(hdfsService, Collections.singletonList("hdfs-patch"));
        Mockito.when(patchUpdateInnerControllerApi.queryNeedUpdatePatchs(any(), any())).thenReturn(needUpdatePatchs);
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("hdfs-patch");
        patchDetailPo.setHotPatch(0);
        Mockito.when(patchInfoService.queryAllPatchExceptScheme()).thenReturn(Collections.singletonList(patchDetailPo));
        List<ServiceDependencyBean> needRestartMap = new ArrayList<>();
        needRestartMap.add(new ServiceDependencyBean(hdfsService, Collections.singletonList(hdfsService)));
        Mockito.when(serviceDependencyControllerApi.queryDependedService(any(), any())).thenReturn(needRestartMap);

    }

    @Test
    public void createPatchTask() throws DaipBaseException {
        // 测试创建补丁任务的方法
        Mockito.when(clusterInfoControllerApi.query("1")).thenReturn(new ClusterBean());
        // 当传入的参数为null时，期望抛出DaipBaseException异常
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.createPatchTask(null));

        // 模拟查询所有补丁任务返回重复记录的情况
        mockQueryAllPatchTasksReturnDuplicateRecord();
        // 期望抛出DaipBaseException异常
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.createPatchTask(patchTaskDto1));

        // 模拟查询所有补丁任务返回空的情况
        mockQueryAllPatchTasksReturnNull();
        // 期望抛出DaipBaseException异常
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.createPatchTask(patchTaskDto1));

        // 模拟添加补丁任务成功的情况
        mockAddPatchTask();
        // 期望抛出DaipBaseException异常
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.createPatchTask(patchTaskDto1));

        // 模拟创建嵌套工作流请求成功的情况
        NestWorkFlowRequest request = new NestWorkFlowRequest();
        Mockito.when(patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTaskPo)).thenReturn(request);
        Mockito.when(taskManagerPlatformApi.createNestWorkflow(request))
            .thenReturn(CommonResponse.success(Boolean.TRUE));
        boolean result = true;
        try {
            patchTaskOperateService.createPatchTask(patchTaskDto1);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryNeedRestartService() throws DaipBaseException {
        // 测试查询需要重启的服务的方法
        List<ServiceInstance> instances = patchTaskOperateService.queryNeedRestartService(patchTaskDto1);
        assertEquals(1, instances.size());
    }

    /* Ended by AICoder, pid:p55387b96ax67e91481008f811f3da1208942737 */

    @Test
    public void modifyPatchTask() throws DaipBaseException {
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.modifyPatchTask(null));

        mockQueryByTaskIdReturnNull();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.modifyPatchTask(patchTaskDto1));

        Mockito.when(patchTaskService.queryByTaskId(patchTaskPo1.getTaskId())).thenReturn(patchTaskPo);
        mockQueryAllPatchTasksReturnDuplicateRecord();
        mockPatchTaskDto2Po1();
        patchTaskDto1.setTaskName(taskName1);
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.modifyPatchTask(patchTaskDto1));

        mockQueryByTaskIdReturnTaskPo();
        mockPatchTaskDto2Po();
        mockAddPatchTask();
        patchTaskDto1.setTaskName(taskName);

        NestWorkFlowRequest request = new NestWorkFlowRequest();
        Mockito.when(patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTaskPo)).thenReturn(request);
        Mockito.when(taskManagerPlatformApi.editNestWorkflow(request)).thenReturn(CommonResponse.success(Boolean.TRUE));

        boolean result = true;
        try {
            patchTaskOperateService.modifyPatchTask(patchTaskDto1);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);

        mockQueryByTaskIdReturnTaskPo2();
        try {
            patchTaskOperateService.modifyPatchTask(patchTaskDto1);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void removePatchTask() throws DaipBaseException {
        mockQueryByTaskIdsReturnNull();
        Assert.assertThrows(DaipBaseException.class,
            () -> patchTaskOperateService.removePatchTask(Lists.newArrayList(taskId)));

        mockQueryByTaskIdsReturnList();
        Mockito.when(taskManagerPlatformApi.delete(anyList())).thenReturn(CommonResponse.success(Boolean.TRUE));
        boolean result = true;
        try {
            patchTaskOperateService.removePatchTask(Lists.newArrayList(taskId));
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void triggerPatchTask() throws DaipBaseException {
        mockQueryByTaskIdReturnNull();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.triggerPatchTask(taskId));

        mockQueryByTaskIdReturnTaskPo();
        boolean result = true;
        try {
            patchTaskOperateService.triggerPatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void retryPatchTask() throws DaipBaseException {
        mockQueryByTaskIdReturnNull();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.retryPatchTask(taskId));

        mockQueryByTaskIdReturnTaskPo();
        boolean result = true;
        try {
            patchTaskOperateService.retryPatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void pausePatchTask() throws DaipBaseException {
        mockQueryByTaskIdReturnNull();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.pausePatchTask(taskId));

        mockQueryByTaskIdReturnTaskPo();
        boolean result = true;
        try {
            patchTaskOperateService.pausePatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void resumePatchTask() throws DaipBaseException {
        mockQueryByTaskIdReturnNull();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.resumePatchTask(taskId));

        mockQueryByTaskIdReturnTaskPo();
        boolean result = true;
        try {
            patchTaskOperateService.resumePatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryAll() throws DaipBaseException {
        mockQueryAllPatchTasksReturnNull();
        List<ClusterBean> clusterBeans = new ArrayList<>();
        ClusterBean clusterBean = new ClusterBean();
        clusterBean.setClusterId(1);
        clusterBean.setClusterName("1");
        clusterBeans.add(clusterBean);
        Mockito.when(clusterInfoControllerApi.queryAll()).thenReturn(clusterBeans);
        List<PatchTaskDto> patchTaskDtos = patchTaskOperateService.queryAll();
        Assert.assertEquals(patchTaskDtos.size(), 0);

        mockQueryAllPatchTasksReturnDuplicateRecord();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.queryAll());

        NestWorkFlowSummary nestWorkFlowSummary = new NestWorkFlowSummary();
        nestWorkFlowSummary.setParentId(taskId);
        nestWorkFlowSummary.setStartTime(new Date());
        Mockito.when(taskManagerPlatformApi.summaries(any()))
            .thenReturn(CommonResponse.success(Lists.newArrayList(nestWorkFlowSummary)));
        mockPatchTaskPo2Dto1();
        mockPatchTaskPo2Dto2();
        List<PatchTaskDto> taskDtos = patchTaskOperateService.queryAll();
        Assert.assertEquals(taskDtos.size(), 2);

        String time = taskDtos.stream().filter(t -> t.getTaskId() == taskId).map(PatchTaskDto::getLatestExecutionTime)
            .findFirst().orElse(null);
        Assert.assertNotNull(time);
    }

    @Test
    public void copyPatchTaskTest() throws DaipBaseException {
        mockPatchTaskPo2Dto1();
        NestWorkFlowRequest request = new NestWorkFlowRequest();
        mockQueryByTaskIdReturnTaskPo();
        mockAddPatchTask();
        Mockito.when(patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTaskPo)).thenReturn(request);
        Mockito.when(taskManagerPlatformApi.createNestWorkflow(request))
            .thenReturn(CommonResponse.success(Boolean.TRUE));
        patchTaskOperateService.copyPatchTask(taskId);
    }

    @Test
    public void queryPatchDetailById() throws DaipBaseException {
        NestWorkFlowSummary nestWorkFlowSummary = new NestWorkFlowSummary();
        nestWorkFlowSummary.setStatus(WfInstanceStatus.FAILURE.getStatus());
        Mockito.when(taskManagerPlatformApi.summary(any()))
                .thenReturn(CommonResponse.success(nestWorkFlowSummary));
        mockPatchTaskPo2Dto1();
        mockQueryByTaskIdReturnTaskPo();
        boolean result = true;
        try {
            patchTaskOperateService.queryPatchDetailById(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryPatchTaskByTaskId() throws DaipBaseException {
        mockQueryByTaskIdReturnNull();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.queryPatchTaskByTaskId(taskId));

        mockQueryByTaskIdReturnTaskPo();
        boolean result = true;
        try {
            patchTaskOperateService.queryPatchTaskByTaskId(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void rollbackPatchTask() throws DaipBaseException {
        Mockito.when(clusterInfoControllerApi.query("1")).thenReturn(new ClusterBean());
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.rollbackPatchTask(null));

        mockQueryAllPatchTasksReturnDuplicateRecord();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.rollbackPatchTask(patchTaskDto1));

        mockQueryAllPatchTasksReturnNull();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.rollbackPatchTask(patchTaskDto1));

        mockAddPatchTask();
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskOperateService.rollbackPatchTask(patchTaskDto1));

        NestWorkFlowRequest request = new NestWorkFlowRequest();
        Mockito.when(patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTaskPo, patchTaskPo1)).thenReturn(request);
        Mockito.when(taskManagerPlatformApi.createNestWorkflow(request))
            .thenReturn(CommonResponse.success(Boolean.TRUE));

        mockQueryByTaskIdReturnTaskPo();
        mockQueryByTaskIdReturnTaskPo1();
        boolean result = true;
        try {
            patchTaskOperateService.rollbackPatchTask(patchTaskDto1);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /* Started by AICoder, pid:4da86l5595e422d144ac0bf25062c51e1b83f433 */
    @Test
    public void checkTaskCanRollbackTest() throws DaipBaseException {
        mockQueryByTaskIdReturnTaskPo();
        mockQueryAllPatchTasksReturnDuplicateRecord();
        mockPatchTaskPo2Dto1();
        PatchOperateResult patchOperateResult = patchTaskOperateService.checkTaskCanRollback(1);
        assertTrue(!patchOperateResult.isStatus());
        patchTaskPo.setRelationTaskId(5);
        patchTaskPo.setPatchCategory(PatchCategoryEnum.SCHEMA.getPatchCategory());
        Mockito.when(currentPatchAssembler.checkTaskIsSequence(any(), any(), any())).thenReturn(true);
        patchOperateResult = patchTaskOperateService.checkTaskCanRollback(patchTaskPo.getTaskId());
        assertTrue(patchOperateResult.isStatus());
    }
    /* Ended by AICoder, pid:4da86l5595e422d144ac0bf25062c51e1b83f433 */

    @Test
    public void checkTaskCanDuplicateTest() throws DaipBaseException {
        mockQueryByTaskIdReturnTaskPo();
        mockQueryAllPatchTasksReturnDuplicateRecord();
        mockPatchTaskPo2Dto1();
        Mockito.when(patchTaskService.queryByTaskId(2)).thenReturn(patchTaskPo2);
        PatchOperateResult patchOperateResult = patchTaskOperateService.checkTaskCanDuplicate(1);
        assertFalse(patchOperateResult.isStatus());
        Mockito.when(currentPatchAssembler.checkTaskIsSequence(any(), any(), any())).thenReturn(true);
        PatchOperateResult patchOperateResult2 = patchTaskOperateService.checkTaskCanDuplicate(1);
        assertTrue(patchOperateResult2.isStatus());
    }

    private void mockQueryAllPatchTasksReturnNull() {
        Mockito.when(patchTaskService.queryAllPatchTasks()).thenReturn(null);
    }

    private void mockQueryAllPatchTasksReturnDuplicateRecord() {
        Mockito.when(patchTaskService.queryAllPatchTasks()).thenReturn(Lists.newArrayList(patchTaskPo, patchTaskPo1));
    }

    private void mockAddPatchTask() {
        Mockito.when(patchTaskService.addPatchTask(patchTaskPo)).thenReturn(patchTaskPo);
    }

    private void mockQueryByTaskIdReturnNull() {
        Mockito.when(patchTaskService.queryByTaskId(patchTaskPo.getTaskId())).thenReturn(null);
    }

    private void mockQueryByTaskIdReturnTaskPo() {
        Mockito.when(patchTaskService.queryByTaskId(patchTaskPo.getTaskId())).thenReturn(patchTaskPo);
    }

    private void mockQueryByTaskIdReturnTaskPo1() {
        Mockito.when(patchTaskService.queryByTaskId(patchTaskPo1.getTaskId())).thenReturn(patchTaskPo1);
    }

    private void mockQueryByTaskIdReturnTaskPo2() {
        Mockito.when(patchTaskService.queryByTaskId(patchTaskDto1.getTaskId())).thenReturn(patchTaskPo2);
    }

    private void mockPatchTaskDto2Po() {
        Mockito.when(patchTaskAssembler.convertPatchTaskDto2Po(patchTaskDto1)).thenReturn(patchTaskPo);
    }

    private void mockPatchTaskDto2Po1() {
        Mockito.when(patchTaskAssembler.convertPatchTaskDto2Po(patchTaskDto1)).thenReturn(patchTaskPo1);
    }

    private void mockPatchTaskPo2Dto1() throws DaipBaseException {
        Mockito.when(patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo)).thenReturn(patchTaskDto1);
    }

    private void mockPatchTaskPo2Dto2() throws DaipBaseException {
        Mockito.when(patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo1)).thenReturn(patchTaskDto2);
    }

    private void mockQueryByTaskIdsReturnNull() {
        Mockito.when(patchTaskService.queryByTaskIds(Lists.newArrayList(taskId))).thenReturn(null);
    }

    private void mockQueryByTaskIdsReturnList() {
        Mockito.when(patchTaskService.queryByTaskIds(Lists.newArrayList(taskId)))
            .thenReturn(Lists.newArrayList(patchTaskPo));
    }
}