package com.zte.daip.manager.patcher.domain.query.service.servicerole;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.domain.query.valobj.UnpatchedParam;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;

@RunWith(SpringRunner.class)
public class ServicePatchSourceGeneratorTest {

    @Mock
    private PatchHomeQueryService patchHomeQueryService;
    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchHistoryService patchHistoryService;
    @Mock
    private ServiceModelInfoCache serviceModelInfoCache;
    @Mock
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;
    @InjectMocks
    private ServicePatchSourceGenerator servicePatchSourceGenerator;

    @Test
    public void obtainUnpatchedRoles() {}

    @Test
    public void queryPatchHome() throws DaipBaseException {
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceId("dap.zdh.zookeeper");
        Mockito.when(serviceModelInfoCache.queryByClusterIdAndServiceName(anyString(), anyString()))
            .thenReturn(serviceModel);
        List<DeploymentServiceInstance> serviceInstances = Lists.newArrayList();
        DeploymentServiceInstance deploymentServiceInstance = new DeploymentServiceInstance();
        deploymentServiceInstance.setServiceInstanceId("zk_1");
        serviceInstances.add(deploymentServiceInstance);
        Mockito.when(deploymentInstanceServiceControllerApi.queryByClusterIdAndServiceId(anyString(), anyString()))
            .thenReturn(serviceInstances);

        List<ConfigInstance> configInstances = Lists.newArrayList();
        ConfigInstance configInstance = new ConfigInstance();
        configInstance.setConfigValue("/zk_1");
        configInstances.add(configInstance);
        Mockito.when(patchHomeQueryService.queryPatchHome(anyString(), anyString())).thenReturn(configInstances);
        PatchHomeParam patchHomeParam = new PatchHomeParam("100001", "zookeeper", "zookeeper1", "HQ");
        String patchHome = servicePatchSourceGenerator.queryPatchHome(patchHomeParam);
        Assert.assertEquals("/zk_1" + File.separator + "patch", patchHome);
    }

    @Test
    public void obtainPatchUpdateInfos() throws DaipBaseException {
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("zookeeper-001");
        patchDetailPo.setIsContainerPatch(1);
        Mockito.when(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchDetailPo));
        PatchHistory patchHistory = new PatchHistory();
        PatchHistoryKey patchHistoryKey = new PatchHistoryKey("1", "DAP-ZOOKEEPER-SP002", "zookeeper");
        patchHistory.setId(patchHistoryKey);
        Mockito.when(patchHistoryService.queryPatchHistoryInfoByServiceNameAndIp(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchHistory));
        RollAutoPatchUpdateParam rollingPatchUpdateParam =
            new RollAutoPatchUpdateParam("100001", "zookeeper", "localhost");
        List<String> updatedContainerPatches = Lists.newArrayList("DAP-ZOOKEEPER-SP001-container");
        List<RollAutoPatchUpdateInfo> rollAutoPatchUpdateInfos =
            servicePatchSourceGenerator.obtainPatchUpdateInfos(rollingPatchUpdateParam, "942", updatedContainerPatches);
        Assert.assertEquals(1, rollAutoPatchUpdateInfos.size());

    }

    @Test
    public void generateServiceVersionKey() {
        PatchDetailDto patchDetailDto = new PatchDetailDto();
        patchDetailDto.setBaseVersion("V20.19.40.R4.B2");
        patchDetailDto.setService("zookeeper");
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setComponentType(Constants.BIG_DATA_SERVICE_ID);
        UnpatchedParam unpatchedParam = new UnpatchedParam();
        servicePatchSourceGenerator.setParams(unpatchedParam);
        String s = servicePatchSourceGenerator.generateServiceVersionKey(patchDetailDto, serviceModel);
        Assert.assertEquals(s, "zookeeper_V20.19.40.R4.B2");
    }

}