package com.zte.daip.manager.patcher.domain.update;
/* Started by AICoder, pid:5a8f4v119d6230314237081a60dcd2871496dbd0 */
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.patcher.domain.update.executor.depend.DependResourceLocalUtil;
import com.zte.daip.manager.patcher.domain.update.service.PatchTypeQueryService;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;

import static com.zte.daip.manager.patcher.domain.utils.Constants.BIG_DATA_SERVICE_ID;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * 测试类，用于测试PatchUpdateService的queryRollbackPoints方法。
 */
@RunWith(SpringRunner.class)
public class PatchUpdateServiceTest {
    /**
     * 注入Mockito的PatchUpdateService实例。
     */
    @InjectMocks
    private PatchUpdateService patchUpdateService;

    /**
     * Mock的PatchTypeQueryService实例。
     */
    @Mock
    private PatchTypeQueryService patchTypeQueryService;

    /**
     * Mock的DependResourceLocalUtil实例。
     */
    @Mock
    private DependResourceLocalUtil dependResourceLocalUtil;

    /**
     * Mock的ProductModelInfoControllerApi实例。
     */
    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    /**
     * Mock的ApplicationContext实例。
     */
    @Mock
    private ApplicationContext applicationContext;

    /**
     * 测试查询回滚点的方法。
     */
    @Test
    public void queryRollbackPoints() {
        // 设置mock对象的行为
        Mockito.when(patchTypeQueryService.queryBigDataServiceIds(anyString())).thenReturn(Collections.singleton("test"));
        ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setServiceId("test");
        Mockito.when(dependResourceLocalUtil.getClusterServiceRoleInfo(anyString())).thenReturn(Collections.singletonList(serviceRoleInfo));

        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceId("test");
        serviceModel.setClusterType(BIG_DATA_SERVICE_ID);
        serviceModel.setVersion("test");
        Mockito.when(productModelInfoControllerApi.queryByClusterId(anyString())).thenReturn(Collections.singletonList(serviceModel));

        // 执行测试操作并捕获异常
        boolean result = true;
        try {
            List<ServiceInstancePatchInfo> serviceInstancePatchInfos = patchUpdateService.queryRollbackPoints("test", Lists.newArrayList("test"));
        } catch (Exception e) {
            result = false;
        }

        // 断言结果
        Assert.assertFalse(result);
    }
}

/* Ended by AICoder, pid:5a8f4v119d6230314237081a60dcd2871496dbd0 */