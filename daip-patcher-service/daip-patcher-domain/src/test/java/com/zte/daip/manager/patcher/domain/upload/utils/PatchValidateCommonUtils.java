/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchCommonUtils.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/1
 * </p>
 * <p>
 * 完成日期：2021/4/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.upload.utils;

import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;

import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.assertj.core.util.Lists;

import java.io.File;
import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public class PatchValidateCommonUtils {

    public List<PatchDetailPo> generateDbDetail() {
        List<PatchDetailPo> patchDetails = Lists.newArrayList();
        PatchDetailPo patchDetail1 = new PatchDetailPo();
        patchDetail1.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP022-20200902");
        patchDetails.add(patchDetail1);
        return patchDetails;
    }

    public String getRepositoryHomeEnv() {
        String applicationPath =
            Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();
        return new File(applicationPath).getParent() + File.separator + "patchhome";
    }

    public File getPatchFile() {
        String applicationPath =
            Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();
        File patchDir = FilePathCleaner.newFile(new File(applicationPath).getParent() + File.separator + "patch");
        File[] files = patchDir.listFiles((d, s) -> !StringUtils.containsIgnoreCase(s, "patches"));
        if (files == null) {
            return null;
        }
        return files[0];
    }

    public File getPatchSet() {
        String applicationPath =
            Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();
        File patchDir = FilePathCleaner.newFile(new File(applicationPath).getParent() + File.separator + "patch");
        List<File> patchList = Lists.newArrayList(patchDir.listFiles());
        return patchList.get(0);
    }

}