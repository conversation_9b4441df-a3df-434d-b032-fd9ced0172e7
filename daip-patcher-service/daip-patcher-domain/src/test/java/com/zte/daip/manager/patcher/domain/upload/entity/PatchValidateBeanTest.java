package com.zte.daip.manager.patcher.domain.upload.entity;

/* Started by AICoder, pid:003c4y72bccf3621405d0bd8a0d8c55bd407b256 */
import static org.hamcrest.CoreMatchers.*;
import static org.junit.Assert.*;

import java.io.File;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

public class PatchValidateBeanTest {

    private PatchValidateBean patchValidateBean;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        patchValidateBean = new PatchValidateBean("batchId", new File("test.patch"));
    }

    @Test
    public void should_SetBatchIdAndPatchFile_When_ConstructorIsCalled() {
        assertThat(patchValidateBean.getBatchId(), is("batchId"));
        assertThat(patchValidateBean.getPatchFile(), is(notNullValue()));
    }

    @Test
    public void should_ReturnEmptyStringForOriginName_When_DefaultConstructorIsCalled() {
        PatchValidateBean defaultBean = new PatchValidateBean();
        assertThat(defaultBean.getOriginName(), is(""));
    }

    @Test
    public void should_SetOriginName_When_AlternateConstructorIsCalled() {
        PatchValidateBean alternateBean = new PatchValidateBean("batchId", new File("test.patch"), "originName");
        assertThat(alternateBean.getOriginName(), is("originName"));
    }

    @Test
    public void should_SetAndGetBatchId() {
        String batchId = "newBatchId";
        patchValidateBean.setBatchId(batchId);
        assertThat(patchValidateBean.getBatchId(), is(batchId));
    }

    @Test
    public void should_SetAndGetPatchFile() {
        File newPatchFile = new File("newTest.patch");
        patchValidateBean.setPatchFile(newPatchFile);
        assertThat(patchValidateBean.getPatchFile(), is(newPatchFile));
    }

    @Test
    public void should_SetAndGetOriginName() {
        String originName = "newOriginName";
        patchValidateBean.setOriginName(originName);
        assertThat(patchValidateBean.getOriginName(), is(originName));
    }
}

/* Ended by AICoder, pid:003c4y72bccf3621405d0bd8a0d8c55bd407b256 */