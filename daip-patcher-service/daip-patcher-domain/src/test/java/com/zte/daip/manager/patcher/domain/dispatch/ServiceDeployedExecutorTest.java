package com.zte.daip.manager.patcher.domain.dispatch;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModelInfo;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: ServiceDeployedExecutorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/11</p>
 * <p>完成日期：2023/3/11</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class ServiceDeployedExecutorTest {

    @InjectMocks
    private ServiceDeployedExecutor serviceDeployedExecutor;

    @Mock
    private HostResourceControllerApi hostResourceControllerApi;

    @Mock
    private ClusterInfoControllerApi clusterInfoControllerApi;

    @Mock
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @Test
    public void test_get_service_deployer() {
        List<HostInfo> hostInfos = getHostInfos();
        List<ServiceRoleInfo> serviceRoleInfos = getServiceRoleInfos();
        ServiceModelInfo serviceModelInfo = getServiceModelInfo();
        when(hostResourceControllerApi.queryByClusterId(anyString())).thenReturn(hostInfos);
        when(serviceResourceControllerApi.queryByClusterId(anyString())).thenReturn(serviceRoleInfos);
        when(clusterInfoControllerApi.queryServiceModelInfo(anyInt(), anyString())).thenReturn(serviceModelInfo);
        Map<String, Map<String, Set<HostInfo>>> serviceInfo = serviceDeployedExecutor.getServiceInfo("100001");
        Assert.assertTrue(serviceInfo.containsKey("kafka"));
    }

    private ServiceModelInfo getServiceModelInfo() {
        ServiceModelInfo serviceModelInfo = new ServiceModelInfo();
        serviceModelInfo.setProjectname("zdh");
        serviceModelInfo.setServiceId("dap.zdh.kafka");
        serviceModelInfo.setServiceName("kafka");
        serviceModelInfo.setVersion("V20.19.40.R4.B2");
        return serviceModelInfo;
    }

    private List<HostInfo> getHostInfos() {
        List<HostInfo> hostInfos = Lists.newArrayList();
        HostInfo hostInfo = new HostInfo();
        hostInfo.setHostName("host-10-228-66-6");
        hostInfo.setClusterId("100001");
        hostInfo.setIpAddress("***********");
        hostInfos.add(hostInfo);
        HostInfo hostInfo1 = new HostInfo();
        hostInfo1.setHostName("host-10-228-66-7");
        hostInfo1.setClusterId("100001");
        hostInfo1.setIpAddress("***********");
        hostInfos.add(hostInfo1);
        return hostInfos;
    }

    private List<ServiceRoleInfo> getServiceRoleInfos() {
        List<ServiceRoleInfo> serviceRoleInfos = Lists.newArrayList();

        ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setClusterId("100001");
        serviceRoleInfo.setIpAddress("***********");
        serviceRoleInfo.setServiceId("dap.zdh.kafka");
        serviceRoleInfo.setRoleId("dap.zdh.kafka.kafka1");
        serviceRoleInfo.setRoleName("kafka1");
        serviceRoleInfos.add(serviceRoleInfo);

        ServiceRoleInfo serviceRoleInfo1 = new ServiceRoleInfo();
        serviceRoleInfo1.setClusterId("100001");
        serviceRoleInfo1.setIpAddress("***********");
        serviceRoleInfo1.setServiceId("dap.zdh.kafka");
        serviceRoleInfo1.setRoleId("dap.zdh.kafka.kafka");
        serviceRoleInfo1.setRoleName("kafka");
        serviceRoleInfos.add(serviceRoleInfo1);
        return serviceRoleInfos;
    }

}