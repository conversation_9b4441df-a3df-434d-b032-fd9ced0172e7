package com.zte.daip.manager.patcher.domain.update.service;

import com.google.common.collect.Sets;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.po.*;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchesNeedUpdateQueryServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/8</p>
 * <p>完成日期：2021/4/8</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchesNeedUpdateQueryServiceTest {

    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchHistoryService patchHistoryService;
    @InjectMocks
    private PatchesNeedUpdateQueryService patchesNeedUpdateQueryService;

    @Test
    public void queryNeedUpdateRolePatches() {

        Set<String> needUpdateIps = Sets.newHashSet("*************", "*************");

        Mockito.when(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository("hdfs", "V20.19.40.R4.B2"))
            .thenReturn(mockPatchDetails());

        Mockito.when(patchHistoryService.queryAllPatchHistoryInfo()).thenReturn(mockPatchHistory());

        Map<String, List<SimplePatchInfo>> hdfs =
            patchesNeedUpdateQueryService.queryNeedUpdatePatchesByService("hdfs", "V20.19.40.R4.B2", needUpdateIps);

        Assert.assertEquals(2, hdfs.entrySet().size());

        List<SimplePatchInfo> simplePatchInfos = Lists.newArrayList();
        hdfs.keySet().forEach(e -> simplePatchInfos.addAll(hdfs.get(e)));
        Assert.assertEquals(2, hdfs.entrySet().size());
        Assert.assertEquals(9, simplePatchInfos.size());

        Map<String, List<SimplePatchInfo>> dn2Patch = patchesNeedUpdateQueryService.queryNeedUpdateRolePatches("hdfs",
            "V20.19.40.R4.B2", needUpdateIps, "DataNode");
        List<SimplePatchInfo> dnPatchInfos = Lists.newArrayList();
        dn2Patch.keySet().forEach(e -> dnPatchInfos.addAll(dn2Patch.get(e)));
        Assert.assertEquals(6, dnPatchInfos.size());

        Map<String, List<SimplePatchInfo>> nn2Patch = patchesNeedUpdateQueryService.queryNeedUpdateRolePatches("hdfs",
            "V20.19.40.R4.B2", needUpdateIps, "NameNode");
        List<SimplePatchInfo> nnPatchInfos = Lists.newArrayList();
        nn2Patch.keySet().forEach(e -> nnPatchInfos.addAll(nn2Patch.get(e)));
        Assert.assertEquals(10, nnPatchInfos.size());
    }

    @Test
    public void queryNeedUpdatePatchesByInstance(){
        Set<String> needUpdateIps = Sets.newHashSet("*************", "*************");

        Mockito.when(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository("cdrdispatch", "V23.10.01.01"))
            .thenReturn(mockInstancePatchDetails());

        Mockito.when(patchHistoryService.queryAllPatchHistoryInfo()).thenReturn(mockPatchHistory());

        Map<String, List<SimplePatchInfo>> cdr2Patch = patchesNeedUpdateQueryService.queryNeedUpdatePatchesByInstance("cdrdispatch",
            "V23.10.01.01", "cdrdispatch", needUpdateIps);
        List<SimplePatchInfo> cdrdispatchPatchInfos = Lists.newArrayList();
        cdr2Patch.keySet().forEach(e -> cdrdispatchPatchInfos.addAll(cdr2Patch.get(e)));
        Assert.assertEquals(9, cdrdispatchPatchInfos.size());

    }

    private List<PatchHistory> mockPatchHistory() {
        List<PatchHistory> patchHistories = Lists.newArrayList();

        patchHistories.add(new PatchHistory(new PatchHistoryKey("*************", "DAP-HDFS-V20.19.40.R4.B2-SP001")));

        PatchHistoryKey patchHistoryKey = new PatchHistoryKey();
        patchHistoryKey.setIp("*************");
        patchHistoryKey.setPatchName("VMAX-S-Cdrdispatch-V23.10.01.01_SP001");
        patchHistoryKey.setServiceName("cdrdispatch");
        patchHistoryKey.setServiceInstanceId("cdrdispatch");
        patchHistoryKey.setRoleName("");
        patchHistories.add(new PatchHistory(patchHistoryKey));

        return patchHistories;
    }

    private List<PatchDetailPo> mockPatchDetails() {

        int count = 5;
        List<PatchDetailPo> patchDetailPos = Lists.newArrayList();
        for (int i = 0; i < count; i++) {

            PatchDetailPo patchDetailPo1 = new PatchDetailPo();
            patchDetailPo1.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP00" + i);
            patchDetailPo1.setBaseVersion("V20.19.40.R4.B2");
            patchDetailPo1.setService("hdfs");
            patchDetailPo1.setDependPatch("DAP-HDFS-V20.19.40.R4.B2-SP00" + (i - 1));

            if (i % 2 == 0) {
                patchDetailPo1.setRoles("NameNode,DataNode");
            } else {
                patchDetailPo1.setRoles("NameNode,JournalNode");
            }

            patchDetailPos.add(patchDetailPo1);

        }
        return patchDetailPos;
    }

    private List<PatchDetailPo> mockInstancePatchDetails() {

        int count = 5;
        List<PatchDetailPo> patchDetailPos = Lists.newArrayList();
        for (int i = 0; i < count; i++) {

            PatchDetailPo patchDetailPo1 = new PatchDetailPo();
            patchDetailPo1.setPatchName("VMAX-S-Cdrdispatch-V23.10.01.01_SP00" + i);
            patchDetailPo1.setBaseVersion("V23.10.01.01");
            patchDetailPo1.setService("cdrdispatch");
            patchDetailPo1.setDependPatch("VMAX-S-Cdrdispatch-V23.10.01.01_SP00" + (i - 1));

            patchDetailPos.add(patchDetailPo1);

        }
        return patchDetailPos;
    }
}