/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UpdateSchemaPatchTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/24
 * </p>
 * <p>
 * 完成日期：2021/3/24
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.schema;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Future;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchRegisterRequest;
import com.zte.daip.manager.patcher.api.schema.UpdateSchemaPatchFile;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.domain.schema.utils.SchemaPatchUtils;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class UpdateSchemaPatchNewTest {

    @Mock
    private SchemaPatchUtils schemaPatchUtils;

    @Mock
    private DaipEventReporter daipEventReporter;

    @Mock
    private SchemaPatchApi schemaPatchApi;

    @InjectMocks
    private UpdateSchemaPatchNew updateSchemaPatch;

    @Before
    public void setUp() throws IOException {
        doNothing().when(daipEventReporter).error(anyString(), anyString());
        doNothing().when(daipEventReporter).info(anyString(), anyString());
        Mockito.when(schemaPatchApi.querySchemaRegisterInfos()).thenReturn(initSchemaPatchRegisterRequest());
        PatchOperateResult patchOperateResult = new PatchOperateResult();
        patchOperateResult.setStatus(true);
        Mockito.when(schemaPatchApi.notifyToThirdService(ArgumentMatchers.any(), ArgumentMatchers.any()))
            .thenReturn(patchOperateResult);
        Mockito.when(schemaPatchUtils.checkBeforeUpdatePatches(any())).thenReturn(patchOperateResult);
        Mockito.when(schemaPatchUtils.checkBeforeRollbackPatches(any())).thenReturn(patchOperateResult);
        Mockito.when(schemaPatchUtils.queryAllSchemaPatchFiles(anyString(), anyString()))
            .thenReturn(initUpdateSchemaPatchFile());
        Mockito.when(schemaPatchUtils.sortedSchemaPatchNames(any(), any()))
            .thenReturn(Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
        Mockito.when(schemaPatchUtils.searchSchemaPatchFiles(any(), any())).thenReturn(initUpdateSchemaPatchFile());
    }

    @Test
    public void 升级scheme补丁() throws Exception {
        Future<PatchOperateResult> patchOperateResultFuture = updateSchemaPatch.updatePatch(initPatchDetail());
        assertThat(true, is(patchOperateResultFuture.get().isStatus()));

        PatchOperateResult patchOperateResult1 = new PatchOperateResult();
        patchOperateResult1.setStatus(false);
        Mockito.when(schemaPatchUtils.checkPatchesHasUpdated(any())).thenReturn(true);
        Future<PatchOperateResult> patchOperateResult2 = updateSchemaPatch.updatePatch(initPatchDetail());
        assertThat(true, is(patchOperateResult2.get().isStatus()));

        Mockito.when(schemaPatchUtils.checkPatchesHasUpdated(any())).thenReturn(false);
        Mockito.when(schemaPatchApi.notifyToThirdService(ArgumentMatchers.any(), ArgumentMatchers.any()))
                .thenReturn(patchOperateResult1);
        Future<PatchOperateResult> patchOperateResult3 = updateSchemaPatch.updatePatch(initPatchDetail());
        assertThat(false, is(patchOperateResult3.get().isStatus()));

        Mockito.when(schemaPatchUtils.checkBeforeUpdatePatches(any())).thenReturn(patchOperateResult1);
        Future<PatchOperateResult> patchOperateResult4 = updateSchemaPatch.updatePatch(initPatchDetail());
        assertThat(false, is(patchOperateResult4.get().isStatus()));
    }

    @Test
    public void 升级scheme补丁_failure() throws Exception {
        Mockito.when(schemaPatchUtils.searchSchemaPatchFiles(any(), any())).thenReturn(Sets.newHashSet());
        Future<PatchOperateResult> patchOperateResult5 = updateSchemaPatch.updatePatch(initPatchDetail());
        assertThat(false, is(patchOperateResult5.get().isStatus()));
        Mockito.when(schemaPatchUtils.searchSchemaPatchFiles(any(), any())).thenReturn(Sets.newHashSet());
        PatchTaskInfo patchTaskInfo = initPatchDetail();
        patchTaskInfo.setVersion("V20.19.40.R4.B2");
        Future<PatchOperateResult> patchOperateResult6 = updateSchemaPatch.updatePatch(patchTaskInfo);
        assertThat(true, is(patchOperateResult6.get().isStatus()));
    }

    @Test
    public void 回退scheme补丁() throws Exception {
        Future<PatchOperateResult> patchOperateResultFuture = updateSchemaPatch.rollbackPatch(initPatchDetail());
        assertThat(true, is(patchOperateResultFuture.get().isStatus()));
    }

    private PatchTaskInfo initPatchDetail() {
        PatchTaskInfo patchTaskInfo = new PatchTaskInfo();
        patchTaskInfo.setServiceName("deployer");
        patchTaskInfo.setVersion("V20.22.40.09");
        patchTaskInfo.setUpdatePatchNames(Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
        return patchTaskInfo;
    }

    private List<SchemaPatchRegisterRequest> initSchemaPatchRegisterRequest() {
        List<SchemaPatchRegisterRequest> requests = Lists.newArrayList();
        SchemaPatchRegisterRequest schemaPatchRegisterRequest = new SchemaPatchRegisterRequest();
        schemaPatchRegisterRequest.setPort("56160");
        schemaPatchRegisterRequest.setServiceName("daip-deployer-svr");
        schemaPatchRegisterRequest.setUrl("/api/daip-deployer-svr/v1/schema/update");
        schemaPatchRegisterRequest.setRegisterSchemaPatchFiles(initRegisterSchemaPatchFile());
        requests.add(schemaPatchRegisterRequest);
        return requests;
    }

    private List<UpdateSchemaPatchFile> initRegisterSchemaPatchFile() {
        List<UpdateSchemaPatchFile> registerSchemaPatchFiles = Lists.newArrayList();
        UpdateSchemaPatchFile registerSchemaPatchFile = new UpdateSchemaPatchFile();
        registerSchemaPatchFile.setUpdatePatchFilePath("");
        registerSchemaPatchFile.setUpdatePatchFileName(".*-configure-default.json");
        registerSchemaPatchFiles.add(registerSchemaPatchFile);
        return registerSchemaPatchFiles;
    }

    private Set<UpdateSchemaPatchFile> initUpdateSchemaPatchFile() {
        Set<UpdateSchemaPatchFile> schemaPatchFiles = Sets.newHashSet();
        UpdateSchemaPatchFile updateSchemaPatchFile = new UpdateSchemaPatchFile();
        updateSchemaPatchFile.setUpdatePatchFileName("deployer-server-i18n.xml");
        updateSchemaPatchFile.setUpdatePatchFilePath("deployer.conf/");
        UpdateSchemaPatchFile updateSchemaPatchFile2 = new UpdateSchemaPatchFile();
        updateSchemaPatchFile2.setUpdatePatchFileName("dap-deployer-configure-default.json");
        updateSchemaPatchFile2.setUpdatePatchFilePath("deployer.scripts/configure/install/");
        schemaPatchFiles.add(updateSchemaPatchFile);
        schemaPatchFiles.add(updateSchemaPatchFile2);
        return schemaPatchFiles;
    }
}