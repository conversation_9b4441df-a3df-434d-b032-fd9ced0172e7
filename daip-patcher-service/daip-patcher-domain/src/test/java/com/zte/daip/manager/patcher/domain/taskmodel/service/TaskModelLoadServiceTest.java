package com.zte.daip.manager.patcher.domain.taskmodel.service;

import com.zte.daip.manager.common.utils.base.BaseResourceService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;

import static org.mockito.Mockito.when;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: TaskModelLoadServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/10/24</p>
 * <p>完成日期：2023/10/24</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = { TaskModelLoadService.class })
public class TaskModelLoadServiceTest
{
    @Autowired
    private TaskModelLoadService taskModelLoadService;

    @MockBean
    private BaseResourceService baseResourceService;

    @MockBean
    private TaskModelRetryService taskModelRetryService;

    @Test
    public void loadTaskModel()
    {
        mockConfCFileDir();

        taskModelLoadService.loadTaskModel();
    }

    private void mockConfCFileDir()
    {
        String path = this.getClass().getClassLoader().getResource("taskmodel/").getPath();
        File file = new File(path);
        when(baseResourceService.getConfDir()).thenReturn(file);
    }
}