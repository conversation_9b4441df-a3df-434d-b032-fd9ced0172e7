package com.zte.daip.manager.patcher.domain.upload.service.listener;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatedResultCache;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.event.PatchValidatedFinishEvent;
import com.zte.daip.manager.patcher.domain.upload.event.UploadPatchEvent;
import com.zte.daip.manager.patcher.domain.upload.operate.NormalPatchOperator;
import com.zte.daip.manager.patcher.domain.upload.operate.PatchTypeOperateFactory;
import com.zte.daip.manager.patcher.domain.upload.service.PatchDetailDomService;
import com.zte.daip.manager.patcher.domain.upload.service.PatchLocalUploadService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class PatchValidatedFinishListenerTest {

    @Mock
    private PatchValidatedResultCache patchValidatedResultCache;
    @Mock
    private PatchLocalUploadService patchLocalUploadService;
    @Mock
    private PatchDetailDomService patchDetailDomService;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchTypeOperateFactory patchTypeOperateFactory;
    @Mock
    private NormalPatchOperator normalPatchOperator;
    @InjectMocks
    private PatchValidatedFinishListener patchValidatedFinishListener;

    @Test
    public void test() {
        ReflectionTestUtils.setField(patchValidatedFinishListener, "ignoreList", "VMAX");
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName("DAP-SPARK-V20.19.40.R4.B2-SP030-20201215");
        patchBean.setFullpatch(true);
        patchBean.setService("spark");
        patchBean.setSrcVersion("V20.19.40.R4.B2");
        patchUploadResult.setPatchBean(patchBean);
        when(patchValidatedResultCache.querySuccessResultByBatchId(anyString()))
            .thenReturn(Lists.newArrayList(patchUploadResult));
        when(patchLocalUploadService.uploadToLocalRepository(any())).thenReturn(true);
        when(patchTypeOperateFactory.getPatchType(any())).thenReturn(normalPatchOperator);
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("DAP-SPARK-V20.19.40.R4.B2-SP030-20201215");
        when(patchInfoService.findByServiceAndBaseVersion(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchDetailPo));
        doNothing().when(patchDetailDomService).saveValidPatches(any());
        doNothing().when(patchValidatedResultCache).updateAllFinish(any());
        doNothing().when(patchValidatedResultCache).updateAllFinish(any());
        doNothing().when(applicationContext).publishEvent(any());

        patchValidatedFinishListener.onApplicationEvent(new PatchValidatedFinishEvent("qqqq"));

    }

    @Test
    public void test_error() {
        ReflectionTestUtils.setField(patchValidatedFinishListener, "ignoreList", "VMAX");
        when(patchValidatedResultCache.querySuccessResultByBatchId(anyString())).thenReturn(Lists.newArrayList());
        when(patchLocalUploadService.uploadToLocalRepository(any())).thenReturn(true);
        when(patchTypeOperateFactory.getPatchType(any())).thenReturn(normalPatchOperator);
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("DAP-SPARK-V20.19.40.R4.B2-SP030-20201215");
        when(patchInfoService.findByServiceAndBaseVersion(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchDetailPo));
        doNothing().when(patchDetailDomService).saveValidPatches(any());
        doNothing().when(patchValidatedResultCache).updateAllFinish(any());
        doNothing().when(patchValidatedResultCache).updateAllFinish(any());
        doNothing().when(applicationContext).publishEvent(any());

        patchValidatedFinishListener.onApplicationEvent(new PatchValidatedFinishEvent("qqqq"));

    }

}