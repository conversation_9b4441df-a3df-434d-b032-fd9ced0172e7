package com.zte.daip.manager.patcher.domain.rollback;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchRollBackServiceDto;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackParam;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchRollbackService;
import com.zte.daip.manager.patcher.domain.rollback.executor.BigDataTypeRollbackExecutor;
import com.zte.daip.manager.patcher.domain.rollback.executor.ServiceTypeRollbackExecutor;
import com.zte.daip.manager.patcher.domain.rollback.progress.RollbackProgressService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRollbackKey;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRollbackPo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchRollbackOperateServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/23</p>
 * <p>完成日期：2023/3/23</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchRollbackOperateServiceTest {

    @InjectMocks
    private PatchRollbackOperateService patchRollbackOperateService;

    @Mock
    private HostResourceInfoCache hostResourceInfoCache;
    @Mock
    private ServiceModelInfoCache serviceModelInfoCache;
    @Mock
    private PatchRollbackService patchRollbackService;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private RollbackProgressService rollbackProgressService;
    @Mock
    private ServiceTypeRollbackExecutor serviceTypeRollbackExecutor;

    @Mock
    private BigDataTypeRollbackExecutor bigDataTypeRollbackExecutor;

    @Before
    public void setUp() throws Exception {
        HostInfo patchHostInfo = new HostInfo();
        patchHostInfo.setIpAddress("127.0.0.1");
        patchHostInfo.setHostName("host1");
        HostInfo patchHostInfo2 = new HostInfo();
        patchHostInfo2.setIpAddress("*********");
        patchHostInfo2.setHostName("host2");
        Mockito.when(hostResourceInfoCache.queryHostInfoByIps(anySet()))
            .thenReturn(Lists.newArrayList(patchHostInfo, patchHostInfo2));
        Mockito.when(hostResourceInfoCache.queryIpAddressByClusterId(anyString()))
            .thenReturn(Lists.newArrayList("127.0.0.1", "*********"));
        Mockito.when(serviceModelInfoCache.queryBigDataService()).thenReturn(Lists.newArrayList("zookeeper", "hdfs"));
        Mockito.when(patchRollbackService.queryByIps(anyList())).thenReturn(getPatchRollbackPos());
        Mockito.when(serviceModelInfoCache.queryServicePatchType(anyString(), anyString())).thenReturn("service");
        Mockito.when(patchRollbackService.queryIpsByServiceAndInstanceAndRole(anyString(), anyString(), anyString(),
            anyString())).thenReturn(getPatchRollbackPos());
        doNothing().when(rollbackProgressService).initRollbackProgress(anyString(), anyList());
        Mockito.when(applicationContext.getBean(any(), (Class<Object>)any())).thenReturn(serviceTypeRollbackExecutor);
    }

    private List<PatchRollbackPo> getPatchRollbackPos() {
        List<PatchRollbackPo> canRollbackPatchByCluster = Lists.newArrayList();
        PatchRollbackKey patchRollbackKey = new PatchRollbackKey("SP001", "", "zookeeper", "", "127.0.0.1");
        PatchRollbackKey patchRollbackKey2 = new PatchRollbackKey("SP001", "", "saturn", "", "*********");
        PatchRollbackPo patchRollbackPo = new PatchRollbackPo();
        patchRollbackPo.setId(patchRollbackKey);
        patchRollbackPo.setPatchUptime(1);
        PatchRollbackPo patchRollbackPo2 = new PatchRollbackPo();
        patchRollbackPo2.setId(patchRollbackKey2);
        patchRollbackPo2.setPatchUptime(1);
        canRollbackPatchByCluster.add(patchRollbackPo);
        canRollbackPatchByCluster.add(patchRollbackPo2);
        return canRollbackPatchByCluster;
    }

    @Test
    public void queryRollBackServices() throws Exception {
        List<PatchRollBackServiceDto> patchRollBackServiceDtos = patchRollbackOperateService.queryRollBackServices("1");
        assertEquals(2, patchRollBackServiceDtos.size());
    }

    @Test
    public void queryRollBackHosts() throws Exception {
        List<PatchRollbackParam> patchRollbackParams = Lists.newArrayList();
        PatchRollbackParam patchRollbackParam = new PatchRollbackParam("zookeeper", "", "", "SP001");
        PatchRollbackParam patchRollbackParam2 = new PatchRollbackParam("saturn", "", "", "SP001");
        List<PatchRollbackHostDto> patchRollbackHostDtos = patchRollbackOperateService.queryRollBackHosts("001",
            Lists.newArrayList(patchRollbackParam, patchRollbackParam2));
        assertEquals(2, patchRollbackHostDtos.size());
    }

    @Test
    public void rollbackNoBigDataPatches() {
        PatchRollbackHostDto patchRollbackHostDto = new PatchRollbackHostDto();
        patchRollbackOperateService.rollbackPatches("1", Lists.newArrayList(patchRollbackHostDto));
    }

    @Test
    public void rollbackBigDataPatches() throws DaipBaseException {
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceName("zookeeper");
        serviceModel.setPatchType("");
        Mockito.when(serviceModelInfoCache.queryByClusterIdAndServiceName(anyString(), anyString()))
            .thenReturn(serviceModel);
        PatchRollbackHostDto patchRollbackHostDto = new PatchRollbackHostDto();
        patchRollbackHostDto.setServiceName("zookeeper");
        patchRollbackOperateService.rollbackPatches("1", Lists.newArrayList(patchRollbackHostDto));
    }
}