package com.zte.daip.manager.patcher.domain.dispatch;

import com.google.common.collect.Maps;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;
import com.zte.daip.manager.filemanagement.api.FileDistributeQueryControllerApi;
import com.zte.daip.manager.filemanagement.api.bean.DistributeSuccessResult;
import com.zte.daip.manager.filemanagement.api.bean.DistributeSummaryResult;
import com.zte.daip.manager.filemanagement.api.bean.FileDistributeResult;
import com.zte.daip.manager.filemanagement.api.bean.HostDistributeResult;
import com.zte.daip.manager.filemanagement.api.progress.DistributeSummaryProgress;
import com.zte.daip.manager.filemanagement.common.util.exception.FileDistributeException;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchDispatchService;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: DistributeProgressServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/11</p>
 * <p>完成日期：2023/3/11</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class DistributeProgressServiceTest {

    @Mock
    private FileDistributeControllerApi fileDistributeControllerApi;
    @Mock
    private FileDistributeQueryControllerApi fileDistributeQueryControllerApi;

    @Mock
    private HostResourceInfoCache hostResourceInfoCache;

    @Mock
    private PatchDispatchService patchDispatchService;

    @Mock
    private DaipEventReporter daipEventReporter;

    @InjectMocks
    private DistributeProgressService distributeProgressService;

    @Before
    public void setUp() throws FileDistributeException {
        ReflectionTestUtils.setField(distributeProgressService, "distributeTimeout", 1);
        DistributeSummaryProgress distributeSummaryProgress = new DistributeSummaryProgress();
        distributeSummaryProgress.setFinish(true);
        Mockito.when(fileDistributeQueryControllerApi.querySummaryProgress(anyString()))
            .thenReturn(distributeSummaryProgress);
        Mockito.when(fileDistributeQueryControllerApi.querySuccessResult(anyString()))
            .thenReturn(getDistributeSuccessResult());
        Mockito.when(fileDistributeQueryControllerApi.queryFailedDetailResult(anyString()))
            .thenReturn(getDistributeFailedResult());
        Mockito.when(hostResourceInfoCache.queryIpAddress(anyString())).thenReturn("127.0.0.1");
        doNothing().when(patchDispatchService).batchSave(anyList());
        doNothing().when(daipEventReporter).error(anyString(), anyString());
    }

    @Test
    public void test_update_distribute() throws Exception {
        boolean result = true;
        try {
            distributeProgressService.checkAndUpdateDistributeProgress("1111");

        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }

    private DistributeSuccessResult getDistributeSuccessResult() {
        DistributeSuccessResult distributeSuccessResult = new DistributeSuccessResult();
        Map<String, List<String>> successHost = Maps.newHashMap();
        successHost.put("/data1/a.zip", Lists.newArrayList("host1", "host2"));
        distributeSuccessResult.setSuccessHost(successHost);
        return distributeSuccessResult;
    }

    private List<FileDistributeResult> getDistributeFailedResult() {
        FileDistributeResult fileDistributeResult = new FileDistributeResult();
        fileDistributeResult.setDistributeId("aaa");
        fileDistributeResult.setFile("/data1/b.zip");
        HostDistributeResult hostDistributeResult = new HostDistributeResult();
        hostDistributeResult.setFinish(true);
        hostDistributeResult.setSuccess(false);
        hostDistributeResult.setHost("001");
        hostDistributeResult.setMessages("error");
        fileDistributeResult.setHostDistributeResults(Lists.newArrayList(hostDistributeResult));
        return Lists.newArrayList(fileDistributeResult);
    }

}