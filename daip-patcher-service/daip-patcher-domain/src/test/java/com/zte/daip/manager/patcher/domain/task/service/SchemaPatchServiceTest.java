package com.zte.daip.manager.patcher.domain.task.service;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.domain.task.assembler.CurrentPatchAssembler;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: SchemaPatchServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/12/15</p>
 * <p>完成日期：2023/12/15</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class SchemaPatchServiceTest {

    @Mock
    private CurrentPatchAssembler currentPatchAssembler;

    @InjectMocks
    private SchemaPatchService schemaPatchService;

    private PatchTaskDto patchTaskDto = new PatchTaskDto();

    private ServiceInstance serviceInstance = new ServiceInstance();

    @Before
    public void init() throws DaipBaseException {
        String serviceName = "hdfs";
        serviceInstance.setServiceName(serviceName);
        serviceInstance.setVersion("V20.23.40.08");
    }

    @Test
    public void organizeServiceInstancePatchInfo() throws DaipBaseException {
        List<ServiceInstancePatchInfo> serviceInstancePatchInfos =
            schemaPatchService.organizeServiceInstancePatchInfo(null);
        Assert.assertEquals(serviceInstancePatchInfos.size(), 0);

        mockEmptyContext();
        serviceInstancePatchInfos = schemaPatchService.organizeServiceInstancePatchInfo(patchTaskDto);
        Assert.assertEquals(serviceInstancePatchInfos.size(), 0);

        mockEmptyServiceInstancePatchInfo();
        serviceInstancePatchInfos = schemaPatchService.organizeServiceInstancePatchInfo(patchTaskDto);
        Assert.assertEquals(serviceInstancePatchInfos.size(), 0);

        mockNotEmptyServiceInstancePatchInfo();
        serviceInstancePatchInfos = schemaPatchService.organizeServiceInstancePatchInfo(patchTaskDto);
        Assert.assertEquals(serviceInstancePatchInfos.size(), 0);

        mockNotEmptyRollbackPoints();
        serviceInstancePatchInfos = schemaPatchService.organizeServiceInstancePatchInfo(patchTaskDto);
        Assert.assertEquals(serviceInstancePatchInfos.size(), 1);
    }

    private void mockEmptyContext() {
        patchTaskDto.setContext(Lists.newArrayList());
    }

    private void mockEmptyServiceInstancePatchInfo() {
        List<ServiceInstancePatchInfo> context = Lists.newArrayList(new ServiceInstancePatchInfo());
        patchTaskDto.setContext(context);
    }

    private void mockNotEmptyServiceInstancePatchInfo() {
        ServiceInstancePatchInfo patchInfo = new ServiceInstancePatchInfo();
        patchInfo.setServiceInstance(serviceInstance);
        List<ServiceInstancePatchInfo> context = Lists.newArrayList(patchInfo);
        patchTaskDto.setContext(context);
    }

    private void mockNotEmptyRollbackPoints() {
        ServiceInstancePatchInfo patchInfo = new ServiceInstancePatchInfo();
        patchInfo.setServiceInstance(serviceInstance);
        RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
        rollBackPatchPointInfo.setRollBackPatchPoint("hdfs-V20.23.40.08-schema-CP01");
        patchInfo.setRollBackPatchPoints(Lists.newArrayList(rollBackPatchPointInfo));
        List<ServiceInstancePatchInfo> context = Lists.newArrayList(patchInfo);
        patchTaskDto.setContext(context);
    }
}