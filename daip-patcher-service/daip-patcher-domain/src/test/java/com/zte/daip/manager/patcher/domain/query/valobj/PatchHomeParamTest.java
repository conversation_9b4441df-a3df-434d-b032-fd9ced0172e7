package com.zte.daip.manager.patcher.domain.query.valobj;

import org.junit.Test;

import static org.junit.Assert.*;

public class PatchHomeParamTest {
    @Test
    public void equals() {
        PatchHomeParam patchHomeParam = new PatchHomeParam();

        PatchHomeParam patchHomeParam1 = new PatchHomeParam();

        assertTrue(patchHomeParam.equals(patchHomeParam));

        assertTrue(!patchHomeParam.equals(null));

        assertFalse(patchHomeParam.equals(patchHomeParam1));
    }

    @Test
    public void testToString() {
        PatchHomeParam patchHomeParam = new PatchHomeParam("100001", "zookeeper", "zookeeper_01", "HQ");

        assertNotNull(patchHomeParam.toString());
        PatchHomeParam patchHomeParam1 = new PatchHomeParam();
        patchHomeParam1.setClusterId("100001");
        patchHomeParam1.setServiceName("zookeeper");
        patchHomeParam1.setServiceInstanceId("zk_1");
        patchHomeParam1.setRoleName("HQ");
        assertNotNull(patchHomeParam1.toString());
    }
}