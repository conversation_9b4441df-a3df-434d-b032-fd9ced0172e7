package com.zte.daip.manager.patcher.domain.cache;

import com.zte.daip.manager.patcher.domain.common.PatchesQueryThreadPool;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;
import java.util.concurrent.Executor;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: ResourceLocalCacheConfigTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2022/1/6</p>
 * <p>完成日期：2022/1/6</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith (SpringRunner.class)
public class ResourceLocalCacheConfigTest
{
    @Test
    public void test_resource_local_cache_config()
    {
        ResourceLocalCacheConfig resourceLocalCacheConfig = new ResourceLocalCacheConfig();
        CacheManager cacheManager = resourceLocalCacheConfig.caffeineCacheManager();

        assertEquals(true, cacheManager != null);
        Collection<String> cacheNames = cacheManager.getCacheNames();
        assertEquals(true, cacheNames.contains(ResourceLocalCacheConfig.HOST_RESOURCE_INFO));
    }
}