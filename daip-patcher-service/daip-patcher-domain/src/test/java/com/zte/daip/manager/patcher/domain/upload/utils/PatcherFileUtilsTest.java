package com.zte.daip.manager.patcher.domain.upload.utils;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static org.hamcrest.core.Is.is;
import static org.junit.Assert.*;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatcherFileUtilsTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/1</p>
 * <p>完成日期：2021/4/1</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatcherFileUtilsTest {

    private String applicationPath =
        Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();

    @Test
    public void copyFileToTempDir() throws Exception {
        File patchDir = FilePathCleaner.newFile(new File(applicationPath).getParent() + File.separator + "patch");
        List<File> patchFiles = Lists.newArrayList(patchDir.listFiles());
        MultipartFile[] patchMultipartFiles = getMultipartFiles(patchFiles);

        String patchUploadTemp = new File(applicationPath).getParent() + File.separator + "patchtemp";
        PatcherFileUtils.copyFileToTempDir(patchMultipartFiles, patchUploadTemp);
        File patchTemp = FilePathCleaner.newFile(patchUploadTemp);
        List<File> patchTempFiles = Lists.newArrayList(patchTemp.listFiles());

        boolean result = patchTempFiles.size() == patchFiles.size();
        assertThat(result, is(true));

    }

    private MultipartFile[] getMultipartFiles(List<File> patchFiles) throws IOException {
        MockMultipartFile file = new MockMultipartFile(patchFiles.get(0).getName(), patchFiles.get(0).getName(),
            "application/zip", new FileInputStream(patchFiles.get(0)));
        MockMultipartFile file2 = new MockMultipartFile(patchFiles.get(0).getName(), patchFiles.get(1).getName(),
            "application/zip", new FileInputStream(patchFiles.get(1)));
        MockMultipartFile file3 = new MockMultipartFile(patchFiles.get(0).getName(), patchFiles.get(2).getName(),
            "application/zip", new FileInputStream(patchFiles.get(2)));
        return new MultipartFile[] {file, file2, file3};
    }

}