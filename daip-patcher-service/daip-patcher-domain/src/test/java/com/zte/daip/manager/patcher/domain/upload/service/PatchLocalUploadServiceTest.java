package com.zte.daip.manager.patcher.domain.upload.service;

import com.zte.daip.manager.common.deployer.bean.host.install.ResponseResult;
import com.zte.daip.manager.miniagent.seed.controller.LocalUploadController;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.upload.utils.PatchValidateCommonUtils;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchLoaclUploadDomServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/9/2</p>
 * <p>完成日期：2021/9/2</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
@Slf4j
public class PatchLocalUploadServiceTest {
    @Mock
    private PatchEnvApi patchEnvApi;
    @Mock
    private LocalUploadController localUploadController;

    private PatchValidateCommonUtils patchValidateCommonUtils = new PatchValidateCommonUtils();

    @InjectMocks
    private PatchLocalUploadService patchLocalUploadService;

    @Test
    public void uploadToLocalRepository() throws Exception {
        String applicationPath =
            PatchLocalUploadServiceTest.class.getClassLoader().getResource("application.yml").getPath();
        String path = new File(applicationPath).getParent() + File.separator + "patchhome";
        Mockito.when(patchEnvApi.getRepositoryHomeEnv()).thenReturn(path);
        Mockito.when(localUploadController.uploadVersionListToLocalRepository(any())).thenReturn(new ResponseResult());
        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP028-20200902");
        patchBean.setService("hdfs");
        List<PatchBean> patchBeans = Lists.newArrayList();
        patchBeans.add(patchBean);
        patchLocalUploadService.uploadToLocalRepository(patchBeans);
    }

}