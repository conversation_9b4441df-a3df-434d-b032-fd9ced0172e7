package com.zte.daip.manager.patcher.domain.upload.cache;

import static org.hamcrest.Matchers.hasSize;
import static org.junit.Assert.*;

/* Started by AICoder, pid:t4aefq6581xd1ab14fa40bca61b9f2171ef69159 */
import static org.hamcrest.CoreMatchers.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.util.*;
import java.util.concurrent.ConcurrentMap;

import com.google.common.cache.Cache;
import com.google.common.collect.Maps;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchValidateBean;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class PatchValidatedResultCacheTest {

    @Mock
    private ApplicationContext applicationContext;

    @InjectMocks
    private PatchValidatedResultCache patchValidatedResultCache;



    @Test
    public void updateNormalValidateResult() {
        doNothing().when(applicationContext).publishEvent(any());
        String batchId = "batch1";
        PatchValidateBean patchValidateKey = new PatchValidateBean();
        patchValidateKey.setBatchId(batchId);
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        patchUploadResult.setOneProcess(OnePatchProcess.NORMAL_CHECK);
        patchValidatedResultCache.updateValidateResult(patchValidateKey, patchUploadResult);
        patchValidatedResultCache.updateNormalValidateResult(patchValidateKey, patchUploadResult);
    }

    @Test
    public void should_UpdateValidateResult_When_ValidDataIsProvided() {
        PatchValidateBean patchValidateBean = new PatchValidateBean();
        PatchUploadResult patchUploadResult = new PatchUploadResult();

        patchValidatedResultCache.updateValidateResult(patchValidateBean, patchUploadResult);

    }

    @Test
    public void test() {
        PatchValidateBean patchValidateBean = new PatchValidateBean();
        patchValidateBean.setBatchId("a");
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        patchUploadResult.setSuccess(true);
        patchValidatedResultCache.updateValidateResult(patchValidateBean, patchUploadResult);
        Map<PatchValidateBean, PatchUploadResult> result = patchValidatedResultCache.queryResultMapByBatchId("a");
        assertNotNull(result);

    }

    @Test
    public void should_UpdateAllFinish_When_BatchIdIsProvided() {
        String batchId = "batch1";
        PatchValidateBean patchValidateKey = new PatchValidateBean();
        patchValidateKey.setBatchId(batchId);
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        patchUploadResult.setOneProcess(OnePatchProcess.FINISH);
        patchValidatedResultCache.updateValidateResult(patchValidateKey, patchUploadResult);
        patchValidatedResultCache.updateAllFinish(batchId);

    }

    @Test
    public void should_UpdateUploadFailMsg_When_BatchIdIsProvided() {
        String batchId = "batch2";
        PatchValidateBean patchValidateKey = new PatchValidateBean();
        patchValidateKey.setBatchId(batchId);
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        patchUploadResult.setSuccess(true);
        patchUploadResult.setOneProcess(OnePatchProcess.FINISH);
        patchUploadResult.setMessage("upload to local repository fail");
        patchValidatedResultCache.updateValidateResult(patchValidateKey, patchUploadResult);
        patchValidatedResultCache.updateUploadFailMsg(batchId);

    }

    @Test
    public void should_QueryAllResultByBatchId_When_BatchIdIsProvided() {
        String batchId = "batch3";
        PatchValidateBean patchValidateBean = new PatchValidateBean();
        patchValidateBean.setBatchId(batchId);
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        patchValidatedResultCache.updateValidateResult(patchValidateBean, patchUploadResult);
        List<PatchUploadResult> result = patchValidatedResultCache.queryAllResultByBatchId(batchId);

        assertThat(result, hasSize(1));
        assertThat(result.get(0), is(patchUploadResult));
    }

    @Test
    public void should_QuerySuccessResultByBatchId_When_BatchIdIsProvided() {
        String batchId = "batch5";
        PatchValidateBean patchValidateBean = new PatchValidateBean();
        patchValidateBean.setBatchId(batchId);
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        patchUploadResult.setSuccess(true);
        patchValidatedResultCache.updateValidateResult(patchValidateBean, patchUploadResult);
        List<PatchUploadResult> result = patchValidatedResultCache.querySuccessResultByBatchId(batchId);

        assertThat(result, hasSize(1));
        assertThat(result.get(0), is(patchUploadResult));
    }
}

/* Ended by AICoder, pid:t4aefq6581xd1ab14fa40bca61b9f2171ef69159 */