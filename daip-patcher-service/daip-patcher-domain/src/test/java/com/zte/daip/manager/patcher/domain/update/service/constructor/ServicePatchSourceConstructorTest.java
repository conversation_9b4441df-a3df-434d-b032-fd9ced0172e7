package com.zte.daip.manager.patcher.domain.update.service.constructor;

import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.ServiceInstance;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.domain.update.service.PatchTypeQueryService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class ServicePatchSourceConstructorTest {

    @Mock
    private PatchHomeQueryService patchHomeQueryService;
    @Mock
    private ServiceModelInfoCache serviceModelInfoCache;
    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchHistoryService patchHistoryService;
    @Mock
    private DeploymentInstanceServiceControllerApi instanceServiceControllerApi;
    @InjectMocks
    private ServicePatchSourceConstructor servicePatchSourceConstructor;

    @Before
    public void setUp() throws DaipBaseException {
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceId("serviceId");
        serviceModel.setPatchType("service");
        serviceModel.setComponentType("dap.manager.common.bigdata");
        when(serviceModelInfoCache.queryByClusterIdAndServiceName(anyString(), anyString())).thenReturn(serviceModel);
        DeploymentServiceInstance deploymentServiceInstance = new DeploymentServiceInstance();
        deploymentServiceInstance.setServiceInstanceId("serviceInstanceId");
        when(instanceServiceControllerApi.queryByClusterIdAndServiceId(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(deploymentServiceInstance));
        ConfigInstance configInstance = new ConfigInstance();
        configInstance.setConfigValue("value");
        when(patchHomeQueryService.queryPatchHome(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(configInstance));
    }

    @Test
    public void queryPatchHome() throws DaipBaseException {
        PatchHomeParam patchHomeParam = new PatchHomeParam();
        patchHomeParam.setClusterId("10000");
        patchHomeParam.setServiceName("serviceName");
        String result = servicePatchSourceConstructor.queryPatchHome(patchHomeParam);
        assertEquals(result, "value" + File.separator + "patch");
    }

    @Test
    public void obtainOfflinePatchUpdateInfo() throws DaipBaseException {
        PatchHistory patchHistory = new PatchHistory();
        PatchHistoryKey patchHistoryKey = new PatchHistoryKey();
        patchHistoryKey.setPatchName("SP001");
        patchHistory.setId(patchHistoryKey);
        when(patchHistoryService.queryPatchHistoryInfoByServiceNameAndIp(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchHistory));

        PatchServiceParam updateParam = new PatchServiceParam();
        updateParam.setVersion("version");
        updateParam.setServiceName("serviceName");
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceInstanceId("instanceId");
        serviceInstance.setRoles(Lists.newArrayList("roleId"));
        updateParam.setServiceInstances(Lists.newArrayList(serviceInstance));

        servicePatchSourceConstructor.obtainOfflinePatchUpdateInfo(updateParam, "01111", "10.000");

    }
}