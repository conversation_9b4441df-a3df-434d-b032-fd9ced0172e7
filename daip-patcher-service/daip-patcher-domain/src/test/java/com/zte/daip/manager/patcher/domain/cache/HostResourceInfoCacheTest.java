package com.zte.daip.manager.patcher.domain.cache;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: HostResourceInfoCacheTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2022/1/6</p>
 * <p>完成日期：2022/1/6</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class HostResourceInfoCacheTest {
    @Mock
    private HostResourceControllerApi hostResourceControllerApi;
    @InjectMocks
    private HostResourceInfoCache hostResourceInfoCache;

    @Before
    public void setUp() throws Exception {

        List<HostInfo> hostInfos = Lists.newArrayList();
        HostInfo hostInfo = new HostInfo();
        hostInfo.setHostName("host-10-228-66-37");
        hostInfo.setClusterId("100000");
        hostInfo.setIpAddress("************");
        hostInfos.add(hostInfo);
        when(hostResourceControllerApi.queryAll()).thenReturn(hostInfos);
        when(hostResourceControllerApi.queryByClusterId(anyString())).thenReturn(hostInfos);
    }

    @Test
    public void queryIpAddress() {
        String ipAddress = hostResourceInfoCache.queryIpAddress("host-10-228-66-37");
        Assert.assertEquals("************", ipAddress);
    }

    @Test
    public void queryHostNames() {
        List<String> hostNames = hostResourceInfoCache.queryHostNames(Lists.newArrayList("************"));
        Assert.assertEquals("host-10-228-66-37", hostNames.get(0));
    }

    @Test
    public void queryHostInfoByIps() {
        List<HostInfo> hostInfoList = hostResourceInfoCache.queryHostInfoByIps(Sets.newHashSet("************"));
        Assert.assertEquals("host-10-228-66-37", hostInfoList.get(0).getHostName());
    }

    @Test
    public void queryIpAddressByClusterId() {
        List<String> ips = hostResourceInfoCache.queryIpAddressByClusterId("10000");
        Assert.assertEquals("************", ips.get(0));
    }

    @Test
    public void test_query_host() {
        Mockito.when(hostResourceControllerApi.queryByClusterId("0")).thenReturn(Lists.newArrayList());

        List<HostInfo> hostInfos = hostResourceInfoCache.queryHostByClusterId("0");
        assertEquals(0, hostInfos.size());
    }

    @Test(expected = NullPointerException.class)
    public void test() {
        HostResourceInfoCache hostResourceInfoCache = new HostResourceInfoCache();
        hostResourceInfoCache.queryHostByClusterId("0");
    }

}