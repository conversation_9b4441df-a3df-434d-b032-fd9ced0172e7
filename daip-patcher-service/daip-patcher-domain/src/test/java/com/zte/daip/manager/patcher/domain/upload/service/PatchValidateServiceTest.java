package com.zte.daip.manager.patcher.domain.upload.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatedResultCache;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchValidateBean;
import com.zte.daip.manager.patcher.domain.upload.service.validator.LoadPatchValidators;
import com.zte.daip.manager.patcher.domain.upload.service.validator.PatchTypeValidator;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.UnzipFileApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class PatchValidateServiceTest {

    @Mock
    private LoadPatchValidators loadPatchValidators;
    @Mock
    private UnzipFileApi unzipFileApi;
    @Mock
    private PatchEnvApi patchEnvApi;
    @Mock
    private PatchValidatedResultCache patchValidatedResultCache;

    @Mock
    private PatchTypeValidator patchTypeValidator;

    @InjectMocks
    private PatchValidateService patchValidateService;

    @Test
    public void validatePatch() throws IOException {
        List<PatchDetailPo> patchDetailPos = Lists.newArrayList();
        when(loadPatchValidators.loadValidators()).thenReturn(Lists.newArrayList(patchTypeValidator));
        when(patchEnvApi.getRepositoryHomeEnv()).thenReturn(getPatchUploadHomeEnv());
        doNothing().when(patchValidatedResultCache).updateNormalValidateResult(any(), any());
        doNothing().when(unzipFileApi).unzipFile(any(), anyString(), anyString());
        PatchValidateBean patchValidateBean = new PatchValidateBean("aaa", new File("test.zip"));
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        patchUploadResult.setSuccess(false);
        when(patchTypeValidator.checkPatch(any(), any(), anyList())).thenReturn(patchUploadResult);
        patchValidateService.validatePatch(patchValidateBean, patchDetailPos);
    }

    @Test
    public void validatePatch_true() throws IOException {
        List<PatchDetailPo> patchDetailPos = Lists.newArrayList();
        when(loadPatchValidators.loadValidators()).thenReturn(Lists.newArrayList(patchTypeValidator));
        when(patchEnvApi.getRepositoryHomeEnv()).thenReturn(getPatchUploadHomeEnv());
        doNothing().when(patchValidatedResultCache).updateNormalValidateResult(any(), any());
        doNothing().when(unzipFileApi).unzipFile(any(), anyString(), anyString());
        PatchValidateBean patchValidateBean = new PatchValidateBean("aaa", new File("test.zip"));
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        patchUploadResult.setSuccess(true);
        when(patchTypeValidator.checkPatch(any(), any(), anyList())).thenReturn(patchUploadResult);
        patchValidateService.validatePatch(patchValidateBean, patchDetailPos);
    }

    private String getPatchUploadHomeEnv() {
        String applicationPath =
            Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();
        return new File(applicationPath).getParent() + File.separator + "patchxml";
    }
}