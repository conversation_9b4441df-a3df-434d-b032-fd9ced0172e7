package com.zte.daip.manager.patcher.domain.upload.service.validator;

import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertThat;

import java.io.File;
import java.util.List;

import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchRepeatValidatorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/3/30</p>
 * <p>完成日期：2021/3/30</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchRepeatValidatorTest {
    @Mock
    private PatchInfoService patchInfoService;

    @InjectMocks
    private PatchRepeatValidator patchRepeatValidator;

    @Test
    public void 校验上传重复补丁并返回失败() {

        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName("DAP-spark-V20.19.40.R4.B2-schema-SP026-20200727");
        File patchFile = new File("DAP-spark-V20.19.40.R4.B2-schema-SP026-20200727.zip");
        Mockito.when(patchInfoService.queryAllPatchInfos()).thenReturn(generateDbDetail());

        PatchUploadResult patchUploadResult = patchRepeatValidator.checkPatch(patchBean, patchFile, generateDbDetail());
        assertThat(patchUploadResult.isSuccess(), is(false));
    }

    @Test
    public void 校验上传新补丁并返回成功() {

        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName("DAP-spark-V20.19.40.R4.B2-schema-SP030-20200727");
        Mockito.when(patchInfoService.queryAllPatchInfos()).thenReturn(generateDbDetail());
        File patchFile = new File("DAP-spark-V20.19.40.R4.B2-schema-SP030-20200727.zip");
        PatchUploadResult patchUploadResult = patchRepeatValidator.checkPatch(patchBean, patchFile, generateDbDetail());
        assertThat(patchUploadResult.isSuccess(), is(true));
    }

    private List<PatchDetailPo> generateDbDetail() {
        List<PatchDetailPo> patchDetails = Lists.newArrayList();
        PatchDetailPo patchDetail1 = new PatchDetailPo();
        patchDetail1.setPatchName("DAP-spark-V20.19.40.R4.B2-schema-SP026-20200727");
        patchDetails.add(patchDetail1);

        PatchDetailPo patchDetail2 = new PatchDetailPo();
        patchDetail2.setPatchName("DAP-spark-V20.19.40.R4.B2-schema-SP023-20200727");
        patchDetails.add(patchDetail2);
        return patchDetails;
    }

}