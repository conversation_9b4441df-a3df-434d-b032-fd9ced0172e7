package com.zte.daip.manager.patcher.domain.upload.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.cache.lock.LockUtils;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatedResultCache;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatingQueue;
import com.zte.daip.manager.patcher.domain.upload.event.PatchValidateEvent;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class PatchUploadServiceTest {

    /* Started by AICoder, pid:ufabfzdf256748e149120a44b01ae6507ac3a6f5 */
    @Mock
    private DaipEventReporter daipEventReporter;
    @Mock
    private PatchEnvApi patchEnvApi;
    @Mock
    private PatchValidatingQueue patchValidatingQueue;
    @Mock
    private PatchValidatedResultCache patchValidatedResultCache;
    @Mock
    private PatchSetsUnzipService patchSetsUnzipService;
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private LockUtils lockUtils;

    @InjectMocks
    private PatchUploadService patchUploadService;

@Test
public void testUploadPatches() {
    when(patchEnvApi.getRepositoryHomeEnv()).thenReturn(getPatchUploadHomeEnv());
    String file = getFileAbsolutePath("upload","DAP-configcenter-V20.19.40.R4.B2-patches-SP013-20200227", "DAP-configcenter-V20.19.40.R4.B2-patches-SP013-20200227.zip");

    when(patchSetsUnzipService.unzipPatchSets(any())).thenReturn(Lists.newArrayList(new File(file)));
    doNothing().when(daipEventReporter).info(anyString(), anyString());
    doNothing().when(patchValidatingQueue).addValidatePatches(anyList());
    doNothing().when(patchValidatedResultCache).initValidateResult(anyList());
    doNothing().when(applicationContext).publishEvent(new PatchValidateEvent(anyString()));

    List<String> patchFiles = Arrays.asList(
        "DAP-configcenter-V20.19.40.R4.B2-patches-SP013-20200227.zip",
        "DAP-HDFS-V20.19.40.R4.B2-SP028-20200902.zip",
        "DAP-ZOOKEEPER-V20.19.40.R4.B2-SP030-20201215.zip"
    );
    patchUploadService.afterIOCBean();
    String batchId = patchUploadService.uploadPatches(patchFiles);
    assertTrue(StringUtils.isNotBlank(batchId));
}

    private String getPatchUploadHomeEnv() {
        String applicationPath =
            Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();
        return new File(applicationPath).getParent();
    }

    private String getFileAbsolutePath(String... paths) {
        StringBuilder sb = new StringBuilder(getPatchUploadHomeEnv());
        for (String path : paths) {
            sb.append(File.separator).append(path);
        }
        return sb.toString();
    }

    /* Ended by AICoder, pid:ufabfzdf256748e149120a44b01ae6507ac3a6f5 */

}