package com.zte.daip.manager.patcher.domain.update.service;

import java.io.File;
import java.util.List;

import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.manager.common.configcenter.api.ServiceConfigControllerApi;
import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.configcenter.bean.ConfigRequest;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchHomeQueryServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/8</p>
 * <p>完成日期：2021/4/8</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchHomeQueryServiceTest {
    @Mock
    private ServiceConfigControllerApi serviceConfigControllerApi;

    @InjectMocks
    private PatchHomeQueryService patchHomeQueryService;

    @Test
    public void queryPatchHome() {

        String clusterId = "0";

        String patchHome = patchHomeQueryService.queryDefaultZdhPatchHome();
        Assert.assertEquals(true, patchHome.endsWith(File.separator + "patch"));

        Mockito.when(serviceConfigControllerApi.queryByCondition(Mockito.any(ConfigRequest.class)))
            .thenReturn(mockServiceModel());

        List<ConfigInstance> configInstance = patchHomeQueryService.queryPatchHome(clusterId, "saturn");

        Assert.assertEquals("/home/<USER>", configInstance.get(0).getConfigValue());

    }

    private List<ConfigInstance> mockServiceModel() {
        ConfigInstance configInstance = new ConfigInstance();
        configInstance.setConfigId("saturn1");
        configInstance.setConfigValue("/home/<USER>");
        configInstance.setServiceInstanceId("saturn");
        return Lists.newArrayList(configInstance);
    }

}