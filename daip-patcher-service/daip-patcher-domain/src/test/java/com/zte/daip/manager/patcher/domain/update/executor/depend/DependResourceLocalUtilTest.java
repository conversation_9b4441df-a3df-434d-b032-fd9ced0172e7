package com.zte.daip.manager.patcher.domain.update.executor.depend;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: DependResourceLocalUtilTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2022/1/7</p>
 * <p>完成日期：2022/1/7</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class DependResourceLocalUtilTest {
    @Mock
    private HostResourceControllerApi hostResourceControllerApi;
    @Mock
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @InjectMocks
    private DependResourceLocalUtil dependResourceLocalUtil;

    @Before
    public void set() {
        HostInfo hostInfo0 = new HostInfo();
        hostInfo0.setClusterId("0");
        hostInfo0.setHostName("zookeeper-0-101");
        Mockito.when(hostResourceControllerApi.queryByClusterId("0")).thenReturn(Lists.newArrayList(hostInfo0));

        HostInfo hostInfo1 = new HostInfo();
        hostInfo1.setClusterId("1");
        hostInfo1.setHostName("hdfs-1-101");
        Mockito.when(hostResourceControllerApi.queryByClusterId("1")).thenReturn(Lists.newArrayList(hostInfo1));

        ServiceRoleInfo serviceRoleInfo0 = new ServiceRoleInfo();
        serviceRoleInfo0.setClusterId("0");
        serviceRoleInfo0.setServiceInstanceId("zookeeper");
        Mockito.when(serviceResourceControllerApi.queryByClusterId("0"))
            .thenReturn(Lists.newArrayList(serviceRoleInfo0));

        ServiceRoleInfo serviceRoleInfo1 = new ServiceRoleInfo();
        serviceRoleInfo1.setClusterId("1");
        serviceRoleInfo1.setServiceInstanceId("hdfs");
        Mockito.when(serviceResourceControllerApi.queryByClusterId("1"))
            .thenReturn(Lists.newArrayList(serviceRoleInfo1));
    }

    @Test
    public void test_should_return_setting_value() {
        dependResourceLocalUtil.initDependResource("0");
        List<ServiceRoleInfo> clusterServiceRoleInfo = dependResourceLocalUtil.getClusterServiceRoleInfo("0");
        assertEquals(1, clusterServiceRoleInfo.size());
        assertEquals("0", clusterServiceRoleInfo.get(0).getClusterId());
        assertEquals("zookeeper", clusterServiceRoleInfo.get(0).getServiceInstanceId());
    }

    @Test
    public void test_should_return_right_value_when_diff_thread() throws InterruptedException {
        dependResourceLocalUtil.initDependResource("0");
        List<ServiceRoleInfo> clusterServiceRoleInfo = dependResourceLocalUtil.getClusterServiceRoleInfo("0");
        dependResourceLocalUtil.getClusterHostInfo("0");
        assertEquals(1, clusterServiceRoleInfo.size());
        assertEquals("0", clusterServiceRoleInfo.get(0).getClusterId());
        assertEquals("zookeeper", clusterServiceRoleInfo.get(0).getServiceInstanceId());

        Thread thread = new Thread(() -> {
            dependResourceLocalUtil.initDependResource("1");
            List<ServiceRoleInfo> clusterServiceRoleInfo1 = dependResourceLocalUtil.getClusterServiceRoleInfo("1");
            assertEquals("hdfs", clusterServiceRoleInfo1.get(0).getServiceInstanceId());

        });
        thread.start();

        TimeUnit.SECONDS.sleep(1);
    }

    @Test
    public void queryServiceRoleInfoByServiceInstanceId()
    {
        dependResourceLocalUtil.initDependResource("0");
        List<ServiceRoleInfo> clusterServiceRoleInfo = dependResourceLocalUtil.queryServiceRoleInfoByServiceInstanceId("0","zookeeper");
        assertEquals(1, clusterServiceRoleInfo.size());
        assertEquals("0", clusterServiceRoleInfo.get(0).getClusterId());
        assertEquals("zookeeper", clusterServiceRoleInfo.get(0).getServiceInstanceId());
    }
}