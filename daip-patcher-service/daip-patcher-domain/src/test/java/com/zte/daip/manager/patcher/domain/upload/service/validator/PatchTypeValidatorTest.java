package com.zte.daip.manager.patcher.domain.upload.service.validator;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.*;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchTypeValidatorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/3/12</p>
 * <p>完成日期：2021/3/12</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class PatchTypeValidatorTest {
    private PatchTypeValidator patchTypeValidator = new PatchTypeValidator();

    @Test
    public void 校验平台补丁并返回失败() {
        ReflectionTestUtils.setField(patchTypeValidator, "blackList",
            "uep,Nginx,DAP-MANAGER,DAP-ZDH-MANAGER,dapmanager-");
        File patchFile = new File("uepi-pro-usf5.19.20.B6-015-20191031.zip");
        PatchUploadResult patchUploadResult =
            patchTypeValidator.checkPatch(new PatchBean(), patchFile, Lists.newArrayList());
        assertThat(patchUploadResult.isSuccess(), is(false));
    }

    @Test
    public void 校验manager补丁并返回失败() {
        ReflectionTestUtils.setField(patchTypeValidator, "blackList",
            "uep,Nginx,DAP-MANAGER,DAP-ZDH-MANAGER,dapmanager-");
        File patchFile = new File("DAP-ZDH-MANAGER-V20.19.40.R4.B2-patches-SP024-20200827.zip");
        PatchUploadResult patchUploadResult =
            patchTypeValidator.checkPatch(new PatchBean(), patchFile, Lists.newArrayList());
        assertThat(patchUploadResult.isSuccess(), is(false));
    }

    @Test
    public void 校验hdfs补丁并返回成功() {
        ReflectionTestUtils.setField(patchTypeValidator, "blackList",
            "uep,Nginx,DAP-MANAGER,DAP-ZDH-MANAGER,dapmanager-");
        File patchFile = new File("DAP-HDFS-V20.19.40.R4.B2-SP025-20200718.zip");
        PatchUploadResult patchUploadResult =
            patchTypeValidator.checkPatch(new PatchBean(), patchFile, Lists.newArrayList());
        assertThat(patchUploadResult.isSuccess(), is(true));
    }
}