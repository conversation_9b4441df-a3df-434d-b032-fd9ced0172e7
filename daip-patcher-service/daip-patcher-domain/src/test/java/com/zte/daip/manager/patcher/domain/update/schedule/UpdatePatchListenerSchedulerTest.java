package com.zte.daip.manager.patcher.domain.update.schedule;

import com.zte.daip.manager.common.cache.lock.LockUtils;
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.ClusterBean;
import com.zte.daip.manager.patcher.api.dto.OnePatchUpdateResult;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateResult;
import com.zte.daip.manager.patcher.api.update.response.OnePatchUpdateInfo;
import com.zte.daip.manager.patcher.api.update.response.PatchRecordAndRecoverInfos;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateCacheService;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateResultService;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: UpdatePatchListenerSchedulerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/20</p>
 * <p>完成日期：2021/4/20</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class UpdatePatchListenerSchedulerTest {

    @InjectMocks
    private UpdatePatchListenerScheduler updatePatchListenerScheduler;
    @Mock
    private PatchHistoryService patchHistoryService;
    @Mock
    private ClusterInfoControllerApi clusterInfoControllerApi;
    @Mock
    private PatchUpdateCacheService patchUpdateCacheService;
    @Mock
    private PatchUpdateResultService patchUpdateResultService;
    @Mock
    private LockUtils lockUtils;

    private static final String clusterId = "1";

    @Test
    public void cron() {
        List<ClusterBean> clusterBeanList = new ArrayList<>();
        ClusterBean clusterBean = new ClusterBean();
        clusterBean.setClusterId(NumberUtils.toInt(clusterId));
        clusterBeanList.add(clusterBean);

        when(clusterInfoControllerApi.queryAll()).thenReturn(clusterBeanList);
        doNothing().when(patchUpdateResultService).saveSuccessPatchResults(anyList());
        mockPatchUpdateCacheDto();
        PatchResult p1 = mockPatchResult("127.0.0.1");
        PatchResult p2 = mockPatchResult("*********");
        when(patchUpdateCacheService.pollSuccessPatchResult(clusterId)).thenReturn(Lists.newArrayList(p1, p2));
        mockFailedUpdateResult();
        when(patchUpdateCacheService.isPatchUpdateResultFinished(clusterId)).thenReturn(true);
        when(patchUpdateCacheService.queryFinishedCache(anyString())).thenReturn(true);

        updatePatchListenerScheduler.cron();
        verify(clusterInfoControllerApi, atLeast(1)).queryAll();

        updatePatchListenerScheduler.savePatchRecord(clusterId);
        verify(patchUpdateCacheService, atLeast(1)).queryUpdateProgress(clusterId);

        PatchUpdateCacheDto patchUpdateCacheDto = new PatchUpdateCacheDto();
        patchUpdateCacheDto.setAllFinished(false);
        when(patchUpdateCacheService.queryUpdateProgress(clusterId)).thenReturn(patchUpdateCacheDto);

        updatePatchListenerScheduler.savePatchRecord(clusterId);
        verify(patchUpdateCacheService, atLeast(1)).queryUpdateProgress(clusterId);
    }

    private void mockPatchUpdateCacheDto() {
        PatchUpdateCacheDto patchUpdateCacheDto = new PatchUpdateCacheDto();
        patchUpdateCacheDto.setFinishTime(System.currentTimeMillis());
        PatchUpdateResult patchUpdateResult = new PatchUpdateResult();
        patchUpdateResult.setPatchType("ZDH");
        patchUpdateResult.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP030-20201215");
        OnePatchUpdateResult o1 = new OnePatchUpdateResult("127.0.0.1", "hdfs", "nn", "/opt/ZDH/patch");
        OnePatchUpdateResult o2 = new OnePatchUpdateResult("*********", "hdfs", "nn", "/opt/ZDH/patch");
        patchUpdateResult.setHosts(Lists.newArrayList(o1, o2));
        patchUpdateCacheDto.setPatchResults(Lists.newArrayList(patchUpdateResult));
        when(patchUpdateCacheService.queryUpdateProgress(clusterId)).thenReturn(patchUpdateCacheDto);
    }

    private void mockFailedUpdateResult() {
        List<PatchUpdateResult> failedPatchResults = Lists.newArrayList();
        PatchUpdateResult patchUpdateResult = new PatchUpdateResult();
        patchUpdateResult.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP030-20201215");
        patchUpdateResult.setPatchType("ZDH");
        failedPatchResults.add(patchUpdateResult);
        when(patchUpdateCacheService.pollFailedPatchResult(clusterId)).thenReturn(failedPatchResults);
    }

    private PatchResult mockPatchResult(String hostIp) {
        PatchResult patchResult1 = new PatchResult(hostIp, true);
        PatchRecordAndRecoverInfos patchRecordAndRecoverInfos = new PatchRecordAndRecoverInfos(true);
        patchRecordAndRecoverInfos.setHostIp(hostIp);
        patchRecordAndRecoverInfos.setRole("");
        patchRecordAndRecoverInfos.setService("hdfs");
        OnePatchUpdateInfo onePatchUpdateInfo1 =
            new OnePatchUpdateInfo("DAP-HDFS-V20.19.40.R4.B2-SP030-20201215", hostIp, "true", "");
        OnePatchUpdateInfo onePatchUpdateInfo2 =
            new OnePatchUpdateInfo("DAP-HDFS-V20.19.40.R4.B2-SP031-20201215", hostIp, "true", "");

        OnePatchUpdateInfo onePatchUpdateInfo3 =
            new OnePatchUpdateInfo("DAP-HDFS-V20.19.40.R4.B2-SP032-contianer-20201215", hostIp, "true", "");

        patchRecordAndRecoverInfos
            .setPatchHistory(Lists.newArrayList(onePatchUpdateInfo1, onePatchUpdateInfo2, onePatchUpdateInfo3));
        patchResult1.setPatchRecordAndRecoverInfos(Lists.newArrayList(patchRecordAndRecoverInfos));
        return patchResult1;
    }
}