/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: SchemaPatchUtilsTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/31
 * </p>
 * <p>
 * 完成日期：2021/3/31
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.schema.utils;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Set;

import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.manager.common.deployer.bean.loadmodel.Product;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.schema.SchemaAction;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchRegisterRequest;
import com.zte.daip.manager.patcher.api.schema.UpdateSchemaPatchFile;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.domain.schema.utils.SchemaPatchUtils;
import com.zte.daip.manager.patcher.infrastructure.UnzipFileApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class SchemaPatchUtilsTest {

    @Mock
    private SchemaPatchApi schemaPatchApi;

    @Mock
    private SpectControllerApi spectControllerApi;

    @Mock
    private UnzipFileApi unzipFileApi;

    @Mock
    private ProductControllerApi productControllerApi;

    @Mock
    private PatchHistoryService patchHistoryService;

    @Mock
    private PatchInfoService patchInfoService;

    @InjectMocks
    private SchemaPatchUtils schemaPatchUtils;

    @Test
    public void persistInDbTest() {
        PatchTaskInfo patchTaskInfo = new PatchTaskInfo();
        patchTaskInfo.setServiceName("deployer");
        patchTaskInfo.setVersion("V20.22.40.09");
        patchTaskInfo.setUpdatePatchNames(Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
        schemaPatchUtils.persistInDb(patchTaskInfo, SchemaAction.UPDATE);
    }

    @Test
    public void registerSchemaPatchServiceTest() {
        PatchOperateResult patchOperateResult =
            schemaPatchUtils.registerSchemaPatchService(new SchemaPatchRegisterRequest());
        assertThat(true, is(patchOperateResult.isStatus()));
    }

    @Test
    public void queryCurrentPatchNameTest() {
        String currentPatchName = schemaPatchUtils.queryCurrentPatchName("deployer", "V20.22.40.09");
        assertThat("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215", is(currentPatchName));
    }

    @Test
    public void queryAllSchemaPatchFilesTest() {
        Mockito.when(schemaPatchApi.queryTargetPath(ArgumentMatchers.anyString(), ArgumentMatchers.anyString()))
            .thenReturn(getRepositoryHome());
        Set<UpdateSchemaPatchFile> updateSchemaPatchFiles = schemaPatchUtils.queryAllSchemaPatchFiles("", "");
        assertThat(12, is(updateSchemaPatchFiles.size()));
    }

    @Test
    public void searchSchemaPatchFilesTest() {
        Mockito.when(schemaPatchApi.queryTargetPath(ArgumentMatchers.anyString(), ArgumentMatchers.anyString()))
            .thenReturn(getRepositoryHome());
        Set<UpdateSchemaPatchFile> updateSchemaPatchFiles = schemaPatchUtils.queryAllSchemaPatchFiles("", "");
        Set<UpdateSchemaPatchFile> needUpdateSchemaPatchFiles =
            schemaPatchUtils.searchSchemaPatchFiles(initSchemaPatchRegisterRequest(), updateSchemaPatchFiles);
        assertThat(1, is(needUpdateSchemaPatchFiles.size()));
    }

    @Test
    public void isProductLoadSuccessTest() {
        boolean productLoadSuccess = schemaPatchUtils.isProductLoadSuccess("", "");
        assertThat(true, is(productLoadSuccess));
    }

    @Test
    public void checkTaskPatchDependencyTest() {
        boolean productLoadSuccess = schemaPatchUtils.checkTaskPatchDependency("", "",
            Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
        assertThat(true, is(productLoadSuccess));
    }

    @Test
    public void checkBeforeUpdatePatchesTest() {
        PatchOperateResult patchOperateResult = schemaPatchUtils.checkBeforeUpdatePatches(new PatchTaskInfo("deployer",
            "V20.19.40.R4.B2", Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215")));
        assertThat(true, is(patchOperateResult.isStatus()));
    }

    /* Started by AICoder, pid:f2c6dnc2c4k063714e4a08dc9000ec1cac78378b */
    /**
     * 测试isProductLoadSuccess方法是否能正确判断产品是否加载成功（测试用例1）。
     *
     * @throws IOException 如果发生I/O错误
     */
    @Test
    public void isProductLoadSuccess1() throws IOException {
        ProductSpect productSpect = new ProductSpect();
        productSpect.setProductFileName("test");
        Mockito.when(spectControllerApi.queryByServiceNameAndVersionNo(ArgumentMatchers.anyString(), ArgumentMatchers.anyString()))
                .thenReturn(productSpect);
        Product product = new Product();
        product.setCheckResult("true");
        Mockito.when(productControllerApi.queryByFileName(ArgumentMatchers.anyString()))
                .thenReturn(product);

        assertTrue(schemaPatchUtils.isProductLoadSuccess("zdh", "test"));
    }
    /* Ended by AICoder, pid:f2c6dnc2c4k063714e4a08dc9000ec1cac78378b */

    @Test
    public void checkBeforeRollbackPatchesTest() {
        PatchOperateResult patchOperateResult =
            schemaPatchUtils.checkBeforeRollbackPatches(new PatchTaskInfo("deployer", "V20.19.40.R4.B2",
                Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215")));
        assertThat(true, is(patchOperateResult.isStatus()));
    }

    @Before
    public void init() throws IOException {
        Mockito.when(schemaPatchApi.queryTargetPath(ArgumentMatchers.anyString(), ArgumentMatchers.anyString()))
            .thenReturn(queryTargetPath("deployer", "V20.19.40.R4.B2"));
        Mockito.when(schemaPatchApi.queryPatchPath(ArgumentMatchers.anyString(), ArgumentMatchers.anyString()))
            .thenReturn(queryPatchPath("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
        Mockito.when(schemaPatchApi.queryPatchHome(ArgumentMatchers.anyString(), ArgumentMatchers.anyString()))
            .thenReturn(queryPatchHome("deployer", "V20.19.40.R4.B2"));
        Mockito.when(spectControllerApi.queryByServiceNameAndVersionNo(ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString())).thenReturn(new ProductSpect());
        Product product = new Product();
        product.setCheckResult("true");
        Mockito.when(productControllerApi.queryByFileName(ArgumentMatchers.any())).thenReturn(product);
        Mockito.when(patchInfoService.queryAllPatchInfos()).thenReturn(getServicePatchPo());
        Mockito.when(patchHistoryService.querySchemaPatchHistory())
            .thenReturn(Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
        Mockito.when(patchInfoService.findSchemaByServiceAndBaseVersion(ArgumentMatchers.anyString(),
            ArgumentMatchers.anyString())).thenReturn(getServicePatchPo());
        Mockito.when(
            patchInfoService.findByServiceAndBaseVersion(ArgumentMatchers.anyString(), ArgumentMatchers.anyString()))
            .thenReturn(getServicePatchPo());
        Mockito
            .when(schemaPatchApi.queryPatchInfosByService(ArgumentMatchers.anyString(), ArgumentMatchers.anyString()))
            .thenReturn(Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
    }

    public String getRepositoryHome() {
        return this.getClass().getResource("/").getPath() + File.separator + "schema";
    }

    public String queryPatchPath(String patchName) {
        return getRepositoryHome() + File.separator + patchName + ".zip";
    }

    public String queryPatchHome(String serviceName, String version) {
        return getRepositoryHome() + File.separator + serviceName + "_" + version;
    }

    public String queryTargetPath(String serviceName, String version) {
        return getRepositoryHome() + File.separator + serviceName + "_" + version + File.separator;
    }

    private List<PatchDetailPo> getServicePatchPo() {
        PatchDetailPo patchBean = new PatchDetailPo();
        patchBean.setPatchName("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215");
        patchBean.setBaseVersion("V20.22.40.09");
        patchBean.setService("deployer");
        List<PatchDetailPo> patchDetailPos = Lists.newArrayList();
        patchDetailPos.add(patchBean);
        return patchDetailPos;
    }

    private SchemaPatchRegisterRequest initSchemaPatchRegisterRequest() {
        SchemaPatchRegisterRequest schemaPatchRegisterRequest = new SchemaPatchRegisterRequest();
        schemaPatchRegisterRequest.setPort("56160");
        schemaPatchRegisterRequest.setServiceName("daip-deployer-svr");
        schemaPatchRegisterRequest.setUrl("/api/daip-deployer-svr/v1/schema/update");
        schemaPatchRegisterRequest.setRegisterSchemaPatchFiles(initRegisterSchemaPatchFile());
        return schemaPatchRegisterRequest;
    }

    private List<UpdateSchemaPatchFile> initRegisterSchemaPatchFile() {
        List<UpdateSchemaPatchFile> registerSchemaPatchFiles = Lists.newArrayList();
        UpdateSchemaPatchFile registerSchemaPatchFile = new UpdateSchemaPatchFile();
        registerSchemaPatchFile.setUpdatePatchFilePath("");
        registerSchemaPatchFile.setUpdatePatchFileName(".*-configure-default.json");
        registerSchemaPatchFiles.add(registerSchemaPatchFile);
        return registerSchemaPatchFiles;
    }
}