package com.zte.daip.manager.patcher.domain.update.bean;
/* Started by AICoder, pid:y70e20a52cacbdf144950a6a1036929008907930 */
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
public class ServiceNeedUpdatePatchInfoTest {

    @InjectMocks
    private ServiceNeedUpdatePatchInfo serviceNeedUpdatePatchInfo;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    // 测试无参构造函数
    @Test
    public void testNoArgsConstructor() {
        ServiceNeedUpdatePatchInfo info = new ServiceNeedUpdatePatchInfo();
        assertNotNull(info);
    }

    // 测试有参构造函数
    @Test
    public void testAllArgsConstructor() {
        List<ServiceInstancePatchInfo> patchInfoList = new ArrayList<>();
        List<String> needUpdatePatchList = new ArrayList<>();
        ServiceNeedUpdatePatchInfo info = new ServiceNeedUpdatePatchInfo("test", patchInfoList, needUpdatePatchList);
        assertEquals("test", info.getServiceInstanceId());
        assertEquals(patchInfoList, info.getPatchInfoList());
        assertEquals(needUpdatePatchList, info.getNeedUpdatePatchList());
    }

    // 测试setter和getter方法
    @Test
    public void testSetterAndGetter() {
        List<ServiceInstancePatchInfo> patchInfoList = new ArrayList<>();
        List<String> needUpdatePatchList = new ArrayList<>();
        serviceNeedUpdatePatchInfo.setServiceInstanceId("test");
        serviceNeedUpdatePatchInfo.setPatchInfoList(patchInfoList);
        serviceNeedUpdatePatchInfo.setNeedUpdatePatchList(needUpdatePatchList);
        assertEquals("test", serviceNeedUpdatePatchInfo.getServiceInstanceId());
        assertEquals(patchInfoList, serviceNeedUpdatePatchInfo.getPatchInfoList());
        assertEquals(needUpdatePatchList, serviceNeedUpdatePatchInfo.getNeedUpdatePatchList());
    }

    // 测试hashCode方法
    @Test
    public void testHashCode() {
        List<ServiceInstancePatchInfo> patchInfoList = new ArrayList<>();
        List<String> needUpdatePatchList = new ArrayList<>();
        ServiceNeedUpdatePatchInfo info1 = new ServiceNeedUpdatePatchInfo("test", patchInfoList, needUpdatePatchList);
        ServiceNeedUpdatePatchInfo info2 = new ServiceNeedUpdatePatchInfo("test", patchInfoList, needUpdatePatchList);
        assertEquals(info1.hashCode(), info2.hashCode());
    }

    // 测试equals方法
    @Test
    public void testEquals_SameObject() {
        List<ServiceInstancePatchInfo> patchInfoList = new ArrayList<>();
        List<String> needUpdatePatchList = new ArrayList<>();
        ServiceNeedUpdatePatchInfo info = new ServiceNeedUpdatePatchInfo("test", patchInfoList, needUpdatePatchList);
        assertTrue(info.equals(info));
    }

    @Test
    public void testEquals_DifferentObject() {
        List<ServiceInstancePatchInfo> patchInfoList = new ArrayList<>();
        List<String> needUpdatePatchList = new ArrayList<>();
        ServiceNeedUpdatePatchInfo info1 = new ServiceNeedUpdatePatchInfo("test", patchInfoList, needUpdatePatchList);
        ServiceNeedUpdatePatchInfo info2 = new ServiceNeedUpdatePatchInfo("different", patchInfoList, needUpdatePatchList);
        assertFalse(info1.equals(info2));
    }

    @Test
    public void testEquals_NullObject() {
        List<ServiceInstancePatchInfo> patchInfoList = new ArrayList<>();
        List<String> needUpdatePatchList = new ArrayList<>();
        ServiceNeedUpdatePatchInfo info = new ServiceNeedUpdatePatchInfo("test", patchInfoList, needUpdatePatchList);
        assertFalse(info.equals(null));
    }
}

/* Ended by AICoder, pid:y70e20a52cacbdf144950a6a1036929008907930 */