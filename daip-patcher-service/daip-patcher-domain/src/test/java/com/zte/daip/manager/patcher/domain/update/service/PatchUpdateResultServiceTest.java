package com.zte.daip.manager.patcher.domain.update.service;

import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.ServiceInstance;
import com.zte.daip.manager.patcher.api.update.response.OnePatchUpdateInfo;
import com.zte.daip.manager.patcher.api.update.response.PatchRecordAndRecoverInfos;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.common.PatchRollbackService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@RunWith(SpringRunner.class)
public class PatchUpdateResultServiceTest {

    @Mock
    private PatchHistoryService patchHistoryService;
    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchRollbackService patchRollbackService;
    @InjectMocks
    private PatchUpdateResultService patchUpdateResultService;

    @Test
    public void saveSuccessPatchResults() {

        when(patchInfoService.findContainerPatchByService(anyString()))
            .thenReturn(Lists.newArrayList(new PatchDetailPo()));
        doNothing().when(patchRollbackService).deleteByRollbackKeyWithoutContainer(anyString(), anyString(),
            anyString(), anyString(), anyList());

        doNothing().when(patchRollbackService).saveRollbackPatches(anyList());
        doNothing().when(patchHistoryService).saveBatch(anyList());

        patchUpdateResultService.saveSuccessPatchResults(generatePatchResults());

    }

    @Test
    public void deleteRollbackServicePatchHistory() {
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("zk_Sp001");
        when(patchInfoService.findByServiceAndBaseVersion(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchDetailPo));
        doNothing().when(patchHistoryService).deleteByServiceNameAndPatchNamesAndIps(anyString(), anyList(), anyList());
        PatchServiceParam patchServiceParam =
            new PatchServiceParam("zk", "v1", "*********", Lists.newArrayList(new ServiceInstance()));
        patchUpdateResultService.deleteRollbackServicePatchHistory(Lists.newArrayList(patchServiceParam));
    }

    @Test
    public void organizePatchHistories() {}

    private List<PatchResult> generatePatchResults() {
        PatchResult patchResult = new PatchResult();
        patchResult.setHostIp("127.0.0.1");
        patchResult.setUpdateSuccess(true);
        List<PatchRecordAndRecoverInfos> patchRecordAndRecoverInfoList = Lists.newArrayList();

        PatchRecordAndRecoverInfos patchRecordAndRecoverInfos = new PatchRecordAndRecoverInfos();
        patchRecordAndRecoverInfos.setHostIp("127.0.0.1");
        patchRecordAndRecoverInfos.setQuerySuccess(true);
        patchRecordAndRecoverInfos.setRole("");
        patchRecordAndRecoverInfos.setService("zk");
        OnePatchUpdateInfo onePatchUpdateInfo =
            new OnePatchUpdateInfo(11111, "patchName", "service", "hostIp", "isSuccess", "roleName");
        patchRecordAndRecoverInfos.setPatchHistory(Lists.newArrayList(onePatchUpdateInfo));
        patchRecordAndRecoverInfos.setRecoverPatch(Lists.newArrayList(onePatchUpdateInfo));
        patchRecordAndRecoverInfoList.add(patchRecordAndRecoverInfos);
        patchResult.setPatchRecordAndRecoverInfos(patchRecordAndRecoverInfoList);
        return Lists.newArrayList(patchResult);
    }
}