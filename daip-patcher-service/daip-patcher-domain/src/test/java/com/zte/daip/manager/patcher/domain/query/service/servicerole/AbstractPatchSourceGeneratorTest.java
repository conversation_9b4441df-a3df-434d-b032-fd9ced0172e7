
package com.zte.daip.manager.patcher.domain.query.service.servicerole;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class AbstractPatchSourceGeneratorTest {

    private AbstractPatchSourceGenerator abstractPatchSourceGeneratorUnderTest;

    private InstancePatchSourceGenerator instancePatchSourceGeneratorTest;

    private RolePatchSourceGenerator rolePatchSourceGeneratorTest;

    @Before
    public void setUp() {
        abstractPatchSourceGeneratorUnderTest = new AbstractPatchSourceGenerator() {
            @Override
            public List<ServiceRoleInfo> obtainUnpatchedRoles(PatchDetailDto patchDetailDto,
                ServiceModel serviceModel) {
                return null;
            }

            @Override
            public String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException {
                return null;
            }

            @Override
            public List<ServiceRoleInfo> obtainUnpatchedRolesByDispatchUnpatchedHosts(PatchDetailDto patchDetailDto,
                ServiceModel serviceModel) {
                return mockRoleInfos1();
            }
        };

        instancePatchSourceGeneratorTest = new InstancePatchSourceGenerator() {
            @Override
            public List<ServiceRoleInfo> obtainUnpatchedRolesByDispatchUnpatchedHosts(PatchDetailDto patchDetailDto,
                ServiceModel serviceModel) {
                return mockRoleInfos1();
            }
        };

        rolePatchSourceGeneratorTest = new RolePatchSourceGenerator() {
            @Override
            public List<ServiceRoleInfo> obtainUnpatchedRoles(PatchDetailDto patchDetailDto,
                ServiceModel serviceModel) {
                return mockRoleInfos2();
            }
        };
    }

    @Test
    public void testObtainUnpatchedHostNum() {

        final PatchDetailDto patchDetailDto1 =
            new PatchDetailDto("patchName-RepositoryVersion-", "baseVersion", "service");
        final ServiceModel serviceModel1 = new ServiceModel("serviceId", "serviceName");
        final int result1 =
            abstractPatchSourceGeneratorUnderTest.obtainUnpatchedHostNum(patchDetailDto1, serviceModel1);
        assertEquals(0, result1);

        final PatchDetailDto patchDetailDto2 =
            new PatchDetailDto("VMAX-S-Cdrdispatch-V6.21.80.02P05_20221226_SP26_018", "V6.21.80.02P05", "cdrdispatch");
        ServiceModel serviceModel2 = new ServiceModel("cdrdispatch", "cdrdispatch");
        serviceModel2.setPatchType("instance");
        final Map<String, Set<PatchHistoryDto>> result2 =
            abstractPatchSourceGeneratorUnderTest.obtainUnpatchedService2Hosts(patchDetailDto2, serviceModel2);
        assertEquals(1, result2.size());

        final PatchDetailDto patchDetailDto3 =
            new PatchDetailDto("ZXVMAX-P-Saturn-V6.20.10.01P02_2021-06-28-SP001_001", "V6.20.10.01P02", "saturn");
        ServiceModel serviceModel3 = new ServiceModel("saturn", "saturn");
        serviceModel3.setPatchType("role");
        final Map<String, Set<PatchHistoryDto>> result3 =
            abstractPatchSourceGeneratorUnderTest.obtainUnpatchedService2Hosts(patchDetailDto3, serviceModel3);
        assertEquals(1, result3.size());

        final PatchDetailDto patchDetailDto4 =
            new PatchDetailDto("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01", "V20.19.40.R4.B2", "zookeeper");
        ServiceModel serviceModel4 = new ServiceModel("zookeeper", "zookeeper");
        final Map<String, Set<PatchHistoryDto>> result4 =
            abstractPatchSourceGeneratorUnderTest.obtainUnpatchedService2Hosts(patchDetailDto4, serviceModel4);
        assertEquals(1, result4.size());
    }

    @Test
    public void testObtainInstanceUnpatchedHostNum() {
        final PatchDetailDto patchDetailDto =
            new PatchDetailDto("VMAX-S-Cdrdispatch-V6.21.80.02P05_20221226_SP26_018", "V6.21.80.02P05", "cdrdispatch");
        final ServiceModel serviceModel = new ServiceModel("cdrdispatch", "cdrdispatch");
        final Map<String, Set<PatchHistoryDto>> service2Hosts =
            instancePatchSourceGeneratorTest.obtainUnpatchedService2Hosts(patchDetailDto, serviceModel);
        assertEquals(1, service2Hosts.size());
    }

    @Test
    public void testObtainRoleUnpatchedHostNum() {
        final PatchDetailDto patchDetailDto =
            new PatchDetailDto("ZXVMAX-P-Saturn-V6.20.10.01P02_2021-06-28-SP001_001", "V6.20.10.01P02", "saturn");
        final ServiceModel serviceModel = new ServiceModel("saturn", "saturn");
        final int result = rolePatchSourceGeneratorTest.obtainUnpatchedHostNum(patchDetailDto, serviceModel);
        assertEquals(1, result);
    }

    private List<ServiceRoleInfo> mockRoleInfos1() {
        List<ServiceRoleInfo> serviceRoleInfos = Lists.newArrayList();

        ServiceRoleInfo serviceRoleInfo1 =
            new ServiceRoleInfo("0", "**********", "vmax.cdrdispatch", "vmax.cdrdispatch.Cdrdispatch", "cdrdispatch");
        serviceRoleInfos.add(serviceRoleInfo1);

        return serviceRoleInfos;
    }

    private List<ServiceRoleInfo> mockRoleInfos2() {
        List<ServiceRoleInfo> serviceRoleInfos = Lists.newArrayList();

        ServiceRoleInfo serviceRoleInfo2 =
            new ServiceRoleInfo("0", "**********", "vmax.saturn", "vmax.saturn.Worker", "saturn");
        serviceRoleInfos.add(serviceRoleInfo2);

        return serviceRoleInfos;
    }
}
