package com.zte.daip.manager.patcher.domain.upload.operate;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Test;
import org.junit.runner.RunWith;


import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/* Started by AICoder, pid:x199c2d34aw80e914f2108425066a9631051d974 */
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.test.context.junit4.SpringRunner;
import org.junit.Assert;

import java.util.Collections;
import java.util.List;

@RunWith(SpringRunner.class)
public class SchemaPatchOperatorTest {

    @Mock
    private PatchInfoService patchInfoService;

    @InjectMocks
    private SchemaPatchOperator schemaPatchOperator;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGenerateFullKey() {
        PatchBean patchBean = new PatchBean();
        patchBean.setService("kafka");
        patchBean.setSrcVersion("942");
        String fullKey = schemaPatchOperator.generateFullKey(patchBean);
        Assert.assertEquals(patchBean.getService() + ":" + patchBean.getSrcVersion() + ":" + Constants.SCHEMA_PATCH, fullKey);
    }


    @Test
    public void testFilterHistoryFullPatch() {
        PatchDetailPo patchDetailPo = createPatchDetailPo("90", 0);
        List<PatchDetailPo> patchDetailPos =
            schemaPatchOperator.filterHistoryFullPatch(Collections.singletonList(patchDetailPo));
        Assert.assertEquals(1, patchDetailPos.size());
    }

    private PatchDetailPo createPatchDetailPo(String version, int isContainerPatch) {
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setIsContainerPatch(isContainerPatch);
        patchDetailPo.setPatchName(version + Constants.SCHEMA_PATCH);
        return patchDetailPo;
    }
}

/* Ended by AICoder, pid:x199c2d34aw80e914f2108425066a9631051d974 */