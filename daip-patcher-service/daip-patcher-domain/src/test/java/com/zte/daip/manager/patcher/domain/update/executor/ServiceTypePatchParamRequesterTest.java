package com.zte.daip.manager.patcher.domain.update.executor;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.patcher.domain.update.bean.ServiceNeedUpdatePatchInfo;
import com.zte.daip.manager.patcher.domain.update.executor.depend.DependResourceLocalUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.update.request.*;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import com.zte.daip.manager.patcher.domain.update.service.PatchesNeedUpdateQueryService;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: ServiceTypePatchParamRequesterTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/13</p>
 * <p>完成日期：2021/4/13</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class ServiceTypePatchParamRequesterTest {
    @Mock
    private DependResourceLocalUtil dependResourceLocalUtil;
    @Mock
    private PatchesNeedUpdateQueryService patchesNeedUpdateQueryService;
    @Mock
    private PatchHomeQueryService patchHomeQueryService;

    @Mock
    private DeploymentInstanceServiceControllerApi instanceControllerApi;

    @InjectMocks
    private ServiceTypePatchParamRequester serviceTypePatchParamRequester;

    @Test
    public void organize_rdk_service_Request() {

        //
        String clusterId = "0";
        OrganizeKey organizeKey = new OrganizeKey("rdk", "vmax.rdk", "V9.4.2");
        List<ServiceRoleInfo> serviceRoles = mockServiceRoleInfo();
        //
        Mockito.when(dependResourceLocalUtil.getClusterServiceRoleInfo(clusterId)).thenReturn(serviceRoles);

        //
        Set<String> ips = serviceRoles.stream().map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        Map<String, List<SimplePatchInfo>> stringListMap = mockPatchInfo(ips, organizeKey.getServiceName());
        Mockito.when(patchesNeedUpdateQueryService.queryNeedUpdatePatchesByService("rdk", "V9.4.2", ips))
            .thenReturn(stringListMap);

        //
        ServiceInstanceInfo serviceInstanceInfo = new ServiceInstanceInfo();
        serviceInstanceInfo.setServiceInstanceId("rdk");
        serviceInstanceInfo.setIps(Lists.newArrayList(ips));
        ConfigInstance configInstance = new ConfigInstance();
        configInstance.setConfigValue("/home/<USER>");
        List<ConfigInstance> configInstances = Lists.newArrayList(configInstance);

        Mockito.when(patchHomeQueryService.queryPatchHome(clusterId, "rdk")).thenReturn(configInstances);
        //
        List<PatchUpdateRequest> patchUpdateRequests = serviceTypePatchParamRequester.organizeRequest(clusterId,
            organizeKey, Lists.newArrayList(serviceInstanceInfo));

        // InstanceTypePatchParamRequesterTest
        PatchUpdateRequest patchUpdateRequest = patchUpdateRequests.get(0);
        Map<String, List<ServicePatchInfo>> ip2Patches = patchUpdateRequest.getIp2Patches();

        Assert.assertEquals(3, ip2Patches.entrySet().size());

    }
    /* Started by AICoder, pid:4766ej109af51f11470c0bd210714f3dec8667c5 */
    @Test
    public void queryRollbackPoints() {
        // 测试查询回滚点的方法，并验证异常处理
        String clusterId = "0";
        OrganizeKey organizeKey = new OrganizeKey("rdk", "vmax.rdk", "V9.4.2");
        List<ServiceRoleInfo> serviceRoles = mockServiceRoleInfo();

        // 模拟依赖资源本地工具获取集群服务角色信息
        Mockito.when(dependResourceLocalUtil.getClusterServiceRoleInfo(clusterId)).thenReturn(serviceRoles);

        // 调用查询回滚点的方法，并验证返回结果是否为空
        ServiceNeedUpdatePatchInfo patchRollbackHostInfos =
                serviceTypePatchParamRequester.queryRollbackPoints(serviceRoles.get(0), organizeKey);
        Assert.assertNull(patchRollbackHostInfos);

        // 创建一个HostInfo对象，并设置IP地址和主机名
        HostInfo hostInfo = new HostInfo();
        hostInfo.setIpAddress("************");
        hostInfo.setHostName("host22");
        List<HostInfo> hostInfos = Lists.newArrayList(hostInfo);

        // 模拟依赖资源本地工具获取集群的主机信息
        Mockito.when(dependResourceLocalUtil.getClusterHostInfo(clusterId)).thenReturn(hostInfos);

        // 从ServiceRoleInfo中提取IP地址，并将它们存储在一个Set中
        Set<String> ips = serviceRoles.stream().map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());

        // 模拟获取需要更新的补丁信息
        Map<String, List<SimplePatchInfo>> stringListMap = mockPatchInfo(ips, organizeKey.getServiceName());
        Mockito.when(patchesNeedUpdateQueryService.queryNeedUpdatePatchesByService("rdk", "V9.4.2", ips))
                .thenReturn(stringListMap);

        // 再次调用查询回滚点的方法，并验证返回结果是否符合预期
        patchRollbackHostInfos = serviceTypePatchParamRequester.queryRollbackPoints(serviceRoles.get(0), organizeKey);
        Assert.assertEquals(1, patchRollbackHostInfos.getPatchInfoList().size());
    }

    /* Ended by AICoder, pid:4766ej109af51f11470c0bd210714f3dec8667c5 */

    private List<ServiceRoleInfo> mockServiceRoleInfo() {

        String message =
            "[{\"id\":13,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"vmax.rdk\",\"roleId\":\"vmax.rdk.master\",\"serviceInstanceId\":\"rdk\",\"roleConfigGroupId\":13,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":14,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"vmax.rdk\",\"roleId\":\"vmax.rdk.master\",\"serviceInstanceId\":\"rdk\",\"roleConfigGroupId\":13,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":15,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"vmax.rdk\",\"roleId\":\"vmax.rdk.master\",\"serviceInstanceId\":\"rdk\",\"roleConfigGroupId\":13,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":39,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.spark\",\"roleId\":\"dap.manager.spark.Master\",\"serviceInstanceId\":\"spark\",\"roleConfigGroupId\":23,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":32,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hbase\",\"roleId\":\"dap.manager.hbase.Master\",\"serviceInstanceId\":\"hbase\",\"roleConfigGroupId\":22,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":33,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hbase\",\"roleId\":\"dap.manager.hbase.RegionServer\",\"serviceInstanceId\":\"hbase\",\"roleConfigGroupId\":21,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":34,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hbase\",\"roleId\":\"dap.manager.hbase.Master\",\"serviceInstanceId\":\"hbase\",\"roleConfigGroupId\":22,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":35,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hbase\",\"roleId\":\"dap.manager.hbase.RegionServer\",\"serviceInstanceId\":\"hbase\",\"roleConfigGroupId\":21,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":36,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hbase\",\"roleId\":\"dap.manager.hbase.RegionServer\",\"serviceInstanceId\":\"hbase\",\"roleConfigGroupId\":21,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":45,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.sparkSQL\",\"roleId\":\"dap.manager.sparkSQL.sparkSQL\",\"serviceInstanceId\":\"sparkSQL\",\"roleConfigGroupId\":28,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":46,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.sparkSQL\",\"roleId\":\"dap.manager.sparkSQL.sparkSQL\",\"serviceInstanceId\":\"sparkSQL\",\"roleConfigGroupId\":28,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":47,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.sparkSQL\",\"roleId\":\"dap.manager.sparkSQL.sparkSQL\",\"serviceInstanceId\":\"sparkSQL\",\"roleConfigGroupId\":28,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":49,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.flink\",\"roleId\":\"dap.manager.flink.HistoryServer\",\"serviceInstanceId\":\"flink\",\"roleConfigGroupId\":31,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":50,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.flink\",\"roleId\":\"dap.manager.flink.JobManager\",\"serviceInstanceId\":\"flink\",\"roleConfigGroupId\":29,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":52,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.flink\",\"roleId\":\"dap.manager.flink.HistoryServer\",\"serviceInstanceId\":\"flink\",\"roleConfigGroupId\":31,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":53,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.flink\",\"roleId\":\"dap.manager.flink.JobManager\",\"serviceInstanceId\":\"flink\",\"roleConfigGroupId\":29,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":42,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.spark\",\"roleId\":\"dap.manager.spark.HistoryServer\",\"serviceInstanceId\":\"spark\",\"roleConfigGroupId\":25,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":41,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.spark\",\"roleId\":\"dap.manager.spark.Worker\",\"serviceInstanceId\":\"spark\",\"roleConfigGroupId\":24,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":16,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.NameNode\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":16,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":17,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.zkfc\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":14,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":18,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.DataNode\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":15,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":19,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.JournalNode\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":17,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":20,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.NameNode\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":16,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":21,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.zkfc\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":14,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":22,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.DataNode\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":15,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":23,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.JournalNode\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":17,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":24,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.DataNode\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":15,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":25,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hdfs\",\"roleId\":\"dap.manager.hdfs.JournalNode\",\"serviceInstanceId\":\"hdfs\",\"roleConfigGroupId\":17,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":26,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.yarn\",\"roleId\":\"dap.manager.yarn.ResourceManager\",\"serviceInstanceId\":\"yarn\",\"roleConfigGroupId\":19,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":27,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.yarn\",\"roleId\":\"dap.manager.yarn.NodeManager\",\"serviceInstanceId\":\"yarn\",\"roleConfigGroupId\":18,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":28,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.yarn\",\"roleId\":\"dap.manager.yarn.ResourceManager\",\"serviceInstanceId\":\"yarn\",\"roleConfigGroupId\":19,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":29,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.yarn\",\"roleId\":\"dap.manager.yarn.NodeManager\",\"serviceInstanceId\":\"yarn\",\"roleConfigGroupId\":18,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":30,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.yarn\",\"roleId\":\"dap.manager.yarn.JobHistoryServer\",\"serviceInstanceId\":\"yarn\",\"roleConfigGroupId\":20,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":31,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.yarn\",\"roleId\":\"dap.manager.yarn.NodeManager\",\"serviceInstanceId\":\"yarn\",\"roleConfigGroupId\":18,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":38,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.spark\",\"roleId\":\"dap.manager.spark.Worker\",\"serviceInstanceId\":\"spark\",\"roleConfigGroupId\":24,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":37,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.spark\",\"roleId\":\"dap.manager.spark.Master\",\"serviceInstanceId\":\"spark\",\"roleConfigGroupId\":23,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":40,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.spark\",\"roleId\":\"dap.manager.spark.Worker\",\"serviceInstanceId\":\"spark\",\"roleConfigGroupId\":24,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":51,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.flink\",\"roleId\":\"dap.manager.flink.TaskManager\",\"serviceInstanceId\":\"flink\",\"roleConfigGroupId\":30,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":48,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.flink\",\"roleId\":\"dap.manager.flink.TaskManager\",\"serviceInstanceId\":\"flink\",\"roleConfigGroupId\":30,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":54,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.flink\",\"roleId\":\"dap.manager.flink.TaskManager\",\"serviceInstanceId\":\"flink\",\"roleConfigGroupId\":30,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":55,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.flink\",\"roleId\":\"dap.manager.flink.HistoryServer\",\"serviceInstanceId\":\"flink\",\"roleConfigGroupId\":31,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":68,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.odpp\",\"roleId\":\"dap.manager.odpp.OdppUser\",\"serviceInstanceId\":\"odpp\",\"roleConfigGroupId\":37,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":86,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hive\",\"roleId\":\"dap.manager.hive.HiveMetastoreServer\",\"serviceInstanceId\":\"hive\",\"roleConfigGroupId\":40,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":69,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.odpp\",\"roleId\":\"dap.manager.odpp.OdppService\",\"serviceInstanceId\":\"odpp\",\"roleConfigGroupId\":36,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":88,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hive\",\"roleId\":\"dap.manager.hive.HiveMetastoreServer\",\"serviceInstanceId\":\"hive\",\"roleConfigGroupId\":40,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":89,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hive\",\"roleId\":\"dap.manager.hive.HiveMetastoreServer\",\"serviceInstanceId\":\"hive\",\"roleConfigGroupId\":40,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":85,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hive\",\"roleId\":\"dap.manager.hive.Hiveserver2\",\"serviceInstanceId\":\"hive\",\"roleConfigGroupId\":41,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":70,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.odpp\",\"roleId\":\"dap.manager.odpp.OdppUser\",\"serviceInstanceId\":\"odpp\",\"roleConfigGroupId\":37,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":65,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.odpp\",\"roleId\":\"dap.manager.odpp.OdppService\",\"serviceInstanceId\":\"odpp\",\"roleConfigGroupId\":36,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":61,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.elasticsearch\",\"roleId\":\"dap.manager.elasticsearch.elasticsearch\",\"serviceInstanceId\":\"elasticsearch\",\"roleConfigGroupId\":34,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":62,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.elasticsearch\",\"roleId\":\"dap.manager.elasticsearch.elasticsearch\",\"serviceInstanceId\":\"elasticsearch\",\"roleConfigGroupId\":34,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":63,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.elasticsearch\",\"roleId\":\"dap.manager.elasticsearch.elasticsearch\",\"serviceInstanceId\":\"elasticsearch\",\"roleConfigGroupId\":34,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":64,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.ranger\",\"roleId\":\"dap.manager.ranger.RangerAdmin\",\"serviceInstanceId\":\"ranger\",\"roleConfigGroupId\":35,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":87,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.hive\",\"roleId\":\"dap.manager.hive.Hiveserver2\",\"serviceInstanceId\":\"hive\",\"roleConfigGroupId\":41,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":82,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.kafka\",\"roleId\":\"dap.manager.kafka.kafka\",\"serviceInstanceId\":\"kafka\",\"roleConfigGroupId\":39,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":83,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.kafka\",\"roleId\":\"dap.manager.kafka.kafka\",\"serviceInstanceId\":\"kafka\",\"roleConfigGroupId\":39,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":84,\"nodeId\":\"4\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.kafka\",\"roleId\":\"dap.manager.kafka.kafka\",\"serviceInstanceId\":\"kafka\",\"roleConfigGroupId\":39,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":66,\"nodeId\":\"2\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.odpp\",\"roleId\":\"dap.manager.odpp.OdppUser\",\"serviceInstanceId\":\"odpp\",\"roleConfigGroupId\":37,\"expertStatus\":\"Started\",\"activeStatus\":\"\"},{\"id\":67,\"nodeId\":\"3\",\"clusterId\":\"0\",\"ipAddress\":\"************\",\"serviceId\":\"dap.manager.odpp\",\"roleId\":\"dap.manager.odpp.OdppService\",\"serviceInstanceId\":\"odpp\",\"roleConfigGroupId\":36,\"expertStatus\":\"Started\",\"activeStatus\":\"\"}]";
        return JSON.parseArray(message, ServiceRoleInfo.class);
    }

    private Map<String, List<SimplePatchInfo>> mockPatchInfo(Set<String> ips, String serviceName) {

        SimplePatchInfo simplePatchInfo1 = new SimplePatchInfo(serviceName + "-SP001", false);
        SimplePatchInfo simplePatchInfo2 = new SimplePatchInfo(serviceName + "-SP002", false);
        SimplePatchInfo simplePatchInfo3 = new SimplePatchInfo(serviceName + "-SP003", false);
        SimplePatchInfo simplePatchInfo4 = new SimplePatchInfo(serviceName + "-SP005-container", true);
        List<SimplePatchInfo> simplePatchInfos =
            Lists.newArrayList(simplePatchInfo1, simplePatchInfo2, simplePatchInfo3, simplePatchInfo4);
        Map<String, List<SimplePatchInfo>> map = Maps.newHashMap();
        for (String ip : ips) {
            map.put(ip, simplePatchInfos);
        }
        return map;
    }

    @Test
    public void mergeRequest() {

        ///
        //
        String clusterId = "0";
        OrganizeKey organizeKey1 = new OrganizeKey("rdk", "vmax.rdk", "V9.4.2");

        List<ServiceRoleInfo> serviceRoles = mockServiceRoleInfo();
        //
        Mockito.when(dependResourceLocalUtil.getClusterServiceRoleInfo(clusterId)).thenReturn(serviceRoles);
        //
        ServiceInstanceInfo serviceInstanceInfo = new ServiceInstanceInfo();
        serviceInstanceInfo.setServiceInstanceId("rdk");
        ConfigInstance configInstance = new ConfigInstance();
        configInstance.setConfigValue("/home/<USER>");
        List<ConfigInstance> configInstances = Lists.newArrayList(configInstance);

        Mockito.when(patchHomeQueryService.queryPatchHome(clusterId, "rdk")).thenReturn(configInstances);
        Set<String> ips = serviceRoles.stream().map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());

        serviceInstanceInfo.setServiceName("rdk");
        serviceInstanceInfo.setIps(Lists.newArrayList(ips));
        //
        Mockito.when(patchesNeedUpdateQueryService.queryNeedUpdatePatchesByService("rdk", "V9.4.2", ips))
            .thenReturn(mockPatchInfo(ips, organizeKey1.getServiceName()));

        //
        List<PatchUpdateRequest> patchUpdateRequests1 = serviceTypePatchParamRequester.organizeRequest(clusterId,
            organizeKey1, Lists.newArrayList(serviceInstanceInfo));

        ///
        PatchRequestKey patchRequestKey = new PatchRequestKey(clusterId, "/home/<USER>", "service");
        List<PatchUpdateRequest> allPatchUpdateRequests = Lists.newArrayList();
        allPatchUpdateRequests.addAll(patchUpdateRequests1);
        PatchUpdateRequest finalPatchUpdateRequest =
            serviceTypePatchParamRequester.mergeRequest(patchRequestKey, Lists.newArrayList(allPatchUpdateRequests));

        //
        Assert.assertEquals("/home/<USER>" + File.separator + "patch", finalPatchUpdateRequest.getPatchHome());
        Assert.assertEquals("service", finalPatchUpdateRequest.getPatchType());
        Assert.assertEquals(clusterId, finalPatchUpdateRequest.getClusterId());

        Assert.assertEquals(ips, finalPatchUpdateRequest.getIp2Patches().keySet());

        Map<String, List<ServicePatchInfo>> ip2Patches = finalPatchUpdateRequest.getIp2Patches();

        List<ServicePatchInfo> allServicePatchInfos = Lists.newArrayList();

        for (Map.Entry<String, List<ServicePatchInfo>> entry : ip2Patches.entrySet()) {
            allServicePatchInfos.addAll(entry.getValue());
        }

        Assert.assertEquals(3, allServicePatchInfos.size());

    }
}