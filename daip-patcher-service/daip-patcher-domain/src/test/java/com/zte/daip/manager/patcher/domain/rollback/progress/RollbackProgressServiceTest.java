package com.zte.daip.manager.patcher.domain.rollback.progress;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.cache.delaytask.DelayTaskExecutor;
import com.zte.daip.manager.common.cache.delaytask.DelayTaskManager;
import com.zte.daip.manager.patcher.api.dto.PatchHostInfo;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.api.dto.RollBackPatchProgress;
import com.zte.daip.manager.patcher.api.dto.RollbackServiceProgressDto;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.rollback.entity.HostRollbackProgress;
import com.zte.daip.manager.patcher.domain.rollback.entity.PatchRollbackDo;
import com.zte.daip.manager.patcher.domain.rollback.entity.RollbackProgressDo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static junit.framework.TestCase.assertEquals;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: RollbackProgressServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/23</p>
 * <p>完成日期：2023/3/23</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class RollbackProgressServiceTest {

    @InjectMocks
    private RollbackProgressService rollbackProgressService;
    @Mock
    private RollbackProgressApi rollbackProgressApi;
    @Mock
    private HostResourceInfoCache hostResourceInfoCache;

    @Before
    public void setUp() {

        Mockito.when(rollbackProgressApi.queryRollbackServices(anyString())).thenReturn(Lists.newArrayList("kafka"));
        Mockito.when(hostResourceInfoCache.queryIpAddress(anyString())).thenReturn("127.0.0.1");
        HostRollbackProgress hostRollbackProgress = new HostRollbackProgress("127.0.0.1", true, true, "");
        HostRollbackProgress hostRollbackProgress1 = new HostRollbackProgress("*********", false, true, "");
        Mockito.when(rollbackProgressApi.queryProgress(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(hostRollbackProgress1, hostRollbackProgress));
        doNothing().when(rollbackProgressApi).deleteRollbackProgress(anyString(), anyString());
        doNothing().when(rollbackProgressApi).initRollbackServices(anyString(), anyList());
        doNothing().when(rollbackProgressApi).initAndUpdateRollbackProgress(anyString(), anyString(), anyString(),
            any());

    }

    @Test
    public void queryRollBackProgress() {
        RollBackPatchProgress rollBackPatchProgress = rollbackProgressService.queryRollBackProgress("1");
        assertEquals(rollBackPatchProgress.isFinish(), true);
        assertEquals(rollBackPatchProgress.isSuccess(), false);
    }

    @Test
    public void initRollbackProgress() {

        boolean result = true;
        try {
            PatchRollbackHostDto patchRollbackDo = new PatchRollbackHostDto();
            patchRollbackDo.setServiceName("kafka");
            patchRollbackDo.setPatchHostInfoList(Lists.newArrayList(new PatchHostInfo("127.0.0.1", "host1")));
            rollbackProgressService.initRollbackProgress("1", Lists.newArrayList(patchRollbackDo));
        } catch (Exception e) {
            result = false;
        }
        assertFalse(result);

    }

    @Test
    public void updateRollbackProgress() {
        boolean result = true;
        try {
            RollbackProgressDo patchRollbackDo = new RollbackProgressDo("1", "host1", "kafka", "", true);
            rollbackProgressService.updateRollbackProgress(Lists.newArrayList(patchRollbackDo));
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }
}