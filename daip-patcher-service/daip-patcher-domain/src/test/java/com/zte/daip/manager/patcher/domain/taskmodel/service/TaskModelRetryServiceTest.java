package com.zte.daip.manager.patcher.domain.taskmodel.service;

import com.zte.daip.manager.common.cache.lock.LockUtils;
import com.zte.daip.manager.response.CommonResponse;
import com.zte.daip.manager.task.api.model.api.TaskModelApi;
import com.zte.daip.manager.task.api.model.dto.TaskModelDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.redisson.RedissonLock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static junit.framework.TestCase.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: TaskModelRetryServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/10/24</p>
 * <p>完成日期：2023/10/24</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class TaskModelRetryServiceTest
{
    @InjectMocks
    private TaskModelRetryService taskModelRetryService;

    @Mock
    private TaskModelApi taskModelApi;

    @Mock
    private LockUtils lockUtils;
    @Mock
    private RedissonLock redissonLock;

    private List<TaskModelDto> taskModelDtos = new ArrayList<>();

    @Before
    public void init()
    {
        TaskModelDto taskModelDto = new TaskModelDto();
        taskModelDto.setModelId(1L);
        taskModelDto.setModelName("1");
        taskModelDtos.add(taskModelDto);
    }

    @Test
    public void saveTaskModelRetry()
    {
        when(redissonLock.isLocked()).thenReturn(false);
        when(lockUtils.getLock(anyString())).thenReturn(redissonLock);

        Mockito.when(taskModelApi.loadModel(taskModelDtos)).thenReturn(CommonResponse.success(false));
//        Assert.assertThrows(DaipBaseException.class, () -> taskModelRetryService.saveTaskModelRetry(taskModelDtos));

        Mockito.when(taskModelApi.loadModel(taskModelDtos)).thenReturn(CommonResponse.success(true));

        boolean result = true;
        try {
            taskModelRetryService.saveTaskModelRetry(taskModelDtos);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }
}