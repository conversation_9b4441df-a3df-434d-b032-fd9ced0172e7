package com.zte.daip.manager.patcher.domain.dispatch;

import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: DistributeFileServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/5/30</p>
 * <p>完成日期：2023/5/30</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class DistributeFileServiceTest
{
    @Mock
    private FileDistributeControllerApi fileDistributeControllerApi;

    @Mock
    private PatchEnvApi patchEnvApi;

    @InjectMocks
    private DistributeFileService distributeFileService;

    @Before
    public void setUp() throws Exception {
        Mockito.when(fileDistributeControllerApi.distributeFiles(any())).thenReturn("aaa");
        Mockito.when(patchEnvApi.getRepositoryHomeEnv()).thenReturn("/data1/version");
        Mockito.when(patchEnvApi.getPatchUrl()).thenReturn("https://127.0.0.1:8988");

    }

    @Test
    public void test_update_distribute() {
        boolean result = true;
        Set<String> targetHostIps = org.assertj.core.util.Sets.newHashSet();
        targetHostIps.add("********");
        List<PatchDetailPo> projectPatchPo = getProjectPatchPo();
        try {
            distributeFileService.distributeFiles(targetHostIps, projectPatchPo);
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }

    private List<PatchDetailPo> getProjectPatchPo() {
        PatchDetailPo patchBean = new PatchDetailPo();
        patchBean.setPatchName("DAP-zdh-V20.19.40.R4.B2-SP01");
        patchBean.setBaseVersion("V20.19.40.R4.B2");
        patchBean.setService("zdh");
        return org.assertj.core.util.Lists.newArrayList(patchBean);
    }
}