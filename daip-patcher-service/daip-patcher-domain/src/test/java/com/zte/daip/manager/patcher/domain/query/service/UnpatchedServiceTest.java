/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UnpatchedServiceTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2022/12/29
 * </p>
 * <p>
 * 完成日期：2022/12/29
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceVersionResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.version.ServiceVersionKey;
import com.zte.daip.manager.common.deployer.bean.version.ServiceVersionModel;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.patcher.api.dto.*;
import com.zte.daip.manager.patcher.domain.common.PatchDispatchService;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchDetailDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchHistoryDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.InstancePatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.RolePatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.ServicePatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.ZDHPatchSourceGenerator;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import org.assertj.core.util.Sets;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class UnpatchedServiceTest {
    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private PatchInfoService patchInfoService;

    @Mock
    private PatchHistoryService patchHistoryService;

    @Mock
    private PatchDispatchService patchDispatchService;

    @Mock
    private HostResourceControllerApi hostResourceControllerApi;

    @Mock
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @Mock
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;

    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Mock
    private ServiceVersionResourceControllerApi serviceVersionResourceControllerApi;

    @Mock
    private PatchDetailAsyncQueryService patchDetailAsyncQueryService;

    @Spy
    private PatchDetailDtoAssembler patchDetailDtoAssembler = new PatchDetailDtoAssembler();

    @Spy
    private PatchHistoryDtoAssembler patchHistoryDtoAssembler = new PatchHistoryDtoAssembler();

    @InjectMocks
    private UnpatchedService unpatchedService;

    @Before
    public void setUp() {
        Mockito.when(applicationContext.getBean(PatchTypeEnum.SERVICE.getServiceName()))
            .thenReturn(new ServicePatchSourceGenerator());
        Mockito.when(applicationContext.getBean(PatchTypeEnum.ROLE.getServiceName()))
            .thenReturn(new RolePatchSourceGenerator());
        Mockito.when(applicationContext.getBean(PatchTypeEnum.INSTANCE.getServiceName()))
            .thenReturn(new InstancePatchSourceGenerator());
        Mockito.when(applicationContext.getBean(PatchTypeEnum.ZDH.getServiceName()))
            .thenReturn(new ZDHPatchSourceGenerator());
        Mockito.when(patchInfoService.queryAllPatchExceptScheme()).thenReturn(mockPatchDetailPo());
        Mockito.when(patchHistoryService.queryAllPatchHistoryInfo()).thenReturn(mockPatchHistory());
        Mockito.when(productModelInfoControllerApi.queryAllServiceModels()).thenReturn(mockAllServiceModels());
        Mockito.when(hostResourceControllerApi.queryAll()).thenReturn(mockAllHost());
        Mockito.when(serviceResourceControllerApi.queryAll()).thenReturn(mockServiceRoleInfos());
        Mockito.when(deploymentInstanceServiceControllerApi.queryAll()).thenReturn(mockAllDeploymentServiceInstances());
        Mockito.when(serviceVersionResourceControllerApi.queryAllServiceVersions())
            .thenReturn(mockAllServiceVersionModels());
        Mockito.when(patchDetailAsyncQueryService.calcOnePatchPatchAndHost(any(), any(), any(), anyString()))
            .thenReturn(mockPatchPatchAndHosts());
    }

    @Test
    public void queryUnpatchedPatchesAndHost() {

        Map<String, Set<PatchServiceHostDto>> clusterServiceHostMap = new HashMap<>();
        Set<PatchServiceHostDto> serviceHostDtos = Sets.newHashSet();
        serviceHostDtos.add(new PatchServiceHostDto("zookeeper", Sets.newHashSet()));
        clusterServiceHostMap.put("1", serviceHostDtos);
        ClusterAndServiceHostBean clusterAndServiceHostBean = new ClusterAndServiceHostBean(clusterServiceHostMap);

        List<PatchHostDto> patchHostDtos =
            unpatchedService.queryUnpatchedPatchesAndHost(clusterAndServiceHostBean, false);

        assertEquals(1, patchHostDtos.size());
    }

    @Test
    public void queryUnpatchedPatchesAndServiceAndHost() {

        Map<String, Set<String>> clusterServicesMap = new HashMap<>();
        Set<String> strings = Sets.newHashSet();
        strings.add("zookeeper");
        clusterServicesMap.put("1", strings);

        ClustersAndServicesBean clustersAndServicesBean = new ClustersAndServicesBean(clusterServicesMap);

        List<PatchServiceHostDto> patchServiceHostDtos =
            unpatchedService.queryUnpatchedPatchesAndServiceAndHost(clustersAndServicesBean);

        assertEquals(1, patchServiceHostDtos.size());
    }

    private List<PatchHistory> mockPatchHistory() {
        return Lists.newArrayList();
    }

    private List<PatchDetailPo> mockPatchDetailPo() {
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01");
        patchDetailPo.setBaseVersion("V20.19.40.R4.B2");
        patchDetailPo.setService("zookeeper");
        return Lists.newArrayList(patchDetailPo);

    }

    private List<ServiceModel> mockAllServiceModels() {

        List<ServiceModel> serviceModels = Lists.newArrayList();
        ServiceModel serviceModel1 = new ServiceModel("dap.manager.zookeeper", "zookeeper");
        serviceModel1.setVersion("V20.19.40.R4.B2");
        serviceModel1.setComponentType("dap.manager.common.bigdata");
        serviceModels.add(serviceModel1);

        return serviceModels;
    }

    private List<ServiceVersionModel> mockAllServiceVersionModels() {
        List<ServiceVersionModel> serviceModels = Lists.newArrayList();
        ServiceVersionModel serviceModel1 =
            new ServiceVersionModel(new ServiceVersionKey("dap.manager.common.bigdata", "**********", ""));
        serviceModel1.setVersion("V20.19.40.R4.B2");

        serviceModels.add(serviceModel1);

        ServiceVersionModel serviceModel2 =
            new ServiceVersionModel(new ServiceVersionKey("dap.manager.common.bigdata", "**********", ""));
        serviceModel2.setVersion("V20.19.40.R4.B2");
        serviceModels.add(serviceModel2);

        return serviceModels;
    }

    private List<DeploymentServiceInstance> mockAllDeploymentServiceInstances() {

        List<DeploymentServiceInstance> instances = Lists.newArrayList();
        DeploymentServiceInstance serviceInstance = new DeploymentServiceInstance();
        serviceInstance.setServiceId("dap.manager.zookeeper");
        serviceInstance.setServiceInstanceId("zookeeper");
        instances.add(serviceInstance);

        return instances;
    }

    private List<HostInfo> mockAllHost() {
        List<HostInfo> hostInfos = Lists.newArrayList();
        final HostInfo hostInfo1 = new HostInfo();
        hostInfo1.setIpAddress("**********");
        hostInfo1.setHostName("**********");
        final HostInfo hostInfo2 = new HostInfo();
        hostInfo2.setIpAddress("**********");
        hostInfo2.setHostName("**********");
        hostInfos.add(hostInfo1);
        hostInfos.add(hostInfo2);
        return hostInfos;
    }

    private List<ServiceRoleInfo> mockServiceRoleInfos() {
        List<ServiceRoleInfo> serviceRoleInfos = Lists.newArrayList();

        ServiceRoleInfo serviceRoleInfo1 = new ServiceRoleInfo("0", "**********", "dap.manager.zookeeper",
            "dap.manager.zookeeper.HQuorumPeer", "zookeeper");

        ServiceRoleInfo serviceRoleInfo2 = new ServiceRoleInfo("0", "**********", "dap.manager.zookeeper",
            "dap.manager.zookeeper.HQuorumPeer", "zookeeper");

        serviceRoleInfos.add(serviceRoleInfo1);

        serviceRoleInfos.add(serviceRoleInfo2);

        return serviceRoleInfos;
    }

    private Future<List<PatchHostDto>> mockPatchPatchAndHosts() {
        Set<PatchHistoryDto> patchHistoryDtos = Sets.newHashSet();
        patchHistoryDtos.add(new PatchHistoryDto("**********","**********"));
        patchHistoryDtos.add(new PatchHistoryDto("**********","**********"));
        
        PatchHostDto patchHostDto =
            new PatchHostDto(new PatchDetailDto("DAP-ZOOKEEPER-V20.19.40.R4.B2.SP01", "V20.19.40.R4.B2", "zookeeper"),
                patchHistoryDtos);

        return new AsyncResult<>(Lists.newArrayList(patchHostDto));
    }

}