package com.zte.daip.manager.patcher.domain.upload.operate;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class ContainerPatchOperatorTest {

    @Mock
    private PatchInfoService patchInfoService;

    @InjectMocks
    private ContainerPatchOperator containerPatchOperator;

    @Test
    public void generateFullKey() {
        PatchBean patchBean = new PatchBean();
        patchBean.setService("kafka");
        patchBean.setSrcVersion("942");
        String fullKey = containerPatchOperator.generateFullKey(patchBean);
        assertEquals(fullKey,
            patchBean.getService() + ":" + patchBean.getSrcVersion() + ":" + Constants.CONTAINER_PATCH);

    }


    @Test
    public void filterHistoryFullPatch() {
        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setIsContainerPatch(1);
        patchDetailPo.setPatchName("90");
        List<PatchDetailPo> patchDetailPos =
            containerPatchOperator.filterHistoryFullPatch(Lists.newArrayList(patchDetailPo));
        assertEquals(1, patchDetailPos.size());
    }

}