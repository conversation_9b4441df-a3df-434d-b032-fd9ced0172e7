package com.zte.daip.manager.patcher.domain.update.schedule;

/* Started by AICoder, pid:jf644vcfefp926014be0089a10fbe2737a484026 */
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.domain.update.cache.PatchRollbackCacheQueue;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateResultService;

import static org.mockito.Mockito.*;

@RunWith(SpringJUnit4ClassRunner.class)
public class RollbackPatchSchedulerTest {

    @Mock
    private PatchRollbackCacheQueue patchRollbackCacheQueue;

    @Mock
    private PatchUpdateResultService patchUpdateResultService;

    @InjectMocks
    @Spy
    private RollbackPatchScheduler rollbackPatchScheduler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void should_NotDeleteAnyServicePatchHistory_When_PollReturnsEmptyList() {
        // Given
        when(patchRollbackCacheQueue.poll()).thenReturn(Lists.newArrayList());
        when(patchRollbackCacheQueue.isEmptyResultCache()).thenReturn(true);
        // When
        rollbackPatchScheduler.cron();
    }
}

/* Ended by AICoder, pid:jf644vcfefp926014be0089a10fbe2737a484026 */