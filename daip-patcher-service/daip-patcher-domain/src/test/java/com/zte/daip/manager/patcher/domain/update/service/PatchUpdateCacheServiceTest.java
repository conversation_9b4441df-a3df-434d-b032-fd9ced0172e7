package com.zte.daip.manager.patcher.domain.update.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.zte.daip.manager.patcher.api.dto.OnePatchUpdateResult;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateResult;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateRequest;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateFinishedCacheApi;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateInfoCacheApi;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateProgressCacheApi;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateResultCacheApi;
import com.zte.daip.manager.patcher.domain.update.listener.PatchInfoCleanLocalCacheSender;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchUpdateCacheServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/16</p>
 * <p>完成日期：2021/4/16</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class PatchUpdateCacheServiceTest {
    @Mock
    private PatchUpdateProgressCacheApi patchUpdateProgressCacheApi;
    @Mock
    private PatchUpdateResultCacheApi patchUpdateResultCacheApi;
    @Mock
    private PatchUpdateFinishedCacheApi patchUpdateFinishedCacheApi;
    @Mock
    private PatchUpdateInfoCacheApi patchUpdateInfoCacheApi;
    @Mock
    private PatchInfoCleanLocalCacheSender patchInfoCleanLocalCacheSender;
    @InjectMocks
    private PatchUpdateCacheService patchUpdateCacheService;
    private static final String clusterId = "0";

    @Test
    public void initUpdateProgress() {
        PatchUpdateResult p1 = mockPatchUpdateResult("*********", false, "error1", "ZDH");
        List<PatchUpdateResult> patchUpdateResults = Lists.newArrayList();
        patchUpdateResults.add(p1);
        PatchUpdateCacheDto patchUpdateCacheDto = mockPatchUpdateCacheDtoWithResults(patchUpdateResults);

        patchUpdateCacheService.initUpdateProgress(clusterId, patchUpdateCacheDto, Sets.newConcurrentHashSet());
        verify(patchUpdateProgressCacheApi, atLeast(1)).updateCache(clusterId,
            JSONObject.toJSONString(patchUpdateCacheDto));
    }

    @Test
    public void queryUpdatePatchProgress() {
        PatchUpdateResult p1 = mockPatchUpdateResult("*********", false, "", "INSTANCE");
        PatchUpdateResult p2 = mockPatchUpdateResult("*********", true, "", "INSTANCE");
        mockPatchUpdateCacheDtoWithResults(Lists.newArrayList(p1, p2));
        PatchUpdateCacheDto patchUpdateCacheDto = patchUpdateCacheService.queryUpdatePatchProgress(clusterId);
        assertThat(patchUpdateCacheDto.getProgress(), is(75));

        mockPatchUpdateCacheDtoWithResults(Lists.newArrayList());
        patchUpdateCacheDto = patchUpdateCacheService.queryUpdatePatchProgress(clusterId);
        assertTrue(patchUpdateCacheDto == null);
    }

    @Test
    public void isAllowUpdatePatch() {
        mockPatchUpdateResultWithTime(System.currentTimeMillis(), 0L, false);
        assertThat(patchUpdateCacheService.isUpdatePatchPermit(clusterId), is(false));

        mockPatchUpdateResultWithTime(System.currentTimeMillis(), System.currentTimeMillis(), true);
        assertThat(patchUpdateCacheService.isUpdatePatchPermit(clusterId), is(true));

        mockPatchUpdateResultWithTime(0L, 0L, false);
        assertThat(patchUpdateCacheService.isUpdatePatchPermit(clusterId), is(true));
    }

    @Test
    public void updatePrgressByHostResult() {
        Mockito.when(patchUpdateProgressCacheApi.queryCacheResult(clusterId)).thenReturn(null);
        patchUpdateCacheService.updateProgressByHostResult(clusterId, Lists.newArrayList(new PatchUpdateResult()));
        verify(patchUpdateProgressCacheApi, atLeast(1)).queryCacheResult(clusterId);

        PatchUpdateResult p1 = mockPatchUpdateResult("*********", false, "error1", "ZDH");
        mockPatchUpdateCacheDtoWithResults(Lists.newArrayList());
        patchUpdateCacheService.updateProgressByHostResult(clusterId, Lists.newArrayList(p1));
        verify(patchUpdateProgressCacheApi, atLeast(1)).queryCacheResult(clusterId);

        List<PatchUpdateResult> patchUpdateResults = Lists.newArrayList();
        patchUpdateResults.add(p1);
        mockPatchUpdateCacheDtoWithResults(patchUpdateResults);
        patchUpdateCacheService.updateProgressByHostResult(clusterId, Lists.newArrayList(p1));
        verify(patchUpdateProgressCacheApi, atLeast(1)).queryCacheResult(clusterId);

        PatchUpdateResult p2 = mockPatchUpdateResult("*********", true, "", "ZDH");
        PatchUpdateResult p3 = mockPatchUpdateResult("*********", true, "", "ZDH");
        mockPatchUpdateCacheDtoWithResults(Lists.newArrayList(p2, p3));
        patchUpdateCacheService.updateProgressByHostResult(clusterId, Lists.newArrayList(p1));
        verify(patchUpdateProgressCacheApi, atLeast(1)).queryCacheResult(clusterId);

        PatchUpdateResult p4 = mockPatchUpdateResult("*********", true, "", "INSTANCE");
        PatchUpdateResult p5 = mockPatchUpdateResult("*********", true, "", "INSTANCE");
        mockPatchUpdateCacheDtoWithResults(Lists.newArrayList(p4, p5));
        patchUpdateCacheService.updateProgressByHostResult(clusterId, Lists.newArrayList(p1));
        verify(patchUpdateProgressCacheApi, atLeast(1)).queryCacheResult(clusterId);

        PatchUpdateResult p7 = mockPatchUpdateResult("*********", true, "", "role");
        PatchUpdateResult p8 = mockPatchUpdateResult("*********", true, "", "role");
        mockPatchUpdateCacheDtoWithResults(Lists.newArrayList(p7, p8));
        patchUpdateCacheService.updateProgressByHostResult(clusterId, Lists.newArrayList(p1));
        verify(patchUpdateProgressCacheApi, atLeast(1)).queryCacheResult(clusterId);
    }

    @Test
    public void updateProgressIsAllFinished() {
        Mockito.when(patchUpdateProgressCacheApi.queryCacheResult(clusterId)).thenReturn(null);
        patchUpdateCacheService.updateProgressFinishedState(clusterId);
        verify(patchUpdateProgressCacheApi, atLeast(1)).queryCacheResult(clusterId);

        PatchUpdateResult p1 = mockPatchUpdateResult("*********", false, "error1", "ZDH");
        List<PatchUpdateResult> patchUpdateResults = Lists.newArrayList();
        patchUpdateResults.add(p1);
        mockPatchUpdateCacheDtoWithResults(patchUpdateResults);
        patchUpdateCacheService.updateProgressFinishedState(clusterId);
        verify(patchUpdateProgressCacheApi, atLeast(1)).queryCacheResult(clusterId);
    }

    @Test
    public void querySuccessPatchResult() {
        Mockito.when(patchUpdateResultCacheApi.pollSuccessPatchUpdateResult(clusterId))
            .thenReturn(Lists.newArrayList());
        List<PatchResult> patchResults = patchUpdateCacheService.pollSuccessPatchResult(clusterId);
        assertThat(CollectionUtils.isEmpty(patchResults), is(true));

        mockPatchResultCache();
        patchResults = patchUpdateCacheService.pollSuccessPatchResult(clusterId);
        assertThat(patchResults.stream().anyMatch(p -> StringUtils.equals(p.getHostIp(), "*********")), is(true));
    }

    @Test
    public void queryFailedPatchResult() {
        Mockito.when(patchUpdateResultCacheApi.pollFailedPatchUpdateResult(clusterId)).thenReturn(Lists.newArrayList());
        List<PatchUpdateResult> pResults = patchUpdateCacheService.pollFailedPatchResult(clusterId);
        assertThat(CollectionUtils.isEmpty(pResults), is(true));

        PatchUpdateResult p1 = mockPatchUpdateResult("*********", false, "error1", "ZDH");
        String patchUpdateResult = JSONObject.toJSONString(p1);
        List<String> pContent = Lists.newArrayList();
        pContent.add(patchUpdateResult);
        Mockito.when(patchUpdateResultCacheApi.pollFailedPatchUpdateResult(clusterId)).thenReturn(pContent);
        List<PatchUpdateResult> patchUpdateResults = patchUpdateCacheService.pollFailedPatchResult(clusterId);
        assertThat(patchUpdateResults.stream().anyMatch(
            p -> p.getHosts().stream().anyMatch(h -> StringUtils.equals(h.getIpAddress(), "*********"))), is(true));
    }

    @Test
    public void putCacheInfo() {

        Mockito.when(patchUpdateProgressCacheApi.queryCacheResult(clusterId)).thenReturn(null);
        PatchUpdateRequest patchUpdateRequest = new PatchUpdateRequest("0", "/opt/ZDH/patch", "ZDH");
        List<PatchUpdateRequest> requests = Lists.newArrayList(patchUpdateRequest);
        Mockito.doNothing().when(patchUpdateInfoCacheApi).putPatchInfo(any(), any());
        Mockito.doNothing().when(patchInfoCleanLocalCacheSender).sendMsgToClearLocalCache( any());

        patchUpdateCacheService.putPatchInfoToCache(requests);
        verify(patchUpdateInfoCacheApi, times(1)).putPatchInfo(any(), any());
    }

    private PatchUpdateResult mockPatchUpdateResult(String ip, boolean isSuccess, String message, String patchType) {
        PatchUpdateResult p1 = new PatchUpdateResult();
        p1.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP030-20201215");
        p1.setPatchType(patchType);
        OnePatchUpdateResult op1 = new OnePatchUpdateResult();
        op1.setInstanceId("hdfs");
        op1.setIpAddress("127.0.0.1");
        op1.setSuccess(true);
        op1.setRoleName("NameNode");
        op1.setPatchHome("/opt/ZDH/patch");
        OnePatchUpdateResult op2 = new OnePatchUpdateResult();
        op2.setInstanceId("hdfs");
        op2.setIpAddress(ip);
        op2.setSuccess(isSuccess);
        op2.setMessage(message);
        op2.setRoleName("DataNode");
        op2.setPatchHome("/opt/ZDH/patch");
        p1.setHosts(Lists.newArrayList(op1, op2));
        return p1;
    }

    private void mockPatchResultCache() {
        PatchResult p1 = new PatchResult();
        p1.setHostIp("127.0.0.1");
        p1.setUpdateSuccess(true);
        String patchResult1 = JSONObject.toJSONString(p1);
        PatchResult p2 = new PatchResult();
        p2.setHostIp("*********");
        p2.setUpdateSuccess(true);
        String patchResult2 = JSONObject.toJSONString(p2);
        Mockito.when(patchUpdateResultCacheApi.pollSuccessPatchUpdateResult(clusterId))
            .thenReturn(Lists.newArrayList(patchResult1, patchResult2));
    }

    private PatchUpdateCacheDto mockPatchUpdateCacheDtoWithResults(List<PatchUpdateResult> patchUpdateResults) {
        PatchUpdateCacheDto patchUpdateCacheDto = new PatchUpdateCacheDto();
        patchUpdateCacheDto.setPatchResults(patchUpdateResults);
        String patchResult = JSONObject.toJSONString(patchUpdateCacheDto);
        Mockito.when(patchUpdateProgressCacheApi.queryCacheResult(clusterId)).thenReturn(patchResult);
        return patchUpdateCacheDto;
    }

    private void mockPatchUpdateResultWithTime(long startTime, long finishTime, boolean isAllFinished) {
        PatchUpdateCacheDto patchUpdateCacheDto = new PatchUpdateCacheDto();
        patchUpdateCacheDto.setStartTime(startTime);
        patchUpdateCacheDto.setFinishTime(finishTime);
        patchUpdateCacheDto.setAllFinished(isAllFinished);
        String patchResult = JSONObject.toJSONString(patchUpdateCacheDto);
        Mockito.when(patchUpdateProgressCacheApi.queryCacheResult(clusterId)).thenReturn(patchResult);
    }
}