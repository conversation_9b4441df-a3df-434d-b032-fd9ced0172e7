package com.zte.daip.manager.patcher.domain.rollback.executor;

import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.manager.patcher.api.dto.PatchHostInfo;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;

/* Started by AICoder, pid:7739c5e1e75b7c41474c0bc000695544de6373b4 */
@RunWith(SpringRunner.class)
public class BigDataTypeRollbackExecutorTest {
    @Mock
    private PublishRollbackMsg publishRollbackMsg;

    @InjectMocks
    private BigDataTypeRollbackExecutor bigDataTypeRollbackExecutor;

    @Test
    public void testExecuteRollbackWithEmptyList() {
        String clusterId = "1";
        List<PatchRollbackHostDto> patchRollbackHostDtos = new ArrayList<>();

        bigDataTypeRollbackExecutor.executeRollback(clusterId, patchRollbackHostDtos);

        verify(publishRollbackMsg).publishBigDataRollbackMsg(eq(clusterId), eq(patchRollbackHostDtos), anyList());
    }

    @Test
    public void testExecuteRollbackWithNonEmptyList() {
        String clusterId = "1";
        List<PatchRollbackHostDto> patchRollbackHostDtos = new ArrayList<>();
        PatchRollbackHostDto dto1 = new PatchRollbackHostDto();
        List<PatchHostInfo> patchHost1 = new ArrayList<>();
        dto1.setPatchName("HDFS-SP001");
        dto1.setServiceName("hdfs");
        patchHost1.add(new PatchHostInfo("127.0.0.1", "host1"));
        dto1.setPatchHostInfoList(patchHost1);
        patchRollbackHostDtos.add(dto1);

        PatchRollbackHostDto dto2 = new PatchRollbackHostDto();
        List<PatchHostInfo> patchHost2 = new ArrayList<>();
        dto2.setPatchName("ZOOKEEPER-SP001");
        dto2.setServiceName("zookeeper");
        patchHost2.add(new PatchHostInfo("*********", "host2"));
        dto2.setPatchHostInfoList(patchHost2);
        patchRollbackHostDtos.add(dto2);

        bigDataTypeRollbackExecutor.executeRollback(clusterId, patchRollbackHostDtos);

        verify(publishRollbackMsg).publishBigDataRollbackMsg(eq(clusterId), eq(patchRollbackHostDtos), anyList());
    }
}

/* Ended by AICoder, pid:7739c5e1e75b7c41474c0bc000695544de6373b4 */