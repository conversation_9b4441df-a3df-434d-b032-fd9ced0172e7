package com.zte.daip.manager.patcher.domain.upload.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatedResultCache;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchValidateBean;
import com.zte.daip.manager.patcher.domain.upload.operate.NormalPatchOperator;
import com.zte.daip.manager.patcher.domain.upload.operate.PatchTypeOperateFactory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@RunWith(SpringRunner.class)
public class PatchFullValidateServiceTest {

    /* Started by AICoder, pid:69c24dea1f09c6414567086b20c1d45444b3e5ee */
@Mock
private PatchValidatedResultCache patchValidatedResultCache;

@Mock
private PatchInfoService patchInfoService;

@Mock
private PatchTypeOperateFactory patchTypeOperateFactory;

@Mock
private ApplicationContext applicationContext;
@Mock
private NormalPatchOperator normalPatchOperator;

@InjectMocks
private PatchFullValidateService patchFullValidateService;

@Test
public void test() {
    // 设置忽略列表
    ReflectionTestUtils.setField(patchFullValidateService, "ignoreList", "VMAX");

    // 创建并初始化测试数据
    Map<PatchValidateBean, PatchUploadResult> patchMap = new HashMap<>();
    PatchValidateBean patchValidateBean = new PatchValidateBean("a", new File("a"));
    PatchUploadResult patchUploadResult = new PatchUploadResult();
    PatchBean patchBean = new PatchBean();
    patchBean.setService("spark");
    patchBean.setPatchName("DAP-SPARK-V20.19.40.R4.B2-SP030-20201215");
    patchBean.setSrcVersion("19");
    patchUploadResult.setPatchBean(patchBean);
    patchUploadResult.setPatchName("DAP-SPARK-V20.19.40.R4.B2-SP030-20201215");
    patchMap.put(patchValidateBean, patchUploadResult);

    // 模拟依赖行为
    when(patchValidatedResultCache.queryResultMapByBatchId(anyString())).thenReturn(patchMap);
    when(patchTypeOperateFactory.getPatchType(any())).thenReturn(normalPatchOperator);
    PatchDetailPo patchDetailPo = new PatchDetailPo();
    patchDetailPo.setPatchName("DAP-SPARK-V20.19.40.R4.B2-SP029-20201215");
    patchDetailPo.setIsFullPatch(1);
    patchDetailPo.setIsContainerPatch(0);
    patchDetailPo.setService("zk");
    patchDetailPo.setBaseVersion("19");
    when(patchInfoService.findByIsFullPatch(1)).thenReturn(Lists.newArrayList(patchDetailPo));
    doNothing().when(patchValidatedResultCache).updateValidateResult(any(), any());
    doNothing().when(applicationContext).publishEvent(any());

    // 执行测试方法
    patchFullValidateService.validateFullPatches("aaa");

    // 验证事件发布次数
    verify(applicationContext, times(1)).publishEvent(any());
}

/* Ended by AICoder, pid:69c24dea1f09c6414567086b20c1d45444b3e5ee */

}