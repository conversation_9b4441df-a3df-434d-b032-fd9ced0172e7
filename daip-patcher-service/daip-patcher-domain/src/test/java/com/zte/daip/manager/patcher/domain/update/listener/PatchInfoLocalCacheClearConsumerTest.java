package com.zte.daip.manager.patcher.domain.update.listener;

import static com.zte.daip.communication.bean.MessageType.MULICAST;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.util.Set;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateInfoCacheApi;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchInfoLocalCacheClearConsumerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2022/10/13</p>
 * <p>完成日期：2022/10/13</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class PatchInfoLocalCacheClearConsumerTest {
    @Mock
    private PatchUpdateInfoCacheApi patchUpdateInfoCacheApi;
    @InjectMocks
    private com.zte.daip.manager.patcher.domain.update.listener.PatchInfoLocalCacheClearConsumer patchInfoLocalCacheClearConsumer;

    @Test
    public void handle() {
        String body = generateRequestMsg();
        patchInfoLocalCacheClearConsumer.handle(body);
        verify(patchUpdateInfoCacheApi, times(1)).validLocalCache(any());
    }

    private String generateRequestMsg() {
        RequestMessageBody requestMessageBody = new RequestMessageBody();
        requestMessageBody.setType(MULICAST.getType());
        Set<String> targetHostsName = Sets.newHashSet();
        Set<String> targetHostIps = Sets.newHashSet();
        targetHostIps.add("********");
        targetHostsName.add("host01");
        requestMessageBody.setBody("");

        return JSONObject.toJSONString(requestMessageBody);
    }
}