package com.zte.daip.manager.patcher.domain.upload.service.listener;

import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatedResultCache;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.event.PatchFullValidateEvent;
import com.zte.daip.manager.patcher.domain.upload.event.PatchValidateEvent;
import com.zte.daip.manager.patcher.domain.upload.service.PatchFullValidateService;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class PatchFullValidateListenerTest {
    @Mock
    private PatchValidatedResultCache patchValidatedResultCache;

    @Mock
    private PatchFullValidateService patchFullValidateService;

    @InjectMocks
    private PatchFullValidateListener patchFullValidateListener;

    @Test
    public void test() {
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        patchUploadResult.setOneProcess(OnePatchProcess.NORMAL_CHECK);
        when(patchValidatedResultCache.queryAllResultByBatchId(anyString()))
            .thenReturn(Lists.newArrayList(patchUploadResult));
        doNothing().when(patchFullValidateService).validateFullPatches(anyString());
        patchFullValidateListener.onApplicationEvent(new PatchFullValidateEvent("111"));
        verify(patchFullValidateService, times(1)).validateFullPatches(anyString());
    }

}