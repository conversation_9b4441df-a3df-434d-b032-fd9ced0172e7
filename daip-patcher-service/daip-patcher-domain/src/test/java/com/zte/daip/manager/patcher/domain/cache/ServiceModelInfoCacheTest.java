package com.zte.daip.manager.patcher.domain.cache;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import org.junit.Before;
import org.junit.Test;

import static junit.framework.TestCase.assertEquals;
import static junit.framework.TestCase.assertTrue;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: ServiceModelInfoCacheTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/23</p>
 * <p>完成日期：2023/3/23</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class ServiceModelInfoCacheTest {

    @InjectMocks
    private ServiceModelInfoCache serviceModelInfoCache;

    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Before
    public void setUp() throws Exception {

        List<ServiceModel> serviceModels = Lists.newArrayList();
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceName("zookeeper");
        serviceModel.setPatchType("service");
        serviceModels.add(serviceModel);
        ServiceModel serviceModel1 = new ServiceModel();
        serviceModel1.setServiceName("kafka");
        serviceModel.setPatchType("service");
        serviceModels.add(serviceModel1);
        when(productModelInfoControllerApi.queryModelsByComponentType(anyString())).thenReturn(serviceModels);
        when(productModelInfoControllerApi.queryByClusterId(anyString())).thenReturn(serviceModels);
    }

    @Test
    public void queryBigDataServices() throws Exception {
        List<String> services = serviceModelInfoCache.queryBigDataService();
        assertTrue(services.contains("kafka") && services.contains("zookeeper"));
    }

    @Test
    public void queryServiceModelByClusterIdAndServiceName() throws Exception {
        ServiceModel serviceModel = serviceModelInfoCache.queryByClusterIdAndServiceName("100000", "zookeeper");
        assertEquals(serviceModel.getPatchType(), "service");
    }

    @Test
    public void queryServicePatchType() throws Exception {
        String type = serviceModelInfoCache.queryServicePatchType("10000", "kafka");
        assertEquals(type, "service");
    }
}