package com.zte.daip.manager.patcher.domain.update.service.constructor;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.ServiceInstance;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class RolePatchSourceConstructorTest {

    @Mock
    private PatchHomeQueryService patchHomeQueryService;
    @Mock
    private ServiceModelInfoCache serviceModelInfoCache;
    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private PatchHistoryService patchHistoryService;

    @InjectMocks
    private RolePatchSourceConstructor rolePatchSourceConstructor;

    @Test
    public void obtainOfflinePatchUpdateInfo() throws DaipBaseException {
        PatchHistory patchHistory = new PatchHistory();
        PatchHistoryKey patchHistoryKey = new PatchHistoryKey();
        patchHistoryKey.setPatchName("SP001");
        patchHistory.setId(patchHistoryKey);
        when(patchHistoryService.queryPatchHistoryInfoByServiceNameAndIp(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(patchHistory));

        PatchServiceParam updateParam = new PatchServiceParam();
        updateParam.setVersion("version");
        updateParam.setServiceName("serviceName");
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceInstanceId("instanceId");
        serviceInstance.setRoles(Lists.newArrayList("roleId"));
        updateParam.setServiceInstances(Lists.newArrayList(serviceInstance));

        rolePatchSourceConstructor.obtainOfflinePatchUpdateInfo(updateParam, "01111", "10.000");
    }
}