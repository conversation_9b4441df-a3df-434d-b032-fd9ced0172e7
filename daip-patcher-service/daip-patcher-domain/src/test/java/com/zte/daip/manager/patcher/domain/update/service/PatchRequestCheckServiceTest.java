package com.zte.daip.manager.patcher.domain.update.service;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

import java.util.Set;

import org.assertj.core.util.Sets;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;

/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchRequestCheckServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/9</p>
 * <p>完成日期：2021/4/9</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchRequestCheckServiceTest {

    @Mock
    private PatchTypeQueryService patchTypeQueryService;
    @Mock
    private DaipEventReporter daipEventReporter;
    @InjectMocks
    private PatchRequestCheckService patchRequestCheckService;

    @Before
    public void setUp() {
        doNothing().when(daipEventReporter).error(anyString(), anyString());
        doNothing().when(daipEventReporter).info(anyString(), anyString());
    }

    @Test
    public void checkUpdateRequest() {
        //
        String clusterId = "0";

        Set<String> serviceIds = Sets.newHashSet();
        serviceIds.add("dap.manager.zookeeper");
        serviceIds.add("dap.manager.hdfs");
        Mockito.when(patchTypeQueryService.queryBigDataServiceIds(clusterId)).thenReturn(serviceIds);

        UpdateRequest updateRequest = mockUpdateRequest(clusterId);

        patchRequestCheckService.checkUpdateRequest(updateRequest);

        Assert.assertEquals(2, updateRequest.getServiceInstanceInfos().size());
    }

    private UpdateRequest mockUpdateRequest(String clusterId) {
        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest.setClusterId(clusterId);

        ServiceInstanceInfo serviceInstanceInfo = new ServiceInstanceInfo();
        serviceInstanceInfo.setServiceName("zookeeper");
        serviceInstanceInfo.setServiceId("dap.manager.zookeeper");
        serviceInstanceInfo.setVersion("V9.4.2");

        updateRequest.setServiceInstanceInfos(Lists.newArrayList(serviceInstanceInfo));
        return updateRequest;
    }

    @Test
    public void checkUpdateRequestWhenHaveZdhBaseService() {
        //
        String clusterId = "0";

        Set<String> serviceIds = Sets.newHashSet();
        serviceIds.add("dap.manager.zookeeper");
        serviceIds.add("dap.manager.hbase");
        Mockito.when(patchTypeQueryService.queryBigDataServiceIds(clusterId)).thenReturn(serviceIds);

        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest.setClusterId(clusterId);

        ServiceInstanceInfo serviceInstanceInfo = new ServiceInstanceInfo();
        serviceInstanceInfo.setServiceName("zookeeper");
        serviceInstanceInfo.setServiceId("dap.manager.zookeeper");
        serviceInstanceInfo.setVersion("V9.4.2");

        ServiceInstanceInfo serviceInstanceInfo2 = new ServiceInstanceInfo();
        serviceInstanceInfo2.setServiceName("zdh");
        serviceInstanceInfo2.setServiceId("");
        serviceInstanceInfo2.setVersion("V9.4.2");

        updateRequest.setServiceInstanceInfos(Lists.newArrayList(serviceInstanceInfo));

        patchRequestCheckService.checkUpdateRequest(updateRequest);

        Assert.assertEquals(2, updateRequest.getServiceInstanceInfos().size());

    }

    @Test
    public void checkUpdateRequestWhenNotHaveZdhBaseService() {
        //
        String clusterId = "0";

        Set<String> serviceIds = Sets.newHashSet();
        serviceIds.add("dap.manager.zookeeper");
        serviceIds.add("dap.manager.hbase");
        Mockito.when(patchTypeQueryService.queryBigDataServiceIds(clusterId)).thenReturn(serviceIds);

        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest.setClusterId(clusterId);

        ServiceInstanceInfo serviceInstanceInfo = new ServiceInstanceInfo();
        serviceInstanceInfo.setServiceName("zookeeper");
        serviceInstanceInfo.setServiceId("dap.manager.zookeeper");
        serviceInstanceInfo.setVersion("V9.4.2");
        serviceInstanceInfo.setIps(Lists.newArrayList("*******", "*******"));

        ServiceInstanceInfo serviceInstanceInfo2 = new ServiceInstanceInfo();
        serviceInstanceInfo2.setServiceName("hbase");
        serviceInstanceInfo2.setServiceId("dap.manager.hbase");
        serviceInstanceInfo2.setVersion("V9.4.2");
        serviceInstanceInfo2.setIps(Lists.newArrayList("*******", "*******"));
        updateRequest.setServiceInstanceInfos(Lists.newArrayList(serviceInstanceInfo, serviceInstanceInfo2));

        patchRequestCheckService.checkUpdateRequest(updateRequest);

        Assert.assertEquals(3, updateRequest.getServiceInstanceInfos().size());
        ServiceInstanceInfo zdhServiceInfo = updateRequest.getServiceInstanceInfos().stream()
            .filter(e -> e.getServiceName().equals("zdh")).findFirst().get();
        Assert.assertEquals(4, zdhServiceInfo.getIps().size());

    }
}