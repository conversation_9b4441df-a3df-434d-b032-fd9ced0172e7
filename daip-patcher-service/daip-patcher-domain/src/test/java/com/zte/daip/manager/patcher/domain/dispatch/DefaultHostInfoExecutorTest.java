package com.zte.daip.manager.patcher.domain.dispatch;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.patcher.domain.dispatch.bean.DispatcherParam;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.util.Set;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: DefaultHostInfoExecutorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/23</p>
 * <p>完成日期：2021/4/23</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class DefaultHostInfoExecutorTest {
    @InjectMocks
    private DefaultHostInfoExecutor defaultHostInfoExecutor;

    private String service = "hdfs";
    private String version = "V20.19.40.R4.B2";
    private String clusterId = "1";
    private String ipAddress = "127.0.0.1";

    @Test
    public void query() {
        DispatcherParam dispatcherParam = getDispatcherParam();

        Set<HostInfo> hostInfos = defaultHostInfoExecutor.query(service, version, dispatcherParam);
        assertThat(CollectionUtils.isEmpty(hostInfos), is(false));
        assertThat(hostInfos.stream().findFirst().orElse(new HostInfo()).getIpAddress(), is(ipAddress));
    }

    private DispatcherParam getDispatcherParam() {
        HostInfo hostInfo = new HostInfo();
        hostInfo.setHostName("h1");
        hostInfo.setIpAddress(ipAddress);

        ClusterProject clusterProject = new ClusterProject();
        clusterProject.setServiceName(service);
        clusterProject.setVersion(version);
        clusterProject.setClusterId(Integer.parseInt(clusterId));

        ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setClusterId(clusterId);
        serviceRoleInfo.setIpAddress(ipAddress);
        serviceRoleInfo.setServiceId("dap.manager.hdfs");
        serviceRoleInfo.setServiceInstanceId(service);
        serviceRoleInfo.setRoleId("dap.manager.hdfs.NameNode");
        serviceRoleInfo.setRoleName("NameNode");

        ServiceModel serviceModel = new ServiceModel();

        serviceModel.setServiceId("dap.manager.hdfs");
        serviceModel.setServiceName(service);

        return new DispatcherParam(Lists.newArrayList(hostInfo), Lists.newArrayList(serviceRoleInfo),
            Lists.newArrayList(clusterProject), Lists.newArrayList(serviceModel), Sets.newHashSet(),
            Maps.newConcurrentMap());

    }

}