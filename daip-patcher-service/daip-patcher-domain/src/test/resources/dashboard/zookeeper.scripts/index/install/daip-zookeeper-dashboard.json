{"annotations": {"list": [{"builtIn": 1, "datasource": "DAIP Prometheus", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "", "editable": true, "gnetId": null, "graphTooltip": 0, "id": 39, "iteration": 1662542578444, "links": [], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "defaultCluster", "value": "defaultCluster"}, "datasource": "DAIP Prometheus", "definition": "label_values(node_uname_info,clusterName)", "description": "集群名称", "error": null, "hide": 0, "includeAll": false, "label": "集群名", "multi": false, "name": "cluster", "options": [], "query": {"query": "label_values(node_uname_info,clusterName)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "zookeeper", "value": "zookeeper"}, "datasource": "DAIP Prometheus", "definition": "label_values(jvm_info{clusterName=~\"$cluster\",roleId=\"dap.manager.zookeeper.HQuorumPeer\"},serviceInstanceId)", "description": "服务名称", "error": null, "hide": 0, "includeAll": false, "label": "实例名", "multi": false, "name": "serviceInstanceId", "options": [], "query": {"query": "label_values(jvm_info{clusterName=~\"$cluster\",roleId=\"dap.manager.zookeeper.HQuorumPeer\"},serviceInstanceId)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "tags": [], "text": ["All"], "value": ["$__all"]}, "datasource": "DAIP Prometheus", "definition": "label_values(jvm_info{clusterName=~\"$cluster\",roleId=\"dap.manager.zookeeper.HQuorumPeer\"},host)", "description": "主机IP", "error": null, "hide": 0, "includeAll": true, "label": "主机IP", "multi": true, "name": "host_ip", "options": [], "query": {"query": "label_values(jvm_info{clusterName=~\"$cluster\",roleId=\"dap.manager.zookeeper.HQuorumPeer\"},host)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}}