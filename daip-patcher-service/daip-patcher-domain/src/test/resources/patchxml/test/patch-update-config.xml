<?xml version="1.0" encoding="UTF-8"?>

<patch>
    <patchName>DAP-AGENT-V20.19.40.R4.B2-SP059-20240928</patchName> <!--不可空,patch的唯一标识-->
    <service>agent</service>  <!--不可空，在安装哪些服务的主机上应用此补丁 -->
    <roles></roles>  <!-- 在安装哪些role的主机上应用此补丁，如为空则所有安装此服务的所有主机 -->
    <createDate>2024-09-23</createDate>
    <description>
        <zh_cn>sssd配置文件改为修改的方式</zh_cn>
        <en_us>Modifying sssd Configuration File</en_us>
    </description>

    <patchDisplayName>
        <zh_cn>DAP-AGENT-V20.19.40.R4.B2-SP059-20240928</zh_cn>
        <en_us>DAP-AGENT-V20.19.40.R4.B2-SP059-20240928</en_us>
    </patchDisplayName>

    <hotpatch>false</hotpatch>  <!-- true|false -->
    <fullpatch>false</fullpatch>

    <srcVersion>V20.19.40.R4.B2</srcVersion><!-- 不可空，补丁应用的版本 -->
    <dependPatch>DAP-AGENT-V20.19.40.R4.B2-SP058-20240630</dependPatch>    <!--当前补丁依赖的补丁-->
    <update>
        <command>
            <sequence>1</sequence>
            <desc>解压agent补丁包</desc>
            <name>unzip -o $AGENT_HOME/service/patch/package/DAP-AGENT-V20.19.40.R4.B2-SP059-20240928 -d $AGENT_HOME;</name>
        </command>
        <command>
            <sequence>2</sequence>
            <desc>删除xml</desc>
            <name>rm -f $AGENT_HOME/patch-update-config.xml</name>
        </command>
        <command>
            <sequence>3</sequence>
            <desc>增加tomcat配置项</desc>
            <name>grep -q "^server.tomcat.connectionTimeout=" ${AGENT_HOME}/service/agent/conf/application.properties || echo "server.tomcat.connectionTimeout=60000" >> ${AGENT_HOME}/service/agent/conf/application.properties</name>
        </command>
        <command>
            <sequence>4</sequence>
            <desc>删除spring 5.3.28系列相关包</desc>
            <name>rm -rf ${AGENT_HOME}/service/agent/lib/dependency/spring-*-5.3.28.jar;exit 0</name>
        </command>
        <command>
            <sequence>5</sequence>
            <desc>删除netty-all-4.1.75.jar相关包</desc>
            <name>rm -rf ${AGENT_HOME}/service/agent/lib/dependency/netty-*-4.1.75.Final.jar;exit 0</name>
        </command>
    </update>
    <rollBack>
    </rollBack>
</patch>
    
