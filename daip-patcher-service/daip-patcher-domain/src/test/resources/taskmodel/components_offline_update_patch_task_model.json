{"modelName": "components_offline_update_patch_task_model", "isDefault": true, "modelNameI18n": "components_offline_update_patch_task_model", "serviceId": "dap.manager.components", "descI18n": "components_offline_update_patch", "resourceRequirement": "service", "version": "default", "steps": [{"order": 1, "definitionRefs": "stop_service", "stage": "stop_cluster", "roleRefs": ["Upgrade_Service"]}, {"order": 2, "definitionRefs": "update_patch", "stage": "upgrade", "roleRefs": ["Upgrade_Service"]}, {"order": 3, "definitionRefs": "start_service", "stage": "start_cluster", "roleRefs": ["Upgrade_Service"]}], "roleOrderDefinitions": [{"roleDefinitionName": "Upgrade_Service", "order": 0, "roleId": null, "roleName": null, "roleType": null, "parallelType": "<PERSON><PERSON><PERSON>"}], "definitions": [{"stepName": "stop_service", "stepNameI18n": "stop_service_label", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": 10, "taskExecutor": {"service": "daip-deployer-svr", "name": "stopClusterProcessor", "param": null}, "extraAttributes": {"triggerSteps": "components_offline_rollback_patch_task_model.start_service", "supportRollBack": "true"}}, {"stepName": "update_patch", "stepNameI18n": "update_patch_label", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": 30, "taskExecutor": {"service": "daip-patcher-svr", "name": "patcherUpdateProcessor", "param": null}, "extraAttributes": {"triggerSteps": "components_offline_rollback_patch_task_model.rollback_patch", "supportRollBack": "true"}}, {"stepName": "start_service", "stepNameI18n": "start_service_label", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": 30, "taskExecutor": {"service": "daip-deployer-svr", "name": "startClusterProcessor", "param": null}, "extraAttributes": {"triggerSteps": "components_offline_rollback_patch_task_model.stop_service", "supportRollBack": "true"}}]}