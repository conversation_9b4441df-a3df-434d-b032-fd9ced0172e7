{"modelName": "hdfs_rolling_restart_task_model", "isDefault": true, "modelNameI18n": "hdfs_rolling_restart_task_model", "serviceId": "dap.manager.hdfs", "descI18n": "hdfs_rolling_restart", "resourceRequirement": "originVersion,service", "version": "V20.23.40.06-SNAPSHOT", "roleOrderDefinitions": [{"roleDefinitionName": "Prepare_Service", "order": 0}, {"roleDefinitionName": "NameNode(StandBy)", "roleId": "dap.manager.hdfs.NameNode", "roleName": "NameNode", "parallelType": "OneByOne", "roleType": "StandBy", "order": 1}, {"roleDefinitionName": "NameNode(Active)", "roleId": "dap.manager.hdfs.NameNode", "roleName": "NameNode", "parallelType": "OneByOne", "roleType": "Active", "order": 2}, {"roleDefinitionName": "zkfc", "roleId": "dap.manager.hdfs.zkfc", "roleName": "zkfc", "parallelType": "OneByOne", "order": 3}, {"roleDefinitionName": "JournalNode", "roleId": "dap.manager.hdfs.JournalNode", "roleName": "JournalNode", "parallelType": "OneByOne", "order": 4}, {"roleDefinitionName": "DataNode", "roleId": "dap.manager.hdfs.DataNode", "roleName": "DataNode", "parallelType": "OneByOne", "order": 5}, {"roleDefinitionName": "Gateway", "roleId": "dap.manager.hdfs.Gateway", "roleName": "Gateway", "parallelType": "OneByOne", "order": 6}, {"roleDefinitionName": "Router", "roleId": "dap.manager.hdfs.Router", "roleName": "Router", "parallelType": "OneByOne", "order": 7}, {"roleDefinitionName": "httpfs", "roleId": "dap.manager.hdfs.httpfs", "roleName": "httpfs", "parallelType": "OneByOne", "order": 8}, {"roleDefinitionName": "NfsServer", "roleId": "dap.manager.hdfs.NfsServer", "roleName": "NfsServer", "parallelType": "OneByOne", "order": 9}, {"roleDefinitionName": "NfsPortmap", "roleId": "dap.manager.hdfs.NfsPortmap", "roleName": "NfsPortmap", "parallelType": "OneByOne", "order": 10}, {"roleDefinitionName": "kms", "roleId": "dap.manager.hdfs.kms", "roleName": "kms", "parallelType": "OneByOne", "order": 11}], "steps": [{"order": 1, "definitionRefs": "inspection_before_upgrade", "stage": "inspection before upgrade", "roleRefs": ["Prepare_Service"]}, {"order": 2, "definitionRefs": "distribute_version", "stage": "distribute version", "roleRefs": ["Prepare_Service"]}, {"order": 3, "definitionRefs": "pre_check", "stage": "upgrade", "roleRefs": ["NameNode(StandBy)", "NameNode(Active)", "DataNode", "JournalNode", "zkfc", "Router", "NfsServer", "kms"]}, {"order": 4, "definitionRefs": "pre_operation", "stage": "upgrade", "roleRefs": ["NameNode(Active)", "NameNode(StandBy)", "JournalNode"]}, {"order": 5, "definitionRefs": "check_after_pre_operation", "stage": "upgrade", "roleRefs": ["NameNode(Active)", "NameNode(StandBy)", "JournalNode"]}, {"order": 6, "definitionRefs": "stop_role", "stage": "upgrade", "roleRefs": ["NameNode(StandBy)", "NameNode(Active)", "DataNode", "JournalNode", "zkfc", "Router", "httpfs", "NfsServer", "NfsPortmap", "kms"]}, {"order": 7, "definitionRefs": "start_role_on_node", "stage": "upgrade", "roleRefs": ["NameNode(StandBy)", "NameNode(Active)", "DataNode", "JournalNode", "zkfc", "Router", "httpfs", "NfsServer", "NfsPortmap", "kms"]}, {"order": 8, "definitionRefs": "dail_test_role", "stage": "upgrade", "roleRefs": ["NameNode(StandBy)", "NameNode(Active)", "DataNode", "JournalNode", "zkfc", "Router", "NfsServer", "kms"]}, {"order": 9, "definitionRefs": "post_operation", "stage": "upgrade", "roleRefs": ["JournalNode"]}, {"order": 10, "definitionRefs": "dail_test_service", "stage": "upgrade", "roleRefs": ["NameNode(StandBy)", "NameNode(Active)", "DataNode", "JournalNode", "zkfc", "Router", "httpfs", "NfsServer", "NfsPortmap", "kms"]}], "definitions": [{"stepName": "distribute_version", "stepNameI18n": "distribute_version_label", "operatorType": "Auto", "stepCategory": "Service", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 30, "taskExecutor": {"service": "daip-deployer-svr", "name": "versionDistributeProcessor"}}, {"stepName": "inspection_before_upgrade", "stepNameI18n": "inspection_before_upgrade_label", "operatorType": "Auto", "StepCategory": "Service", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 20, "taskExecutor": {"service": "daip-inspection-svr", "name": "inspectionSuiteProcessor", "param": "default"}}, {"stepName": "dail_test_service", "stepNameI18n": "dail_test_service_label", "operatorType": "Auto", "StepCategory": "Node", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 5, "taskExecutor": {"service": "agent", "name": "simpleRestProcessor", "param": "/config/v1/rollUpdate/dialingTestService/hdfs"}}, {"stepName": "pre_check", "stepNameI18n": "pre_check_label", "operatorType": "Auto", "StepCategory": "Node", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 5, "taskExecutor": {"service": "agent", "name": "simpleRestProcessor", "param": "/config/v1/rollUpdate/preCheck/hdfs"}}, {"stepName": "pre_operation", "stepNameI18n": "pre_operation_label", "operatorType": "Auto", "StepCategory": "Node", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 10, "taskExecutor": {"service": "agent", "name": "simpleRestProcessor", "param": "/config/v1/rollUpdate/process/hdfs"}}, {"stepName": "check_after_pre_operation", "stepNameI18n": "pre_operation_label", "operatorType": "Auto", "StepCategory": "Node", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 5, "taskExecutor": {"service": "agent", "name": "simpleRestProcessor", "param": "/config/v1/rollUpdate/preCheck/hdfs"}}, {"stepName": "stop_role", "stepNameI18n": "stop_role_label", "operatorType": "Auto", "StepCategory": "Node", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 5, "taskExecutor": {"service": "daip-deployer-svr", "name": "roleStopProcessor"}}, {"stepName": "start_role_on_node", "stepNameI18n": "start_role_on_node_label", "operatorType": "Auto", "StepCategory": "Node", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 10, "taskExecutor": {"service": "daip-deployer-svr", "name": "roleStartProcessor"}}, {"stepName": "dail_test_role", "stepNameI18n": "dail_test_role_label", "operatorType": "Auto", "StepCategory": "Node", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 5, "taskExecutor": {"service": "agent", "name": "simpleRestProcessor", "param": "/config/v1/rollUpdate/dialingTest/hdfs"}}, {"stepName": "post_operation", "stepNameI18n": "post_operation_label", "operatorType": "Auto", "StepCategory": "Node", "skipWhenFailed": false, "retryCntWhenFailed": 3, "timeout": 10, "taskExecutor": {"service": "agent", "name": "simpleRestProcessor", "param": "/config/v1/rollUpdate/process/hdfs"}}]}