{"service": "hdfs", "project": "DAIP", "description": {"zh": "DAIP hdfs服务", "en": "DAIP hdfs service"}, "alarm": [{"code": 1017, "description": {"zh": "NameNode主备切换告警", "en": "NameNode active/standby switch alarm"}, "type": 7, "level": 3, "use_raise_level": 0, "maintenance": {"zh": "集群发生了主备切换：若切换成功完成，则自动解除告警，该告警可在历史中查看。1.网络波动；2.人工检查集群主备情况，以及集群运行情况，恢复正常后，需要手工将告警恢复。", "en": "Active/standby switch occurs in the cluster: If successfully completed switch, the alarm will be automatically cleared, and the alarm can be viewed in the history. 1.The network fluctuates; 2.Manually check the cluster's active and standby conditions, and the cluster operation. After it returns to normal, you need to manually restore the alarm."}}, {"code": 10001, "description": {"zh": "目录文件数告警", "en": "Directory files number alarm"}, "type": 7, "level": 3, "use_raise_level": 0, "maintenance": {"zh": "当前告警目录中存放的文件与目录之和达到了配置数额的百分之九十。1.修改目录存放文件数配额；2.删除告警目录中的文件。", "en": "The sum of the files and directories stored in the current alarm directory has reached 90% of the configured amount. 1.Modify the quota for the number of files stored in the directory; 2.Delete the files in the alarm directory."}}, {"code": 1104, "description": {"zh": "空间系统处于安全模式告警", "en": "Enter the safe mode alarm"}, "type": 7, "level": 3, "use_raise_level": 0, "maintenance": {"zh": "进入安全模式告警。1.可等待集群完成数据收集，系统自动退出安全模式；2.检查是否是手动进入了安全模式：可参考命令hdfs dfsadmin -safemode <enter|leave|get|wait>；3.查看是否有较多台datanode节点出现节点状态异常。", "en": "Enter the safe mode alarm. 1.You can wait for the cluster to complete data collection, and the system automatically exits the safe mode; 2.Check whether the safe mode is manually entered: Refer to the command hdfs dfsadmin -safemode <enter|leave|get|wait>; 3.Check whether many datanode nodes in abnormal node status."}}, {"code": 41200001, "description": {"zh": "Datanode死掉数告警", "en": "datanode dead alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "hdfs集群datanode死掉,查看集群死掉的datanode日志,确认是因为什么原因被杀掉，解决并且重新拉起datanode", "en": "If the datanode of the hdfs cluster is dead, check the log of the dead datanode of the cluster to confirm that it was killed for some reason, solve it and restart the datanode"}}, {"code": 41200002, "description": {"zh": "HDFS服务文件系统当前丢失的块数告警", "en": "missing_blocks alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "HDFS服务文件系统当前丢失的块数告警,当丢失块大于50时候，确认hdfs集群健康情况，确认丢失块原因，是否在复制中，满足最小副本要求", "en": "The number of blocks currently lost in the HDFS service file system is alarmed. When the number of lost blocks is greater than 50, check the health of the hdfs cluster, confirm the reason for the lost blocks, whether they are replicating, and meet the minimum copy requirements."}}, {"code": 41200003, "description": {"zh": "HDFS服务文件系统当前损坏的块数告警", "en": "corrupt_blocks alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "HDFS服务文件系统当前损坏的块数告警,当损坏块大于50时候，确认hdfs集群健康情况，确认块损坏原因，是否可以删除损坏块", "en": "The current number of damaged blocks in the HDFS service file system is alarmed. When the damaged block is greater than 50, check the health of the hdfs cluster, confirm the cause of the block damage, and whether the damaged block can be deleted."}}, {"code": 51201003, "description": {"zh": "Datanode卷故障数告警", "en": "num_failed_volumes alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "datanode卷故障数告警,当data卷故障超过3块，确认坏盘路径，将坏盘取消挂载", "en": "The number of datanode volume failures is alarmed. When the data volume fails more than 3 blocks, confirm the path of the bad disk and unmount the bad disk."}}, {"code": 51200001, "description": {"zh": "NameNode进程每分钟内存回收时间所占百分比告警", "en": "gc_time_millis alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "NameNode进程每分钟内存回收时间告警,内存回收时间所占百分比达到配置的90%之后，分析gc日志确认原因", "en": "The memory recovery time of the NameNode process is alarmed every minute. After the percentage of memory recovery time reaches 90% of the configuration, analyze the gc log to confirm the reason."}}, {"code": 51201004, "description": {"zh": "DataNode进程每分钟内存回收时间所占百分比告警", "en": "gc_time_millis alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "DataNode进程每分钟内存回收时间所占的百分比,内存回收时间所占百分比达到配置的90%之后，分析gc日志确认原因", "en": "The percentage of the memory recovery time of the DataNode process per minute. After the percentage of the memory recovery time reaches 90% of the configuration, analyze the gc log to confirm the reason"}}, {"code": 51202008, "description": {"zh": "JournalNode进程每分钟内存回收时间所占百分比告警", "en": "gc_time_millis alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "JournalNode进程每分钟内存回收时间所占的百分比,内存回收时间所占百分比达到配置的90%之后，分析gc日志确认原因", "en": "he percentage of memory recovery time of ournalNode process per minute. After the percentage of memory recovery time reaches 90% of the configuration, analyze the gc log to confirm the reason"}}, {"code": 41200004, "description": {"zh": "HDFS服务副本个数不足的块数所占百分比告警", "en": "under_replicated_block alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "HDFS服务副本个数不足的块数，达到配置的所占百分比40时候，需要分析hdfs集群健康报告，查看原因", "en": "When the number of blocks with insufficient HDFS service replicas reaches 40% of the configured percentage, you need to analyze the hdfs cluster health report and check the cause."}}, {"code": 51200002, "description": {"zh": "NameNode节点堆内存使用所占百分比告警", "en": "namenode_mem_heap_used alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "NameNode节点堆内存使用百分比,堆内存使用所占百分比达到配置的90%之后，分析堆栈信息确认原因，防止内存溢出", "en": "he heap memory usage percentage of the NameNode node. After the heap memory usage percentage reaches 90% of the configuration, analyze the stack information to confirm the cause and prevent memory overflow.\n"}}, {"code": 51201001, "description": {"zh": "DataNode节点堆内存使用所占百分比告警", "en": "datanode_mem_heap_used alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "DataNode节点堆内存使用百分比,堆内存使用所占百分比达到配置的90%之后，分析堆栈信息确认原因，防止内存溢出", "en": "The percentage of heap memory usage of the ataNode node. After the percentage of heap memory usage reaches 90% of the configuration, analyze the stack information to confirm the cause and prevent memory overflow."}}, {"code": 51202007, "description": {"zh": "JournalNode节点堆内存使用所占百分比告警", "en": "journalnode_mem_heap_used alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "JournalNode节点堆内存使用百分比,堆内存使用所占百分比达到配置的90%之后，分析堆栈信息确认原因，防止内存溢出", "en": "The heap memory usage percentage of the JournalNode node. After the heap memory usage percentage reaches 90% of the configuration, analyze the stack information to confirm the cause and prevent memory overflow"}}, {"code": 51201002, "description": {"zh": "DataNode DFS空间使用率告警", "en": "dfs_used_percent alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "DataNode DFS空间使用率超过配置的95%之后，分析存储情况，确认是否需要删除冗余数据或者扩容", "en": "After the DataNode DFS space usage exceeds 95% of the configuration, analyze the storage situation and confirm whether it is necessary to delete redundant data or expand the capacity."}}, {"code": 51200003, "description": {"zh": "HDFS服务RPC在最近的交互中平均操作时间告警", "en": "rpc_processing_time_avg_time alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "HDFS服务RPC在最近的交互中平均操作时间", "en": "rpc_processing_time_avg_time"}}, {"code": 51200004, "description": {"zh": "HDFS服务RPC在交互中平均等待时间告警", "en": "rpc_rpc_queue_time_avg_time alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "HDFS服务RPC在交互中平均等待时间", "en": "rpc_rpc_queue_time_avg_time"}}, {"code": 51202006, "description": {"zh": "JournalNode节点一分钟内同步延时中位数告警", "en": "journal_node_syncs60s50th_percentile_latency_micros alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "JournalNode节点一分钟内同步延时中位数", "en": "journal_node_syncs60s50th_percentile_latency_micros"}}, {"code": 51202005, "description": {"zh": "JournalNode节点一分钟内最大同步延时告警", "en": "journal_node_ssyncs60s99th_percentile_latency_micros alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "JournalNode节点一分钟内最大同步延时", "en": "journal_node_ssyncs60s99th_percentile_latency_micros "}}, {"code": 51202003, "description": {"zh": "JournalNode节点五分钟内同步延时中位数告警", "en": "journal_node_syncs300s50th_percentile_latency_micros alarm"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "JournalNode节点五分钟内同步延时中位数", "en": "journal_node_syncs300s50th_percentile_latency_micros"}}, {"code": 51202004, "description": {"zh": "JournalNode节点五分钟内最大同步延时告警", "en": "journal_node_syncs300s90th_percentile_latency_micros"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "JournalNode节点五分钟内最大同步延时", "en": "journal_node_syncs300s90th_percentile_latency_micros"}}, {"code": 51202003, "description": {"zh": "JournalNode节点一小时内同步延时中位数告警", "en": "journal_node_syncs3600s50th_percentile_latency_micros"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "JournalNode节点一小时内同步延时中位数", "en": "journal_node_syncs3600s50th_percentile_latency_micros"}}, {"code": 51202002, "description": {"zh": "JournalNode节点一小时内最大同步延时告警", "en": "journal_node_syncs3600s90th_percentile_latency_micros"}, "type": 7, "level": 3, "use_raise_level": 1, "maintenance": {"zh": "JournalNode节点一小时内最大同步延时", "en": "journal_node_syncs3600s90th_percentile_latency_micros"}}]}