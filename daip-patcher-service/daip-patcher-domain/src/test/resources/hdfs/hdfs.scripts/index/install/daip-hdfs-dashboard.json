{"annotations": {"list": [{"builtIn": 1, "datasource": "DAIP Prometheus", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 9, "isExport": false, "iteration": 1680834013181, "links": [], "panels": [{"collapsed": false, "datasource": "DAIP Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 30, "panels": [], "title": "概况", "type": "row"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 80}, {"color": "red", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 0, "y": 1}, "id": 10, "interval": "30s", "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "fs_namesystem_capacity_used{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}*100/fs_namesystem_capacity_total{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "HDFS文件系统使用率", "type": "gauge"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 4, "y": 1}, "id": 8, "interval": "30s", "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "fs_namesystem_capacity_total{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "HDFS文件系统总容量", "type": "stat"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 7, "y": 1}, "id": 42, "interval": "30s", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "fs_namesystem_num_live_data_nodes{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "活着的datanode数", "type": "stat"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 10, "y": 1}, "id": 18, "interval": "30s", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "fs_namesystem_blocks_total{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "HDFS文件系统当前存储的总块数", "type": "stat"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 13, "y": 1}, "id": 6, "interval": "30s", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "fs_namesystem_capacity_remaining{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "HDFS文件系统剩余容量", "type": "stat"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "custom.width"}, {"id": "displayName", "value": "坏盘数量"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, {"id": "custom.displayMode", "value": "color-background"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 1}, "id": 210, "interval": "30s", "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_num_failed_volumes{clusterName=~\"$cluster\",context=\"FSDatasetState\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "datanode卷故障数", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["hostname", "Value"]}}}], "type": "table"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1000000000000}, {"color": "dark-red", "value": 60000000000000}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 4, "y": 4}, "id": 2, "interval": "30s", "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "fs_namesystem_capacity_used{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "HDFS文件系统已使用容量", "type": "stat"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 7, "y": 4}, "id": 44, "interval": "30s", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "fs_namesystem_num_dead_data_nodes{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "死掉的datanode数", "type": "stat"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 10, "y": 4}, "id": 16, "interval": "30s", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "fs_namesystem_files_total{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "HDFS文件系统当前存储的总文件数", "type": "stat"}, {"datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 13, "y": 4}, "id": 14, "interval": "30s", "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.5", "targets": [{"exemplar": true, "expr": "fs_namesystem_capacity_used_non_dfs{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "HDFS文件系统非HDFS容量", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 7}, "hiddenSeries": false, "id": 48, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_pending_deletion_blocks{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": false, "interval": "", "legendFormat": "{{nsid}}-HDFS文件系统等待删除的块数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统等待删除的块数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2843", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2844", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 7}, "hiddenSeries": false, "id": 22, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_corrupt_blocks{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": false, "interval": "", "legendFormat": "{{nsid}}-HDFS文件系统当前损坏的块数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统当前损坏的块数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:511", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:512", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 7}, "hiddenSeries": false, "id": 20, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_missing_blocks{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": false, "interval": "", "legendFormat": "{{nsid}}-HDFS文件系统当前丢失的块数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统当前丢失的块数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:587", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:588", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 12}, "hiddenSeries": false, "id": 24, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_under_replicated_blocks{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": false, "interval": "", "legendFormat": "{{nsid}}-副本个数不足的块数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "副本个数不足的块数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:269", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:270", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 12}, "hiddenSeries": false, "id": 50, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_excess_blocks{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": false, "interval": "", "legendFormat": "{{nsid}}-HDFS文件系统超标的副本数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统超标的副本数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:663", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:664", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 12}, "hiddenSeries": false, "id": 46, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_pending_replication_blocks{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": false, "interval": "", "legendFormat": "{{nsid}}-HDFS文件系统等待备份的块数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统等待备份的块数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:739", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:740", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 17}, "hiddenSeries": false, "id": 12, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_rpc_queue_time_avg_time{clusterName=~\"$cluster\",servername=\"ClientNamenodeProtocol\",context=\"rpc\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RPC在交互中平均等待时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:815", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:816", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 17}, "hiddenSeries": false, "id": 26, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_rpc_processing_time_avg_time{clusterName=~\"$cluster\",servername=\"ClientNamenodeProtocol\",context=\"rpc\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RPC在最近的交互中平均操作时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:361", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:362", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 17}, "hiddenSeries": false, "id": 32, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_call_queue_length{clusterName=~\"$cluster\",servername=\"ClientNamenodeProtocol\",context=\"rpc\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RPC调用队列长度", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:891", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:892", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "mbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 22}, "hiddenSeries": false, "id": 34, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_committed_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "主节点堆内存已提交", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:983", "decimals": null, "format": "mbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:984", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "mbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 22}, "hiddenSeries": false, "id": 36, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_used_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "主节点堆内存已使用", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1059", "format": "mbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1060", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 22}, "hiddenSeries": false, "id": 28, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_num_open_connections{clusterName=~\"$cluster\",servername=\"ClientNamenodeProtocol\",context=\"rpc\",hostname=~\"$hostname\"}", "instant": false, "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RPC连接打开的数目", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1135", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1136", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "mbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 27}, "hiddenSeries": false, "id": 38, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_max_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "主节点JVM最大的可用堆内存大小", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1227", "format": "mbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1228", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 27}, "hiddenSeries": false, "id": 40, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_used_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}*100/jvm_metrics_mem_heap_max_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\"}", "instant": false, "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "主节点堆内存使用百分比", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1303", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:1304", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "DAIP Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 32}, "id": 4, "panels": [], "title": "NameNode", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 33}, "hiddenSeries": false, "id": 188, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_missing_blocks{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}+fs_namesystem_corrupt_blocks{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "interval": "", "legendFormat": "{{nsid}}-文件系统损坏和丢失的数据块个数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "文件系统损坏和丢失的数据块个数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1024", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1025", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 33}, "hiddenSeries": false, "id": 180, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_under_replicated_blocks{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}*100/fs_namesystem_blocks_total{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "interval": "", "legendFormat": "{{nsid}}-副本个数不足的块数所占的百分比", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "副本个数不足的块数所占的百分比", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:460", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:461", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 33}, "hiddenSeries": false, "id": 190, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_blocks_total{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}/fs_namesystem_files_total{clusterName=~\"$cluster\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": false, "interval": "", "legendFormat": "{{nsid}}-文件平均数据块数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "文件平均数据块数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:856", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:857", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 33}, "hiddenSeries": false, "id": 54, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "fs_namesystem_transactions_since_last_checkpoint{clusterName=~\"$cluster\",context=\"dfs\",hastate=\"active\",nsid=~\"$nameserviceId\"}", "instant": false, "interval": "", "legendFormat": "{{nsid}}-自上个检查点开始的事务个数", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "自上个检查点开始的事务个数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1699", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1700", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 38}, "hiddenSeries": false, "id": 162, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_received_bytes{clusterName=~\"$cluster\",servername=\"ClientNamenodeProtocol\",context=\"rpc\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点RPC服务收到字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1395", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1396", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 38}, "hiddenSeries": false, "id": 164, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_sent_bytes{clusterName=~\"$cluster\",servername=\"ClientNamenodeProtocol\",context=\"rpc\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点RPC服务发送字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1471", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1472", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 38}, "hiddenSeries": false, "id": 166, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_received_bytes{clusterName=~\"$cluster\",servername=\"ClientNamenodeProtocol\",context=\"rpc\",hostname=~\"$hostname\"}/60", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点RPC服务每秒收到字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1547", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1548", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 38}, "hiddenSeries": false, "id": 168, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_sent_bytes{clusterName=~\"$cluster\",servername=\"ClientNamenodeProtocol\",context=\"rpc\",hostname=~\"$hostname\"}/60", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点RPC服务每秒发送字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1623", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1624", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 43}, "hiddenSeries": false, "id": 64, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_time_millis{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}*100/60000", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "每分钟内存GC时间所占的百分比", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2093", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:2094", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 43}, "hiddenSeries": false, "id": 52, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "namenode_transactions_num_ops{clusterName=~\"$cluster\",processname=\"NameNode\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "事务的个数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1775", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1776", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 43}, "hiddenSeries": false, "id": 220, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "namenode_syncs_avg_time{clusterName=~\"$cluster\",processname=\"NameNode\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "将操作同步为编辑日志的平均时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1941", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1942", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 43}, "hiddenSeries": false, "id": 218, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "namenode_syncs_num_ops{clusterName=~\"$cluster\",context=\"dfs\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "将操作同步为编辑日志的次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2017", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2018", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 48}, "hiddenSeries": false, "id": 170, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_new{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统新启动的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2415", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2416", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 48}, "hiddenSeries": false, "id": 58, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "maxDataPoints": null, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_time_millis{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC花费的时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2169", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2170", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 48}, "hiddenSeries": false, "id": 56, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_count{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "JVM进行GC的次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2245", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2246", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 48}, "hiddenSeries": false, "id": 62, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_count_concurrent_mark_sweep{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "FULL GC次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2919", "decimals": 0, "format": "none", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2920", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 53}, "hiddenSeries": false, "id": 178, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_waiting{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统等待中的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:384", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:385", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 53}, "hiddenSeries": false, "id": 172, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_blocked{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统阻塞的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2491", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2492", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 53}, "hiddenSeries": false, "id": 60, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_time_millis_concurrent_mark_sweep{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "FULL GC花费的时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:232", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:233", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 53}, "hiddenSeries": false, "id": 66, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_num_active_rpc_handler{clusterName=~\"$cluster\",servername=\"ClientNamenodeProtocol\",context=\"rpc\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "namenode中活跃的rpc handler数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:308", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:309", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 58}, "hiddenSeries": false, "id": 186, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_total_extra_sleep_time{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "JVM暂停的时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:688", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:689", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 58}, "hiddenSeries": false, "id": 174, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_runnable{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统处于运行状态的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:536", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:537", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 58}, "hiddenSeries": false, "id": 176, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_terminated{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HDFS文件系统已经终止的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:612", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:613", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 58}, "hiddenSeries": false, "id": 182, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_num_warn_threshold_exceeded{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "JVM暂停时间超过告警线(缺省为10s)次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:932", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:933", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 63}, "hiddenSeries": false, "id": 184, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_num_info_threshold_exceeded{clusterName=~\"$cluster\",context=\"jvm\",processname=\"NameNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "JVM暂停时间超过预警线(缺省为1s)次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:780", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:781", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "DAIP Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 68}, "id": 68, "panels": [], "title": "DataNode", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 69}, "hiddenSeries": false, "id": 76, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_used_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}*100/jvm_metrics_mem_heap_max_m{context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode节点内存使用率", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3119", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:3120", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 69}, "hiddenSeries": false, "id": 108, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_dfs_used{clusterName=~\"$cluster\",context=\"FSDatasetState\",hostname=~\"$hostname\"}*100/(org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_dfs_used{clusterName=~\"$cluster\",context=\"FSDatasetState\",hostname=~\"$hostname\"}+org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_remaining{clusterName=~\"$cluster\",context=\"FSDatasetState\",hostname=~\"$hostname\"})", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "dfs空间使用率", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3027", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:3028", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 69}, "hiddenSeries": false, "id": 110, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_capacity{clusterName=~\"$cluster\",context=\"FSDatasetState\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "dfs总空间大小", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1660", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1661", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 69}, "hiddenSeries": false, "id": 104, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_dfs_used{clusterName=~\"$cluster\",context=\"FSDatasetState\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "dfs已使用空间大小", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1508", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1509", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 74}, "hiddenSeries": false, "id": 106, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_remaining{clusterName=~\"$cluster\",context=\"FSDatasetState\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "dfs剩余可用空间大小", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1584", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1585", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "mbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 74}, "hiddenSeries": false, "id": 70, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_committed_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode节点堆内存(已提交)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1206", "format": "mbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1207", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "mbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 74}, "hiddenSeries": false, "id": 72, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_used_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode节点堆内存(已经使用)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1282", "format": "mbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1283", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "mbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 74}, "hiddenSeries": false, "id": 74, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_max_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode节点堆内存(JVM最大的可用堆内存大小)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1358", "format": "mbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1359", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 79}, "hiddenSeries": false, "id": 80, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_bytes_read{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点读出字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1752", "decimals": null, "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1753", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 79}, "hiddenSeries": false, "id": 82, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_bytes_written{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点写入字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1828", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1829", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 79}, "hiddenSeries": false, "id": 84, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_bytes_read{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}/60", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点每秒读出字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1904", "format": "binBps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1905", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 79}, "hiddenSeries": false, "id": 86, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_bytes_written{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}/60", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点每秒写入字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1980", "format": "binBps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1981", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 84}, "hiddenSeries": false, "id": 88, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_data_node_active_xceivers_count{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode传输数据连接数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2072", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2073", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 84}, "hiddenSeries": false, "id": 90, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_blocks_read{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "从该datanode节点上读取的块数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2148", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2149", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 84}, "hiddenSeries": false, "id": 92, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_blocks_written{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "向该datanode节点上写入的块数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2224", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2225", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 84}, "hiddenSeries": false, "id": 102, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_num_active_rpc_handler{clusterName=~\"$cluster\",servername=\"ClientDatanodeProtocolService\",context=\"rpc\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode中活跃的rpc handler数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2300", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2301", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 89}, "hiddenSeries": false, "id": 98, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_read_block_op_num_ops{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "每秒读数据块的次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:348", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:349", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 89}, "hiddenSeries": false, "id": 100, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_write_block_op_num_ops{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "每秒写数据块的次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:424", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:425", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 89}, "hiddenSeries": false, "id": 94, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_read_block_op_avg_time{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "读数据块的平均时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:500", "decimals": null, "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:501", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 89}, "hiddenSeries": false, "id": 96, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "datanode_write_block_op_avg_time{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "写数据块的平均时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:576", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:577", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 94}, "hiddenSeries": false, "id": 112, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_count{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "JVM进行GC的次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:652", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:653", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 94}, "hiddenSeries": false, "id": 114, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_time_millis{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC花费的时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:728", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:729", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 94}, "hiddenSeries": false, "id": 116, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_count_concurrent_mark_sweep{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode的FULL GC次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:804", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:805", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 94}, "hiddenSeries": false, "id": 118, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_time_millis_concurrent_mark_sweep{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode的FULL GC时长", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:880", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:881", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 99}, "hiddenSeries": false, "id": 192, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_new{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode新启动的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:956", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:957", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 99}, "hiddenSeries": false, "id": 194, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_blocked{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode阻塞的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1080", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1081", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 99}, "hiddenSeries": false, "id": 196, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_runnable{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",sessionid=\"null\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode处于运行状态的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1156", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1157", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 99}, "hiddenSeries": false, "id": 198, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_terminated{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",sessionid=\"null\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode已经终止的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1232", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1233", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 104}, "hiddenSeries": false, "id": 200, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_threads_waiting{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",sessionid=\"null\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode等待中的线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1308", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1309", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 104}, "hiddenSeries": false, "id": 204, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_num_info_threshold_exceeded{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode进程JVM暂停时间超过预警线(缺省为1s)次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1384", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1385", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 104}, "hiddenSeries": false, "id": 202, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_num_warn_threshold_exceeded{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode进程JVM暂停时间超过告警线(缺省为10s)次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1460", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1461", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 104}, "hiddenSeries": false, "id": 206, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_total_extra_sleep_time{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "datanode进程JVM暂停的时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1536", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1537", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 109}, "hiddenSeries": false, "id": 208, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_time_millis{clusterName=~\"$cluster\",context=\"jvm\",processname=\"DataNode\",hostname=~\"$hostname\"}*100/60000", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "每分钟内存GC时间所占的百分比", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1612", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:1613", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "DAIP Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 114}, "id": 120, "panels": [], "title": "JournalNode", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 115}, "hiddenSeries": false, "id": 122, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_received_bytes{clusterName=~\"$cluster\",servername=\"QJournalProtocolService\",context=\"rpc\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点RPC服务收到字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1720", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1721", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 115}, "hiddenSeries": false, "id": 124, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_sent_bytes{clusterName=~\"$cluster\",servername=\"QJournalProtocolService\",context=\"rpc\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点RPC服务发送字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1796", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1797", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 115}, "hiddenSeries": false, "id": 126, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_received_bytes{clusterName=~\"$cluster\",servername=\"QJournalProtocolService\",context=\"rpc\",hostname=~\"$hostname\"}/60", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点RPC服务每秒收到字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1872", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1873", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 115}, "hiddenSeries": false, "id": 128, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "rpc_sent_bytes{clusterName=~\"$cluster\",servername=\"QJournalProtocolService\",context=\"rpc\",hostname=~\"$hostname\"}/60", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点RPC服务每秒发送字节数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1948", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1949", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 120}, "hiddenSeries": false, "id": 130, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_count{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "JVM进行GC的次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2024", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2025", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 120}, "hiddenSeries": false, "id": 132, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_time_millis{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC花费的时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2100", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2101", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 120}, "hiddenSeries": false, "id": 134, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_time_millis{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}*100/60000", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "每分钟内存GC时间所占的百分比", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2176", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:2177", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 120}, "hiddenSeries": false, "id": 136, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_time_millis_concurrent_mark_sweep{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "FULL GC花费的时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2252", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2253", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 125}, "hiddenSeries": false, "id": 138, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_gc_count_concurrent_mark_sweep{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "FULL GC次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2328", "decimals": -4, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2329", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "mbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 125}, "hiddenSeries": false, "id": 140, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_committed_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "journalnode节点堆内存(已提交)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:428", "format": "mbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:429", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "mbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 125}, "hiddenSeries": false, "id": 142, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_used_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "journalnode节点堆内存(已经使用)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:578", "format": "mbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:579", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "mbytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 125}, "hiddenSeries": false, "id": 144, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_max_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "journalnode节点堆内存(JVM最大的可用堆内存大小)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:654", "format": "mbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:655", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 130}, "hiddenSeries": false, "id": 146, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "jvm_metrics_mem_heap_used_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}*100/jvm_metrics_mem_heap_max_m{clusterName=~\"$cluster\",context=\"jvm\",processname=\"JournalNode\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "journalnode节点内存使用率", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:730", "format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"$$hashKey": "object:731", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 130}, "hiddenSeries": false, "id": 148, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "journal_node_syncs60s50th_percentile_latency_micros{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "一分钟内同步延时中位数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:806", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:807", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 130}, "hiddenSeries": false, "id": 150, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "journal_node_syncs60s99th_percentile_latency_micros{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "一分钟内最大同步延时", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:882", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:883", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 130}, "hiddenSeries": false, "id": 152, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "journal_node_syncs300s50th_percentile_latency_micros{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "五分钟内同步延时中位数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:958", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:959", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 135}, "hiddenSeries": false, "id": 154, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "journal_node_syncs300s90th_percentile_latency_micros{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "五分钟内最大同步延时", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1034", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1035", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 135}, "hiddenSeries": false, "id": 156, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "journal_node_syncs3600s50th_percentile_latency_micros{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "一小时内同步延时中位数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1110", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1111", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {"unit": "ms"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 135}, "hiddenSeries": false, "id": 158, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "journal_node_syncs3600s90th_percentile_latency_micros{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "一小时内最大同步延时", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1186", "format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1187", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "DAIP Prometheus", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 135}, "hiddenSeries": false, "id": 160, "interval": "30s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "journal_node_current_lag_txns{clusterName=~\"$cluster\",context=\"dfs\",hostname=~\"$hostname\"}", "interval": "", "legendFormat": "{{hostname}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "事务延迟数量", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1262", "decimals": null, "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1263", "decimals": null, "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "30s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "VMAXCluster", "value": "VMAXCluster"}, "datasource": "DAIP Prometheus", "definition": "label_values(node_uname_info, clusterName)", "description": "集群ID", "error": null, "hide": 0, "includeAll": false, "label": "集群名", "multi": false, "name": "cluster", "options": [], "query": {"query": "label_values(node_uname_info, clusterName)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "tags": [], "text": ["All"], "value": ["$__all"]}, "datasource": "DAIP Prometheus", "definition": "label_values(jvm_metrics_mem_heap_committed_m{serviceInstanceId=\"hdfs\", clusterName=~\"$cluster\"}, hostname)", "description": "主机名", "error": null, "hide": 0, "includeAll": true, "label": "主机名", "multi": true, "name": "hostname", "options": [], "query": {"query": "label_values(jvm_metrics_mem_heap_committed_m{serviceInstanceId=\"hdfs\", clusterName=~\"$cluster\"}, hostname)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "ns1", "value": "ns1"}, "datasource": null, "definition": "label_values(fs_namesystem_capacity_total{clusterName=~\"$cluster\",serviceInstanceId=\"hdfs\"},nsid)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "", "multi": false, "name": "nameserviceId", "options": [], "query": {"query": "label_values(fs_namesystem_capacity_total{clusterName=~\"$cluster\",serviceInstanceId=\"hdfs\"},nsid)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "hdfs", "uid": "hdfs-dashboard", "version": 42}