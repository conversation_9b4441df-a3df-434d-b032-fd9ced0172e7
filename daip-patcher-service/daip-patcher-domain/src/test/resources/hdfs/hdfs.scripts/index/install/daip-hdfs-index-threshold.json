[{"sampleName": "hdfs_1", "expr": "fs_namesystem_num_dead_data_nodes{#CLUSTERID#,#SERVICEINSTANCEID#,hastate=\"active\"}", "description": {"zh": "死掉的datanode数", "en": "DeadNodes"}, "unit": "个", "category": "service", "roleName": "", "alarmThreshold": {"alarmCode": 41200001, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 3, "value": "0"}]}}, {"sampleName": "hdfs_2", "expr": "sum by(clusterId,serviceInstanceId)(fs_namesystem_missing_blocks{#CLUSTERID#,#SERVICEINSTANCEID#,hastate=\"active\",context=\"dfs\"})", "description": {"zh": "HDFS服务文件系统当前丢失的块数", "en": "missing_blocks"}, "unit": "块", "category": "service", "roleName": "", "alarmThreshold": {"alarmCode": 41200002, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "50"}, {"level": 2, "value": "30"}, {"level": 3, "value": "10"}, {"level": 4, "value": "1"}]}}, {"sampleName": "hdfs_3", "expr": "sum by(clusterId,serviceInstanceId)(fs_namesystem_corrupt_blocks{#CLUSTERID#,#SERVICEINSTANCEID#,hastate=\"active\",context=\"dfs\"})", "description": {"zh": "HDFS服务文件系统当前损坏的块数", "en": "corrupt_blocks"}, "unit": "块", "category": "service", "roleName": "", "alarmThreshold": {"alarmCode": 41200003, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "50"}, {"level": 2, "value": "30"}, {"level": 3, "value": "10"}, {"level": 4, "value": "1"}]}}, {"sampleName": "hdfs_4", "expr": "org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_num_failed_volumes{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"FSDatasetState\"}", "description": {"zh": "datanode卷故障数", "en": "num_failed_volumes"}, "unit": "个", "category": "role", "roleName": "DataNode", "alarmThreshold": {"alarmCode": 51201003, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "6"}, {"level": 2, "value": "4"}, {"level": 3, "value": "2"}, {"level": 4, "value": "1"}]}}, {"sampleName": "hdfs_5", "expr": "(jvm_metrics_gc_time_millis{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"jvm\",processname=\"NameNode\"})*100/60000", "description": {"zh": "NameNode进程每分钟内存回收时间所占的百分比", "en": "gc_time_millis"}, "unit": "%", "category": "role", "roleName": "NameNode", "alarmThreshold": {"alarmCode": 51200001, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "90"}, {"level": 2, "value": "80"}, {"level": 3, "value": "70"}, {"level": 4, "value": "60"}]}}, {"sampleName": "hdfs_6", "expr": "(jvm_metrics_gc_time_millis{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"jvm\",processname=\"DataNode\"})*100/60000", "description": {"zh": "DataNode进程每分钟内存回收时间所占的百分比", "en": "gc_time_millis"}, "unit": "%", "category": "role", "roleName": "DataNode", "alarmThreshold": {"alarmCode": 51201004, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "90"}, {"level": 2, "value": "80"}, {"level": 3, "value": "70"}, {"level": 4, "value": "60"}]}}, {"sampleName": "hdfs_7", "expr": "(jvm_metrics_gc_time_millis{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"jvm\",processname=\"JournalNode\"})*100/60000", "description": {"zh": "JournalNode进程每分钟内存回收时间所占的百分比", "en": "gc_time_millis"}, "unit": "%", "category": "role", "roleName": "JournalNode", "alarmThreshold": {"alarmCode": 51202008, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "90"}, {"level": 2, "value": "80"}, {"level": 3, "value": "70"}, {"level": 4, "value": "60"}]}}, {"sampleName": "hdfs_8", "expr": "(sum by(clusterId,serviceInstanceId)(fs_namesystem_under_replicated_blocks{#CLUSTERID#,#SERVICEINSTANCEID#,hastate=\"active\"}))*100/(sum by(clusterId,serviceInstanceId)(fs_namesystem_blocks_total{#CLUSTERID#,#SERVICEINSTANCEID#,hastate=\"active\"}))", "description": {"zh": "HDFS服务副本个数不足的块数所占的百分比", "en": "under_replicated_block"}, "unit": "%", "category": "service", "roleName": "", "alarmThreshold": {"alarmCode": 41200004, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "40"}, {"level": 2, "value": "30"}, {"level": 3, "value": "20"}, {"level": 4, "value": "10"}]}}, {"sampleName": "hdfs_9", "expr": "jvm_metrics_mem_heap_used_m{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"jvm\",processname=\"NameNode\"}*100/jvm_metrics_mem_heap_max_m{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"jvm\",processname=\"NameNode\"}", "description": {"zh": "NameNode节点堆内存使用百分比", "en": "namenode_mem_heap_used"}, "unit": "%", "category": "role", "roleName": "NameNode", "alarmThreshold": {"alarmCode": 51200002, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "90"}, {"level": 2, "value": "80"}, {"level": 3, "value": "70"}, {"level": 4, "value": "60"}]}}, {"sampleName": "hdfs_10", "expr": "jvm_metrics_mem_heap_used_m{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"jvm\",processname=\"DataNode\"}*100/jvm_metrics_mem_heap_max_m{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"jvm\",processname=\"DataNode\"}", "description": {"zh": "DataNode节点堆内存使用百分比", "en": "datanode_mem_heap_used"}, "unit": "%", "category": "role", "roleName": "DataNode", "alarmThreshold": {"alarmCode": 51201001, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "90"}, {"level": 2, "value": "80"}, {"level": 3, "value": "70"}, {"level": 4, "value": "60"}]}}, {"sampleName": "hdfs_11", "expr": "jvm_metrics_mem_heap_used_m{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"jvm\",processname=\"JournalNode\"}*100/jvm_metrics_mem_heap_max_m{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"jvm\",processname=\"JournalNode\"}", "description": {"zh": "JournalNode节点堆内存使用百分比", "en": "journalnode_mem_heap_used"}, "unit": "%", "category": "role", "roleName": "JournalNode", "alarmThreshold": {"alarmCode": 51202007, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "90"}, {"level": 2, "value": "80"}, {"level": 3, "value": "70"}, {"level": 4, "value": "60"}]}}, {"sampleName": "hdfs_12", "expr": "org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_dfs_used{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"FSDatasetState\"}*100/(org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_dfs_used{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"FSDatasetState\"}+org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_remaining{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"FSDatasetState\"})", "description": {"zh": "DataNode DFS空间使用率", "en": "dfs_used_percent"}, "unit": "%", "category": "role", "roleName": "DataNode", "alarmThreshold": {"alarmCode": 51201002, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "95"}, {"level": 2, "value": "90"}, {"level": 3, "value": "85"}, {"level": 4, "value": "80"}]}}, {"sampleName": "hdfs_13", "expr": "rpc_rpc_processing_time_avg_time{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,servername=\"ClientNamenodeProtocol\",context=\"rpc\"}", "description": {"zh": "HDFS服务RPC在最近的交互中平均操作时间", "en": "rpc_processing_time_avg_time"}, "unit": "ms", "category": "role", "roleName": "NameNode", "alarmThreshold": {"alarmCode": 51200003, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "25"}, {"level": 2, "value": "20"}, {"level": 3, "value": "15"}, {"level": 4, "value": "10"}]}}, {"sampleName": "hdfs_14", "expr": "rpc_rpc_queue_time_avg_time{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,servername=\"ClientNamenodeProtocol\",context=\"rpc\"}", "description": {"zh": "HDFS服务RPC在交互中平均等待时间", "en": "rpc_rpc_queue_time_avg_time"}, "unit": "ms", "category": "role", "roleName": "NameNode", "alarmThreshold": {"alarmCode": 51200004, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "25"}, {"level": 2, "value": "20"}, {"level": 3, "value": "15"}, {"level": 4, "value": "10"}]}}, {"sampleName": "hdfs_15", "expr": "journal_node_syncs60s50th_percentile_latency_micros{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"dfs\"}", "description": {"zh": "JournalNode节点一分钟内同步延时中位数", "en": "journal_node_syncs60s50th_percentile_latency_micros"}, "unit": "ms", "category": "role", "roleName": "JournalNode", "alarmThreshold": {"alarmCode": 51202006, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "300000"}, {"level": 2, "value": "240000"}, {"level": 3, "value": "180000"}, {"level": 4, "value": "120000"}]}}, {"sampleName": "hdfs_16", "expr": "journal_node_syncs60s99th_percentile_latency_micros{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"dfs\"}", "description": {"zh": "JournalNode节点一分钟内最大同步延时", "en": "journal_node_ssyncs60s99th_percentile_latency_micros"}, "unit": "ms", "category": "role", "roleName": "JournalNode", "alarmThreshold": {"alarmCode": 51202005, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "300000"}, {"level": 2, "value": "240000"}, {"level": 3, "value": "180000"}, {"level": 4, "value": "120000"}]}}, {"sampleName": "hdfs_17", "expr": "journal_node_syncs300s50th_percentile_latency_micros{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"dfs\"}", "description": {"zh": "JournalNode节点五分钟内同步延时中位数", "en": "journal_node_syncs300s50th_percentile_latency_micros"}, "unit": "ms", "category": "role", "roleName": "JournalNode", "alarmThreshold": {"alarmCode": 51202003, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "300000"}, {"level": 2, "value": "240000"}, {"level": 3, "value": "180000"}, {"level": 4, "value": "120000"}]}}, {"sampleName": "hdfs_18", "expr": "journal_node_syncs300s90th_percentile_latency_micros{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"dfs\"}", "description": {"zh": "JournalNode节点五分钟内最大同步延时", "en": "journal_node_syncs300s90th_percentile_latency_micros"}, "unit": "ms", "category": "role", "roleName": "JournalNode", "alarmThreshold": {"alarmCode": 51202004, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "300000"}, {"level": 2, "value": "240000"}, {"level": 3, "value": "180000"}, {"level": 4, "value": "120000"}]}}, {"sampleName": "hdfs_19", "expr": "journal_node_syncs3600s50th_percentile_latency_micros{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"dfs\"}", "description": {"zh": "JournalNode节点一小时内同步延时中位数", "en": "journal_node_syncs3600s50th_percentile_latency_micros"}, "unit": "ms", "category": "role", "roleName": "JournalNode", "alarmThreshold": {"alarmCode": 51202003, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "300000"}, {"level": 2, "value": "240000"}, {"level": 3, "value": "180000"}, {"level": 4, "value": "120000"}]}}, {"sampleName": "hdfs_20", "expr": "journal_node_syncs3600s90th_percentile_latency_micros{#CLUSTERID#,#SERVICEINSTANCEID#,#ROLEID#,context=\"dfs\"}", "description": {"zh": "JournalNode节点一小时内最大同步延时", "en": "journal_node_syncs3600s90th_percentile_latency_micros"}, "unit": "ms", "category": "role", "roleName": "JournalNode", "alarmThreshold": {"alarmCode": 51202002, "isTriggerAlarm": true, "operator": "gt", "threshold": [{"level": 1, "value": "300000"}, {"level": 2, "value": "240000"}, {"level": 3, "value": "180000"}, {"level": 4, "value": "120000"}]}}]