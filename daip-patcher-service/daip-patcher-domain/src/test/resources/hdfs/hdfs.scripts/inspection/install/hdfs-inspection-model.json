[{"inspectionItemId": "hdfs11", "itemNameI18nLabel": "dapmanager_inspection_hdfs_service_health", "level": "ERROR", "category": "SYSTEM", "roleName": "", "descriptionI18nLabel": "dapmanager_inspection_hdfs_service_health_desc", "existFaq": false, "order": 11, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "StatusInspector", "algorithm": "serviceStatus", "expression": "", "thresholdAlgorithm": "in", "thresholdExpression": "Good", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs41", "itemNameI18nLabel": "dapmanager_inspection_hdfs_namenode_role_health", "level": "ERROR", "category": "NODE", "roleName": "NameNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_namenode_role_health_desc", "existFaq": false, "order": 41, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "StatusInspector", "algorithm": "roleStatus", "expression": "", "thresholdAlgorithm": "in", "thresholdExpression": "Started", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs42", "itemNameI18nLabel": "dapmanager_inspection_hdfs_datanode_role_health", "level": "ERROR", "category": "NODE", "roleName": "DataNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_datanode_role_health_desc", "existFaq": false, "order": 42, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "StatusInspector", "algorithm": "roleStatus", "expression": "", "thresholdAlgorithm": "in", "thresholdExpression": "Started", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs43", "itemNameI18nLabel": "dapmanager_inspection_hdfs_journalnode_role_health", "level": "ERROR", "category": "NODE", "roleName": "JournalNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_journalnode_role_health_desc", "existFaq": false, "order": 43, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "StatusInspector", "algorithm": "roleStatus", "expression": "", "thresholdAlgorithm": "in", "thresholdExpression": "Started", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs44", "itemNameI18nLabel": "dapmanager_inspection_hdfs_httpfs_role_health", "level": "ERROR", "category": "NODE", "roleName": "httpfs", "descriptionI18nLabel": "dapmanager_inspection_hdfs_httpfs_role_health_desc", "existFaq": false, "order": 44, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "StatusInspector", "algorithm": "roleStatus", "expression": "", "thresholdAlgorithm": "in", "thresholdExpression": "Started", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs45", "itemNameI18nLabel": "dapmanager_inspection_hdfs_zkfc_role_health", "level": "ERROR", "category": "NODE", "roleName": "zkfc", "descriptionI18nLabel": "dapmanager_inspection_hdfs_zkfc_role_health_desc", "existFaq": false, "order": 45, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "StatusInspector", "algorithm": "roleStatus", "expression": "", "thresholdAlgorithm": "in", "thresholdExpression": "Started", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs46", "itemNameI18nLabel": "dapmanager_inspection_hdfs_kms_role_health", "level": "ERROR", "category": "NODE", "roleName": "kms", "descriptionI18nLabel": "dapmanager_inspection_hdfs_kms_role_health_desc", "existFaq": false, "order": 46, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "StatusInspector", "algorithm": "roleStatus", "expression": "", "thresholdAlgorithm": "in", "thresholdExpression": "Started", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs61", "itemNameI18nLabel": "dapmanager_inspection_hdfs_namenode_multi_process", "level": "WARN", "category": "NODE", "roleName": "NameNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_namenode_multi_process_desc", "existFaq": false, "order": 61, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "AgentInspector", "algorithm": "shell", "expression": "ps -ef | grep NameNode | grep Xms | grep -v grep | wc -l", "thresholdAlgorithm": "gt", "thresholdExpression": "1", "unit": ""}}, {"inspectionItemId": "hdfs62", "itemNameI18nLabel": "dapmanager_inspection_hdfs_datanode_multi_process", "level": "WARN", "category": "NODE", "roleName": "DataNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_datanode_multi_process_desc", "existFaq": false, "order": 62, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "AgentInspector", "algorithm": "shell", "expression": "ps -ef | grep DataNode | grep Xms | grep -v grep | wc -l", "thresholdAlgorithm": "gt", "thresholdExpression": "1", "unit": ""}}, {"inspectionItemId": "hdfs63", "itemNameI18nLabel": "dapmanager_inspection_hdfs_journalnode_multi_process", "level": "WARN", "category": "NODE", "roleName": "JournalNode", "descriptionI18nLabel": "da<PERSON>anager_inspection_hdfs_journalnode_multi_process_desc", "existFaq": false, "order": 63, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "AgentInspector", "algorithm": "shell", "expression": "ps -ef | grep JournalNode | grep Xms | grep -v grep | wc -l", "thresholdAlgorithm": "gt", "thresholdExpression": "1", "unit": ""}}, {"inspectionItemId": "hdfs64", "itemNameI18nLabel": "dapmanager_inspection_hdfs_httpfs_multi_process", "level": "WARN", "category": "NODE", "roleName": "httpfs", "descriptionI18nLabel": "dapmanager_inspection_hdfs_httpfs_multi_process_desc", "existFaq": false, "order": 64, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "AgentInspector", "algorithm": "shell", "expression": "ps -ef | grep httpfs | grep Xms | grep -v grep | wc -l", "thresholdAlgorithm": "gt", "thresholdExpression": "1", "unit": ""}}, {"inspectionItemId": "hdfs65", "itemNameI18nLabel": "da<PERSON>anager_inspection_hdfs_zkfc_multi_process", "level": "WARN", "category": "NODE", "roleName": "zkfc", "descriptionI18nLabel": "da<PERSON>anager_inspection_hdfs_zkfc_multi_process_desc", "existFaq": false, "order": 65, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "AgentInspector", "algorithm": "shell", "expression": "ps -ef | grep DFSZKFailoverController | grep Xmx | grep -v grep | wc -l", "thresholdAlgorithm": "gt", "thresholdExpression": "1", "unit": ""}}, {"inspectionItemId": "hdfs66", "itemNameI18nLabel": "dapmanager_inspection_hdfs_kms_multi_process", "level": "WARN", "category": "NODE", "roleName": "kms", "descriptionI18nLabel": "dapmanager_inspection_hdfs_kms_multi_process_desc", "existFaq": false, "order": 66, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "AgentInspector", "algorithm": "shell", "expression": "ps -ef | grep kms | grep Xms | grep -v grep | wc -l", "thresholdAlgorithm": "gt", "thresholdExpression": "1", "unit": ""}}, {"inspectionItemId": "hdfs111", "itemNameI18nLabel": "dapmanager_inspection_hdfs_namenode_memory_usage", "level": "WARN", "category": "NODE", "roleName": "NameNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_namenode_memory_usage_desc", "existFaq": false, "order": 111, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "jvm_metrics_mem_heap_used_m{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#}*100/jvm_metrics_mem_heap_max_m{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#}", "thresholdAlgorithm": "gt", "thresholdExpression": "80", "unit": "%"}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs112", "itemNameI18nLabel": "dapmanager_inspection_hdfs_datanode_memory_usage", "level": "WARN", "category": "NODE", "roleName": "DataNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_datanode_memory_usage_desc", "existFaq": false, "order": 112, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "jvm_metrics_mem_heap_used_m{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#}*100/jvm_metrics_mem_heap_max_m{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#}", "thresholdAlgorithm": "gt", "thresholdExpression": "80", "unit": "%"}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs113", "itemNameI18nLabel": "dapmanager_inspection_hdfs_journalnode_memory_usage", "level": "WARN", "category": "NODE", "roleName": "JournalNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_journalnode_memory_usage_desc", "existFaq": false, "order": 113, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "jvm_metrics_mem_heap_used_m{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#}*100/jvm_metrics_mem_heap_max_m{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#}", "thresholdAlgorithm": "gt", "thresholdExpression": "80", "unit": "%"}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs1061", "itemNameI18nLabel": "dapmanager_inspection_hdfs_trash_count", "level": "WARN", "category": "SYSTEM", "roleName": "NameNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_trash_count_desc", "existFaq": false, "order": 1061, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "AgentInspector", "algorithm": "shell", "expression": "sh $ZDH_HOME/hdfs/manager/lib/inspect/hdfs_recycle_files_count.sh", "thresholdAlgorithm": "gt", "thresholdExpression": "10000000", "unit": ""}}, {"inspectionItemId": "hdfs1071", "itemNameI18nLabel": "dapmanager_inspection_hdfs_missing_files_count", "level": "WARN", "category": "SYSTEM", "roleName": "NameNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_missing_files_count_desc", "existFaq": false, "order": 1071, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "fs_namesystem_missing_blocks{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#,hastate=\"active\"}", "thresholdAlgorithm": "gt", "thresholdExpression": "0", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs1081", "itemNameI18nLabel": "dapmanager_inspection_hdfs_corrupt_files_count", "level": "WARN", "category": "SYSTEM", "roleName": "NameNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_corrupt_files_count_desc", "existFaq": false, "order": 1081, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "fs_namesystem_corrupt_blocks{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#,hastate=\"active\"}", "thresholdAlgorithm": "gt", "thresholdExpression": "0", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs10101", "itemNameI18nLabel": "dapmanager_inspection_hdfs_namenode_rpc_processing_time", "level": "WARN", "category": "SYSTEM", "roleName": "NameNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_namenode_rpc_processing_time_desc", "existFaq": false, "order": 10101, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "rpc_rpc_processing_time_avg_time{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#,servername=\"ClientNamenodeProtocol\"}", "thresholdAlgorithm": "gt", "thresholdExpression": "20", "unit": "ms"}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs10111", "itemNameI18nLabel": "da<PERSON>anager_inspection_hdfs_namenode_rpc_waiting_time", "level": "WARN", "category": "SYSTEM", "roleName": "NameNode", "descriptionI18nLabel": "da<PERSON>anager_inspection_hdfs_namenode_rpc_waiting_time_desc", "existFaq": false, "order": 10111, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "rpc_rpc_queue_time_avg_time{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#,servername=\"ClientNamenodeProtocol\"}", "thresholdAlgorithm": "gt", "thresholdExpression": "20", "unit": "ms"}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs10131", "itemNameI18nLabel": "da<PERSON>anager_inspection_hdfs_datanode_dfs_uneven", "level": "WARN", "category": "NODE", "roleName": "DataNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_datanode_dfs_uneven_desc", "existFaq": false, "order": 10131, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "AgentInspector", "algorithm": "shell", "expression": "sh $ZDH_HOME/hdfs/manager/lib/inspect/hdfs_node_size_uneven_percent.sh", "thresholdAlgorithm": "gt", "thresholdExpression": "10", "unit": ""}}, {"inspectionItemId": "hdfs10171", "itemNameI18nLabel": "dapmanager_inspection_hdfs_dead_datanode_count", "level": "WARN", "category": "SYSTEM", "roleName": "DataNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_dead_datanode_count_desc", "existFaq": false, "order": 10171, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "fs_namesystem_num_dead_data_nodes{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,hastate=\"active\"}", "thresholdAlgorithm": "gt", "thresholdExpression": "0", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs10181", "itemNameI18nLabel": "dapmanager_inspection_hdfs_under_replication_block_percent", "level": "WARN", "category": "SYSTEM", "roleName": "NameNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_under_replication_block_percent_desc", "existFaq": false, "order": 10181, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "fs_namesystem_under_replicated_blocks{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#,hastate=\"active\"}*100/fs_namesystem_blocks_total{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#,hastate=\"active\"}", "thresholdAlgorithm": "gt", "thresholdExpression": "20", "unit": "%"}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs10281", "itemNameI18nLabel": "dapmanager_inspection_hdfs_file_count", "level": "WARN", "category": "SYSTEM", "roleName": "NameNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_file_count_desc", "existFaq": false, "order": 10281, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "fs_namesystem_files_total{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#,hastate=\"active\"}", "thresholdAlgorithm": "gt", "thresholdExpression": "100000000", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs10291", "itemNameI18nLabel": "dapmanager_inspection_hdfs_datanode_failed_storage", "level": "WARN", "category": "NODE", "roleName": "DataNode", "descriptionI18nLabel": "dapmanager_inspection_hdfs_datanode_failed_storage_desc", "existFaq": false, "order": 10291, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "org_apache_hadoop_hdfs_server_datanode_fsdataset_impl_fs_dataset_impl_num_failed_volumes{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,#ROLEID#,context=\"FSDatasetState\"}", "thresholdAlgorithm": "gt", "thresholdExpression": "0", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs_inspection_114", "itemNameI18nLabel": "dapmanager_inspection_hdfs_config_consistence_check", "level": "ERROR", "category": "NODE", "roleName": "", "descriptionI18nLabel": "dapmanager_inspection_hdfs_config_consistence_check_desc", "existFaq": false, "order": 114, "scene": "Daily", "suiteType": "ConfigConsistence", "visible": false, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "ConfigInspector", "algorithm": "config<PERSON>onsist<PERSON><PERSON><PERSON><PERSON>"}}, {"inspectionItemId": "hdfs_inspection_115", "itemNameI18nLabel": "fs_namesystem_highest_priority_low_redundancy_replicated_blocks", "level": "WARN", "category": "SYSTEM", "roleName": "NameNode", "descriptionI18nLabel": "fs_namesystem_highest_priority_low_redundancy_replicated_blocks_desc", "existFaq": false, "order": 115, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "IndexInspector", "algorithm": "promql", "expression": "fs_namesystem_highest_priority_low_redundancy_replicated_blocks{#CLUSTERID#,#HOST#,#SERVICEINSTANCEID#,hastate=\"active\"}", "thresholdAlgorithm": "gt", "thresholdExpression": "0", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}, {"inspectionItemId": "hdfs_inspection_117", "itemNameI18nLabel": "dapmanager_inspection_hdfs_dependentservice_health", "level": "ERROR", "category": "SYSTEM", "roleName": "", "descriptionI18nLabel": "dapmanager_inspection_hdfs_dependentservice_health_desc", "existFaq": false, "order": 12, "scene": "Daily", "suiteType": "default", "visible": true, "groupName": "", "groupNameI18nLabel": "", "canFix": false, "inspectorConfigure": {"type": "StatusInspector", "algorithm": "dependentServiceStatus", "expression": "", "thresholdAlgorithm": "in", "thresholdExpression": "Good", "unit": ""}, "fixerConfigure": {"fixerName": "", "fixStrategy": "", "fixAlgorithm": "", "fixExpression": ""}}]