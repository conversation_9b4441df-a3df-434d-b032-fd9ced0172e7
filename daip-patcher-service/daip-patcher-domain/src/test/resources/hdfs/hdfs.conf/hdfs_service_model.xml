<?xml version="1.0" encoding="UTF-8"?>

<service>
    <serviceId>dap.manager.hdfs</serviceId>
    <serviceName>hdfs</serviceName>
    <componentType>dap.manager.common.bigdata</componentType>
    <operations>
        startService,stopService,uninstallService,hdfs_init,deployConfig,downloadConfig,downloadClientPackage
    </operations>
    <heartBeatReportType>JMX</heartBeatReportType>
    <serviceShowTabs>0,1,2,5,11,16,18,19,20</serviceShowTabs>
    <serviceInstallType>singleService</serviceInstallType>
    <descriptions-zh>Hadoop 分布式文件系统 (HDFS) 是 Hadoop 应用程序使用的主要存储系统。HDFS创建多个数据块副本并将它们分布在整个群集的计算主机上，以启用可靠且极其快速的计算功能，当单节点部署时无需依赖Zookeeper服务，否则，依赖Zookeeper服务。</descriptions-zh>
    <descriptions-en>The Apache Hadoop software library is a framework that allows for the distributed processing of large data sets across clusters of computers using simple programming models. It is designed to scale up from single servers to thousands of machines, each offering local computation and storage(requires Zookeeper).</descriptions-en>
    <displayOrder>120</displayOrder>
    <role>
        <roleId>dap.manager.hdfs.NameNode</roleId>
        <roleName>NameNode</roleName>
        <abbreviation>NN</abbreviation>
        <roleType>master</roleType>
        <heartBeatUrl>
            http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat
        </heartBeatUrl>
        <pidFile>
            <file>/home/<USER>/hdfs/pids/hadoop-hdfs-namenode.pid</file>
        </pidFile>
        <roleShowTabs>0,1,2,3,20</roleShowTabs>
        <rule>
            <name>number</name>
            <type>regex</type>
            <value>^[1-9]\d*$</value>
        </rule>
        <rule>
            <name>dependRoleNameNodeNumber</name>
            <type>dap.manager.hdfs.JournalNode,dap.manager.hdfs.SecondaryNameNode</type>
            <value>2</value>
        </rule>
        <rule>
            <name>attachRole</name>
            <type>dap.manager.hdfs.zkfc</type>
            <value>2</value><!-- 控制联动:NN个数大于等于此值时,zkfc个数自动与NN联动 -->
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat</collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.SecondaryNameNode</roleId>
        <roleName>SecondaryNameNode</roleName>
        <roleType>unique</roleType>
        <heartBeatUrl>
            http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat
        </heartBeatUrl>
        <pidFile>
            <file>/home/<USER>/hdfs/pids/hadoop-hdfs-secondarynamenode.pid</file>
        </pidFile>
        <roleShowTabs>1,3,20</roleShowTabs>
        <rule>
            <name>number</name>
            <type>regex</type>
            <value>^[1]$</value>
        </rule>
        <rule>
            <name>dependRoleSingleNameNode</name>
            <type>dap.manager.hdfs.NameNode</type>
            <value>1</value>
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat</collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.DataNode</roleId>
        <roleName>DataNode</roleName>
        <abbreviation>DN</abbreviation>
        <roleType>slave</roleType>
        <heartBeatUrl>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat
        </heartBeatUrl>
        <pidFile>
            <file>/home/<USER>/hdfs/pids/hadoop-hdfs-datanode.pid</file>
        </pidFile>
        <roleShowTabs>0,1,2,3,20</roleShowTabs>
        <rule>
            <name>number</name>
            <type>regex</type>
            <value>^[1-9]\d*$</value>
        </rule>
        <rule>
            <name>dependService</name>
            <type>unlimited</type>
            <value>dap.manager.hdfs.NameNode</value>
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat</collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.JournalNode</roleId>
        <roleName>JournalNode</roleName>
        <abbreviation>JN</abbreviation>
        <roleType>halt3</roleType>
        <heartBeatUrl>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat
        </heartBeatUrl>
        <pidFile>
            <file>/home/<USER>/hdfs/pids/hadoop-hdfs-journalnode.pid</file>
        </pidFile>
        <roleShowTabs>0,1,2,3,20</roleShowTabs>
        <rule>
            <name>dependServiceForJournalNodeRule</name>
            <type>unlimited</type>
            <value>dap.manager.hdfs.NameNode</value>
        </rule>
        <rule>
            <name>number</name>
            <type>regex</type>
            <value>^[3579]|([1-9]+[13579])$</value>
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat</collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.Router</roleId>
        <roleName>Router</roleName>
        <abbreviation>R</abbreviation>
        <roleType>no_limit</roleType>
        <heartBeatUrl>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat
        </heartBeatUrl>
        <pidFile>
            <file>/home/<USER>/hdfs/pids/hadoop-hdfs-router.pid</file>
        </pidFile>
        <roleShowTabs>0,1,2,3,20</roleShowTabs>
        <rule>
            <name>number</name>
            <type>regex</type>
            <value>^[1-9]\d*$</value>
        </rule>
        <rule>
            <name>dependService</name>
            <type>unlimited</type>
            <value>dap.manager.hdfs.NameNode</value>
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat</collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.zkfc</roleId>
        <roleName>zkfc</roleName>
        <abbreviation>ZKFC</abbreviation>
        <roleType>disable</roleType>
        <heartBeatUrl>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat
        </heartBeatUrl>
        <pidFile>
            <file>/home/<USER>/hdfs/pids/hadoop-hdfs-zkfc.pid</file>
        </pidFile>
        <roleShowTabs></roleShowTabs>
        <rule>
            <name>number</name>
            <type>regex</type>
            <value>^[1-9]\d*$</value>
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat</collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.httpfs</roleId>
        <roleName>httpfs</roleName>
        <abbreviation>HFS</abbreviation>
        <roleType>no_limit</roleType>
        <heartBeatUrl>http://${HOST}:${PORT}/webhdfs/jmx?user.name=hdfs</heartBeatUrl>
        <pidFile>
            <file>/home/<USER>/hdfs/pids/httpfs.pid</file>
        </pidFile>
        <roleShowTabs></roleShowTabs>
        <rule>
            <name>dependService</name>
            <type>unlimited</type>
            <value>dap.manager.hdfs.NameNode</value>
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/webhdfs/jmx?user.name=hdfs</collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.kms</roleId>
        <roleName>kms</roleName>
        <abbreviation>KMS</abbreviation>
        <roleType>no_limit</roleType>
        <heartBeatUrl>http://${HOST}:${PORT}/kms/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat&amp;user.name=hdfs</heartBeatUrl>
        <pidFile>
            <file>/home/<USER>/kms.pid</file>
        </pidFile>
        <roleShowTabs></roleShowTabs>
        <rule>
            <name>dependService</name>
            <type>unlimited</type>
            <value>dap.manager.hdfs.NameNode</value>
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/kms/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat&amp;user.name=hdfs</collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.NfsServer</roleId>
        <roleName>NfsServer</roleName>
        <abbreviation>NS</abbreviation>
        <roleType>no_limit</roleType>
        <heartBeatUrl>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat</heartBeatUrl>
        <pidFile>
            <file>/home/<USER>/hdfs/pids/hadoop-hdfs-nfsserver.pid</file>
        </pidFile>
        <roleShowTabs>0,1,2,3,20</roleShowTabs>
        <rule>
            <name>number</name>
            <type>regex</type>
            <value>^[1-9]\d*$</value>
        </rule>
        <rule>
            <name>BOTHROLE</name>
            <type>dap.manager.hdfs.NfsPortmap</type>
        </rule>
        <rule>
            <name>dependService</name>
            <type>unlimited</type>
            <value>dap.manager.hdfs.NameNode</value>
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/jmx?qry=Hadoop:service=HeartBeatService,name=Heartbeat</collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.NfsPortmap</roleId>
        <roleName>NfsPortmap</roleName>
        <abbreviation>NP</abbreviation>
        <roleType>disable</roleType>
        <pidFile>
            <file>/home/<USER>/hdfs/pids/hadoop-hdfs-nfsportmap.pid</file>
        </pidFile>
        <roleShowTabs>0,1,2,3,20</roleShowTabs>
        <rule>
            <name>number</name>
            <type>regex</type>
            <value>^[1-9]\d*$</value>
        </rule>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleStatusCalcType>PID</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters></collectionParameters>
        </statusCollection>
    </role>
    <role>
        <roleId>dap.manager.hdfs.Gateway</roleId>
        <roleName>Gateway</roleName>
        <abbreviation>GW</abbreviation>
        <roleType>no_limit</roleType>
        <roleStatusCalcType>N/A</roleStatusCalcType>
    </role>
</service>
