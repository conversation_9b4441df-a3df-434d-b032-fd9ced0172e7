<?xml version="1.0" encoding="utf-8"?>
<patch> 
  <patchName>DAP-KAFKA-V20.22.40.09-schema-09CP05-20231031</patchName>  
  <!--不可空,patch的唯一标识，与补丁名需要保持一致-->  
  <service>kafka</service>  
  <!--不可空，在安装该service主机上应用此补丁 -->  
  <roles></roles>  
  <!-- 可以为空，在安装该role的主机上应用此补丁。如为空，则所有安装此服务的所有主机-->  
  <createDate>2023-10-31</createDate>  
  <!-- 不可空，补丁创建时间，格式yyyy-mm-dd -->  
  <description>
    <zh_cn>1.Kafka支持topic积压以及生产消费速率指标 2.Kafka支持topic积压告警 3.Kafka指标告警阈值修改 4.部分指标支持主机筛选以及去除不合理阈值显示</zh_cn>
    <en_us>1.Ka<PERSON>ka supports metric for topic lag,produce rate and consume rate 2.<PERSON><PERSON><PERSON> supports topic lag alarm 3.Modify the alert threshold for Kafka metrics 4.Some indicators support host filtering and removal of unreasonable threshold display</en_us>
  </description>  
  <!-- 当前补丁功能描述-->  
  <hotpatch>true</hotpatch>  
  <!-- 是否热补丁 true|false -->  
  <srcVersion>V20.22.40.09</srcVersion>  
  <!-- 不可空，补丁应用的版本 -->  
  <dependPatch>DAP-KAFKA-V20.22.40.09-schema-09CP01-20230228</dependPatch>
  <!--当前补丁依赖的补丁，无依赖则为空-->  
  <update>
    <!--升级补丁步骤，默认为空，调用补丁容器的默认升级实现流程-->  
	
    <!--
	如果自定义升级补丁的每一步流程，需要配置如下的格式的一个或者多个command。 
	sequence ：升级流程执行顺序，必填     
	desc：升级流程执行步骤描述，必填    
	name：填写相对补丁容器中bin目录的脚本，也可以填写绝对路径的脚本， 必填 -->
  </update>  
  <rollBack>
    <!--回退补丁步骤，默认为空，调用补丁容器的默认回退实现流程-->  
	
    <!--
	如果自定义回退补丁的每一步流程，需要配置如下的格式的一个或者多个command。 
	sequence ：升级流程执行顺序，必填     
	desc：升级流程执行步骤描述，必填    
	name：填写相对补丁容器中bin目录的脚本，也可以填写绝对路径的脚本， 必填
   	<command> 
      <sequence>1</sequence>  
      <desc>回退升级补丁步骤1</desc>  
      <name>recover1.sh</name>   
    </command>
	<command> 
      <sequence>2</sequence>  
      <desc>回退升级补丁步骤2</desc>  
      <name>recover2.sh</name>   
    </command> 
	-->  
  </rollBack> 
</patch>
