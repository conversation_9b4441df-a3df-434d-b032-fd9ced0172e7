<?xml version="1.0" encoding="UTF-8"?>
<service>
    <serviceId>default</serviceId>
    <step>
        <operatorType>install</operatorType>
        <stepName>resource_service</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.ResourceServiceExecutor
        </classPath>
        <roleType>host</roleType>
        <order>16</order>
        <shellParameter></shellParameter>
    </step>
    <step>
        <operatorType>install</operatorType>
        <stepName>expand_service</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.DeployServiceExecutor
        </classPath>
        <roleType>host</roleType>
        <order>20</order>
        <shellParameter>serviceInfo</shellParameter>
    </step>
    <step>
        <operatorType>install</operatorType>
        <stepName>service_params</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.ServiceParamsExecutor
        </classPath>
        <roleType>host</roleType>
        <order>40</order>
        <shellParameter></shellParameter>
    </step>
    <step>
        <operatorType>install</operatorType>
        <stepName>mini_config</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.MiniConfigExecutor
        </classPath>
        <roleType>host</roleType>
        <order>50</order>
        <shellParameter></shellParameter>
    </step>

    <!-- 服务卸载 -->
    <step>
        <operatorType>uninstall</operatorType>
        <stepName>uninstall_service</stepName>
        <classPath>
            com.zte.ums.dap.manager.deploys.service.uninstall.executors.UninstallServiceMiniConfigExecutor
        </classPath>
        <roleType>host</roleType>
        <order>200</order>
        <shellParameter></shellParameter>
    </step>

    <step>
        <operatorType>uninstall</operatorType>
        <stepName>uninstall_unknown_role</stepName>
        <classPath>
            com.zte.ums.dap.manager.deploys.service.uninstall.executors.UninstallUnknownRoleExecutor
        </classPath>
        <roleType>host</roleType>
        <order>-20</order>
        <shellParameter></shellParameter>
    </step>

    <step>
        <operatorType>addRole</operatorType>
        <stepName>expand_service</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.DeployServiceExecutor
        </classPath>
        <roleType>host</roleType>
        <order>11</order>
        <shellParameter>serviceInfo</shellParameter>
    </step>
    <step>
        <operatorType>addRole</operatorType>
        <stepName>resource_service</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.ResourceServiceExecutor
        </classPath>
        <roleType>host</roleType>
        <order>12</order>
        <shellParameter></shellParameter>
    </step>
    <step>
        <operatorType>addRole</operatorType>
        <stepName>service_params</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.ServiceParamsExecutor
        </classPath>
        <roleType>host</roleType>
        <order>13</order>
        <shellParameter></shellParameter>
    </step>
    <step>
        <operatorType>addRole</operatorType>
        <stepName>mini_config</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.MiniConfigExecutor
        </classPath>
        <roleType>host</roleType>
        <order>14</order>
        <shellParameter></shellParameter>
    </step>

    <!--添加联邦\扩容联邦默认步骤-->
    <step>
        <operatorType>federate</operatorType>
        <eventType>EVENT_FEDERATION_ADD_ROLE</eventType>
        <stepName>expand_service</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.DeployServiceExecutor
        </classPath>
        <roleType>host</roleType>
        <order>11</order>
        <shellParameter>serviceInfo</shellParameter>
    </step>
    <step>
        <operatorType>federate</operatorType>
        <eventType>EVENT_FEDERATION_ADD_ROLE</eventType>
        <stepName>resource_service</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.ResourceServiceExecutor
        </classPath>
        <roleType>host</roleType>
        <order>12</order>
        <shellParameter></shellParameter>
    </step>
    <step>
        <operatorType>federate</operatorType>
        <eventType>EVENT_FEDERATION_ADD_ROLE</eventType>
        <stepName>service_params</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.ServiceParamsExecutor
        </classPath>
        <roleType>host</roleType>
        <order>13</order>
        <shellParameter></shellParameter>
    </step>
    <step>
        <operatorType>federate</operatorType>
        <eventType>EVENT_FEDERATION_ADD_ROLE</eventType>
        <stepName>mini_config</stepName>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.MiniConfigExecutor
        </classPath>
        <roleType>host</roleType>
        <order>14</order>
        <shellParameter></shellParameter>
    </step>

    <!-- 服务启动 -->
    <step>
        <stepName>dispatch_config_for_manager_dap</stepName>
        <classPath>
            com.zte.ums.dap.manager.framework.workflow.executor.common.DispatchServiceConfigsExecutor
        </classPath>
        <operatorType>start</operatorType>
        <order>0</order>
    </step>

    <step>
        <stepName>update_status_to_worked</stepName>
        <classPath>
            com.zte.ums.dap.manager.framework.workflow.executor.common.UpdateStatusTypeExecutor
        </classPath>
        <operatorType>start</operatorType>
        <eventType>EVENT_START_SERVICE</eventType>
        <order>995</order>
    </step>

    <step>
        <stepName>send_startservice_msg</stepName>
        <classPath>com.zte.ums.dap.manager.framework.workflow.executor.common.SendStartServiceMsg
        </classPath>
        <operatorType>start</operatorType>
        <eventType>EVENT_START_SERVICE</eventType>
        <order>999</order>
    </step>

    <step>
        <stepName>send_startrole_msg</stepName>
        <classPath>com.zte.ums.dap.manager.framework.workflow.executor.common.SendStartRoleMsg
        </classPath>
        <operatorType>start</operatorType>
        <eventType>EVENT_START_ROLE</eventType>
        <order>999</order>
    </step>

    <step>
        <stepName>dispatch_config_for_manager_dap</stepName>
        <classPath>
            com.zte.ums.dap.manager.framework.workflow.executor.common.DispatchServiceConfigsExecutor
        </classPath>
        <operatorType>format</operatorType>
        <order>0</order>
    </step>

    <step>
        <stepName>update_status_to_worked</stepName>
        <classPath>
            com.zte.ums.dap.manager.framework.workflow.executor.common.UpdateStatusTypeExecutor
        </classPath>
        <operatorType>format</operatorType>
        <eventType>HDFS_FORMAT</eventType>
        <order>999</order>
    </step>

    <!-- managerService服务重启 -->
    <step>
        <stepName>update_role_configure_for_manager_dap</stepName>
        <operatorType>restart</operatorType>
        <classPath>com.zte.ums.dap.manager.framework.workflow.executor.common.ServiceOperationMiniConfigExecutor</classPath>
        <eventType>EVENT_ROLLING_RESTART_SERVICE</eventType>
        <roleType>service</roleType>
        <order>-10</order>
    </step>
    <step>
        <stepName>dispatch_config_for_manager_dap</stepName>
        <classPath>
            com.zte.ums.dap.manager.framework.workflow.executor.common.DispatchServiceConfigsExecutor
        </classPath>
        <operatorType>restart</operatorType>
        <order>0</order>
    </step>

    <step>
        <stepName>send_startservice_msg</stepName>
        <classPath>com.zte.ums.dap.manager.framework.workflow.executor.common.SendStartServiceMsg
        </classPath>
        <operatorType>restart</operatorType>
        <eventType>EVENT_ROLLING_RESTART_SERVICE</eventType>
        <order>999</order>
    </step>

    <step>
        <stepName>send_startrole_msg</stepName>
        <classPath>com.zte.ums.dap.manager.framework.workflow.executor.common.SendStartRoleMsg
        </classPath>
        <operatorType>restart</operatorType>
        <eventType>EVENT_ROLLING_RESTART_ROLE</eventType>
        <order>999</order>
    </step>

    <step>
        <stepName>update_publish_event</stepName>
        <operatorType>offlineUpdateService</operatorType>
        <classPath>com.zte.ums.dap.manager.deploys.service.update.executors.PublishUpdateServiceEventExecutor</classPath>
        <order>999</order>
    </step>
</service>
