<?xml version="1.0" encoding="UTF-8"?>
<service>
    <serviceId>dap.manager.deployer</serviceId>

    <!-- 安装 -->
    <step>
        <stepName>install_service</stepName>
        <operatorType>install</operatorType>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.DeployServiceExecutor</classPath>
        <deployShell>chmod 755 pre_install.sh;sh pre_install.sh</deployShell>
        <roleType>host</roleType>
        <order>20</order>
    </step>

    <!-- 启动 -->
    <step>
        <stepName>start_role_for_manager_dap</stepName>
        <operatorType>start</operatorType>
        <classPath>com.zte.ums.dap.manager.framework.workflow.executor.common.ExeShellForStartServiceExecutor</classPath>
        <deployShell>$FEAGLE_INSTALL_PATH/dapmanager-deployer/bin/run.sh</deployShell>
        <roleType>host</roleType>
        <order>110</order>
    </step>

    <!-- 停止 -->
    <step>
        <stepName>stop_role_for_manager_dap</stepName>
        <operatorType>stop</operatorType>
        <classPath>com.zte.ums.dap.manager.framework.workflow.executor.common.ExeShellForStartServiceExecutor</classPath>
        <deployShell>$FEAGLE_INSTALL_PATH/dapmanager-deployer/bin/stop.sh</deployShell>
        <roleType>host</roleType>
        <order>110</order>
    </step>

    <!-- 扩容 -->
    <!-- 采用默认步骤 -->

    <!-- 减容，卸载 -->
    <step>
        <stepName>clear_role_data_for_manager_dap</stepName>
        <operatorType>uninstall</operatorType>
        <classPath>com.zte.ums.dap.manager.deploys.service.uninstall.executors.UninstallServiceExecutor</classPath>
        <deployShell>$FEAGLE_INSTALL_PATH/dapmanager-deployer/bin/uninstall.sh</deployShell>
        <roleType>host</roleType>
        <order>110</order>
    </step>
    <step>
        <stepName>update_role_configure_for_manager_dap</stepName>
        <operatorType>uninstall</operatorType>
        <classPath>com.zte.ums.dap.manager.deploys.service.install.executors.MiniConfigExecutor</classPath>
        <eventType>EVENT_UNINSTALL_ROLE</eventType>
        <roleType>service</roleType>
        <order>120</order>
    </step>
    <step>
        <stepName>delete_role_resource_for_manager_dap</stepName>
        <operatorType>uninstall</operatorType>
        <classPath>com.zte.ums.dap.manager.deploys.service.uninstall.executors.RoleResourceDeleteExecutor</classPath>
        <eventType>EVENT_UNINSTALL_ROLE</eventType>
        <roleType>service</roleType>
        <order>130</order>
    </step>
    <step>
        <stepName>delete_service_resource_for_manager_dap</stepName>
        <operatorType>uninstall</operatorType>
        <classPath>com.zte.ums.dap.manager.deploys.service.uninstall.executors.ServiceResourceDeleteExecutor</classPath>
        <eventType>EVENT_UNINSTALL_SERVICE</eventType>
        <roleType>service</roleType>
        <order>130</order>
    </step>

    <!--managerService重启-->
    <step>
        <stepName>managerservice_cluster_restart_commond_executor</stepName>
        <operatorType>restart</operatorType>
        <classPath>com.zte.ums.dap.manager.restart.executor.GeneralRollingRestartExecutor</classPath>
        <roleType>rollingHeader</roleType>
        <order>10</order>
    </step>
    <step>
        <stepName>managerservice_cluster_restart_commond_executor</stepName>
        <operatorType>restart</operatorType>
        <classPath>com.zte.ums.dap.manager.restart.executor.GeneralRollingRestartExecutor</classPath>
        <roleType>rollingMiddle</roleType>
        <order>20</order>
    </step>
    <step>
        <stepName>managerservice_cluster_restart_commond_executor</stepName>
        <operatorType>restart</operatorType>
        <classPath>com.zte.ums.dap.manager.restart.executor.GeneralRollingRestartExecutor</classPath>
        <roleType>rollingTail</roleType>
        <order>30</order>
    </step>
</service>
