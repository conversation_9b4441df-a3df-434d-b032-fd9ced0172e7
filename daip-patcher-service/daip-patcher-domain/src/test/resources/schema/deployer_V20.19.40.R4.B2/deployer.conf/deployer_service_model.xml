<?xml version="1.0" encoding="UTF-8"?>

<service>
    <serviceId>dap.manager.deployer</serviceId>
    <serviceName>deployer</serviceName>
    <componentType>dap.manager.common.feagle</componentType>
    <heartBeatReportType>UNDEFINED</heartBeatReportType>
    <serviceShowTabs>0,1,11,12</serviceShowTabs>
    <serviceInstallType>singleService</serviceInstallType>
    <operations>rollingRestartService</operations>
    <webConsolePath></webConsolePath>
    <descriptions-zh>deployer提供版本管理及部署功能</descriptions-zh>
    <descriptions-en>deployer provides version management and version deployment</descriptions-en>
    <displayOrder>10002</displayOrder>
    <isClusterRestart>true</isClusterRestart>
    <clusterType>dapmanagerCluster</clusterType>
    <role>
        <roleId>dap.manager.deployer.deployer</roleId>
        <roleName>deployer</roleName>
        <roleType>no_limit</roleType>
        <operations>startRole,stopRole,uninstallRole</operations>
        <roleShowTabs>3</roleShowTabs>
        <roleStatusCalcType>PID_JMX</roleStatusCalcType>
        <statusCollection>
            <collectionType>PROCESS</collectionType>
            <collectionParameters>$FEAGLE_INSTALL_PATH/dapmanager-deployer/dapmanager-deployer.pid</collectionParameters>
        </statusCollection>
        <statusCollection>
            <collectionType>JMX</collectionType>
            <collectionParameters>http://${HOST}:${PORT}/jmx</collectionParameters>
        </statusCollection>
    </role>
</service>