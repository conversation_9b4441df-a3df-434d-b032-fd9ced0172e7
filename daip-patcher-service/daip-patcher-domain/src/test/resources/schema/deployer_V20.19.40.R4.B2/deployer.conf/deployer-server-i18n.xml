﻿<?xml version="1.0" encoding="UTF-8" ?>
<!-- !DOCTYPE locales PUBLIC "i18n.dtd" "i18n.dtd"  -->
<locales>
    <locale language-country="zh_CN">
        <!-- update host -->
        <pair label-key="deploy_update_miniAgent" label-value="升级MiniAgent"/>
        <pair label-key="deploy_update_agent" label-value="升级Agent"/>
        <pair label-key="deploy_update_configs" label-value="配置处理"/>
        <!--update service-->
        <pair label-key="deploy_update_ZDH_download_shell" label-value="执行升级脚本"/>
        <pair label-key="deploy_update_pull_config" label-value="拉取配置"/>
        <pair label-key="update_service_download_package" label-value="下载版本"/>
        <pair label-key="update_service_execute_db_sql" label-value="执行升级梯度"/>
        <pair label-key="update_service_execute_shell" label-value="执行脚本"/>
        <pair label-key="update_service_modify_version" label-value="更新升级表"/>
        <pair label-key="update_service_patch" label-value="升级补丁"/>
        <pair label-key="update_publish_event" label-value="发布消息"/>

        <!--rollback service-->
        <pair label-key="deploy_rollback_shell" label-value="执行回退脚本"/>
        <pair label-key="rollback_service_modify_version" label-value="版本表回退"/>
        <pair label-key="rollback_service_config" label-value="配置回退"/>
        <pair label-key="delete_patch_record" label-value="删除补丁记录"/>

        <!--配置-->
        <pair label-key="dapDiscovery.depend.name" label-value="依赖的dapDiscovery实例"/>
        <pair label-key="dapDiscovery.depend.desc" label-value="此服务依赖的dapDiscovery服务的名称。"/>
        <pair label-key="dapCache.depend.name" label-value="依赖的dapCache实例"/>
        <pair label-key="dapCache.depend.desc" label-value="此服务依赖的dapCache服务的名称。"/>

        <pair label-key="server.port.name" label-value="server 端口"/>
        <pair label-key="server.port.des" label-value="server 服务端口。"/>


        <pair label-key="spring.datasource.password.name" label-value="spring数据源登录密码"/>
        <pair label-key="spring.datasource.password.desc" label-value="spring数据源登录密码"/>
        <pair label-key="cache.data.password.name" label-value="缓存系统 server 连接密码"/>
        <pair label-key="cache.data.password.desc"
              label-value="缓存系统 server 连接密码，客户端访问redis需要提供此密码进行验证。"/>


        <pair label-key="common_config" label-value="主要"/>
        <pair label-key="advanced_config" label-value="高级"/>
        <pair label-key="log_config" label-value="日志"/>
        <pair label-key="other_config" label-value="其他"/>
        <pair label-key="effect_after_restarting" label-value="重启生效"/>

        <pair label-key="jvm.dapmanager-deployer.heapsize.name" label-value="dapAlarm的Java堆栈大小"/>
        <pair label-key="jvm.dapmanager-deployer.heapsize.desc"
              label-value="Java进程堆栈内存的最大大小。已传递到 Java -Xmx。"/>
        <pair label-key="jvm.dapmanager-deployer.oom_heap_dump_dir.name"
              label-value="dapAlarm内存不足时的转储堆目录"/>
        <pair label-key="jvm.dapmanager-deployer.oom_heap_dump_dir.desc"
              label-value="引发 java.lang.OutOfMemoryError 错误时生成堆转储所在目录的路径。该目录必须已存在，且角色用户必须拥有对此目录的写入权限。如果多个角色共享该目录，应有 1777 项权限。堆转储文件使用 600 项权限创建并且由角色用户所有。该目录中的可用空间应大于为该角色配置的最大 Java 进程堆大小。"/>

        <pair label-key="deploy_install_miniAgent" label-value="安装MiniAgent"/>
        <pair label-key="deploy_install_agent" label-value="安装Agent"/>

        <pair label-key="uninstall_service_exception" label-value="其他用户正在卸载操作，请稍后....."/>

        <!-- 版本加载异常 -->
        <pair label-key="ConfigChain" label-value="默认配置异常"/>
        <pair label-key="CopyChain" label-value="种子下载异常"/>
        <pair label-key="ModelChain" label-value="模型文件异常"/>
        <pair label-key="ScriptExeChain" label-value="alarm脚本执行异常"/>
        <pair label-key="StepChain" label-value="step文件异常"/>
        <pair label-key="VerifyInfoChain" label-value="版本包信息异常"/>
        <pair label-key="ZipVerifyChain" label-value="zip包异常"/>

        <!-- 模型加载 -->
        <pair label-key="CONFIGCHAIN" label-value="默认配置加载"/>
        <pair label-key="COPYCHAIN" label-value="种子下载"/>
        <pair label-key="MODELCHAIN" label-value="模型文件加载"/>
        <pair label-key="STEPCHAIN" label-value="step文件加载"/>
        <pair label-key="VERIFYINFOCHAIN" label-value="版本包加载"/>
        <pair label-key="ZIPVERIFYCHAIN" label-value="zip校验"/>
        <pair label-key="SCRIPTEXECHAIN" label-value="alarm脚本执行"/>
        <!--版本删除失败-->
        <pair label-key="VersionUsed" label-value="版本已使用"/>
        <pair label-key="ConfigDeleteChain" label-value="配置删除失败"/>
        <pair label-key="ProductDeleteChain" label-value="版本信息删除失败"/>
        <pair label-key="SeedDeleteChain" label-value="种子文件删除失败"/>
        <pair label-key="ServiceModelDeleteChain" label-value="模型信息删除失败"/>
        <pair label-key="SpectDeleteChain" label-value="自述文件信息删除失败"/>
        <pair label-key="StepDeleteChain" label-value="Step信息删除失败"/>
        <pair label-key="VersionDeleteMsgChain" label-value="版本文件删除失败"/>
        <pair label-key="PatchDeleteChain" label-value="删除补丁失败"/>
        <pair label-key="remove_version_exception" label-value="服务已经安装，请先卸载再移除版本"/>

        <!--uninstall service -->
        <pair label-key="clear_role_data_for_manager_dap" label-value="清理角色数据"/>
        <pair label-key="drop_GateWay_for_manager_dap" label-value="删除GateWay"/>
        <pair label-key="update_role_configure_for_manager_dap" label-value="更新配置"/>
        <pair label-key="delete_role_resource_for_manager_dap" label-value="删除角色资源信息"/>
        <pair label-key="uninstall_service" label-value="卸载服务"/>
        <pair label-key="uninstall_unknown_role" label-value="删除未知角色"/>

        <!-- start service -->
        <pair label-key="dispatch_config_for_manager_dap" label-value="下发服务配置"/>
        <pair label-key="start_role_for_manager_dap" label-value="启动角色"/>
        <pair label-key="gateway_deploy_for_manager_dap" label-value="GateWay部署"/>
        <pair label-key="grant_permission_for_sparkSQL_normal_user" label-value="为sparkSQL普通用户赋权"/>
        <pair label-key="update_status_to_worked" label-value="更新服务状态"/>
        <pair label-key="send_startservice_msg" label-value="发送启动服务消息"/>

        <!--install service -->
        <pair label-key="install_zdh_common" label-value="下载zdh公共组件"/>
        <pair label-key="resource_service" label-value="资源化"/>
        <pair label-key="expand_service" label-value="执行安装脚本"/>
        <pair label-key="service_params" label-value="创建动态配置表"/>
        <pair label-key="mini_config" label-value="最小化配置"/>
        <pair label-key="hdfs_formatting" label-value="hdfs格式化"/>
        <pair label-key="update_status_to_worked" label-value="更新服务状态"/>

        <!--自定义文件上传，证书上传-->
        <pair label-key="dispatch_file_ca" label-value="上传CA证书"/>
        <pair label-key="dispatch_file_ssl" label-value="上传ssl秘钥"/>
        <pair label-key="dispatch_file_aes" label-value="上传aes秘钥"/>
        <pair label-key="dispatch_file_encryption" label-value="上传列加密算法"/>
        <pair label-key="dispatch_file_driver" label-value="上传数据库驱动"/>

        <!--服务启停-->
        <pair label-key="hdfs_has_not_formatted" label-value="HDFS未初始化"/>
        <pair label-key="command_first_stop_and_start" label-value="服务正在运行，请先停止后再启动"/>
        <pair label-key="command_depend_service_unusable" label-value="依赖的服务不可用，请先启动该服务"/>
        <pair label-key="command_has_stop_run" label-value="服务已经停止运行"/>
        <pair label-key="service_stop_depend_illegal" label-value="有其他服务依赖该实例，请先停止"/>
    </locale>
    <locale language-country="en_US">
        <!--update host-->
        <pair label-key="deploy_update_miniAgent" label-value="Update miniAgent"/>
        <pair label-key="deploy_update_agent" label-value="Update agent"/>
        <pair label-key="deploy_update_configs" label-value="Update configs"/>
        <!--update service-->
        <pair label-key="deploy_update_ZDH_download_shell" label-value="Shell execution"/>
        <pair label-key="deploy_update_pull_config" label-value="Pull configs"/>
        <pair label-key="update_service_download_package" label-value="Download package"/>
        <pair label-key="update_service_execute_db_sql" label-value="Execute DB sql"/>
        <pair label-key="update_service_execute_shell" label-value="Shell execution"/>
        <pair label-key="update_service_modify_version" label-value="Modify version"/>
        <pair label-key="update_service_patch" label-value="Update patch"/>
        <pair label-key="update_publish_event" label-value="Publish event"/>

        <!--rollback service-->
        <pair label-key="deploy_rollback_shell" label-value="Shell rollback execution"/>
        <pair label-key="rollback_service_modify_version" label-value="Rollback version"/>
        <pair label-key="rollback_service_config" label-value="Rollback configs"/>
        <pair label-key="delete_patch_record" label-value="Delete patch record"/>

        <!--config-->
        <pair label-key="dapDiscovery.depend.name" label-value="Depended dapDiscovery instance"/>
        <pair label-key="dapDiscovery.depend.desc" label-value="Depended dapDiscovery instance."/>
        <pair label-key="dapCache.depend.name" label-value="Depended dapCache instance"/>
        <pair label-key="dapCache.depend.desc" label-value="Depended dapCache instance."/>


        <pair label-key="server.port.name" label-value="server port"/>
        <pair label-key="server.port.des" label-value="server port."/>

        <pair label-key="spring.datasource.password.name" label-value="Spring Datasource Password"/>
        <pair label-key="spring.datasource.password.desc" label-value="Spring Datasource Password"/>
        <pair label-key="cache.data.password" label-value="cache.data.password"/>
        <pair label-key="cache.data.password.des" label-value="cache.data.password"/>


        <pair label-key="common_config" label-value="Common"/>
        <pair label-key="advanced_config" label-value="Advanced config"/>
        <pair label-key="log_config" label-value="Logs"/>
        <pair label-key="other_config" label-value="Other"/>

        <pair label-key="jvm.dapmanager-deployer.heapsize.name"
              label-value="Java Heap Size of dapAlarm"/>
        <pair label-key="jvm.dapmanager-deployer.heapsize.desc"
              label-value="Maximum size for the Java Process heap memory. Passed to Java -Xmx."/>
        <pair label-key="jvm.dapmanager-deployer.oom_heap_dump_dir.name"
              label-value="Dump Heap Directory for dapAlarm "/>
        <pair label-key="jvm.dapmanager-deployer.oom_heap_dump_dir.desc"
              label-value="Path to directory where heap dumps are generated when java.lang.OutOfMemoryError error is thrown. This directory must be already exist, role user must have write access to this directory. If this directory is shared among multiple roles, it should have 1777 permissions. The heap dump files are created with 600 permissions and are owned by the role user. The amount of free space in this directory should be greater than the maximum Java Process heap size configured for this role."/>


        <pair label-key="deploy_install_miniAgent" label-value="Install MiniAgent"/>
        <pair label-key="deploy_install_agent" label-value="Install Agent"/>

        <!--版本-->
        <pair label-key="ConfigChain" label-value="Default configs exception"/>
        <pair label-key="CopyChain" label-value="Seed download exception"/>
        <pair label-key="ModelChain" label-value="ServiceModel file exception"/>
        <pair label-key="ScriptExeChain" label-value="Alarm script execution exception"/>
        <pair label-key="StepChain" label-value="Step file exception"/>
        <pair label-key="VerifyInfoChain" label-value="Version info error"/>
        <pair label-key="ZipVerifyChain" label-value="Zip file exception"/>

        <!--版本删除失败-->
        <pair label-key="VersionUsed" label-value="Version already in use"/>
        <pair label-key="ConfigDeleteChain" label-value="Configs deletion failed"/>
        <pair label-key="ProductDeleteChain" label-value="Version information deletion failed"/>
        <pair label-key="SeedDeleteChain" label-value="Seed File Deletion Failed"/>
        <pair label-key="ServiceModelDeleteChain" label-value="Service model deletion failed"/>
        <pair label-key="SpectDeleteChain" label-value="Spect info deletion failed"/>
        <pair label-key="StepDeleteChain" label-value="Step info deletion failed"/>
        <pair label-key="VersionDeleteMsgChain" label-value="Version file deletion failed"/>
        <pair label-key="PatchDeleteChain" label-value="Patch deletion failed"/>


        <!-- uninstall service -->
        <pair label-key="clear_role_data_for_manager_dap" label-value="Clean role data"/>
        <pair label-key="drop_GateWay_for_manager_dap" label-value="Delete GateWay"/>
        <pair label-key="update_role_configure_for_manager_dap" label-value="Update configure"/>
        <pair label-key="delete_role_resource_for_manager_dap" label-value="Delete role resource"/>
        <pair label-key="uninstall_service" label-value="Uninstall service"/>
        <pair label-key="uninstall_unknown_role" label-value="uninstall unknown role"/>

        <!-- start service -->
        <pair label-key="dispatch_config_for_manager_dap" label-value="Dispatch service configs"/>
        <pair label-key="start_role_for_manager_dap" label-value="Start role"/>
        <pair label-key="gateway_deploy_for_manager_dap" label-value="Deploy GateWay"/>
        <pair label-key="grant_permission_for_sparkSQL_normal_user"
              label-value="Grant permission for sparkSQL normal user"/>
        <pair label-key="update_status_to_worked" label-value="Update service status"/>
        <pair label-key="send_startservice_msg" label-value="send start service message"/>

        <!--install service -->
        <pair label-key="install_zdh_common" label-value="Install zdh-common"/>
        <pair label-key="resource_service" label-value="resource"/>
        <pair label-key="expand_service" label-value="shell execution"/>
        <pair label-key="service_params" label-value="create dynamic configuration table"/>
        <pair label-key="mini_config" label-value="mini config"/>
        <pair label-key="hdfs_formatting" label-value="hdfs formatting"/>
        <pair label-key="update_status_to_worked" label-value="update worked status"/>

        <!--模型加载-->
        <pair label-key="CONFIGCHAIN" label-value="Default configs"/>
        <pair label-key="COPYCHAIN" label-value="Seed download"/>
        <pair label-key="MODELCHAIN" label-value="ServiceModel file"/>
        <pair label-key="STEPCHAIN" label-value="Step file"/>
        <pair label-key="VERIFYINFOCHAIN" label-value="Version info"/>
        <pair label-key="ZIPVERIFYCHAIN" label-value="Zip file"/>
        <pair label-key="SCRIPTEXECHAIN" label-value="Alarm script execution"/>

        <pair label-key="remove_version_exception"
              label-value="Service has installed，Please uninstall service and remove version"/>

        <!--自定义文件上传，证书上传-->
        <pair label-key="dispatch_file_ca" label-value="Upload CA certificate"/>
        <pair label-key="dispatch_file_ssl" label-value="Upload SSL secret key"/>
        <pair label-key="dispatch_file_aes" label-value="Upload AES secret key"/>
        <pair label-key="dispatch_file_encryption"
              label-value="Upload column encryption algorithm"/>
        <pair label-key="dispatch_file_driver" label-value="Upload database driver"/>


        <!--install service -->
        <pair label-key="mini_config" label-value="mini config"/>

        <!--模型加载-->
        <pair label-key="CONFIGCHAIN" label-value="Default configs"/>
        <pair label-key="COPYCHAIN" label-value="Seed download"/>
        <pair label-key="MODELCHAIN" label-value="ServiceModel file"/>
        <pair label-key="STEPCHAIN" label-value="Step file"/>
        <pair label-key="VERIFYINFOCHAIN" label-value="Version info"/>
        <pair label-key="ZIPVERIFYCHAIN" label-value="Zip file"/>
        <pair label-key="SCRIPTEXECHAIN" label-value="Alarm script execution"/>

        <pair label-key="uninstall_service_exception" label-value="Other users are uninstalling service, please try again later."/>

        <!--服务启停-->
        <pair label-key="hdfs_has_not_formatted" label-value="Hdfs has not formatted"/>
        <pair label-key="command_first_stop_and_start"
              label-value="service is running,please stop and restart"/>
        <pair label-key="command_depend_service_unusable"
              label-value="dependable service is unavailable,please start it first!"/>
        <pair label-key="command_has_stop_run" label-value="service has stopped"/>
        <pair label-key="service_stop_depend_illegal" label-value="some services depends on this instance, and runing, please stop first!"/>
    </locale>
</locales>