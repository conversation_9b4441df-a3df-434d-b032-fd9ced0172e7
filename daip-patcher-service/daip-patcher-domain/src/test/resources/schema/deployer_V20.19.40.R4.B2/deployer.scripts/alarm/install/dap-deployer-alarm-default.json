{"clusterId": "100000", "clusterName": "managerService", "componentName": "deployer", "componentType": "dap.manager.common.feagle", "configInfo": [{"clientUsed": false, "clusterId": "100000", "configId": "deployer1", "fileName": "EnvParameter", "hostIp": "", "itemType": "DependService_1", "key": "dapDiscovery", "keyWord": "dapDiscovery", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "dapDiscovery_1000000"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer2", "fileName": "EnvParameter", "hostIp": "", "itemType": "DependService_1", "key": "<PERSON><PERSON><PERSON><PERSON>", "keyWord": "<PERSON><PERSON><PERSON><PERSON>", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "dapCache_1000000"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer3", "fileName": "application.properties", "hostIp": "", "itemType": "FeagleRestPort,JmxPort", "key": "server.port", "keyWord": "server.port", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "56160"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer4", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "dapmanager.host.name", "keyWord": "dapmanager.host.name", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": ""}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer5", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "spring.datasource.password", "keyWord": "spring.datasource.password", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "enRlX3NlY3VyaXR5X2NyeXB0ATQxNDU1MzVGNDM0MjQzNUYzMjM1MzY7NDMzNDQxNDEzNDM1MzQ0MzQyMzgzMTQ2NDEzMTM5NDM0MzM5MzEzNzM3MzEzMzM1MzkzNTM2NDMzNDQxMzI0Mzs7MzUzMDMwMzAzMDtFM0U1Q0Q5NEY5QzM0ODM0MjkyM0JBRDBBQzhGRjY3MDswRTE4M0YzQUFDRjFFN0YwMjlDMjVEQ0QyNDIwQ0IxQjs2NTM4MzMzNjMwNjE2MTYxMkQzMjM0Mzk2MzJEMzQ2NTMzMzgyRDYyMzIzMDY0MkQzNDM0NjM2NTMxMzQzNzY0MzUzOTM0NjY7"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer6", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "spring.datasource.url", "keyWord": "spring.datasource.url", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "123456"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer8", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "cache.data.password", "keyWord": "cache.data.password", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "enRlX3NlY3VyaXR5X2NyeXB0ATQxNDU1MzVGNDM0MjQzNUYzMjM1MzY7NDY0MjM5MzM0NDM5MzkzNzM2MzIzNjM5MzY0MTQxMzIzOTMwMzUzNDMyMzg0MTM3MzYzODM0MzQzNzQ0NDYzNjs7MzUzMDMwMzAzMDtGRjU0OEREM0U1Q0IwNzJFMjUxRkEyQUVERDI4QUU4ODtGNkM5N0U2RTM0MDE5RkQ4OTk1NkQ5M0ExMURBQjU1MDs2NTM4MzMzNjMwNjE2MTYxMkQzMjM0Mzk2MzJEMzQ2NTMzMzgyRDYyMzIzMDY0MkQzNDM0NjM2NTMxMzQzNzY0MzUzOTM0NjY7"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer9", "fileName": "application.properties", "hostIp": "", "itemType": "deployerChangeInfluence", "key": "eureka.instance.hostname", "keyWord": "eureka.instance.hostname", "roleId": "dap.manager.deployer.deployer", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "zdh194"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer10", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "eureka.client.serviceUrl.defaultZone", "keyWord": "eureka.client.serviceUrl.defaultZone", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": ""}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer11", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "spring.redis.cluster.nodes", "keyWord": "spring.redis.cluster.nodes", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": ""}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer12", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "spring.kafka.bootstrap-servers", "keyWord": "spring.kafka.bootstrap-servers", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "zdh194:9092"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer13", "fileName": "application.properties", "hostIp": "", "itemType": "deployerChangeInfluence", "key": "Manager.<PERSON><PERSON><PERSON>", "keyWord": "Manager.<PERSON><PERSON><PERSON>", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "zdh194"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer17", "fileName": "", "hostIp": "", "itemType": "", "key": "", "keyWord": "", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "dFKp74Qpwc2oGPwgBNqOIh84WGTSntU4Fj1m+u+x1/rOGXNfD/xdT6j07V1IlHLnv8hKwlkcIBwi\nuC8zgBE41RddionTMo+EFUiulFRk9FyCrLPAO3zqMvxu/yjzT8P6WcO4IycP1L0xXITndsBWDS1E\nRynShcm/J6cs0jV4OBoR9mNsH0yJBZqpVoRu73i0R3asWgo7nlb16V4hh1g1FlRcMqSHEGARimYr\npRSojPHRULgIUpYGrN4tRO8kecSiEOuOcwylkXVaLyIdIBEmM3woJp6wdPlhWphsySDSEKcxaV0u\nYb8tjIkpGiZ4W69Zcj6EPbWbwA/yLJt14xXkT6lsvipw+9RlGese0ogVi4LMdroqfshL9MprDJ5d\nr7qqtVjDdzteWWxj355wFyS1V9QN+K7VDbK10j1oFQoI3YfsCqHe1ExhHLsANss2Cj+s9rrTYayj\n0t4KGUwvL9977babomReq71QgFB77iddnxh3+Jif8VxIw1k2B+H2WVQqfEIqNJruxuW4jUy7LXb9\nBrqdLzKy6EC1JxkBMF8ccma/7VLBv1x2aMXuSmghOHg13ga63AgHQBVB281dPsBDkd2xPi5m5asN\n3NNjNOsBUDrjlTgkxsODsiaIZz0iEQEhPkjtWHg9RdZ/kz7V7dmd7U+lZewjfhgZifPtC3nEdygl\nix2/KIdgkh88MZiCpwpEsOQsTO0wDVdLBiUG9xCzzDA2DoLHfwt7V6vpAtm7m5Bvtt305HWAjNJl\n32VlTCh+Wki0/8h7ACcQvtlhtXnTzTB92PJsakCLRkat2jSOTbJAamBSlKw4akxSDvJGmAIYkmOf\n11tmEKrChrosUqjuqd25QdUl4jpKzbA4MZqhUPBDnYdNi8H7Ff5iTg5YYzKuoS3LswZMxZYyi88i\nREFq6Q6qX369x2gJovJBvyUfPHzzjmDLaZ3BbQV/uxBlbNwnbTomx0OKU4mInZsonr/0ej7sjYRS\nVqsc/lrNKbgcH2DC/egipi5E8H0sz3COqpQ8PmtMmsGzOI539g3/LBXJ2WBZ5986zu0KwtDLnwOE\nol9kUVnMvExF3K1FpPxK+lZp"}], "dependenceConfig": {"dapCache": {"clusterId": "100000", "clusterName": "managerService", "componentName": "<PERSON><PERSON><PERSON><PERSON>", "componentType": "dap.manager.common.feagle", "configInfo": [{"clientUsed": false, "clusterId": "100000", "configId": "dapCache1", "fileName": "EnvParameter", "hostIp": "", "itemType": "DependService_1", "key": "dapDiscovery", "keyWord": "dapDiscovery", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "dapDiscovery_1000000"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapCache8", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "spring.datasource.url", "keyWord": "spring.datasource.url", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "123456"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapCache10", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "spring.datasource.password", "keyWord": "spring.datasource.password", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "enRlX3NlY3VyaXR5X2NyeXB0ATQxNDU1MzVGNDM0MjQzNUYzMjM1MzY7MzQ0NDM5NDEzOTM0MzA0MzQxNDU0NjMxMzk0MzM2MzA0NjM3NDIzNjM3MzYzOTQ1MzQ0NjM4MzIzNDQyMzczNjs7MzUzMDMwMzAzMDs0QzI2QTU2QzFBRTcwMjlFMTg1MkZEQzg1Q0UzRDcyRDs3NUQyQjUyOEYzMkNGNzExODU1OUM1MUIzM0QxMDgxNjs2NTM4MzMzNjMwNjE2MTYxMkQzMjM0Mzk2MzJEMzQ2NTMzMzgyRDYyMzIzMDY0MkQzNDM0NjM2NTMxMzQzNzY0MzUzOTM0NjY7"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapCache15", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "cache.data.password", "keyWord": "cache.data.password", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "enRlX3NlY3VyaXR5X2NyeXB0ATQxNDU1MzVGNDM0MjQzNUYzMjM1MzY7NDU0MTQzMzkzMjQ0MzUzOTM5NDEzMTM2NDIzNDQ0MzI0NTM5NDU0MjQzMzY0NDM0MzQzOTQ0Mzk0NDQ2MzI0NTs7MzUzMDMwMzAzMDtDNTlDMTQ1OEE1MjQzMUM1RTMzRUE3QTgxRTBDMDFGRTswMkMwNTkxMDgzMUM4ODhEN0RGQTU1QTExRUIxQjkzMDs2NTM4MzMzNjMwNjE2MTYxMkQzMjM0Mzk2MzJEMzQ2NTMzMzgyRDYyMzIzMDY0MkQzNDM0NjM2NTMxMzQzNzY0MzUzOTM0NjY7"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapCache16", "fileName": "application.properties", "hostIp": "", "itemType": "dapCacheChangeInfluence", "key": "spring.redis.cluster.nodes", "keyWord": "spring.redis.cluster.nodes", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "*************:6381,*************:6382,*************:6383,*************:6384,*************:6385,*************:6386,"}], "dependenceConfig": {"dapDiscovery": {"clusterId": "100000", "clusterName": "managerService", "componentName": "dapDiscovery", "componentType": "dap.manager.common.feagle", "configInfo": [{"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery10", "fileName": "application.properties", "hostIp": "", "itemType": "dapDiscoveryServer", "key": "eureka.client.serviceUrl.defaultZone", "keyWord": "eureka.client.serviceUrl.defaultZone", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "http://zdh194:56140/eureka/"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery22", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "dapmanager.security.config.enabled", "keyWord": "dapmanager.security.config.enabled", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "true"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery23", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "dapmanager.security.config.user", "keyWord": "dapmanager.security.config.user", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "admin"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery24", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "dapmanager.security.config.psw", "keyWord": "dapmanager.security.config.psw", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "Vm5mYaT/Fq2/SCk4P9kKQ0+1w6j+dRO1+VqTAQGLaM8="}], "dependenceConfig": {}, "dependentServiceHost": {"dapDiscovery": [{"host": "zdh194", "port": ""}]}, "gateWayConfigs": [{"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery10", "fileName": "application.properties", "hostIp": "", "itemType": "dapDiscoveryServer", "key": "eureka.client.serviceUrl.defaultZone", "keyWord": "eureka.client.serviceUrl.defaultZone", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "http://zdh194:56140/eureka/"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery22", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "dapmanager.security.config.enabled", "keyWord": "dapmanager.security.config.enabled", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "true"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery23", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "dapmanager.security.config.user", "keyWord": "dapmanager.security.config.user", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "admin"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery24", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "dapmanager.security.config.psw", "keyWord": "dapmanager.security.config.psw", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "Vm5mYaT/Fq2/SCk4P9kKQ0+1w6j+dRO1+VqTAQGLaM8="}], "hostIp": "*************", "hostName": "zdh194", "jmxPort": "", "kerberosConfig": {"enable": false, "kerberosConfigs": []}, "roleHosts": {"dapDiscovery": ["zdh194"]}, "roleName": "", "rooms": [{"name": "default-room", "racks": [{"ips": ["*************"], "name": "default-rack"}]}], "sequenceId": "0", "serviceInstanceId": "dapDiscovery_1000000", "serviceName": "dapDiscovery"}}, "dependentServiceHost": {"dapCache": [{"host": "zdh194", "port": ""}]}, "gateWayConfigs": [{"clientUsed": false, "clusterId": "100000", "configId": "dapCache1", "fileName": "EnvParameter", "hostIp": "", "itemType": "DependService_1", "key": "dapDiscovery", "keyWord": "dapDiscovery", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "dapDiscovery_1000000"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapCache8", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "spring.datasource.url", "keyWord": "spring.datasource.url", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "123456"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapCache10", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "spring.datasource.password", "keyWord": "spring.datasource.password", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "enRlX3NlY3VyaXR5X2NyeXB0ATQxNDU1MzVGNDM0MjQzNUYzMjM1MzY7MzQ0NDM5NDEzOTM0MzA0MzQxNDU0NjMxMzk0MzM2MzA0NjM3NDIzNjM3MzYzOTQ1MzQ0NjM4MzIzNDQyMzczNjs7MzUzMDMwMzAzMDs0QzI2QTU2QzFBRTcwMjlFMTg1MkZEQzg1Q0UzRDcyRDs3NUQyQjUyOEYzMkNGNzExODU1OUM1MUIzM0QxMDgxNjs2NTM4MzMzNjMwNjE2MTYxMkQzMjM0Mzk2MzJEMzQ2NTMzMzgyRDYyMzIzMDY0MkQzNDM0NjM2NTMxMzQzNzY0MzUzOTM0NjY7"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapCache15", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "cache.data.password", "keyWord": "cache.data.password", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "enRlX3NlY3VyaXR5X2NyeXB0ATQxNDU1MzVGNDM0MjQzNUYzMjM1MzY7NDU0MTQzMzkzMjQ0MzUzOTM5NDEzMTM2NDIzNDQ0MzI0NTM5NDU0MjQzMzY0NDM0MzQzOTQ0Mzk0NDQ2MzI0NTs7MzUzMDMwMzAzMDtDNTlDMTQ1OEE1MjQzMUM1RTMzRUE3QTgxRTBDMDFGRTswMkMwNTkxMDgzMUM4ODhEN0RGQTU1QTExRUIxQjkzMDs2NTM4MzMzNjMwNjE2MTYxMkQzMjM0Mzk2MzJEMzQ2NTMzMzgyRDYyMzIzMDY0MkQzNDM0NjM2NTMxMzQzNzY0MzUzOTM0NjY7"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapCache16", "fileName": "application.properties", "hostIp": "", "itemType": "dapCacheChangeInfluence", "key": "spring.redis.cluster.nodes", "keyWord": "spring.redis.cluster.nodes", "roleId": "", "serviceId": "dap.manager.dap<PERSON><PERSON>", "serviceInstanceId": "dapCache_1000000", "value": "*************:6381,*************:6382,*************:6383,*************:6384,*************:6385,*************:6386,"}], "hostIp": "*************", "hostName": "zdh194", "jmxPort": "", "kerberosConfig": {"enable": false, "kerberosConfigs": []}, "roleHosts": {"dapCache": ["zdh194"]}, "roleName": "", "rooms": [{"name": "default-room", "racks": [{"ips": ["*************"], "name": "default-rack"}]}], "sequenceId": "0", "serviceInstanceId": "dapCache_1000000", "serviceName": "<PERSON><PERSON><PERSON><PERSON>"}, "dapDiscovery": {"clusterId": "100000", "clusterName": "managerService", "componentName": "dapDiscovery", "componentType": "dap.manager.common.feagle", "configInfo": [{"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery10", "fileName": "application.properties", "hostIp": "", "itemType": "dapDiscoveryServer", "key": "eureka.client.serviceUrl.defaultZone", "keyWord": "eureka.client.serviceUrl.defaultZone", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "http://zdh194:56140/eureka/"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery22", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "dapmanager.security.config.enabled", "keyWord": "dapmanager.security.config.enabled", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "true"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery23", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "dapmanager.security.config.user", "keyWord": "dapmanager.security.config.user", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "admin"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery24", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "dapmanager.security.config.psw", "keyWord": "dapmanager.security.config.psw", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "Vm5mYaT/Fq2/SCk4P9kKQ0+1w6j+dRO1+VqTAQGLaM8="}], "dependenceConfig": {}, "dependentServiceHost": {"dapDiscovery": [{"host": "zdh194", "port": ""}]}, "gateWayConfigs": [{"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery10", "fileName": "application.properties", "hostIp": "", "itemType": "dapDiscoveryServer", "key": "eureka.client.serviceUrl.defaultZone", "keyWord": "eureka.client.serviceUrl.defaultZone", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "http://zdh194:56140/eureka/"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery22", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "dapmanager.security.config.enabled", "keyWord": "dapmanager.security.config.enabled", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "true"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery23", "fileName": "application.properties", "hostIp": "", "itemType": "", "key": "dapmanager.security.config.user", "keyWord": "dapmanager.security.config.user", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "admin"}, {"clientUsed": true, "clusterId": "100000", "configId": "dapDiscovery24", "fileName": "application.properties", "hostIp": "", "itemType": "AesPwd", "key": "dapmanager.security.config.psw", "keyWord": "dapmanager.security.config.psw", "roleId": "", "serviceId": "dap.manager.dapDiscovery", "serviceInstanceId": "dapDiscovery_1000000", "value": "Vm5mYaT/Fq2/SCk4P9kKQ0+1w6j+dRO1+VqTAQGLaM8="}], "hostIp": "*************", "hostName": "zdh194", "jmxPort": "", "kerberosConfig": {"enable": false, "kerberosConfigs": []}, "roleHosts": {"dapDiscovery": ["zdh194"]}, "roleName": "", "rooms": [{"$ref": "$.dependenceConfig.dapCache.dependenceConfig.dapDiscovery.rooms[0]"}], "sequenceId": "0", "serviceInstanceId": "dapDiscovery_1000000", "serviceName": "dapDiscovery"}}, "dependentServiceHost": {"deployer": [{"host": "zdh194", "port": ""}]}, "gateWayConfigs": [{"clientUsed": false, "clusterId": "100000", "configId": "deployer1", "fileName": "EnvParameter", "hostIp": "", "itemType": "DependService_1", "key": "dapDiscovery", "keyWord": "dapDiscovery", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "dapDiscovery_1000000"}, {"clientUsed": false, "clusterId": "100000", "configId": "deployer2", "fileName": "EnvParameter", "hostIp": "", "itemType": "DependService_1", "key": "<PERSON><PERSON><PERSON><PERSON>", "keyWord": "<PERSON><PERSON><PERSON><PERSON>", "roleId": "", "serviceId": "dap.manager.deployer", "serviceInstanceId": "deployer_1000000", "value": "dapCache_1000000"}], "hostIp": "*************", "hostName": "zdh194", "jmxPort": "56160", "kerberosConfig": {"enable": false, "kerberosConfigs": []}, "roleHosts": {"deployer": ["zdh194"]}, "roleName": "deployer", "rooms": [{"name": "default-room", "racks": [{"ips": ["*************"], "name": "default-rack"}]}], "sequenceId": "0", "serviceInstanceId": "deployer_1000000", "serviceName": "deployer"}