[{"categoryName": "common_config", "clientUsed": false, "configId": "deployer1", "configItemAttr": "ServiceDisplay", "configItemName": "dapDiscovery", "configItemPath": "EnvParameter", "defaultValue": "dapDiscovery", "description": "dapDiscovery.depend.desc", "displayName": "deployer.dependencyservice", "displayOrder": 1, "effectiveImpl": "", "effectiveWay": "effect_after_restarting", "installUsed": false, "itemType": "DependService_1", "length": "0", "matcher": "", "maxValues": "", "minValues": "", "namei18n": "dapDiscovery.depend.name", "paramUnits": "", "roleId": "", "selectValue": "CustomClass:com.zte.ums.dap.manager.configuration.dynamic.DependServiceSelectValueGenerator", "serviceId": "dap.manager.deployer", "tagType": "EnumRadioParam", "validatorImpl": "", "visabled": true}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer2", "configItemAttr": "ServiceDisplay", "configItemName": "<PERSON><PERSON><PERSON><PERSON>", "configItemPath": "EnvParameter", "defaultValue": "<PERSON><PERSON><PERSON><PERSON>", "description": "dapCache.depend.desc", "displayName": "deployer.dependencyservice", "displayOrder": 2, "effectiveImpl": "", "effectiveWay": "effect_after_restarting", "installUsed": false, "itemType": "DependService_1", "length": "0", "matcher": "", "maxValues": "", "minValues": "", "namei18n": "dapCache.depend.name", "paramUnits": "", "roleId": "", "selectValue": "CustomClass:com.zte.ums.dap.manager.configuration.dynamic.DependServiceSelectValueGenerator", "serviceId": "dap.manager.deployer", "tagType": "EnumRadioParam", "validatorImpl": "", "visabled": true}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer3", "configItemAttr": "ServiceDisplay", "configItemName": "server.port", "configItemPath": "application.properties", "defaultValue": "56160", "description": "server.port.des", "displayName": "server.port", "displayOrder": 3, "effectiveImpl": "", "effectiveWay": "", "installUsed": false, "itemType": "FeagleRestPort,JmxPort", "length": "", "maxValues": "", "minValues": "", "namei18n": "server.port.name", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "PortNumberParam", "validatorImpl": "", "visabled": true}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer4", "configItemAttr": "ServiceDisplay", "configItemName": "dapmanager.host.name", "configItemPath": "application.properties", "defaultValue": "", "description": "dapmanager.host.name.des", "displayName": "dapmanager.host.name", "displayOrder": 4, "effectiveImpl": "", "effectiveWay": "", "installUsed": false, "itemType": "", "length": "", "maxValues": "", "minValues": "", "namei18n": "dapmanager.host.name", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "StringParam", "validatorImpl": "", "visabled": false}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer5", "configItemAttr": "ServiceDisplay", "configItemName": "spring.datasource.password", "configItemPath": "application.properties", "defaultValue": "Fqj63k/YfwwaPAdZwwhkCw==", "description": "spring.datasource.password.desc", "displayName": "spring.datasource.password", "displayOrder": 5, "effectiveImpl": "", "effectiveWay": "", "installUsed": true, "itemType": "AesPwd", "length": "", "matcher": "", "maxValues": "", "minValues": "", "namei18n": "spring.datasource.password.name", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "PasswordParam", "validatorImpl": "", "visabled": true}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer6", "configItemAttr": "ServiceDisplay", "configItemName": "spring.datasource.url", "configItemPath": "application.properties", "defaultValue": "******************************************", "description": "spring.datasource.url.desc", "displayName": "spring.datasource.url", "displayOrder": 6, "effectiveImpl": "", "effectiveWay": "", "installUsed": true, "itemType": "", "length": "0", "matcher": "", "maxValues": "", "minValues": "", "namei18n": "spring.datasource.url.name", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "", "validatorImpl": "", "visabled": false}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer8", "configItemAttr": "ServiceDisplay", "configItemName": "cache.data.password", "configItemPath": "application.properties", "defaultValue": "Fqj63k/YfwwaPAdZwwhkCw==", "description": "cache.data.password.des", "displayName": "cache.data.password", "displayOrder": 8, "effectiveImpl": "", "effectiveWay": "", "installUsed": false, "itemType": "AesPwd", "length": "", "maxValues": "", "minValues": "", "namei18n": "cache.data.password", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "PasswordParam", "validatorImpl": "", "visabled": false}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer9", "configItemAttr": "ServiceAndRoleDisplay", "configItemName": "eureka.instance.hostname", "configItemPath": "application.properties", "defaultValue": "", "description": "eureka.instance.hostname.des", "displayName": "eureka.instance.hostname", "displayOrder": 9, "effectiveImpl": "", "effectiveWay": "", "installUsed": false, "itemType": "deployerChangeInfluence", "length": "", "maxValues": "", "minValues": "", "namei18n": "eureka.instance.hostname", "paramUnits": "", "roleId": "dap.manager.deployer.deployer", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "StringParam", "validatorImpl": "", "visabled": false}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer10", "configItemAttr": "ServiceDisplay", "configItemName": "eureka.client.serviceUrl.defaultZone", "configItemPath": "application.properties", "defaultValue": "", "description": "eureka.client.serviceUrl.defaultZone.des", "displayName": "eureka.client.serviceUrl.defaultZone", "displayOrder": 10, "effectiveImpl": "", "effectiveWay": "", "installUsed": false, "itemType": "", "length": "", "maxValues": "", "minValues": "", "namei18n": "eureka.client.serviceUrl.defaultZone", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "StringParam", "validatorImpl": "", "visabled": false}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer11", "configItemAttr": "ServiceDisplay", "configItemName": "spring.redis.cluster.nodes", "configItemPath": "application.properties", "defaultValue": "", "description": "spring.redis.cluster.nodes.des", "displayName": "spring.redis.cluster.nodes", "displayOrder": 11, "effectiveImpl": "", "effectiveWay": "", "installUsed": false, "itemType": "", "length": "", "maxValues": "", "minValues": "", "namei18n": "spring.redis.cluster.nodes", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "StringParam", "validatorImpl": "", "visabled": false}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer12", "configItemAttr": "ServiceDisplay", "configItemName": "spring.kafka.bootstrap-servers", "configItemPath": "application.properties", "defaultValue": "", "description": "spring.kafka.bootstrap-servers.des", "displayName": "spring.kafka.bootstrap-servers", "displayOrder": 12, "effectiveImpl": "", "effectiveWay": "", "installUsed": false, "itemType": "", "length": "", "maxValues": "", "minValues": "", "namei18n": "spring.kafka.bootstrap-servers", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "StringParam", "validatorImpl": "", "visabled": false}, {"categoryName": "", "clientUsed": false, "configId": "deployer13", "configItemAttr": "ServiceAndRoleDisplay", "configItemName": "Manager.<PERSON><PERSON><PERSON>", "configItemPath": "application.properties", "defaultValue": "", "description": "Manager.hostName_desc", "displayName": "Manager.<PERSON><PERSON><PERSON>", "displayOrder": 13, "effectiveImpl": "", "effectiveWay": "effect_after_restarting", "installUsed": false, "itemType": "deployerChangeInfluence", "length": "", "matcher": "", "maxValues": "", "minValues": "localhost", "namei18n": "Manager.hostName_name", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "StringParam", "validatorImpl": "", "visabled": false}, {"categoryName": "advanced_config", "clientUsed": false, "configId": "deployer14", "configItemAttr": "ServiceAndRoleDisplay", "configItemName": "jvm.dapmanager-deployer.oom_heap_dump_dir", "configItemPath": "", "defaultValue": "/data1/Feagle/dapmanager-deployer/logs", "description": "jvm.dapmanager-deployer.oom_heap_dump_dir.desc", "displayName": "jvm.dapmanager-deployer.oom_heap_dump_dir", "displayOrder": 14, "effectiveImpl": "", "effectiveWay": "effect_after_restarting", "installUsed": true, "itemType": "DumpDir", "length": "", "matcher": "", "maxValues": "", "minValues": "", "namei18n": "jvm.dapmanager-deployer.oom_heap_dump_dir.name", "paramUnits": "", "roleId": "dap.manager.deployer.deployer", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "PathParam", "validatorImpl": "", "visabled": true}, {"categoryName": "advanced_config", "clientUsed": false, "configId": "deployer15", "configItemAttr": "ServiceAndRoleDisplay", "configItemName": "jvm.dapmanager-deployer.heapsize", "configItemPath": "application.properties", "defaultValue": "2048", "description": "jvm.dapmanager-deployer.heapsize.desc", "displayName": "jvm.dapmanager-deployer.heapsize", "displayOrder": 15, "effectiveImpl": "", "effectiveWay": "effect_after_restarting", "installUsed": true, "itemType": "JvmMemory", "length": "", "matcher": "", "maxValues": "", "minValues": "", "namei18n": "jvm.dapmanager-deployer.heapsize.name", "paramUnits": "", "roleId": "dap.manager.deployer.deployer", "selectValue": "MB,GB", "serviceId": "dap.manager.deployer", "tagType": "MemoryParam", "validatorImpl": "", "visabled": true}, {"categoryName": "logs_config", "clientUsed": false, "configId": "deployer16", "configItemAttr": "ServiceDisplay", "configItemName": "log.path", "configItemPath": "application.properties", "defaultValue": "/data1/Feagle/dapmanager-deployer/logs", "description": "log.dir_des", "displayName": "log.dir", "displayOrder": 16, "effectiveImpl": "", "effectiveWay": "effect_after_restarting", "installUsed": true, "itemType": "LogDir", "length": "", "matcher": "", "maxValues": "", "minValues": "", "namei18n": "log.dir_name", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "PathParam", "validatorImpl": "", "visabled": true}, {"categoryName": "common_config", "clientUsed": false, "configId": "deployer17", "configItemAttr": "ServiceDisplay", "configItemName": "work.key", "configItemPath": "", "defaultValue": "", "description": "", "displayName": "", "displayOrder": 17, "effectiveImpl": "", "effectiveWay": "effect_after_restarting", "installUsed": true, "itemType": "work<PERSON>ey", "length": "", "matcher": "", "maxValues": "", "minValues": "", "namei18n": "", "paramUnits": "", "roleId": "", "selectValue": "", "serviceId": "dap.manager.deployer", "tagType": "StringParam", "validatorImpl": "", "visabled": false}]