{"projectName": "feagle", "serviceName": "deployer", "version": "V20.19.40.R4.B2", "fileName": "dapmanager-deployer-V20.19.40.R4.B2.tar.gz", "install": "chmod 755 pre_install.sh;sh pre_install.sh", "uninstall": "sh ${FEAGLE_INSTALL_PATH}/dapmanager-deployer/bin/uninstall.sh", "start": "sh ${FEAGLE_INSTALL_PATH}/dapmanager-deployer/bin/restart.sh", "restart": "sh ${FEAGLE_INSTALL_PATH}/dapmanager-deployer/bin/restart.sh", "update": "", "dependency": [{"projectName": "feagle", "serviceName": "dapmanager-dependency", "version": "V20.19.40.R4.B2"}]}