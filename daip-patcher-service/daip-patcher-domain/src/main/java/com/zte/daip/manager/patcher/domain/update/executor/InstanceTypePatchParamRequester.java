/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServicePatchUpdate.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/1
 * </p>
 * <p>
 * 完成日期：2021/4/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.executor;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.*;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.patcher.domain.update.bean.ServiceNeedUpdatePatchInfo;
import com.zte.daip.manager.patcher.inner.api.dto.PatchHostInfoDto;
import com.zte.daip.manager.patcher.inner.api.dto.RollBackPatchPointInfo;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.update.request.*;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.update.executor.api.MergeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.api.OrganizeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.depend.DependResourceLocalUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

/*
 * 实例级补丁：
 * 1.每个实例自有安装目录
 * 2.每实例
 *
 *
 * */
@Slf4j
@Service("instance")
public class InstanceTypePatchParamRequester extends AbstractPatchParamRequester
    implements OrganizeRequest, MergeRequest {
    @Autowired
    private DependResourceLocalUtil dependResourceLocalUtil;

    @Override
    public List<PatchUpdateRequest> organizeRequest(String clusterId, OrganizeKey key,
        List<ServiceInstanceInfo> serviceInstances) {
        List<PatchUpdateRequest> patchUpdateRequests = Lists.newArrayList();
        for (ServiceInstanceInfo instance : serviceInstances) {
            Set<String> targetIps = buildTargetIps(clusterId, instance.getServiceInstanceId(),
                Sets.newConcurrentHashSet(instance.getIps()));
            Map<String, List<SimplePatchInfo>> ip2NeedPatches =
                patchesNeedUpdateQueryService.queryNeedUpdatePatchesByInstance(key.getServiceName(), key.getVersion(),
                    instance.getServiceInstanceId(), targetIps);
            log.debug(String.format("instance level service %s:finished to queryNeedUpdatePatches: %s",
                instance.getServiceInstanceId(), JSON.toJSONString(ip2NeedPatches)));
            Map<String, List<ServicePatchInfo>> hostName2InstancePatches = Maps.newHashMap();
            targetIps.stream().forEach(ip -> {
                List<SimplePatchInfo> instancePatches = ip2NeedPatches.get(ip);
                if (!CollectionUtils.isEmpty(instancePatches)) {

                    ServicePatchInfo servicePatchInfo = new ServicePatchInfo(key.getServiceName(), key.getVersion());
                    servicePatchInfo.setServiceInstances(
                        Lists.newArrayList(new ServiceInstance(instance.getServiceInstanceId(), Lists.newArrayList())));
                    servicePatchInfo.setPatches(instancePatches);
                    //
                    hostName2InstancePatches.put(ip, Lists.newArrayList(servicePatchInfo));
                }
            });

            PatchUpdateRequest updateRequest =
                genPatchUpdateRequests(clusterId, instance.getServiceInstanceId(), hostName2InstancePatches);
            if (Objects.nonNull(updateRequest)) {
                patchUpdateRequests.add(updateRequest);
            }
        }
        return patchUpdateRequests;
    }

    private PatchUpdateRequest genPatchUpdateRequests(String clusterId, String instance,
        Map<String, List<ServicePatchInfo>> hostName2InstancePatches) {
        if (!CollectionUtils.isEmpty(hostName2InstancePatches)) {
            String patchHome = queryPatchHome(clusterId, instance);
            PatchUpdateRequest patchUpdateRequest =
                new PatchUpdateRequest(clusterId, patchHome, PatchTypeEnum.INSTANCE.getType());
            patchUpdateRequest.setIp2Patches(hostName2InstancePatches);
            return patchUpdateRequest;
        }
        return null;
    }

    @Override
    public PatchUpdateRequest mergeRequest(PatchRequestKey key, List<PatchUpdateRequest> patchUpdateRequests) {
        if ((!CollectionUtils.isEmpty(patchUpdateRequests)) && patchUpdateRequests.size() == 1) {
            return patchUpdateRequests.get(0);
        }
        log.warn("instanceType merge before:" + JSON.toJSONString(patchUpdateRequests));
        ArrayListMultimap<String, List<ServicePatchInfo>> ip2ServicePatches = ArrayListMultimap.create();
        for (PatchUpdateRequest request : patchUpdateRequests) {
            Map<String, List<ServicePatchInfo>> hostName2Patches = request.getIp2Patches();
            hostName2Patches.forEach(ip2ServicePatches::put);
        }
        //
        Map<String, List<ServicePatchInfo>> hostName2Patches = Maps.newHashMap();
        Map<String, Collection<List<ServicePatchInfo>>> stringCollectionMap = ip2ServicePatches.asMap();

        stringCollectionMap.forEach((hostIp, values) -> {
            List<ServicePatchInfo> servicePatchInfos = Lists.newArrayList();
            values.forEach(servicePatchInfos::addAll);

            hostName2Patches.put(hostIp, Lists.newArrayList(mergeForInstanceTypePatch(servicePatchInfos)));
        });
        //
        PatchUpdateRequest finalRequest =
            new PatchUpdateRequest(key.getClusterId(), key.getPatchHome(), key.getPatchType());
        finalRequest.setIp2Patches(hostName2Patches);

        log.warn("instanceType merge after:" + JSON.toJSONString(finalRequest));
        return finalRequest;
    }

    private ServicePatchInfo mergeForInstanceTypePatch(List<ServicePatchInfo> servicePatchInfos) {
        if (CollectionUtils.isEmpty(servicePatchInfos)) {
            return new ServicePatchInfo();
        }
        Set<SimplePatchInfo> patches = Sets.newHashSet();
        Set<String> updatedContainerPatches = Sets.newHashSet();
        List<ServiceInstance> serviceInstances = new ArrayList();
        for (ServicePatchInfo servicePatchInfo : servicePatchInfos) {
            patches.addAll(servicePatchInfo.getPatches());
            updatedContainerPatches.addAll(servicePatchInfo.getUpdatedContainerPatches());
            serviceInstances.addAll(servicePatchInfo.getServiceInstances());
        }
        return new ServicePatchInfo(servicePatchInfos.get(0).getServiceName(), servicePatchInfos.get(0).getVersion(),
            serviceInstances, Lists.newArrayList(patches), Lists.newArrayList(updatedContainerPatches));
    }

    @Override
    public ServiceNeedUpdatePatchInfo queryRollbackPoints(ServiceRoleInfo serviceRoleInfo, OrganizeKey key) {
        List<ServiceRoleInfo> instanceServiceRoles =
            dependResourceLocalUtil.getClusterServiceRoleInfo(serviceRoleInfo.getClusterId());
        List<HostInfo> instanceHostInfos = dependResourceLocalUtil.getClusterHostInfo(serviceRoleInfo.getClusterId());
        if (CollectionUtils.isEmpty(instanceHostInfos) || CollectionUtils.isEmpty(instanceServiceRoles)) {
            log.warn("query rollback points service: depend resource local cache info is empty.");
            return null;
        }
        Set<String> instanceRequestIps = instanceServiceRoles.stream()
            .filter(s -> StringUtils.equals(s.getServiceId(), serviceRoleInfo.getServiceId())
                && StringUtils.equals(s.getServiceInstanceId(), serviceRoleInfo.getServiceInstanceId()))
            .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        Set<String> instanceTargetIps =
            buildTargetIps(serviceRoleInfo.getClusterId(), serviceRoleInfo.getServiceId(), instanceRequestIps);
        log.info("query rollback points service: {}, ips: {}", key.getServiceName(), instanceTargetIps);
        return generateServiceInstancePatchInfos(serviceRoleInfo, key, instanceHostInfos, instanceTargetIps);
    }

    private ServiceNeedUpdatePatchInfo generateServiceInstancePatchInfos(ServiceRoleInfo serviceRoleInfo,
        OrganizeKey key, List<HostInfo> hostInfos, Set<String> targetIps) {
        ServiceNeedUpdatePatchInfo serviceNeedUpdatePatchInfo = new ServiceNeedUpdatePatchInfo();
        serviceNeedUpdatePatchInfo.setServiceInstanceId(serviceRoleInfo.getServiceInstanceId());
        List<ServiceInstancePatchInfo> serviceInstancePatchInfos = Lists.newArrayList();
        Map<String, List<SimplePatchInfo>> instanceIp2NeedPatches =
            patchesNeedUpdateQueryService.queryNeedUpdatePatchesByInstance(key.getServiceName(), key.getVersion(),
                serviceRoleInfo.getServiceInstanceId(), targetIps);
        List<RollBackPatchPointInfo> instanceTaskPatchRollbackPoints =
            generateRollbackPoints(hostInfos, instanceIp2NeedPatches, targetIps);

        if (!CollectionUtils.isEmpty(instanceIp2NeedPatches)) {
            ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
            String targetPatchPoint = generateTargetPatch(instanceIp2NeedPatches, targetIps);

            com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance serviceInstance =
                new com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance();

            serviceInstance.setServiceInstanceId(serviceRoleInfo.getServiceInstanceId());
            serviceInstance.setVersion(key.getVersion());
            serviceInstance.setServiceName(key.getServiceName());
            serviceInstance.setServiceId(key.getServiceId());
            serviceInstance.setServiceInstanceName(
                queryInstanceName(serviceRoleInfo.getClusterId(), serviceRoleInfo.getServiceInstanceId()));
            serviceInstancePatchInfo.setServiceInstance(serviceInstance);
            serviceInstancePatchInfo.setRollBackPatchPoints(instanceTaskPatchRollbackPoints);
            serviceInstancePatchInfo.setPatchType(PatchTypeEnum.INSTANCE.getType());
            serviceInstancePatchInfo.setTargetPatchPoint(targetPatchPoint);
            serviceInstancePatchInfos.add(serviceInstancePatchInfo);
            /* Started by AICoder, pid:27f62g1876d98741480609b6c01183041fe68970 */
            // 收集需要更新的补丁名称列表
            List<String> needUpdatePatch = targetIps.stream()
                    .flatMap(ip -> instanceIp2NeedPatches.getOrDefault(ip, Collections.emptyList()).stream())
                    .map(SimplePatchInfo::getPatchName)
                    .distinct()
                    .collect(Collectors.toList());
            /* Ended by AICoder, pid:27f62g1876d98741480609b6c01183041fe68970 */
            serviceNeedUpdatePatchInfo.setNeedUpdatePatchList(needUpdatePatch);
            serviceNeedUpdatePatchInfo.setPatchInfoList(serviceInstancePatchInfos);
        }
        return serviceNeedUpdatePatchInfo;
    }

    private List<RollBackPatchPointInfo> generateRollbackPoints(List<HostInfo> hostInfos,
        Map<String, List<SimplePatchInfo>> instanceIp2NeedPatches, Set<String> targetIps) {
        ListMultimap<String, PatchHostInfoDto> instancePatchName2Hosts = ArrayListMultimap.create();
        List<RollBackPatchPointInfo> instanceTaskPatchRollbackPoints = Lists.newArrayList();
        for (String ip : targetIps) {
            List<SimplePatchInfo> instancePatchesOfTheHost = instanceIp2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(instancePatchesOfTheHost)) {
                String rollbackPatchPoint = instancePatchesOfTheHost.stream().filter(p -> !p.isContainer())
                    .map(SimplePatchInfo::getPatchName).min(String::compareToIgnoreCase).orElse("");
                PatchHostInfoDto patchHostInfoDto = new PatchHostInfoDto();
                patchHostInfoDto.setHostName(hostInfos.stream().filter(h -> StringUtils.equals(h.getIpAddress(), ip))
                    .map(HostInfo::getHostName).findFirst().orElse(""));
                patchHostInfoDto.setIp(ip);
                instancePatchName2Hosts.put(rollbackPatchPoint, patchHostInfoDto);
            }
        }
        for (String key : instancePatchName2Hosts.keySet()) {
            RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
            rollBackPatchPointInfo.setRollBackPatchPoint(key);
            rollBackPatchPointInfo.setPatchHostInfos(instancePatchName2Hosts.get(key));
            instanceTaskPatchRollbackPoints.add(rollBackPatchPointInfo);
        }
        return instanceTaskPatchRollbackPoints;
    }

    private String generateTargetPatch(Map<String, List<SimplePatchInfo>> instanceIp2NeedPatches,
        Set<String> targetIps) {
        for (String ip : targetIps) {
            List<SimplePatchInfo> instancePatchesOfTheHost = instanceIp2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(instancePatchesOfTheHost)) {
                return instancePatchesOfTheHost.stream().filter(p -> !p.isContainer())
                    .map(SimplePatchInfo::getPatchName).max(String::compareToIgnoreCase).orElse("");
            }
        }
        return "";
    }

    private Set<String> buildTargetIps(String clusterId, String instanceId, Set<String> requestIps) {
        List<ServiceRoleInfo> serviceRoles =
            dependResourceLocalUtil.queryServiceRoleInfoByServiceInstanceId(clusterId, instanceId);
        Set<String> theInstanceAllIps =
            serviceRoles.stream().map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());

        return CollectionUtils.isEmpty(requestIps) ? theInstanceAllIps : requestIps;
    }
}