/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UploadedPatchQueryService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/18
 * </p>
 * <p>
 * 完成日期：2021/3/18
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.paging.jpa.QueryParam;
import com.zte.daip.manager.common.utils.pool.DaipThreadPoolExecutor;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.api.dto.PendingUpdatePatchPageInfo;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchDispatchService;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchDetailDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchDispatchPoAssembler;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchDetailParam;
import com.zte.daip.manager.patcher.domain.query.valobj.UnpatchedParam;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatch;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchDetailService {
    @Autowired
    private PatchInfoService patchInfoService;
    @Autowired
    private PatchHistoryService patchHistoryService;
    @Autowired
    private PatchDispatchService patchDispatchService;
    @Autowired
    private UnpatchedService unpatchedService;
    @Autowired
    private PatchDetailAsyncQueryService patchDetailAsyncQueryService;
    @Autowired
    private PatchDetailDtoAssembler patchDetailDtoAssembler;

    private static final String UPDATE_FLAG = "updated";

    public List<PatchDetailDto> queryAllPatch() {

        List<PatchDetailPo> patchDetails = patchInfoService.queryAllPatchInfos();

        List<String> patchDetailPatchNameList =
            patchDetails.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toList());

        return getPatchDetailDtos(patchDetails, patchDetailPatchNameList);
    }

    public PendingUpdatePatchPageInfo queryPatchesByPaging(QueryParam query) {
        PendingUpdatePatchPageInfo pendingUpdatePatchPageInfo = new PendingUpdatePatchPageInfo();

        final Page<PatchDetailPo> patchDetailPos = patchInfoService.queryPatchInfoByPaging(query);

        final List<PatchDetailPo> patchDetailPosContent = patchDetailPos.getContent();

        final List<String> patchDetailPatchNameList = patchInfoService.queryPatchName();

        final List<PatchDetailDto> patchDetailDtos =
            getPatchDetailDtos(patchDetailPosContent, patchDetailPatchNameList);

        pendingUpdatePatchPageInfo.setIDisplayLength(patchDetailPos.getPageable().getPageSize());
        pendingUpdatePatchPageInfo.setIDisplayStart((int)patchDetailPos.getPageable().getOffset());
        pendingUpdatePatchPageInfo.setITotalDisplayRecords((int)patchDetailPos.getTotalElements());
        pendingUpdatePatchPageInfo.setITotalRecords(patchDetailPatchNameList.size());
        pendingUpdatePatchPageInfo.setData(patchDetailDtos);

        return pendingUpdatePatchPageInfo;
    }

    public List<String> queryAllPatchType() {
        List<PatchDetailPo> patchDetails = patchInfoService.queryAllPatchInfos();
        return Optional.ofNullable(patchDetails).orElse(Lists.newArrayList()).stream()
            .map(patchDetailPo -> StringUtils.isBlank(patchDetailPo.getRoles()) ? patchDetailPo.getService()
                : String.format("%s(%s)", patchDetailPo.getService(), patchDetailPo.getRoles()))
            .distinct().collect(Collectors.toList());
    }

    private List<PatchDetailDto> getPatchDetailDtos(List<PatchDetailPo> patchDetails,
        List<String> patchDetailPatchNameList) {
        UnpatchedParam unpatchedParam = unpatchedService.getUnpatchedParam();

        final Map<PatchKeyDo, Map<Boolean, List<PatchDispatch>>> patchDispatchStatisticsMap =
            getPatchDispatchStatisticsMap();

        PatchDetailParam patchDetailParam = new PatchDetailParam(patchDetailPatchNameList, patchDispatchStatisticsMap,
            unpatchedParam.getPatchHistoryInfoMap());

        List<Future<PatchDetailDto>> futureList = new ArrayList<>();

        List<PatchDetailDto> patchDetailDtos = patchDetails.stream().filter(Objects::nonNull)
            .map(patchDetailDtoAssembler::patchDetail2Dto).collect(Collectors.toList());

        for (PatchDetailDto patchDetailDto : patchDetailDtos) {

            final Future<PatchDetailDto> future =
                patchDetailAsyncQueryService.calcOnePatchPatchDetail(patchDetailParam, unpatchedParam, patchDetailDto);
            futureList.add(future);

        }
        final List<PatchDetailDto> patchDetailDtoList = DaipThreadPoolExecutor.waitThreadsEnd(futureList);
        return patchDetailDtoList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Map<PatchKeyDo, Map<Boolean, List<PatchDispatch>>> getPatchDispatchStatisticsMap() {
        final List<PatchDispatch> patchDispatches = patchDispatchService.queryAllPatchDispatchInfo();
        PatchDispatchPoAssembler patchDispatchPoAssembler = new PatchDispatchPoAssembler();
        return patchDispatches.stream().collect(Collectors.groupingBy(patchDispatchPoAssembler::patchDispatch2Do,
            Collectors.groupingBy(PatchDispatch::isSuccess)));
    }

    public boolean checkPatchIsLoaded(String patchName) {
        List<PatchDetailPo> allPatchInfos = patchInfoService.queryAllPatchInfos();
        for (PatchDetailPo patchDetailPo : allPatchInfos) {
            if (StringUtils.equals(patchName, patchDetailPo.getPatchName())) {
                return true;
            }
        }
        return false;
    }

    public List<PatchBean> queryNeedDispatchPatchListMap() {

        final List<PatchDetailPo> patchInfoRepositoryAll = patchInfoService.queryAllPatchInfos();

        return patchInfoRepositoryAll.stream().map(this::generatePatchBean).collect(Collectors.toList());

    }

      public List<PatchBean> queryNeedDispatchExceptSchemaPatch() {
        final List<PatchDetailPo> patchInfoRepositoryAll = patchInfoService.queryAllPatchExceptScheme();

        return patchInfoRepositoryAll.stream().map(this::generatePatchBean).collect(Collectors.toList());
    }

    private PatchBean generatePatchBean(PatchDetailPo patchDetailPo) {
        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName(patchDetailPo.getPatchName());
        patchBean.setService(patchDetailPo.getService());
        patchBean.setSrcVersion(patchDetailPo.getBaseVersion());
        return patchBean;
    }

    public List<PatchHistoryDto> queryPatchesHistoryByUpdateType(String patchName, String serviceName, String version,
        String updateType) {

        if (StringUtils.isBlank(updateType) || StringUtils.equalsIgnoreCase(UPDATE_FLAG, updateType)) {
            return patchHistoryService.queryPatchDispatchInfoByPatchKey(new PatchKeyDo(patchName, serviceName, ""));
        }
        UnpatchedParam unpatchedParam = unpatchedService.getUnpatchedParam();
        final List<PatchDetailPo> patchDetailPoList =
            patchInfoService.findByPatchNameAnderviceAndBaseVersion(patchName, serviceName, version);

        if (CollectionUtils.isEmpty(patchDetailPoList)) {
            return Lists.newArrayList();
        }
        PatchDetailPo patchDetailPo = patchDetailPoList.get(0);

        final Map<String, Set<PatchHistoryDto>> patchUnpatchedService2Hosts = patchDetailAsyncQueryService
            .calcOnePatchUnpatchedService2Hosts(unpatchedParam, patchDetailDtoAssembler.patchDetail2Dto(patchDetailPo));

        if (null == patchUnpatchedService2Hosts) {
            return Lists.newArrayList();
        }
        return patchUnpatchedService2Hosts.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
    }

}