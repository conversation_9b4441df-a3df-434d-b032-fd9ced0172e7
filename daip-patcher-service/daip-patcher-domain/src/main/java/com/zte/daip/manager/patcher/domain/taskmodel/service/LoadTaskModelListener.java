package com.zte.daip.manager.patcher.domain.taskmodel.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.daip.communication.api.ConsumerHandler;
import com.zte.daip.communication.api.EventListenerTo;
import com.zte.daip.communication.util.KafkaMessageKey;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@EventListenerTo(KafkaMessageKey.TASKMANAGEMENT_APPLICATION_STARTED_MSG)
public class LoadTaskModelListener implements ConsumerHandler<String>
{
    @Autowired
    private TaskModelLoadService taskModelLoadService;

    @Override
    public String handle(String body)
    {
        log.info("receive msg body: [{}]", body);
        taskModelLoadService.loadTaskModel();
        return "success";
    }
}