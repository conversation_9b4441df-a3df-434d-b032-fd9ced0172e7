package com.zte.daip.manager.patcher.domain.upload.operate;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class PatchTypeOperateFactory {

    private Map<String, PatchTypeOperator> typeOperator;

    @Autowired(required = false)
    public PatchTypeOperateFactory(List<PatchTypeOperator> typeOperator) {
        this.typeOperator =
            typeOperator.stream().collect(Collectors.toMap(PatchTypeOperator::patchType, algorithm -> algorithm));
    }

    public PatchTypeOperator getPatchType(PatchBean patchBean) {
        String patchType = Constants.NORMAL_PATCH;
        String patchName = patchBean.getPatchName();
        if (patchName.contains(Constants.SCHEMA_PATCH)) {
            patchType = Constants.SCHEMA_PATCH;
        } else if (patchName.contains(Constants.REPOSITORY_VERSION_PATCH)) {
            patchType = Constants.REPOSITORY_VERSION_PATCH;
        } else if (patchBean.isContainPatch()) {
            patchType = Constants.CONTAINER_PATCH;
        }
        return Optional.ofNullable(typeOperator.get(patchType)).orElse(new NormalPatchOperator());
    }
}
