package com.zte.daip.manager.patcher.domain.upload.cache;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchValidateBean;
import com.zte.daip.manager.patcher.domain.upload.event.PatchFullValidateEvent;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatchValidatedResultCache {

    @Autowired
    private ApplicationContext applicationContext;

    private LoadingCache<PatchValidateBean, PatchUploadResult> cache = CacheBuilder.newBuilder().maximumSize(5000)
        .expireAfterAccess(25, TimeUnit.MINUTES).build(new CacheLoader<PatchValidateBean, PatchUploadResult>() {
            @Override
            public PatchUploadResult load(PatchValidateBean key) throws Exception {
                return null;
            }
        });

    public void initValidateResult(List<PatchValidateBean> patchValidateBeans) {
        patchValidateBeans.forEach(patchValidateBean -> {
            PatchUploadResult patchUploadResult = new PatchUploadResult();
            patchUploadResult.setOriginSetName(patchValidateBean.getOriginName());
            cache.put(patchValidateBean, patchUploadResult);
        });
    }

    public void updateNormalValidateResult(PatchValidateBean patchValidateBean, PatchUploadResult patchUploadResult) {
        patchUploadResult.setOriginSetName(patchValidateBean.getOriginName());
        cache.put(patchValidateBean, patchUploadResult);
        String batchId = patchValidateBean.getBatchId();
        if (checkNormalFinish(batchId)) {
            applicationContext.publishEvent(new PatchFullValidateEvent(batchId));
        }
    }

    private boolean checkNormalFinish(String batchId) {
        List<PatchUploadResult> normalResults = queryAllResultByBatchId(batchId);
        List<
            PatchUploadResult> unFinishResults =
                normalResults
                    .stream().filter(patchUploadResult -> !StringUtils
                        .equalsIgnoreCase(patchUploadResult.getOneProcess(), OnePatchProcess.NORMAL_CHECK))
                    .collect(Collectors.toList());
        return CollectionUtils.isEmpty(unFinishResults);
    }

    public void updateValidateResult(PatchValidateBean patchValidateBean, PatchUploadResult patchUploadResult) {
        patchUploadResult.setOriginSetName(patchValidateBean.getOriginName());
        cache.put(patchValidateBean, patchUploadResult);
    }

    public void updateAllFinish(String batchId) {
        cache.asMap().forEach((patchValidateBean, patchUploadResult) -> {
            if (patchValidateBean.getBatchId().equals(batchId)) {
                patchUploadResult.setOriginSetName(patchValidateBean.getOriginName());
                patchUploadResult.setOneProcess(OnePatchProcess.FINISH);
                cache.put(patchValidateBean, patchUploadResult);
            }
        });
    }

    public void updateUploadFailMsg(String batchId) {
        cache.asMap().forEach((patchValidateBean, patchUploadResult) -> {
            if (StringUtils.equals(batchId, patchValidateBean.getBatchId()) && patchUploadResult.isSuccess()) {
                patchUploadResult.setSuccess(false);
                patchUploadResult.setOneProcess(OnePatchProcess.FINISH);
                patchUploadResult.setOriginSetName(patchValidateBean.getOriginName());
                patchUploadResult.setMessage("upload to local repository fail");
                cache.put(patchValidateBean, patchUploadResult);
            }
        });
    }

    public List<PatchUploadResult> queryAllResultByBatchId(String batchId) {
        return cache.asMap().entrySet().stream().filter(entry -> entry.getKey().getBatchId().equals(batchId))
            .map(Map.Entry::getValue).collect(Collectors.toList());
    }

    public Map<PatchValidateBean, PatchUploadResult> queryResultMapByBatchId(String batchId) {
        Map<PatchValidateBean, PatchUploadResult> fullPatchValidateLists = Maps.newHashMap();
        cache.asMap().forEach((patchValidateBean, patchUploadResult) -> {
            if (patchValidateBean.getBatchId().equals(batchId) && patchUploadResult.isSuccess()) {
                fullPatchValidateLists.put(patchValidateBean, patchUploadResult);
            }
        });
        return fullPatchValidateLists;
    }

    public List<PatchUploadResult> querySuccessResultByBatchId(String batchId) {
        return cache.asMap().entrySet().stream()
            .filter(entry -> entry.getKey().getBatchId().equals(batchId) && entry.getValue().isSuccess())
            .map(Map.Entry::getValue).collect(Collectors.toList());
    }
}
