package com.zte.daip.manager.patcher.domain.rollback.executor;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.dto.BigDataPatchRollbackMsg;
import com.zte.daip.manager.patcher.api.dto.PatchHostInfo;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */

/* Started by AICoder, pid:28b59o991c4db0d149c00beb70a6634f0d26938c */
@Service
public class BigDataTypeRollbackExecutor {

    @Autowired
    private PublishRollbackMsg publishRollbackMsg;

    public void executeRollback(String clusterId, List<PatchRollbackHostDto> bigDataPatchRollbackHostDtos) {
        List<BigDataPatchRollbackMsg> bigDataPatchRollbackMsgs =
            createBigDataPatchRollBackMsg(clusterId, bigDataPatchRollbackHostDtos);
        publishRollbackMsg.publishBigDataRollbackMsg(clusterId, bigDataPatchRollbackHostDtos, bigDataPatchRollbackMsgs);
    }

    private List<BigDataPatchRollbackMsg> createBigDataPatchRollBackMsg(String clusterId,
        List<PatchRollbackHostDto> bigDataPatchRollbackHostDtos) {
        ListMultimap<String, PatchRollbackHostDto> patchRollbackHostDtoMap = ArrayListMultimap.create();
        for (PatchRollbackHostDto patchRollbackHostDto : bigDataPatchRollbackHostDtos) {
            List<PatchHostInfo> patchHostInfoList = patchRollbackHostDto.getPatchHostInfoList();
            patchHostInfoList.forEach(
                patchHostInfo -> patchRollbackHostDtoMap.put(patchHostInfo.getHostName(), patchRollbackHostDto));
        }

        ListMultimap<BigDataPatchRollbackMsg, String> patchRollbackMsgMap = ArrayListMultimap.create();
        for (String hostName : patchRollbackHostDtoMap.keySet()) {
            List<PatchRollbackHostDto> patchRollbackHostDtos = patchRollbackHostDtoMap.get(hostName);
            List<String> patchNames =
                patchRollbackHostDtos.stream().map(PatchRollbackHostDto::getPatchName).collect(Collectors.toList());
            List<String> serviceNames =
                patchRollbackHostDtos.stream().map(PatchRollbackHostDto::getServiceName).collect(Collectors.toList());
            BigDataPatchRollbackMsg bigDataPatchRollbackMsg = new BigDataPatchRollbackMsg();
            bigDataPatchRollbackMsg.setClusterId(clusterId);
            bigDataPatchRollbackMsg.setServiceNames(serviceNames);
            bigDataPatchRollbackMsg.setPatchNames(patchNames);
            patchRollbackMsgMap.put(bigDataPatchRollbackMsg, hostName);
        }
        List<BigDataPatchRollbackMsg> bigDataPatchRollbackMsgs = Lists.newArrayList();
        for (BigDataPatchRollbackMsg bigDataPatchRollbackMsg : patchRollbackMsgMap.keySet()) {
            bigDataPatchRollbackMsg.setHostNames(patchRollbackMsgMap.get(bigDataPatchRollbackMsg));
            bigDataPatchRollbackMsgs.add(bigDataPatchRollbackMsg);
        }
        return bigDataPatchRollbackMsgs;
    }
}

/* Ended by AICoder, pid:28b59o991c4db0d149c00beb70a6634f0d26938c */