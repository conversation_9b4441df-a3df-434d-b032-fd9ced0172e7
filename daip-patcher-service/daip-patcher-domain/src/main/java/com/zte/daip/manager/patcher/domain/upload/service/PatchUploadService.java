package com.zte.daip.manager.patcher.domain.upload.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.cache.lock.LockUtils;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatedResultCache;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatingQueue;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchValidateBean;
import com.zte.daip.manager.patcher.domain.upload.event.PatchValidateEvent;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.glassfish.jersey.internal.guava.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatchUploadService {

    @Autowired
    private DaipEventReporter daipEventReporter;

    @Autowired
    private PatchEnvApi patchEnvApi;

    @Autowired
    private PatchValidatingQueue patchValidatingQueue;

    @Autowired
    private PatchValidatedResultCache patchValidatedResultCache;

    @Autowired
    private PatchSetsUnzipService patchSetsUnzipService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private LockUtils lockUtils;

    public String uploadPatches(List<String> validatePatches) {
        log.info("Start to upload patches. ");
        File uploadDir = FilePathCleaner.newFile(patchEnvApi.getRepositoryHomeEnv(), "upload");
        if (uploadDir != null) {
            List<File> patchList = Arrays
                .stream(Objects
                    .requireNonNull(uploadDir.listFiles((d, s) -> validatePatches.contains(s) && s.endsWith(".zip"))))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(patchList)) {
                log.info("No patches need upload.");
                daipEventReporter.info(I18nKeyConstants.HANDLER_PATCH_FILES, "No patches need upload.");
                return "";
            }
            String batchId = String.valueOf(Calendar.getInstance().getTimeInMillis());
            List<PatchValidateBean> patchValidateBeanList = generatePatchLists(patchList, batchId);
            patchValidatingQueue.addValidatePatches(patchValidateBeanList);
            patchValidatedResultCache.initValidateResult(patchValidateBeanList);
            applicationContext.publishEvent(new PatchValidateEvent(batchId));
            return batchId;
        }
        return "";
    }

    private List<PatchValidateBean> generatePatchLists(List<File> patchList, String batchId) {
        /* Started by AICoder, pid:n2f729b47ac377014daf098b0003d52ba352df0d */
        File uploadDir = FilePathCleaner.newFile(patchEnvApi.getRepositoryHomeEnv(), "upload");
        List<PatchValidateBean> patchValidateBeans = Lists.newArrayList();
        Set<String> uniqueFilenames = Sets.newHashSet();
        List<File> patchSetList = patchList.stream()
            .filter(file -> StringUtils.containsIgnoreCase(file.getName(), "patches")).collect(Collectors.toList());
        for (File patchSet : patchSetList) {
            List<File> files = patchSetsUnzipService.unzipPatchSets(patchSet);
            copyFile(files, uploadDir);
            files.forEach(file -> {
                if (!uniqueFilenames.contains(file.getName())) {
                    uniqueFilenames.add(file.getName());
                    File patch = FilePathCleaner.newFile(patchEnvApi.getRepositoryHomeEnv() + File.separator + "upload",
                        file.getName());
                    String originName = patchSet.getName().substring(0, patchSet.getName().indexOf(".zip"));
                    patchValidateBeans.add(new PatchValidateBean(batchId, patch, originName));
                }
            });
            deleteUnzipTmpDir(patchSet);
        }
        List<File> patchFileList = patchList.stream()
            .filter(file -> !StringUtils.containsIgnoreCase(file.getName(), "patches")).collect(Collectors.toList());
        patchFileList.forEach(file -> {
            if (!uniqueFilenames.contains(file.getName())) {
                uniqueFilenames.add(file.getName());
                patchValidateBeans.add(new PatchValidateBean(batchId, file));
            }
        });
        return patchValidateBeans;
        /* Ended by AICoder, pid:n2f729b47ac377014daf098b0003d52ba352df0d */
    }

    private void deleteUnzipTmpDir(File patchSet) {
        try {
            String patchSetFileName = patchSet.getName().substring(0, patchSet.getName().indexOf(".zip"));
            String unzipDirStr = patchEnvApi.getRepositoryHomeEnv() + "/upload/" + patchSetFileName;
            File unzipDir = FilePathCleaner.newFile(unzipDirStr);
            if (unzipDir != null) {
                FileUtils.deleteDirectory(unzipDir);
            }
            FileUtils.delete(patchSet);
        } catch (IOException e) {
            log.error("delete unzip patchSets tmpDir error", e);
        }

    }

    private void copyFile(List<File> files, File uploadDir) {
        try {
            FileUtils.copyToDirectory(files, uploadDir);
        } catch (IOException e) {
            log.error("copy files error", e);
            daipEventReporter.error(I18nKeyConstants.COPY_FILE, "copy files error");
        }
    }

    public void afterIOCBean() {
        log.info("afterIOCBean patcher has been initialized.start loading patch for the first time. ");
        File uploadDir = FilePathCleaner.newFile(patchEnvApi.getRepositoryHomeEnv(), "upload");
        if (uploadDir != null) {
            File[] patchFiles = uploadDir.listFiles((d, s) -> s.endsWith(".zip"));
            if (patchFiles == null || patchFiles.length == 0) {
                return;
            }
            List<String> fileNames = Arrays.stream(patchFiles).map(File::getName).collect(Collectors.toList());
            uploadPatches(fileNames);
        }
    }

    public void uploadPatchAfterIocBean() {
        log.info("uploadPatchAfterIocBean patcher has been initialized.start loading patch for the first time. ");
        File uploadDir = FilePathCleaner.newFile(patchEnvApi.getPatchUploadHomeEnv());
        File[] patchFiles = uploadDir.listFiles((d, s) -> s.endsWith(".zip"));
        if (patchFiles == null || patchFiles.length == 0) {
            return;
        }
        List<String> fileNames = Lists.newArrayList();
        File repositoryHomeDir = FilePathCleaner.newFile(patchEnvApi.getRepositoryHomeEnv(), "upload");
        Lists.newArrayList(patchFiles).forEach(file -> {
            try {
                lockUtils.executeWithLockAndReturnWithLocked("upload:" + file.getName(), 180 * 1000L, () -> {
                    extracted(file, repositoryHomeDir, fileNames);
                });
            } catch (Exception e) {
                log.error("lock exception:", e);
            }
        });
        uploadPatches(fileNames);
    }

    private void extracted(File file, File repositoryHomeDir, List<String> fileNames) {
        try {
            if (file.exists()) {
                log.debug("copy patchFile ");
                FileUtils.copyFileToDirectory(file, repositoryHomeDir);
                fileNames.add(file.getName());
                log.debug("delete  patchFile result:{}", FileUtils.delete(file));
            }
        } catch (IOException e) {
            log.error("copy {} error.", file.getName());
        }
    }

    public List<PatchUploadResult> uploadPatchesProcess(String batchId) {
        return patchValidatedResultCache.queryAllResultByBatchId(batchId);
    }
}
