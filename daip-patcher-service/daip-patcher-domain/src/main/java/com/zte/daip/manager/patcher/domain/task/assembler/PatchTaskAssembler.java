package com.zte.daip.manager.patcher.domain.task.assembler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.task.common.enums.UpgradeTarget;
import com.zte.daip.manager.common.task.common.worker.ServiceInfo;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchCategoryEnum;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchOperateTypeEnum;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskModifyPermisionEnum;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskTypeEnum;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowRequest;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowSummaryRequest;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Date;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchTaskAssembler {
    private static final String COMPONENT_TYPE = "dap.manager.components";

    private DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    public PatchTaskPo convertPatchTaskDto2Po(PatchTaskDto patchTaskDto) {
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setTaskId(patchTaskDto.getTaskId());
        patchTaskPo.setTaskName(patchTaskDto.getTaskName());
        patchTaskPo.setTaskType(patchTaskDto.getTaskType());
        patchTaskPo.setClusterId(patchTaskDto.getClusterId());
        patchTaskPo.setClusterName(patchTaskDto.getClusterName());
        patchTaskPo.setOperateType(patchTaskDto.getOperateType());
        patchTaskPo
            .setAllowModify(patchTaskDto.isAllowModify() ? PatchTaskModifyPermisionEnum.ALLOW.getModifyPermision()
                : PatchTaskModifyPermisionEnum.DENIED.getModifyPermision());
        patchTaskPo.setPatchCategory(patchTaskDto.getPatchCategory());
        if (patchTaskDto.getCreateTime() != null) {
            patchTaskPo.setCreateTime(Date.from(
                LocalDateTime.parse(patchTaskDto.getCreateTime(), df).atZone(ZoneId.systemDefault()).toInstant()));
        }
        patchTaskPo.setRelationTaskId(patchTaskDto.getRelationTaskId());
        patchTaskPo.setContext(JSONObject.toJSONString(patchTaskDto.getContext()));
        patchTaskPo.setRelationServices(JSONObject.toJSONString(patchTaskDto.getRelationServices()));
        if (null != patchTaskDto.getNeedRestartServices()){
            patchTaskPo.setNeedRestartServices(JSONObject.toJSONString(patchTaskDto.getNeedRestartServices()));
        }
        return patchTaskPo;
    }

    public PatchTaskDto convertPatchTaskPo2Dto(PatchTaskPo patchTaskPo) throws DaipBaseException {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setTaskId(patchTaskPo.getTaskId());
        patchTaskDto.setTaskName(patchTaskPo.getTaskName());
        patchTaskDto.setTaskType(patchTaskPo.getTaskType());
        patchTaskDto.setClusterId(patchTaskPo.getClusterId());
        patchTaskDto.setClusterName(patchTaskPo.getClusterName());
        patchTaskDto.setOperateType(patchTaskPo.getOperateType());
        patchTaskDto
            .setAllowModify(patchTaskPo.getAllowModify() == PatchTaskModifyPermisionEnum.ALLOW.getModifyPermision());
        patchTaskDto.setPatchCategory(patchTaskPo.getPatchCategory());
        patchTaskDto.setAccessType(assemblePatchTaskPo2AccessType(patchTaskPo));
        if (patchTaskPo.getCreateTime() != null) {
            patchTaskDto.setCreateTime(
                df.format(patchTaskPo.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()));
        }
        patchTaskDto.setRelationTaskId(patchTaskPo.getRelationTaskId());
        List<ServiceInstancePatchInfo> data =
            JSONObject.parseObject(patchTaskPo.getContext(), new TypeReference<List<ServiceInstancePatchInfo>>() {});
        patchTaskDto.setContext(data);
        List<ServiceInstance> relationInstanceIds =
            JSONObject.parseObject(patchTaskPo.getRelationServices(), new TypeReference<List<ServiceInstance>>() {});
        patchTaskDto.setRelationServices(relationInstanceIds);
        if (!StringUtils.isEmpty(patchTaskPo.getNeedRestartServices())) {
            List<ServiceInstance> needRestartServices = JSONObject.parseObject(patchTaskPo.getNeedRestartServices(),
                new TypeReference<List<ServiceInstance>>() {});
            patchTaskDto.setNeedRestartServices(needRestartServices);
        }
        return patchTaskDto;
    }

    public NestWorkFlowRequest patchTaskPo2NestWorkRequest(PatchTaskPo patchTaskPo) throws DaipBaseException {
        return patchTaskPo2NestWorkRequest(patchTaskPo, null);
    }

    public NestWorkFlowRequest patchTaskPo2NestWorkRequest(PatchTaskPo patchTaskPo, PatchTaskPo relationPatchTaskPo)
        throws DaipBaseException {
        List<ServiceModel> serviceModels = Lists.newArrayList();
        if (StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.ORDINARY.getPatchCategory())) {
            serviceModels = productModelInfoControllerApi.queryByClusterId(patchTaskPo.getClusterId());
        }
        NestWorkFlowRequest nestWorkFlowRequest = new NestWorkFlowRequest();
        nestWorkFlowRequest.setParentId(patchTaskPo.getTaskId());
        nestWorkFlowRequest.setParentName(patchTaskPo.getTaskName());
        nestWorkFlowRequest.setClusterId(NumberUtils.toInt(patchTaskPo.getClusterId()));
        if (StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.SCHEMA.getPatchCategory())) {
            nestWorkFlowRequest.setClusterName("all");
        } else {
            nestWorkFlowRequest.setClusterName(patchTaskPo.getClusterName());
        }
        List<ServiceInfo> netWorkFlowServices = assembleNetWorkFlowServices(patchTaskPo, serviceModels);
        List<String> netWorkFlowHosts = assembleNetWorkFlowHosts(patchTaskPo, netWorkFlowServices);
        nestWorkFlowRequest.setHostList(netWorkFlowHosts);
        nestWorkFlowRequest.setServiceInfoList(netWorkFlowServices);
        String accessType = assemblePatchTaskPo2AccessType(patchTaskPo);
        nestWorkFlowRequest.setAccessType(accessType);
        assembleRelationNetWorkFlowRequest(relationPatchTaskPo, nestWorkFlowRequest);
        nestWorkFlowRequest.setOperatorTarget(UpgradeTarget.SERVICE.getTarget());
        return nestWorkFlowRequest;
    }

    public NestWorkFlowSummaryRequest patchTaskPo2NestWorkFlowSummaryRequest(PatchTaskPo patchTaskPo)
        throws DaipBaseException {
        NestWorkFlowSummaryRequest nestWorkFlowSummaryRequest = new NestWorkFlowSummaryRequest();

        nestWorkFlowSummaryRequest.setParentId(patchTaskPo.getTaskId());
        nestWorkFlowSummaryRequest.setParentName(patchTaskPo.getTaskName());
        String accessType = assemblePatchTaskPo2AccessType(patchTaskPo);
        nestWorkFlowSummaryRequest.setAccessType(accessType);
        return nestWorkFlowSummaryRequest;
    }

    public String assemblePatchTaskPo2AccessType(PatchTaskPo patchTaskPo) throws DaipBaseException {
        PatchTaskTypeEnum patchTaskTypeEnum = PatchTaskTypeEnum.queryPatchTaskType(patchTaskPo.getTaskType());
        if (patchTaskTypeEnum == null) {
            throw new DaipBaseException(
                String.format("failed to assemble task access type: %s, task type is null", patchTaskPo.getTaskName()));
        }
        PatchOperateTypeEnum patchOperateTypeEnum =
            PatchOperateTypeEnum.queryPatchOperateType(patchTaskPo.getOperateType());
        if (patchOperateTypeEnum == null) {
            throw new DaipBaseException(String.format("failed to assemble task access type: %s, operate type is null",
                patchTaskPo.getTaskName()));
        }
        String taskTypeName = patchTaskTypeEnum.getTaskTypeName();
        String operateTypeName = patchOperateTypeEnum.getOperateTypeName();
        if (StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.ORDINARY.getPatchCategory())) {
            return String.format("%s_%s_%s", operateTypeName, taskTypeName, "patch");
        } else {
            return String.format("%s_%s_%s", operateTypeName, taskTypeName, "schema_patch");
        }
    }

    public List<ServiceRoleInfo> queryServiceRoleByInstanceIds(String clusterId, List<String> instanceIds) {
        List<ServiceRoleInfo> serviceInstanceInfos = serviceResourceControllerApi.queryByClusterId(clusterId);
        if (CollectionUtils.isEmpty(serviceInstanceInfos)) {
            log.info("queryTaskRollBackResource: service instances is empty, cluster: {}, instance: {}", clusterId,
                instanceIds);
            return Lists.newArrayList();
        }
        return serviceInstanceInfos.stream().filter(s -> instanceIds.contains(s.getServiceInstanceId()))
            .collect(Collectors.toList());
    }

    private List<String> assembleNetWorkFlowHosts(PatchTaskPo patchTaskPo, List<ServiceInfo> serviceInfoList) {
        if (StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.SCHEMA.getPatchCategory())) {
            return Lists.newArrayList();
        }
        String clusterId = patchTaskPo.getClusterId();
        List<String> instanceIds =
            serviceInfoList.stream().map(ServiceInfo::getServiceInstanceId).distinct().collect(Collectors.toList());
        List<ServiceRoleInfo> serviceRoleInfos = queryServiceRoleByInstanceIds(clusterId, instanceIds);
        return serviceRoleInfos.stream().map(ServiceRoleInfo::getIpAddress).distinct().collect(Collectors.toList());
    }

    private List<ServiceInfo> assembleNetWorkFlowServices(PatchTaskPo patchTaskPo, List<ServiceModel> serviceModels)
        throws DaipBaseException {
        if (CollectionUtils.isEmpty(serviceModels)
            && StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.ORDINARY.getPatchCategory())) {
            throw new DaipBaseException(String.format("failed to assemble task access type: %s, service model is null",
                patchTaskPo.getTaskName()));
        }
        List<ServiceInstancePatchInfo> context =
            JSONObject.parseObject(patchTaskPo.getContext(), new TypeReference<List<ServiceInstancePatchInfo>>() {});
        if (CollectionUtils.isEmpty(context)) {
            throw new DaipBaseException(
                String.format("failed to assemble task access type: %s, context is null", patchTaskPo.getTaskName()));
        }
        List<ServiceInstance> relationServices =
            context.stream().map(ServiceInstancePatchInfo::getServiceInstance).collect(Collectors.toList());
        return relationServices.stream().map(r -> convertServiceInfo(serviceModels, r)).collect(Collectors.toList());
    }

    private void assembleRelationNetWorkFlowRequest(PatchTaskPo relationPatchTaskPo,
        NestWorkFlowRequest nestWorkFlowRequest) throws DaipBaseException {
        if (relationPatchTaskPo != null) {
            long relationTaskId = relationPatchTaskPo.getTaskId();
            if (relationTaskId != 0L) {
                nestWorkFlowRequest.setRelationTaskId(relationTaskId);
                String relationAccessType = assemblePatchTaskPo2AccessType(relationPatchTaskPo);
                nestWorkFlowRequest.setRelationAccessType(relationAccessType);
            }
        }
    }

    private ServiceInfo convertServiceInfo(List<ServiceModel> serviceModels, ServiceInstance r) {
        ServiceInfo serviceInfo = new ServiceInfo();
        serviceInfo.setServiceId(r.getServiceId());
        serviceInfo.setServiceName(r.getServiceName());
        serviceInfo.setServiceInstanceId(r.getServiceInstanceId());
        serviceInfo.setServiceInstanceName(r.getServiceInstanceName());
        String serviceName;
        String targetVersion;
        if (CollectionUtils.isEmpty(serviceModels)) {
            serviceName = r.getServiceName();
            targetVersion = r.getVersion();
        } else {
            ServiceModel serviceModel =
                serviceModels.stream().filter(s -> StringUtils.equals(s.getServiceId(), r.getServiceId())).findFirst()
                    .orElse(new ServiceModel());
            targetVersion = StringUtils.isEmpty(serviceModel.getVersion()) ? "" : serviceModel.getVersion();
            serviceName = StringUtils.isEmpty(serviceModel.getServiceName()) ? "" : serviceModel.getServiceName();
        }
        serviceInfo.setTargetVersion(targetVersion);
        serviceInfo.setComponentType(COMPONENT_TYPE);
        serviceInfo.setServiceName(serviceName);
        return serviceInfo;
    }
}