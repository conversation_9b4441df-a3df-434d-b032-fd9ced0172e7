/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: SchemaPatchTaskService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2024/1/17
 * </p>
 * <p>
 * 完成日期：2024/1/17
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.schema.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.ClusterBean;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.common.deployer.model.controller.api.ClusterVersionControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.event.reporter.api.annotation.BusinessDomainEvent;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchFlagApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchDetailKey;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.domain.schema.utils.SchemaPatchUtils;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchCategoryEnum;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchOperateTypeEnum;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Slf4j
@Service
public class SchemaPatchTaskService {

    @Autowired
    private SchemaPatchFlagApi schemaPatchFlagApi;

    @Autowired
    private SchemaPatchApi schemaPatchApi;

    @Autowired
    private PatchTaskOperateService patchTaskOperateService;

    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Autowired
    private ClusterInfoControllerApi clusterInfoControllerApi;

    @Autowired
    private DaipEventReporter daipEventReporter;

    @Autowired
    private PatchHistoryService patchHistoryService;

    @Autowired
    private PatchInfoService patchInfoService;

    @Autowired
    private SchemaPatchUtils schemaPatchUtils;

    public synchronized void executeSchemaPatchTask() {

        boolean isFinished = schemaPatchFlagApi.isFinished();
        if (!isFinished) {
            log.warn("update schema patch not finished");
            return;
        }

        List<PatchTaskInfo> patchTaskInfos = querySchemaPatch();

        List<PatchTaskInfo> canUpdatePatchTaskInfos = filterCanUpdatePatch(patchTaskInfos, false);

        createAndTriggerPatchTask("", canUpdatePatchTaskInfos);
    }

    public void createAndTriggerPatchTask(String taskName, List<PatchTaskInfo> canUpdatePatchTaskInfos) {
        if (canUpdatePatchTaskInfos.isEmpty()) {
            return;
        }
        try {
            schemaPatchFlagApi.authorize();

            PatchTaskDto patchTaskDto = organizePatchTask(canUpdatePatchTaskInfos, taskName);

            long taskId = patchTaskOperateService.createPatchTask(patchTaskDto);

            log.info("start to create update schema patch task,taskId={}", taskId);

            schemaPatchApi.saveUpdateSchemaPatchInfos(taskId, canUpdatePatchTaskInfos);

            patchTaskOperateService.triggerPatchTask(taskId);
        } catch (Exception e) {
            log.error("failed to create update schema patch task", e);
        } finally {
            log.info("finish to create update schema patch task");
            schemaPatchFlagApi.release();
        }
    }

    private PatchTaskDto organizePatchTask(List<PatchTaskInfo> canUpdatePatchTaskInfos, String taskName) {
        PatchTaskDto patchTask = new PatchTaskDto();

        if (StringUtils.isEmpty(taskName)) {
            taskName = organizeTaskName();
        }
        patchTask.setTaskName(taskName);

        patchTask.setTaskType(PatchTaskTypeEnum.UPDATE.getTaskType());

        patchTask.setOperateType(PatchOperateTypeEnum.OFFLINE.getOperateType());

        patchTask.setPatchCategory(PatchCategoryEnum.SCHEMA.getPatchCategory());

        patchTask.setClusterId(queryClusterId());

        List<ServiceInstancePatchInfo> serviceInstancePatchInfos = Lists.newArrayList();

        List<ServiceInstance> relationServices = Lists.newArrayList();

        for (PatchTaskInfo patchTaskInfo : canUpdatePatchTaskInfos) {
            ServiceInstancePatchInfo instancePatchInfo = new ServiceInstancePatchInfo();
            ServiceInstance instance = organizeSchemaServiceInstance(patchTaskInfo);
            List<String> updatePatchNames = patchTaskInfo.getUpdatePatchNames();
            instancePatchInfo.setTargetPatchPoint(updatePatchNames.get(updatePatchNames.size() - 1));
            RollBackPatchPointInfo rollBackPointInfo = new RollBackPatchPointInfo();
            rollBackPointInfo.setRollBackPatchPoint(updatePatchNames.get(0));
            rollBackPointInfo.setPatchHostInfos(Lists.newArrayList(new PatchHostInfoDto("", "")));
            instancePatchInfo.setServiceInstance(instance);
            instancePatchInfo.setRollBackPatchPoints(Lists.newArrayList(rollBackPointInfo));
            serviceInstancePatchInfos.add(instancePatchInfo);
            relationServices.add(instance);
        }

        patchTask.setContext(serviceInstancePatchInfos);
        patchTask.setRelationServices(relationServices);
        return patchTask;
    }

    private ServiceInstance organizeSchemaServiceInstance(PatchTaskInfo patchTaskInfo) {
        String serviceName = patchTaskInfo.getServiceName();
        String version = patchTaskInfo.getVersion();;
        ServiceInstance instance = new ServiceInstance();
        instance.setServiceName(serviceName);
        instance.setVersion(version);
        instance.setServiceId(queryServiceId(version, serviceName));
        return instance;
    }

    private String queryServiceId(String version, String serviceName) {
        ServiceModel serviceModel = productModelInfoControllerApi.query(serviceName, version);
        return serviceModel.getServiceId();
    }

    private String queryClusterId() {
        List<ClusterBean> allClusterBeans = clusterInfoControllerApi.queryAll();
        if (!allClusterBeans.isEmpty()) {
            return String.valueOf(allClusterBeans.get(0).getClusterId());
        }
        return "";
    }

    private String organizeTaskName() {
        long currentTime = System.currentTimeMillis();

        SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");

        Date date = new Date(currentTime);

        return String.format("update_schema_%s", sdf.format(date));
    }

    private List<PatchTaskInfo> querySchemaPatch() {

        List<PatchDetailPo> patchDetails = patchInfoService.queryAllPatchInfos();

        List<String> updatedSchemaPatch = patchHistoryService.querySchemaPatchHistory();
        Map<PatchDetailKey,
            List<PatchDetailPo>> patchDetailKeyListMap = patchDetails.stream()
                .filter(patchDetail -> patchDetail.getPatchName().contains(Constants.SCHEMA_PATCH)
                    && !updatedSchemaPatch.contains(patchDetail.getPatchName()))
                .sorted(Comparator.comparing(a -> a.getPatchName().toLowerCase()))
                .collect(Collectors.groupingBy(this::getKey));
        List<PatchTaskInfo> patchTaskInfos = Lists.newArrayList();
        for (Map.Entry<PatchDetailKey, List<PatchDetailPo>> patchDetailKeyListEntry : patchDetailKeyListMap
            .entrySet()) {
            PatchDetailKey patchDetailKey = patchDetailKeyListEntry.getKey();
            List<PatchDetailPo> patchDetailPos = patchDetailKeyListEntry.getValue();
            PatchTaskInfo patchTask = new PatchTaskInfo();
            patchTask.setServiceName(patchDetailKey.getServiceName());
            patchTask.setVersion(patchDetailKey.getVersion());
            patchTask.setUpdatePatchNames(
                patchDetailPos.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toList()));
            patchTaskInfos.add(patchTask);
        }
        return patchTaskInfos;
    }

    private PatchDetailKey getKey(PatchDetailPo patchDetail) {
        return new PatchDetailKey(patchDetail.getService(), patchDetail.getBaseVersion());
    }

    public List<String> filterTaskPatches(PatchTaskInfo patchTaskInfo) {
        List<String> taskPatchNames =
            schemaPatchApi.queryPatchInfosByService(patchTaskInfo.getServiceName(), patchTaskInfo.getVersion());

        return patchTaskInfo.getUpdatePatchNames().stream().filter(s -> !taskPatchNames.contains(s))
            .collect(Collectors.toList());
    }

    public List<PatchTaskInfo> filterCanUpdatePatch(List<PatchTaskInfo> patchTaskInfos, boolean isCopy) {
        List<PatchTaskInfo> canUpdateTaskInfos = Lists.newArrayList();
        for (PatchTaskInfo patchTaskInfo : patchTaskInfos) {
            String serviceName = patchTaskInfo.getServiceName();
            String version = patchTaskInfo.getVersion();
            if (!schemaPatchUtils.isProductLoadSuccess(serviceName, version)) {
                log.error("check product load error,serviceName={},version={}", serviceName, version);
                continue;
            }

            List<String> updatePatchNames;
            if (isCopy) {
                updatePatchNames = patchTaskInfo.getUpdatePatchNames();
            } else {
                updatePatchNames = filterTaskPatches(patchTaskInfo);
            }

            if (updatePatchNames.isEmpty()) {
                log.info("serviceName={},version={},isCopy={} need update patches is empty", serviceName, version,
                    isCopy);
                continue;
            }

            boolean result = schemaPatchUtils.checkTaskPatchDependency(serviceName, version, updatePatchNames);

            if (result) {
                canUpdateTaskInfos.add(new PatchTaskInfo(serviceName, version, updatePatchNames));
            } else {
                log.error("check dependency error,serviceName={},version={},updatePatchNames={}", serviceName, version,
                    updatePatchNames.toString());
            }
        }
        return canUpdateTaskInfos;
    }

    /* Started by AICoder, pid:i8590336cc19d1f148080bd220827a135b569bb6 */
    public List<PatchTaskInfo> queryCanCopyPatches(long taskId) {
        List<PatchTaskInfo> copyTaskInfos = schemaPatchApi.queryByUpdateTaskId(taskId);

        List<String> patchDetailNames = patchInfoService.queryPatchName();

        List<String> updatedSchemaPatch = patchHistoryService.querySchemaPatchHistory();

        for (PatchTaskInfo patchTaskInfo : copyTaskInfos) {
            List<String> updatePatchNames = patchTaskInfo.getUpdatePatchNames();
            List<String> filterPatchNames =
                updatePatchNames.stream().filter(s -> patchDetailNames.contains(s) && !updatedSchemaPatch.contains(s))
                    .collect(Collectors.toList());
            patchTaskInfo.setUpdatePatchNames(filterPatchNames);
        }
        return filterCanUpdatePatch(copyTaskInfos, true);
    }

    /* Ended by AICoder, pid:i8590336cc19d1f148080bd220827a135b569bb6 */

}