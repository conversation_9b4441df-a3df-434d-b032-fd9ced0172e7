/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: CurrentPatchAssembler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2024/5/17
 * </p>
 * <p>
 * 完成日期：2024/5/17
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.task.assembler;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.*;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;

import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.service.PatchDetailService;
import com.zte.daip.manager.patcher.domain.task.entity.PatchTaskKey;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import org.springframework.stereotype.Service;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Service
@Slf4j
public class CurrentPatchAssembler {

    @Autowired
    private PatchHistoryService patchHistoryService;

    @Autowired
    private PatchDetailService patchDetailService;

    @Autowired
    private PatchTaskService patchTaskService;

    @Autowired
    private PatchTaskAssembler patchTaskAssembler;

    public void organizeCurrentPatchPoints(List<ServiceInstancePatchInfo> serviceInstancePatchInfos) {
        ListMultimap<PatchTaskKey, PatchHistory> patchHistoryListMultimap = groupHistoryPatches();

        serviceInstancePatchInfos.parallelStream().forEach(serviceInstancePatchInfo -> {
            PatchTaskKey patchInstanceKey = queryServiceInstanceKey(serviceInstancePatchInfo.getPatchType(),
                serviceInstancePatchInfo.getServiceInstance());

            if (!patchHistoryListMultimap.containsKey(patchInstanceKey)) {
                return;
            }
            List<PatchHistory> patchHistoryBeanList = patchHistoryListMultimap.get(patchInstanceKey);

            List<PatchHistory> sortedPatchHistoryBeans = sortedPatchHistoryBeans(patchHistoryBeanList);

            List<RollBackPatchPointInfo> currentPatchPoints =
                queryCurrentPatchPoints(serviceInstancePatchInfo, sortedPatchHistoryBeans);

            serviceInstancePatchInfo.setCurrentPatchPoints(currentPatchPoints);
        });

    }

    /* Started by AICoder, pid:obbbf52084s3a22149030a4771f5283e5131fe8f */
    public boolean checkTaskIsSequence(List<ServiceInstancePatchInfo> serviceInstancePatchInfos,
        PatchTaskTypeEnum taskType, StringBuilder infos) {
        organizeCurrentPatchPoints(serviceInstancePatchInfos);
        for (ServiceInstancePatchInfo serviceInstancePatchInfo : serviceInstancePatchInfos) {
            Map<String, String> currentPatchPointMap = serviceInstancePatchInfo.convertCurrentPatchPointMap();;
            Map<String, String> rollbackPatchPointMap = serviceInstancePatchInfo.convertRollbackPatchPointMap();
            for (String hostIp : rollbackPatchPointMap.keySet()) {
                String currentName = currentPatchPointMap.getOrDefault(hostIp, "");
                if (taskType.getTaskType() == PatchTaskTypeEnum.ROLLBACK.getTaskType()) {
                    if (currentName.compareTo(serviceInstancePatchInfo.getTargetPatchPoint()) > 0) {
                        String sequenceTaskName =
                            findSequenceTaskName(hostIp, currentName, serviceInstancePatchInfo, taskType);
                        log.error(
                            "check task is sequence failed,taskType={} hostIp={} currentName={} targetName={} sequenceTaskName={}",
                            taskType.getTaskTypeName(), hostIp, currentName,
                            serviceInstancePatchInfo.getTargetPatchPoint(), sequenceTaskName);
                        infos.append(sequenceTaskName);
                        return false;
                    }
                } else {
                    if (currentName.compareTo(rollbackPatchPointMap.get(hostIp)) < 0) {
                        String sequenceTaskName =
                            findSequenceTaskName(hostIp, currentName, serviceInstancePatchInfo, taskType);
                        log.error(
                            "check task is sequence failed,taskType={} hostIp={} currentName={} rollbackName={} sequenceTaskName={}",
                            taskType.getTaskTypeName(), hostIp, currentName, rollbackPatchPointMap.get(hostIp),
                            sequenceTaskName);
                        infos.append(sequenceTaskName);
                        return false;
                    }
                }
            }
        }
        return true;
    }

    private String findSequenceTaskName(String hostIp, String currentName,
        ServiceInstancePatchInfo serviceInstancePatchInfo, PatchTaskTypeEnum taskType) {
        List<PatchTaskPo> allPatchTaskPos = patchTaskService.queryAllPatchTasks();
        List<PatchTaskPo> updateTaskPos = allPatchTaskPos.stream()
            .filter(patchTaskPo -> patchTaskPo.getTaskType() == PatchTaskTypeEnum.UPDATE.getTaskType())
            .collect(Collectors.toList());
        for (PatchTaskPo patchTaskPo : updateTaskPos) {
            try {
                PatchTaskDto patchTaskDto = patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo);
                List<ServiceInstancePatchInfo> serviceInstancePatchInfos = patchTaskDto.getContext();
                List<ServiceInstancePatchInfo> sequenceServiceInstancePatchInfos = serviceInstancePatchInfos.stream()
                    .filter(sequenceServiceInstancePatchInfo -> filterSequenceTask(sequenceServiceInstancePatchInfo,
                        serviceInstancePatchInfo))
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(sequenceServiceInstancePatchInfos)) {
                    continue;
                }
                if (checkCurrentSmallerTarget(sequenceServiceInstancePatchInfos, currentName, taskType)
                    && checkCurrentBiggerRollback(sequenceServiceInstancePatchInfos, hostIp, currentName, taskType)) {
                    return patchTaskPo.getTaskName();
                }
            } catch (DaipBaseException e) {
                log.error("find sequence task name failed,{},{}", taskType.getTaskTypeName(), currentName, e);
            }
        }
        return "";
    }

    private boolean checkCurrentBiggerRollback(List<ServiceInstancePatchInfo> sequenceServiceInstancePatchInfos,
        String hostIp, String currentName, PatchTaskTypeEnum taskType) {
        if (CollectionUtils.isNotEmpty(sequenceServiceInstancePatchInfos)) {
            ServiceInstancePatchInfo serviceInstancePatchInfo = sequenceServiceInstancePatchInfos.get(0);
            Map<String, String> rollbackPatchPointMap = serviceInstancePatchInfo.convertRollbackPatchPointMap();
            if (rollbackPatchPointMap.containsKey(hostIp)) {
                String rollbackPatchName = rollbackPatchPointMap.get(hostIp);
                log.info(
                    "check current patch bigger than rollback patch,currentName={} rollbackName={} hostIp={} taskType={}",
                    currentName, rollbackPatchName, hostIp, taskType.getTaskTypeName());
                if (taskType.getTaskType() == PatchTaskTypeEnum.ROLLBACK.getTaskType()) {
                    return currentName.compareTo(rollbackPatchName) > 0;
                } else {
                    return currentName.compareTo(rollbackPatchName) >= 0;
                }
            }
        }
        return false;
    }

    private boolean checkCurrentSmallerTarget(List<ServiceInstancePatchInfo> sequenceServiceInstancePatchInfos,
        String currentName, PatchTaskTypeEnum taskType) {
        if (CollectionUtils.isNotEmpty(sequenceServiceInstancePatchInfos)) {
            ServiceInstancePatchInfo serviceInstancePatchInfo = sequenceServiceInstancePatchInfos.get(0);
            String targetPatchName = serviceInstancePatchInfo.getTargetPatchPoint();
            log.info("check current patch smaller than target patch,currentName={} targetPatchName={} taskType={}",
                currentName, targetPatchName, taskType.getTaskTypeName());
            if (taskType.getTaskType() == PatchTaskTypeEnum.ROLLBACK.getTaskType()) {
                return currentName.compareTo(targetPatchName) <= 0;
            } else {
                return currentName.compareTo(targetPatchName) < 0;
            }
        }
        return false;
    }

    private boolean filterSequenceTask(ServiceInstancePatchInfo sequenceServiceInstancePatchInfo,
        ServiceInstancePatchInfo serviceInstancePatchInfo) {
        ServiceInstance sequenceTask = sequenceServiceInstancePatchInfo.getServiceInstance();
        ServiceInstance serviceInstance = serviceInstancePatchInfo.getServiceInstance();
        return StringUtils.equals(serviceInstance.getServiceName(), sequenceTask.getServiceName())
            && StringUtils.equals(serviceInstance.getServiceInstanceId(), sequenceTask.getServiceInstanceId())
            && StringUtils.equals(serviceInstance.getRoleName(), sequenceTask.getRoleName())
            && StringUtils.equals(serviceInstance.getServiceId(), sequenceTask.getServiceId())
            && StringUtils.equals(serviceInstance.getVersion(), sequenceTask.getVersion()) && StringUtils
                .equals(sequenceServiceInstancePatchInfo.getPatchType(), serviceInstancePatchInfo.getPatchType());
    }

    public boolean checkTaskIsAllUpdate(List<ServiceInstancePatchInfo> serviceInstancePatchInfos) {
        organizeCurrentPatchPoints(serviceInstancePatchInfos);
        for (ServiceInstancePatchInfo serviceInstancePatchInfo : serviceInstancePatchInfos) {
            Set<String> currentPatchNames = serviceInstancePatchInfo.getCurrentPatchPoints().stream()
                .map(RollBackPatchPointInfo::getRollBackPatchPoint).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(currentPatchNames)
                && StringUtils.isNotEmpty(serviceInstancePatchInfo.getTargetPatchPoint())) {
                return false;
            }
            for (String currentPatchName : currentPatchNames) {
                if (currentPatchName.compareTo(serviceInstancePatchInfo.getTargetPatchPoint()) < 0) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean checkTaskIsAllRollback(List<ServiceInstancePatchInfo> serviceInstancePatchInfos) {
        organizeCurrentPatchPoints(serviceInstancePatchInfos);
        for (ServiceInstancePatchInfo serviceInstancePatchInfo : serviceInstancePatchInfos) {
            Map<String, String> rollbackPatchPointMap = serviceInstancePatchInfo.convertRollbackPatchPointMap();
            Map<String, String> currentPatchPointMap = serviceInstancePatchInfo.convertCurrentPatchPointMap();
            for (String hostIp : currentPatchPointMap.keySet()) {
                String currentPatchName = currentPatchPointMap.get(hostIp);
                if (rollbackPatchPointMap.containsKey(hostIp)
                    && currentPatchName.compareTo(rollbackPatchPointMap.get(hostIp)) > 0) {
                    return false;
                }
            }
        }
        return true;
    }

    /* Ended by AICoder, pid:obbbf52084s3a22149030a4771f5283e5131fe8f */

    public void organizeDisplayPatchPoints(List<ServiceInstancePatchInfo> serviceInstancePatchInfos) {
        ListMultimap<PatchTaskKey, PatchHistory> patchHistoryListMultimap = groupHistoryPatches();

        serviceInstancePatchInfos.parallelStream().forEach(serviceInstancePatchInfo -> {
            PatchTaskKey patchInstanceKey = queryServiceInstanceKey(serviceInstancePatchInfo.getPatchType(),
                serviceInstancePatchInfo.getServiceInstance());

            if (!patchHistoryListMultimap.containsKey(patchInstanceKey)) {
                return;
            }
            List<PatchHistory> patchHistoryBeanList = patchHistoryListMultimap.get(patchInstanceKey);

            List<PatchHistory> sortedPatchHistoryBeans = sortedPatchHistoryBeans(patchHistoryBeanList);

            List<RollBackPatchPointInfo> rollBackPatchPoints = serviceInstancePatchInfo.getRollBackPatchPoints();

            for (RollBackPatchPointInfo rollbackPoint : rollBackPatchPoints) {
                List<String> rollbackHosts = rollbackPoint.getPatchHostInfos().stream().map(PatchHostInfoDto::getIp)
                    .collect(Collectors.toList());
                List<PatchHistory> filterHistoryPatches = sortedPatchHistoryBeans.stream()
                    .filter(patchHistoryBean -> patchHistoryBean.getId().getPatchName()
                        .compareToIgnoreCase(rollbackPoint.getRollBackPatchPoint()) < 0
                        && (rollbackHosts.contains(patchHistoryBean.getId().getIp())
                            || StringUtils.isEmpty(patchHistoryBean.getId().getIp())))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterHistoryPatches)) {
                    PatchHistory currentPatch = filterHistoryPatches.get(filterHistoryPatches.size() - 1);
                    rollbackPoint.setDisplayRollbackPatchPoint(currentPatch.getId().getPatchName());
                }
            }
        });
    }

    private List<RollBackPatchPointInfo> queryCurrentPatchPoints(ServiceInstancePatchInfo serviceInstancePatchInfo,
        List<PatchHistory> sortedPatchHistoryBeans) {

        Map<String, List<PatchHistory>> sortedHistoryPatchMap = sortedPatchHistoryBeans.stream()
            .collect(Collectors.groupingBy(patchHistory -> patchHistory.getId().getIp()));

        List<RollBackPatchPointInfo> currentPatchPoints = Lists.newArrayList();

        ListMultimap<String, PatchHostInfoDto> currentPatchMap;

        Set<PatchHostInfoDto> rollbackPatchHosts = serviceInstancePatchInfo.queryRollBackPointHosts();

        if (rollbackPatchHosts.isEmpty()) {
            currentPatchMap = organizeSchemaCurrentPatch(sortedHistoryPatchMap);
        } else {
            currentPatchMap = organizeNormalCurrentPatch(rollbackPatchHosts, sortedHistoryPatchMap);
        }

        for (String patchName : currentPatchMap.keySet()) {
            currentPatchPoints.add(new RollBackPatchPointInfo(patchName, currentPatchMap.get(patchName)));
        }
        return currentPatchPoints;
    }

    private ListMultimap<String, PatchHostInfoDto> organizeNormalCurrentPatch(Set<PatchHostInfoDto> rollbackPatchHosts,
        Map<String, List<PatchHistory>> sortedHistoryPatchMap) {
        ListMultimap<String, PatchHostInfoDto> currentPatchMap = ArrayListMultimap.create();
        for (PatchHostInfoDto patchHost : rollbackPatchHosts) {
            List<PatchHistory> filterHistoryPatches = Lists.newArrayList();
            if (sortedHistoryPatchMap.containsKey(patchHost.getIp())) {
                filterHistoryPatches = sortedHistoryPatchMap.get(patchHost.getIp());
            }
            if (CollectionUtils.isNotEmpty(filterHistoryPatches)) {
                PatchHistory currentPatch = filterHistoryPatches.get(filterHistoryPatches.size() - 1);
                currentPatchMap.put(currentPatch.getId().getPatchName(),
                    new PatchHostInfoDto(patchHost.getIp(), patchHost.getHostName()));
            }
        }
        return currentPatchMap;
    }

    private ListMultimap<String, PatchHostInfoDto>
        organizeSchemaCurrentPatch(Map<String, List<PatchHistory>> sortedHistoryPatchMap) {
        ListMultimap<String, PatchHostInfoDto> currentPatchMap = ArrayListMultimap.create();
        List<PatchHistory> filterHistoryPatches = Lists.newArrayList();
        if (sortedHistoryPatchMap.containsKey("")) {
            filterHistoryPatches = sortedHistoryPatchMap.get("");
        }
        if (CollectionUtils.isNotEmpty(filterHistoryPatches)) {
            PatchHistory currentPatch = filterHistoryPatches.get(filterHistoryPatches.size() - 1);
            currentPatchMap.put(currentPatch.getId().getPatchName(), new PatchHostInfoDto("", ""));
        }
        return currentPatchMap;
    }

    private List<PatchHistory> sortedPatchHistoryBeans(List<PatchHistory> patchHistoryBeanList) {
        patchHistoryBeanList.sort((o1, o2) -> {
            if (o1.getPatchUptime() > o2.getPatchUptime()) {
                return 1;
            } else if (o1.getPatchUptime() < o2.getPatchUptime()) {
                return -1;
            } else {
                return o1.getId().getPatchName().compareToIgnoreCase(o2.getId().getPatchName());
            }
        });
        return patchHistoryBeanList;
    }

    private PatchTaskKey queryServiceInstanceKey(String patchType, ServiceInstance serviceInstancePatchInfo) {
        PatchTaskKey patchTaskKey = new PatchTaskKey();
        if (StringUtils.equals(patchType, PatchTypeEnum.ROLE.getType())) {
            patchTaskKey.setServiceName(serviceInstancePatchInfo.getServiceName());
            patchTaskKey.setServiceInstanceId(serviceInstancePatchInfo.getServiceInstanceId());
            patchTaskKey.setRoleName(serviceInstancePatchInfo.getRoleName());
            patchTaskKey.setVersion(serviceInstancePatchInfo.getVersion());
        } else if (StringUtils.equals(patchType, PatchTypeEnum.INSTANCE.getType())) {
            patchTaskKey.setServiceName(serviceInstancePatchInfo.getServiceName());
            patchTaskKey.setServiceInstanceId(serviceInstancePatchInfo.getServiceInstanceId());
            patchTaskKey.setVersion(serviceInstancePatchInfo.getVersion());
        } else if (StringUtils.equals(patchType, Constants.SCHEMA_PATCH)) {
            patchTaskKey.setServiceName(serviceInstancePatchInfo.getServiceName());
            patchTaskKey.setVersion(serviceInstancePatchInfo.getVersion());
            patchTaskKey.setIsSchema(1);
        } else {
            patchTaskKey.setServiceName(serviceInstancePatchInfo.getServiceName());
            patchTaskKey.setVersion(serviceInstancePatchInfo.getVersion());
        }
        return patchTaskKey;
    }

    private ListMultimap<PatchTaskKey, PatchHistory> groupHistoryPatches() {
        ListMultimap<PatchTaskKey, PatchHistory> patchHistoryMap =
            Multimaps.synchronizedListMultimap(ArrayListMultimap.create());

        List<PatchHistory> allPatchHistoryInfos = patchHistoryService.queryAllPatchHistoryInfo();

        List<PatchDetailDto> patchDetailDtos = patchDetailService.queryAllPatch();

        Map<String, List<PatchDetailDto>> patchDetailMap =
            patchDetailDtos.stream().collect(Collectors.groupingBy(PatchDetailDto::getPatchName));

        /* Started by AICoder, pid:e96aeo4cf167b58147580b2f809884146b56ca91 */
        // 遍历所有补丁历史信息，根据特定条件将数据存储到补丁历史映射中
        allPatchHistoryInfos.parallelStream().forEach(historyBean -> {
            if (patchDetailMap.containsKey(historyBean.getId().getPatchName())) {
                List<PatchDetailDto> patchDetails = patchDetailMap.get(historyBean.getId().getPatchName());
                if (CollectionUtils.isNotEmpty(patchDetails)) {
                    PatchDetailDto patchDetail = patchDetails.get(0);
                    if (patchDetail.getIsContainerPatch() != 1) {
                        patchHistoryMap.put(
                            new PatchTaskKey(historyBean.getId().getServiceName(),
                                historyBean.getId().getServiceInstanceId(), historyBean.getId().getRoleName(),
                                patchDetail.getBaseVersion(), checkIsSchemaPatch(patchDetail.getPatchName()) ? 1 : 0),
                            historyBean);
                    }
                }
            }
        });

        /* Ended by AICoder, pid:e96aeo4cf167b58147580b2f809884146b56ca91 */
        return patchHistoryMap;
    }

    private boolean checkIsSchemaPatch(String patchName) {
        return StringUtils.contains(patchName, Constants.SCHEMA_PATCH);
    }
}