/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ZDHPatchSourceGenerator.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/8
 * </p>
 * <p>
 * 完成日期：2021/4/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service.servicerole;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.valobj.NeedPatchServiceInfo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service("ZDHPatchSourceGenerator")
@Slf4j
public class ZDHPatchSourceGenerator extends AbstractPatchSourceGenerator {

    private static final PatchTypeEnum patchTypeEnum = PatchTypeEnum.ZDH;

    @Override
    public List<ServiceRoleInfo> obtainUnpatchedRoles(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {

        final NeedPatchServiceInfo needPatchServiceInfo = patch2ServiceInfo.computeIfAbsent(
            generatePatch2ServiceInfoKey(patchDetailDto), k -> obtainAllRoles(patchDetailDto, serviceModel));

        return filterRolesUpdatedPatch(patchDetailDto.getPatchName(), Constants.ZDH_SERVICE,
            needPatchServiceInfo.getServiceRoleInfos());
    }

    @Override
    public String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException {
        return "${ZDH_HOME}" + PATCH_HOME;
    }

    @Override
    public List<RollAutoPatchUpdateInfo> obtainPatchUpdateInfos(RollAutoPatchUpdateParam rollingPatchUpdateParam,
        String version, List<String> updatedContainerPatches) throws DaipBaseException {
        List<RollAutoPatchUpdateInfo> rollAutoPatchUpdateInfoList =
            super.obtainPatchUpdateInfos(rollingPatchUpdateParam, version, updatedContainerPatches);
        if (!StringUtils.endsWithIgnoreCase(rollingPatchUpdateParam.getServiceName(), "zdh")) {
            List<RollAutoPatchUpdateInfo> zdh =
                super.obtainPatchUpdateInfos(new RollAutoPatchUpdateParam(rollingPatchUpdateParam.getClusterId(), "zdh",
                    rollingPatchUpdateParam.getHostName()), version, updatedContainerPatches);
            if (CollectionUtils.isNotEmpty(zdh)) {
                rollAutoPatchUpdateInfoList.addAll(zdh);
            }
        }
        rollAutoPatchUpdateInfoList
            .forEach(rollAutoPatchUpdateInfo -> rollAutoPatchUpdateInfo.setPatchType(patchTypeEnum.getType()));
        return rollAutoPatchUpdateInfoList;
    }

    @Override
    protected List<ServiceRoleInfo> filterByVersionIps(List<ServiceRoleInfo> roleInfos, List<String> serviceVersion2Ip,
        String serviceId) {

        List<String> bigDataServiceIds = queryBigDataServiceIds();
        return roleInfos.stream().filter(roleInfo -> serviceVersion2Ip.contains(roleInfo.getIpAddress())
            && bigDataServiceIds.contains(roleInfo.getServiceId())).collect(Collectors.toList());
    }

    @Override
    protected int calPatchedHostSize(String patchName, String serviceName) {
        return super.calPatchedHostSize(patchName, Constants.ZDH_SERVICE);
    }

    @Override
    protected String generatePatch2ServiceInfoKey(PatchDetailDto patchDetailDto) {
        return String.format("%s_%s", Constants.ZDH_SERVICE, patchDetailDto.getBaseVersion());
    }

    @Override
    protected String generateServiceVersionKey(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {
        return String.format("%s_%s", Constants.ZDH_SERVICE, patchDetailDto.getBaseVersion());
    }

    private List<String> queryBigDataServiceIds() {
        List<String> bigDataService = Lists.newArrayList();
        for (ServiceModel model : queryServiceModels()) {
            if (StringUtils.equals(Constants.BIG_DATA_SERVICE_ID, model.getComponentType())) {
                bigDataService.add(model.getServiceId());
            }
        }
        return bigDataService;
    }

    @Override
    public void obtainUnpatchedClustersAndServiceInstanceInfo(PatchDetailDto patchDetailDto, ServiceModel serviceModel,
        Map<String, Set<ServiceInstanceInfo>> clustersAndServiceInstanceInfo) {
        final List<ServiceRoleInfo> unUpdatePatchRoles =
            obtainUnpatchedRolesByDispatchUnpatchedHosts(patchDetailDto, serviceModel);

        Set<String> clusterIds =
            unUpdatePatchRoles.stream().map(ServiceRoleInfo::getClusterId).collect(Collectors.toSet());
        for (String clusterId : clusterIds) {
            clustersAndServiceInstanceInfo.computeIfAbsent(clusterId, k -> Sets.newConcurrentHashSet())
                .add(new ServiceInstanceInfo(Constants.ZDH_SERVICE, patchDetailDto.getBaseVersion(), "", "",
                    Lists.newArrayList()));
        }
    }
}