package com.zte.daip.manager.patcher.domain.update.service.constructor;

import org.apache.commons.lang3.StringUtils;

public enum PatchTypeConstructorEnum {

    SERVICE("service", "ServicePatchSourceConstructor"),

    /*角色级*/
    ROLE("role", "RolePatchSourceConstructor"),

    /*实例级*/
    INSTANCE("instance", "InstancePatchSourceConstructor"),

    /*ZDH级*/
    ZDH("ZDH", "ZdhSourceConstructor");

    private final String type;

    private final String constructorName;

    PatchTypeConstructorEnum(String type, String constructorName) {
        this.type = type;
        this.constructorName = constructorName;
    }

    public String getType() {
        return type;
    }

    public String getConstructorName() {
        return constructorName;
    }

    public static PatchTypeConstructorEnum queryPatchType(String type) {
        PatchTypeConstructorEnum[] patchTypes = PatchTypeConstructorEnum.values();

        for (PatchTypeConstructorEnum patchType : patchTypes) {
            if (StringUtils.equalsIgnoreCase(type, patchType.getType())) {
                return patchType;
            }
        }
        return SERVICE;

    }
}
