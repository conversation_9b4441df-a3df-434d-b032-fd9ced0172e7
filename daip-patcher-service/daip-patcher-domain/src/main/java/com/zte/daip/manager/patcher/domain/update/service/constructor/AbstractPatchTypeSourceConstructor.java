package com.zte.daip.manager.patcher.domain.update.service.constructor;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.api.update.response.OfflinePatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public abstract class AbstractPatchTypeSourceConstructor {

    @Autowired
    protected PatchHomeQueryService patchHomeQueryService;
    @Autowired
    protected ServiceModelInfoCache serviceModelInfoCache;
    @Autowired
    protected PatchInfoService patchInfoService;
    @Autowired
    protected PatchHistoryService patchHistoryService;

    protected final static String PATCH_HOME = File.separator + "patch";

    /**
     * 查询patchHome
     * @param patchHomeParam 查询patchHome
     * @return 返回patchHome
     * @throws DaipBaseException 通用异常
     */
    public abstract String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException;

    /**
     * 构造升级对象
     * @param updateParam 升级参数
     * @param clusterId 集群号
     * @param hostIp 主机IP
     * @return 升级对象
     * @throws DaipBaseException 通用流程
     */
    public abstract List<OfflinePatchUpdateInfo> obtainOfflinePatchUpdateInfo(PatchServiceParam updateParam,
        String clusterId, String hostIp) throws DaipBaseException;

    protected Map<String, List<PatchHistory>> queryIp2UpdatedContainerPatches(String serviceName, String version) {
        Set<String> containerPatches = patchInfoService.findByServiceAndBaseVersion(serviceName, version).stream()
            .filter(patch -> patch.getIsContainerPatch() == 1).map(PatchDetailPo::getPatchName)
            .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(containerPatches)) {
            List<PatchHistory> updatedContainerPatches =
                patchHistoryService.queryPatchHistoryInfoByServiceName(serviceName).stream()
                    .filter(patchHistory -> containerPatches.contains(patchHistory.getId().getPatchName()))
                    .collect(Collectors.toList());
            log.info("updatedContainerPatches:{}", JSON.toJSONString(updatedContainerPatches));
            return updatedContainerPatches.stream()
                .collect(Collectors.groupingBy(patchHistory -> patchHistory.getId().getIp()));

        }
        return Maps.newHashMap();
    }

    protected List<SimplePatchInfo> buildSimplePatchInfo(String serviceName, String version, String roleName,
        Set<String> updatedPatches) {
        /* Started by AICoder, pid:g7728s25a6td36f143a70bdfb0258b1620a6b235 */
        return patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(serviceName, version).stream()
            .filter(patchDetailPo -> !updatedPatches.contains(patchDetailPo.getPatchName())
                && (StringUtils.isBlank(roleName) || patchDetailPo.getRoles().contains(roleName)))
            .map(patchDetailPo -> {
                SimplePatchInfo simplePatchInfo = new SimplePatchInfo();
                simplePatchInfo.setPatchName(patchDetailPo.getPatchName());
                simplePatchInfo.setContainer(patchDetailPo.getIsContainerPatch() == 1);
                return simplePatchInfo;
            }).collect(Collectors.toList());
        /* Ended by AICoder, pid:g7728s25a6td36f143a70bdfb0258b1620a6b235 */
    }

    protected List<OfflinePatchUpdateInfo> getOfflinePatchUpdateInfoList(
        Map<String, List<ServicePatchInfo>> patchHome2ServicePatchInfo, PatchTypeConstructorEnum patchTypeEnum) {
        List<OfflinePatchUpdateInfo> offlinePatchUpdateInfoList = Lists.newArrayList();
        patchHome2ServicePatchInfo.forEach((patchHome, servicePatchInfoList) -> {
            OfflinePatchUpdateInfo offlinePatchUpdateInfo = new OfflinePatchUpdateInfo();
            offlinePatchUpdateInfo.setPatchType(patchTypeEnum.getType());
            offlinePatchUpdateInfo.setPatchHome(patchHome);
            offlinePatchUpdateInfo.setServicePatchInfos(servicePatchInfoList);
            offlinePatchUpdateInfoList.add(offlinePatchUpdateInfo);

        });
        return offlinePatchUpdateInfoList;
    }
}
