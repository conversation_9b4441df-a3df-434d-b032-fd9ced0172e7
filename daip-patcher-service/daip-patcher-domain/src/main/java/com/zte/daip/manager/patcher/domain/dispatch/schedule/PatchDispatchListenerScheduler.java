package com.zte.daip.manager.patcher.domain.dispatch.schedule;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.dto.PatchDispatchResult;
import com.zte.daip.manager.patcher.domain.common.PatchDispatchService;
import com.zte.daip.manager.patcher.domain.dispatch.cache.PatchDispatchCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchDispatchListenerScheduler
{
    @Autowired
    private PatchDispatchService patchDispatchService;
    @Autowired
    private PatchDispatchCacheService dispatchResultCacheService;

    @Async ("patchDispatchPool")
    @Scheduled (cron = "0/2 * * * * ?")
    public void cron()
    {
        List<PatchDispatchResult> patchDispatchResults = Lists.newArrayList();

        while (!dispatchResultCacheService.isEmptyDispatchCache()
            && patchDispatchResults.size() < 5000)
        {
            List<PatchDispatchResult> dispatchResults = dispatchResultCacheService.poll();
            if (null != dispatchResults)
            {
                patchDispatchResults.addAll(dispatchResults);
            }
        }

        if (!CollectionUtils.isEmpty(patchDispatchResults))
        {
            patchDispatchService.batchUpdate(patchDispatchResults);
        }
    }

}