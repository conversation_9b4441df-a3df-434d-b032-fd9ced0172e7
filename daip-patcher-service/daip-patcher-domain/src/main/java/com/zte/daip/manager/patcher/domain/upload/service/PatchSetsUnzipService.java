package com.zte.daip.manager.patcher.domain.upload.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.UnzipFileApi;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.internal.guava.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatchSetsUnzipService {
    @Autowired
    private PatchEnvApi patchEnvApi;

    @Autowired
    private UnzipFileApi unzipFileApi;

    public List<File> unzipPatchSets(File patchSet) {
        List<File> patchLists = Lists.newArrayList();
        Set<String> uniqueFilenames = Sets.newHashSet();
        List<File> patches = unzipOnePatchSet(patchSet);
        patches.forEach(patch -> {
            String patchName = patch.getName();
            if (!uniqueFilenames.contains(patchName)) {
                uniqueFilenames.add(patchName);
                patchLists.add(patch);
            }
        });
        return patchLists;
    }

    private List<File> unzipOnePatchSet(File patchSet) {
        String patchSetFileName = patchSet.getName().substring(0, patchSet.getName().indexOf(".zip"));
        String unzipDirStr = patchEnvApi.getRepositoryHomeEnv() + "/upload/" + patchSetFileName;
        unzipFileApi.unzipFile(patchSet, unzipDirStr);
        File unzipDir = FilePathCleaner.newFile(unzipDirStr);
        if (unzipDir != null) {
            return Arrays.stream(Objects.requireNonNull(unzipDir.listFiles()))
                .filter(file -> file.getName().endsWith(".zip")).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

}
