/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServiceModelInfoCache.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/21
 * </p>
 * <p>
 * 完成日期：2023/3/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.cache;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class ServiceModelInfoCache {
    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    private static final String BIGDATA = "dap.manager.common.bigdata";
    private static final String ZDH = "ZDH";
    private static final String DEFAULT_PATCH_TYPE = "service";

    private LoadingCache<String, List<String>> bigDataServiceCache = CacheBuilder.newBuilder().maximumSize(10000)
        .refreshAfterWrite(30, TimeUnit.MINUTES).build(new CacheLoader<String, List<String>>() {
            @Override
            public List<String> load(String key) {
                List<ServiceModel> serviceModels = productModelInfoControllerApi.queryModelsByComponentType(BIGDATA);
                if (!CollectionUtils.isEmpty(serviceModels)) {
                    return Lists.newArrayList(
                        serviceModels.stream().map(ServiceModel::getServiceName).collect(Collectors.toSet()));
                }
                return Lists.newArrayList();
            }
        });

    private LoadingCache<String, List<ServiceModel>> serviceModelCache = CacheBuilder.newBuilder().maximumSize(10000)
        .refreshAfterWrite(30, TimeUnit.SECONDS).build(new CacheLoader<String, List<ServiceModel>>() {
            @Override
            public List<ServiceModel> load(String clusterId) {
                List<ServiceModel> serviceModels = productModelInfoControllerApi.queryByClusterId(clusterId);
                if (!CollectionUtils.isEmpty(serviceModels)) {
                    return serviceModels;
                }
                return Lists.newArrayList();
            }
        });

    public List<String> queryBigDataService() throws DaipBaseException {
        log.debug("queryBigDataService");
        try {
            List<String> bigDataServices = Lists.newArrayList(bigDataServiceCache.get(BIGDATA));
            bigDataServices.add("zdh");
            return bigDataServices;
        } catch (Exception e) {
            throw new DaipBaseException("queryBigDataService exception:", e);
        }

    }

    public ServiceModel queryByClusterIdAndServiceName(String clusterId, String serviceName) throws DaipBaseException {
        try {
            log.debug("queryByClusterIdAndServiceName:{}:{}", clusterId, serviceName);
            List<ServiceModel> serviceModels = serviceModelCache.get(clusterId);
            if (!CollectionUtils.isEmpty(serviceModels)) {
                List<ServiceModel> filteredServiceModels = serviceModels.stream()
                    .filter(serviceModel -> StringUtils.equalsIgnoreCase(serviceName, serviceModel.getServiceName()))
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filteredServiceModels)) {
                    return filteredServiceModels.get(0);
                }
            }
            return null;
        } catch (Exception e) {
            throw new DaipBaseException("query By ClusterId And ServiceName exception:", e);
        }
    }

    public String queryServicePatchType(String clusterId, String serviceName) throws DaipBaseException {
        log.debug("queryServicePatchType:{}:{}", clusterId, serviceName);
        try {
            if (StringUtils.equalsIgnoreCase(serviceName, ZDH)) {
                return DEFAULT_PATCH_TYPE;
            }
            List<ServiceModel> serviceModels = serviceModelCache.get(clusterId);
            List<ServiceModel> filteredServiceModels = serviceModels.stream()
                .filter(serviceModel -> StringUtils.equalsIgnoreCase(serviceName, serviceModel.getServiceName()))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filteredServiceModels)) {
                return StringUtils.isEmpty(filteredServiceModels.get(0).getPatchType()) ? DEFAULT_PATCH_TYPE
                    : filteredServiceModels.get(0).getPatchType();
            }
            log.warn("query service patchType empty:{}:{}, return default(service).", clusterId, serviceName);
            return DEFAULT_PATCH_TYPE;
        } catch (Exception e) {
            throw new DaipBaseException("query service patchType exception:", e);
        }

    }

}