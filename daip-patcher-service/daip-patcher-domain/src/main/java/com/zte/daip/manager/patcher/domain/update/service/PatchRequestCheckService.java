/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchRequestCheckService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/9
 * </p>
 * <p>
 * 完成日期：2021/4/9
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.event.reporter.api.annotation.BusinessDomainEvent;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class PatchRequestCheckService {

    @Autowired
    private PatchTypeQueryService patchTypeQueryService;

    @Autowired
    private DaipEventReporter daipEventReporter;

    @BusinessDomainEvent(eventName = I18nKeyConstants.PREPARE_BEFORE_UPGRADE, clusterId = "#updateRequest.clusterId")
    public void checkUpdateRequest(UpdateRequest updateRequest) {

        daipEventReporter.info(I18nKeyConstants.PREPARE_BEFORE_UPGRADE, "Ensure zdh base service exists if bigData");
        ensureZdhBaseServiceExistsIfBigData(updateRequest);
    }

    private void ensureZdhBaseServiceExistsIfBigData(UpdateRequest updateRequest) {
        List<ServiceInstanceInfo> serviceInstanceInfos = updateRequest.getServiceInstanceInfos();

        Set<String> requestServiceNames =
            serviceInstanceInfos.stream().map(ServiceInstanceInfo::getServiceName).collect(Collectors.toSet());
        Set<String> requestServiceIds =
            serviceInstanceInfos.stream().map(ServiceInstanceInfo::getServiceId).collect(Collectors.toSet());

        Set<String> bigDataServiceIds = patchTypeQueryService.queryBigDataServiceIds(updateRequest.getClusterId());
        Set<String> bigData = Sets.newHashSet(bigDataServiceIds);
        bigData.retainAll(requestServiceIds);

        if (!bigData.isEmpty() && !requestServiceNames.contains(PatchTypeQueryService.DEFAULT_ZDH_BASE_SERVICE_NAME)) {
            ServiceInstanceInfo zdhBaseService = new ServiceInstanceInfo();
            zdhBaseService.setServiceName(PatchTypeQueryService.DEFAULT_ZDH_BASE_SERVICE_NAME);
            Optional<ServiceInstanceInfo> first =
                serviceInstanceInfos.stream().filter(e -> bigDataServiceIds.contains(e.getServiceId())).findFirst();
            if (first.isPresent()) {
                zdhBaseService.setVersion(first.get().getVersion());
            }
            Set<String> targetIps =
                serviceInstanceInfos.stream().filter(e -> bigDataServiceIds.contains(e.getServiceId()))
                    .map(ServiceInstanceInfo::getIps).flatMap(Collection::stream).collect(Collectors.toSet());
            zdhBaseService.setIps(Lists.newArrayList(targetIps));

            serviceInstanceInfos.add(zdhBaseService);
        }
    }

}