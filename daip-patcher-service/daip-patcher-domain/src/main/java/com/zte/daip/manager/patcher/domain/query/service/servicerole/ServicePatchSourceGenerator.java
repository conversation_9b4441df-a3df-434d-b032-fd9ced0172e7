/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServicePatchSourceGenerator.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/8
 * </p>
 * <p>
 * 完成日期：2021/4/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service.servicerole;

import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.valobj.NeedPatchServiceInfo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service("ServicePatchSourceGenerator")
@Slf4j
public class ServicePatchSourceGenerator extends AbstractPatchSourceGenerator {

    @Autowired
    private PatchHomeQueryService patchHomeQueryService;
    @Autowired
    private PatchInfoService patchInfoService;
    @Autowired
    private ServiceModelInfoCache serviceModelInfoCache;
    @Autowired
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;

    private static final PatchTypeEnum patchTypeEnum = PatchTypeEnum.SERVICE;

    @Override
    public List<ServiceRoleInfo> obtainUnpatchedRoles(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {
        final NeedPatchServiceInfo needPatchServiceInfo = patch2ServiceInfo.computeIfAbsent(
            generatePatch2ServiceInfoKey(patchDetailDto), k -> obtainAllRoles(patchDetailDto, serviceModel));
        return filterRolesUpdatedPatch(patchDetailDto.getPatchName(), patchDetailDto.getService(),
            needPatchServiceInfo.getServiceRoleInfos());
    }

    @Override
    public String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException {
        String clusterId = patchHomeParam.getClusterId();
        String serviceName = patchHomeParam.getServiceName();
        ServiceModel serviceModel = serviceModelInfoCache.queryByClusterIdAndServiceName(clusterId, serviceName);
        if (serviceModel != null) {
            List<DeploymentServiceInstance> serviceInstances = deploymentInstanceServiceControllerApi
                .queryByClusterIdAndServiceId(clusterId, serviceModel.getServiceId());
            if (!CollectionUtils.isEmpty(serviceInstances)) {
                List<ConfigInstance> configInstances =
                    patchHomeQueryService.queryPatchHome(clusterId, serviceInstances.get(0).getServiceInstanceId());
                if (!CollectionUtils.isEmpty(configInstances)) {
                    return configInstances.get(0).getConfigValue() + PATCH_HOME;
                }
            }
        }
        return "";
    }

    @Override
    public List<RollAutoPatchUpdateInfo> obtainPatchUpdateInfos(RollAutoPatchUpdateParam rollingPatchUpdateParam,
        String version, List<String> updatedContainerPatches) throws DaipBaseException {
        List<RollAutoPatchUpdateInfo> rollAutoPatchUpdateInfos =
            super.obtainPatchUpdateInfos(rollingPatchUpdateParam, version, updatedContainerPatches);
        rollAutoPatchUpdateInfos
            .forEach(rollAutoPatchUpdateInfo -> rollAutoPatchUpdateInfo.setPatchType(patchTypeEnum.getType()));
        return rollAutoPatchUpdateInfos;
    }

    @Override
    protected String generateServiceVersionKey(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {
        String baseVersion = patchDetailDto.getBaseVersion();
        String versionKey = String.format("%s_%s", patchDetailDto.getService(), baseVersion);
        if (StringUtils.equals(serviceModel.getComponentType(), Constants.BIG_DATA_SERVICE_ID)) {
            String zdhVersion = String.format("%s_%s", Constants.ZDH_SERVICE, baseVersion);
            if (queryServiceVersion2Ip().containsKey(zdhVersion)) {
                return zdhVersion;
            }
        }
        return versionKey;
    }

}