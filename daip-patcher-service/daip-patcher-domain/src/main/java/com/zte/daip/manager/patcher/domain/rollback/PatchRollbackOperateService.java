/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchRollbackModifyIpService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/21
 * </p>
 * <p>
 * 完成日期：2023/3/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.rollback;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimaps;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.dto.PatchHostInfo;
import com.zte.daip.manager.patcher.api.dto.PatchRollBackServiceDto;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackParam;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchRollbackService;
import com.zte.daip.manager.patcher.domain.rollback.cache.PatchRollbackProgressQueue;
import com.zte.daip.manager.patcher.domain.rollback.entity.RollbackProgressDo;
import com.zte.daip.manager.patcher.domain.rollback.executor.BigDataTypeRollbackExecutor;
import com.zte.daip.manager.patcher.domain.rollback.executor.PatchRollbackExecutor;
import com.zte.daip.manager.patcher.domain.rollback.progress.RollbackProgressService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRollbackKey;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRollbackPo;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchRollbackOperateService {

    @Autowired
    private HostResourceInfoCache hostResourceInfoCache;
    @Autowired
    private ServiceModelInfoCache serviceModelInfoCache;
    @Autowired
    private PatchRollbackService patchRollbackService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private RollbackProgressService rollbackProgressService;
    @Autowired
    private PatchRollbackProgressQueue patchRollbackProgressQueue;
    @Autowired
    private DaipEventReporter daipEventReporter;

    @Autowired
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;

    @Autowired
    private BigDataTypeRollbackExecutor bigDataTypeRollbackExecutor;

    public List<PatchRollBackServiceDto> queryRollBackServices(String clusterId) {
        log.info("Query rollback services by  clusterId:{}", clusterId);
        try {
            List<String> ips = hostResourceInfoCache.queryIpAddressByClusterId(clusterId);
            if (!CollectionUtils.isEmpty(ips)) {
                List<String> bigDataService = serviceModelInfoCache.queryBigDataService();
                List<PatchRollbackPo> canRollbackPatchByCluster = patchRollbackService.queryByIps(ips);
                if (!CollectionUtils.isEmpty(canRollbackPatchByCluster)) {
                    ListMultimap<String, PatchRollbackPo> rollbackPatches =
                        groupPatchesMap(canRollbackPatchByCluster, bigDataService);
                    log.debug("rollbackPatches:{}", rollbackPatches.toString());
                    return convertServicePath2List(rollbackPatches);
                }
            }
        } catch (Exception e) {
            log.error("query rollback services error", e);
        }
        return Lists.newArrayList();
    }

    public List<PatchRollbackHostDto> queryRollBackHosts(String clusterId,
        List<PatchRollbackParam> patchRollbackParams) {
        log.info(" Query rollback hosts by clusterId:{}", clusterId);
        List<String> ips = hostResourceInfoCache.queryIpAddressByClusterId(clusterId);
        List<PatchRollbackHostDto> patchRollbackHostDtos = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(ips)) {
            patchRollbackParams.forEach(patchRollbackParam -> {
                List<PatchRollbackPo> patchRollbackPos = patchRollbackService.queryIpsByServiceAndInstanceAndRole(
                    patchRollbackParam.getServiceName(), patchRollbackParam.getServiceInstanceId(),
                    patchRollbackParam.getRoleName(), patchRollbackParam.getPatchName());
                Set<String> canRollbackIps =
                    patchRollbackPos.stream().filter(patchRollbackPo -> ips.contains(patchRollbackPo.getId().getIp()))
                        .map(PatchRollbackPo::getId).map(PatchRollbackKey::getIp).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(canRollbackIps)) {
                    log.debug(" canRollbackIps:{}", canRollbackIps.toString());
                    List<PatchHostInfo> hostInfos = hostResourceInfoCache.queryHostInfoByIps(canRollbackIps).stream()
                        .map(hostInfo -> new PatchHostInfo(hostInfo.getIpAddress(), hostInfo.getHostName()))
                        .collect(Collectors.toList());
                    PatchRollbackHostDto patchRollbackHostDto = new PatchRollbackHostDto(
                        patchRollbackParam.getServiceName(), patchRollbackParam.getServiceInstanceId(),
                        patchRollbackParam.getRoleName(), patchRollbackParam.getPatchName(), hostInfos);
                    patchRollbackHostDtos.add(patchRollbackHostDto);
                }
            });
        }
        return patchRollbackHostDtos;
    }

    public void rollbackPatches(String clusterId, List<PatchRollbackHostDto> patchRollbackHostDtos) {
        log.info("start to rollback patch:clusterId:{}", clusterId);
        rollbackProgressService.initRollbackProgress(clusterId, patchRollbackHostDtos);

        List<PatchRollbackHostDto> bigDataPatchRollbackHostDtos = Lists.newArrayList();
        List<PatchRollbackHostDto> noBigDataPatchRollbackHostDtos;
        try {
            List<String> bigDataServices = serviceModelInfoCache.queryBigDataService();
            bigDataPatchRollbackHostDtos =
                patchRollbackHostDtos.stream().filter(patchRollbackHostDto -> filterBigDataService(clusterId,
                    patchRollbackHostDto.getServiceName(), bigDataServices)).collect(Collectors.toList());
            noBigDataPatchRollbackHostDtos =
                patchRollbackHostDtos.stream().filter(patchRollbackHostDto -> !filterBigDataService(clusterId,
                    patchRollbackHostDto.getServiceName(), bigDataServices)).collect(Collectors.toList());
        } catch (DaipBaseException e) {
            log.info("failed query big data rollback services");
            noBigDataPatchRollbackHostDtos = patchRollbackHostDtos;
        }

        if (!CollectionUtils.isEmpty(noBigDataPatchRollbackHostDtos)) {
            rollbackNoBigDataPatches(clusterId, noBigDataPatchRollbackHostDtos);
        }

        if (!CollectionUtils.isEmpty(bigDataPatchRollbackHostDtos)) {
            rollbackBigDataPatches(clusterId, bigDataPatchRollbackHostDtos);
        }
    }

    private void rollbackBigDataPatches(String clusterId, List<PatchRollbackHostDto> bigDataPatchRollbackHostDtos) {
        log.info(" Operate rollback bigdata patch:{},{}", clusterId, bigDataPatchRollbackHostDtos.toString());
        bigDataTypeRollbackExecutor.executeRollback(clusterId, bigDataPatchRollbackHostDtos);
    }

    private boolean filterBigDataService(String clusterId, String serviceName, List<String> bigDataServices) {
        try {
            boolean isBigData = bigDataServices.contains(serviceName);
            ServiceModel serviceModel = serviceModelInfoCache.queryByClusterIdAndServiceName(clusterId, serviceName);
            return (serviceModel == null || StringUtils.equals(serviceModel.getPatchType(), "")) && isBigData;
        } catch (DaipBaseException e) {
            return false;
        }
    }

    private void rollbackNoBigDataPatches(String clusterId, List<PatchRollbackHostDto> noBigDataPatchRollbackHostDtos) {
        for (PatchRollbackHostDto patchRollbackHostDto : noBigDataPatchRollbackHostDtos) {
            log.info(" Operate rollback no bigdata patch:{},{}", clusterId, patchRollbackHostDto.toString());
            try {
                String patchType =
                    serviceModelInfoCache.queryServicePatchType(clusterId, patchRollbackHostDto.getServiceName());
                log.info("Rollback " + patchRollbackHostDto.getServiceName() + ":patchType:" + patchType);
                PatchRollbackExecutor executor =
                    applicationContext.getBean(patchType + "Rollback", PatchRollbackExecutor.class);
                executor.executeRollback(clusterId, patchRollbackHostDto);
            } catch (Exception e) {
                log.error("rollback no bigdata patch exception:{}", clusterId, e);
                patchRollbackHostDto.getPatchHostInfoList().forEach(patchHostInfo -> {
                    RollbackProgressDo rollbackProgressDo = new RollbackProgressDo(clusterId,
                        patchHostInfo.getHostName(), patchRollbackHostDto.getServiceName(), e.getMessage(), false);
                    patchRollbackProgressQueue.add(rollbackProgressDo);
                });
                daipEventReporter.info("rollback_patch",
                    String.format("rollback no bigdata patch exception clusterId: %s", clusterId));
            }
        }
    }

    private List<PatchRollBackServiceDto>
        convertServicePath2List(ListMultimap<String, PatchRollbackPo> rollbackPatches) {
        List<PatchRollBackServiceDto> service2Patches = Lists.newLinkedList();
        for (String key : rollbackPatches.keySet()) {
            List<PatchRollbackPo> rollbackServicePatches = rollbackPatches.get(key);
            Set<String> patchNameList = rollbackServicePatches.stream().map(PatchRollbackPo::getId)
                .map(PatchRollbackKey::getPatchName).collect(Collectors.toSet());
            List<String> patchNames = Lists.newArrayList(patchNameList).stream().filter(Objects::nonNull)
                .sorted((o1, o2) -> o2.compareToIgnoreCase(o1)).collect(Collectors.toList());
            PatchRollbackPo patchRollbackPo = rollbackServicePatches.get(0);
            service2Patches.add(new PatchRollBackServiceDto(patchRollbackPo.getId().getServiceName(),
                patchRollbackPo.getId().getRoleName(), patchRollbackPo.getId().getServiceInstanceId(), patchNames));
        }
        log.info("Can rollback patch list:{}", service2Patches.toString());
        return service2Patches;
    }

    private ListMultimap<String, PatchRollbackPo> groupPatchesMap(List<PatchRollbackPo> patchRollbackPos,
        List<String> bigDataService) {
        return Multimaps.index(patchRollbackPos, patchRollbackPo -> {
            PatchRollbackKey id = patchRollbackPo.getId();
            String serviceName = id.getServiceName();
            if (bigDataService.contains(serviceName)) {
                return serviceName;
            } else {
                return serviceName + id.getRoleName() + id.getServiceInstanceId();
            }
        });
    }

}