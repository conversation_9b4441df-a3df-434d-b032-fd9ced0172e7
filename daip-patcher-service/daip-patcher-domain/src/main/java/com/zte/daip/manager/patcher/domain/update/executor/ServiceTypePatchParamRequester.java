/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServicePatchUpdate.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/1
 * </p>
 * <p>
 * 完成日期：2021/4/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.executor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.patcher.domain.update.bean.ServiceNeedUpdatePatchInfo;
import com.zte.daip.manager.patcher.inner.api.dto.PatchHostInfoDto;
import com.zte.daip.manager.patcher.inner.api.dto.RollBackPatchPointInfo;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.update.request.*;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.update.executor.api.MergeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.api.OrganizeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.depend.DependResourceLocalUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service("service")
public class ServiceTypePatchParamRequester extends AbstractPatchParamRequester
    implements OrganizeRequest, MergeRequest {
    @Autowired
    private DependResourceLocalUtil dependResourceLocalUtil;

    @Override
    public List<PatchUpdateRequest> organizeRequest(String clusterId, OrganizeKey key,
        List<ServiceInstanceInfo> serviceInstances) {
        Set<String> requestIps =
            serviceInstances.stream().flatMap(s -> s.getIps().stream()).collect(Collectors.toSet());
        Set<String> targetIps = buildTargetIps(clusterId, key.getServiceId(), requestIps);
        List<ServiceInstance> instances = serviceInstances.stream()
            .map(e -> new ServiceInstance(e.getServiceInstanceId(), Lists.newArrayList())).collect(Collectors.toList());
        Map<String, List<SimplePatchInfo>> ip2NeedPatches = patchesNeedUpdateQueryService
            .queryNeedUpdatePatchesByService(key.getServiceName(), key.getVersion(), targetIps);
        log.debug(String.format("service level service %s:finished to queryNeedUpdatePatches: %s", key.getServiceName(),
            JSON.toJSONString(ip2NeedPatches)));

        Map<String, List<ServicePatchInfo>> hostName2ServicePatches = Maps.newHashMap();
        for (String ip : targetIps) {
            ServicePatchInfo servicePatchInfo = new ServicePatchInfo(key.getServiceName(), key.getVersion());
            servicePatchInfo.setServiceInstances(instances);
            List<SimplePatchInfo> servicePatches = ip2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(servicePatches)) {
                servicePatchInfo.setPatches(servicePatches);
                hostName2ServicePatches.put(ip, Lists.newArrayList(servicePatchInfo));
            }
        }
        if (!CollectionUtils.isEmpty(hostName2ServicePatches)) {
            String instanceId =
                serviceInstances.stream().map(ServiceInstanceInfo::getServiceInstanceId).findFirst().orElse("");
            String patchHome = queryPatchHome(clusterId, instanceId);
            PatchUpdateRequest patchUpdateRequest =
                new PatchUpdateRequest(clusterId, patchHome, PatchTypeEnum.SERVICE.getType());
            patchUpdateRequest.setIp2Patches(hostName2ServicePatches);
            return Lists.newArrayList(patchUpdateRequest);
        } else {
            return Lists.newArrayList();
        }
    }

    @Override
    public PatchUpdateRequest mergeRequest(PatchRequestKey patchRequestKey,
        List<PatchUpdateRequest> patchUpdateRequests) {
        if (CollectionUtils.isEmpty(patchUpdateRequests)) {
            return new PatchUpdateRequest();
        }
        return patchUpdateRequests.stream().findFirst().orElse(new PatchUpdateRequest());
    }

    @Override
    public ServiceNeedUpdatePatchInfo queryRollbackPoints(ServiceRoleInfo serviceRoleInfo, OrganizeKey key) {
        List<ServiceRoleInfo> serviceRoles =
            dependResourceLocalUtil.getClusterServiceRoleInfo(serviceRoleInfo.getClusterId());
        List<HostInfo> hostInfos = dependResourceLocalUtil.getClusterHostInfo(serviceRoleInfo.getClusterId());
        if (CollectionUtils.isEmpty(hostInfos) || CollectionUtils.isEmpty(serviceRoles)) {
            log.warn("query rollback points service: depend resource local cache info is empty.");
            return null;
        }
        Set<String> requestIps = serviceRoles.stream()
            .filter(s -> StringUtils.equals(s.getServiceId(), serviceRoleInfo.getServiceId())
                && StringUtils.equals(s.getServiceInstanceId(), serviceRoleInfo.getServiceInstanceId()))
            .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        Set<String> targetIps =
            buildTargetIps(serviceRoleInfo.getClusterId(), serviceRoleInfo.getServiceId(), requestIps);
        log.info("query rollback points service: {}, ips: {}", key.getServiceName(), targetIps);
        return generateServiceInstancePatchInfos(key, serviceRoleInfo, hostInfos, targetIps);
    }

    private ServiceNeedUpdatePatchInfo generateServiceInstancePatchInfos(OrganizeKey key,
        ServiceRoleInfo serviceRoleInfo, List<HostInfo> hostInfos, Set<String> targetIps) {
        List<ServiceInstancePatchInfo> serviceInstancePatchInfos = Lists.newArrayList();
        Map<String, List<SimplePatchInfo>> serviceIp2NeedPatches = patchesNeedUpdateQueryService
            .queryNeedUpdatePatchesByService(key.getServiceName(), key.getVersion(), targetIps);
        List<RollBackPatchPointInfo> serviceTaskPatchRollbackPoints =
            generateRollbackPoints(hostInfos, serviceIp2NeedPatches, targetIps);

        if (!CollectionUtils.isEmpty(serviceTaskPatchRollbackPoints)) {
            ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
            String targetPatchPoint = generateTargetPatch(serviceIp2NeedPatches, targetIps);

            serviceInstancePatchInfo.setRollBackPatchPoints(serviceTaskPatchRollbackPoints);
            serviceInstancePatchInfo.setPatchType(PatchTypeEnum.SERVICE.getType());
            serviceInstancePatchInfo.setTargetPatchPoint(targetPatchPoint);

            com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance serviceInstance =
                new com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance();
            serviceInstance.setVersion(key.getVersion());
            serviceInstance.setServiceInstanceId(serviceRoleInfo.getServiceInstanceId());
            serviceInstance.setServiceName(key.getServiceName());
            serviceInstance.setServiceId(key.getServiceId());
            serviceInstance.setServiceInstanceName(
                queryInstanceName(serviceRoleInfo.getClusterId(), serviceRoleInfo.getServiceInstanceId()));
            serviceInstancePatchInfo.setServiceInstance(serviceInstance);
            serviceInstancePatchInfos.add(serviceInstancePatchInfo);
        }
        /* Started by AICoder, pid:0d60av5b47188371488f0a715024f70e1e759eb3 */
        List<String> needUpdatePatch = targetIps.stream()
                .flatMap(ip -> serviceIp2NeedPatches.getOrDefault(ip, Collections.emptyList()).stream())
                .map(SimplePatchInfo::getPatchName)
                .distinct()
                .collect(Collectors.toList());
        /* Ended by AICoder, pid:0d60av5b47188371488f0a715024f70e1e759eb3 */
        ServiceNeedUpdatePatchInfo serviceNeedUpdatePatchInfo = new ServiceNeedUpdatePatchInfo();
        serviceNeedUpdatePatchInfo.setServiceInstanceId(serviceRoleInfo.getServiceInstanceId());
        serviceNeedUpdatePatchInfo.setPatchInfoList(serviceInstancePatchInfos);
        serviceNeedUpdatePatchInfo.setNeedUpdatePatchList(needUpdatePatch);
        return serviceNeedUpdatePatchInfo;
    }

    private String generateTargetPatch(Map<String, List<SimplePatchInfo>> serviceIp2NeedPatches,
        Set<String> targetIps) {
        for (String ip : targetIps) {
            List<SimplePatchInfo> servicePatchesOfTheHost = serviceIp2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(servicePatchesOfTheHost)) {
                return servicePatchesOfTheHost.stream().filter(p -> !p.isContainer()).map(SimplePatchInfo::getPatchName)
                    .max(String::compareToIgnoreCase).orElse("");
            }
        }
        return "";
    }

    private List<RollBackPatchPointInfo> generateRollbackPoints(List<HostInfo> hostInfos,
        Map<String, List<SimplePatchInfo>> serviceIp2NeedPatches, Set<String> targetIps) {
        ListMultimap<String, PatchHostInfoDto> servicePatchName2Hosts = ArrayListMultimap.create();
        List<RollBackPatchPointInfo> rollBackPatchPointInfos = Lists.newArrayList();
        for (String ip : targetIps) {
            List<SimplePatchInfo> servicePatchesOfTheHost = serviceIp2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(servicePatchesOfTheHost)) {
                String rollbackPatchPoint = servicePatchesOfTheHost.stream().filter(p -> !p.isContainer())
                    .map(SimplePatchInfo::getPatchName).min(String::compareToIgnoreCase).orElse("");
                PatchHostInfoDto patchHostInfoDto = new PatchHostInfoDto();
                patchHostInfoDto.setHostName(hostInfos.stream().filter(h -> StringUtils.equals(h.getIpAddress(), ip))
                    .map(HostInfo::getHostName).findFirst().orElse(""));
                patchHostInfoDto.setIp(ip);
                servicePatchName2Hosts.put(rollbackPatchPoint, patchHostInfoDto);
            }
        }

        for (String key : servicePatchName2Hosts.keySet()) {
            RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
            rollBackPatchPointInfo.setRollBackPatchPoint(key);
            rollBackPatchPointInfo.setPatchHostInfos(servicePatchName2Hosts.get(key));
            rollBackPatchPointInfos.add(rollBackPatchPointInfo);
        }
        return rollBackPatchPointInfos;
    }

    private Set<String> buildTargetIps(String clusterId, String serviceId, Set<String> requestIps) {
        List<ServiceRoleInfo> serviceRoles = dependResourceLocalUtil.getClusterServiceRoleInfo(clusterId);
        return serviceRoles.stream().filter(serviceIdFilter(serviceId)).filter(ipFilter(requestIps))
            .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
    }
}