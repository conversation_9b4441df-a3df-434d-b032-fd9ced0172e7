package com.zte.daip.manager.patcher.domain.taskmodel.service;

import com.alibaba.fastjson.JSON;
import com.zte.daip.manager.common.task.common.model.TaskModel;
import com.zte.daip.manager.common.utils.base.BaseResourceService;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.task.api.model.dto.TaskModelDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述: 任务模型加载服务<br>
 * 
 * <p>
 * Note: 负责从指定目录加载任务模型配置文件，并将其持久化到重试队列。
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class TaskModelLoadService {
    @Autowired
    private BaseResourceService baseResourceService;

    @Autowired
    private TaskModelRetryService taskModelRetryService;

    private static final String TASK_MODEL_DIR_NAME = "taskmodel";

    private static final String TASK_MODEL_FILE_SUFFIX = ".json";

    /**
     * 功能说明：初始化加载任务模型。
     *
     * 业务背景：系统启动时自动加载预定义的任务模型，确保任务调度有可用的模板。
     */
    @PostConstruct
    @Async
    public void loadTaskModel() {
        log.info("start to load task model....");
        String taskModelDir = getTaskModelDir();

        File[] files = getFilesByFilePathAndFilter(taskModelDir, TASK_MODEL_FILE_SUFFIX);
        try {
            if (files != null && files.length > 0) {
                List<TaskModelDto> taskModelDtos = constructTaskModel(files);
                if (!CollectionUtils.isEmpty(taskModelDtos)) {
                    taskModelRetryService.saveTaskModelRetry(taskModelDtos);
                }
                log.info("finished to load task model....");
            }
        } catch (Exception e) {
            log.error("failed to parse task model", e);
        }

    }

    /**
     * 功能说明：构建任务模型DTO列表。
     *
     * 业务背景：解析JSON文件为任务模型对象，并转换为DTO格式。
     *
     * @param files 需要解析的文件数组，不能为空。
     * @return 任务模型DTO列表，非空列表。
     */
    private List<TaskModelDto> constructTaskModel(File[] files) {
        List<TaskModelDto> taskModelDtos = new ArrayList<>();
        if (files != null) {
            for (File f : files) {
                if (f != null) {
                    log.info("start to load task model:{}", f.getAbsoluteFile());
                    try {
                        taskModelDtos.add(parseTaskModel(f));
                    } catch (DaipBaseException e) {
                        log.error("failed to parse task model", e);
                    }
                }
            }
        }
        return taskModelDtos;
    }

    /**
     * 功能说明：获取任务模型目录路径。
     *
     * 业务背景：确定任务模型配置文件的存储位置。
     *
     * @return 任务模型目录路径，非空字符串。
     */
    private String getTaskModelDir() {
        File confDir = this.baseResourceService.getConfDir();
        if (confDir != null && confDir.exists()) {
            return confDir.getAbsolutePath() + File.separator + ".." + File.separator + TASK_MODEL_DIR_NAME
                + File.separator;
        } else {
            return "";
        }
    }

    /**
     * 功能说明：根据路径和过滤条件获取文件列表。
     *
     * 业务背景：筛选出符合后缀名要求的任务模型配置文件。
     *
     * @param path 需要搜索的目录路径，不能为空。
     * @param filterString 文件名过滤后缀，例如".json"。
     * @return 符合条件的文件数组，非空数组。
     */
    private File[] getFilesByFilePathAndFilter(String path, String filterString) {
        File fileFolder = FilePathCleaner.newFile(path);
        File[] files;
        if (null == fileFolder || !fileFolder.exists()) {
            log.error("file:{} not exist!", path);
            return new File[] {};
        }

        if (filterString == null) {
            files = fileFolder.listFiles();
        } else {
            files = fileFolder.listFiles((dir, name) -> name.endsWith(filterString));
        }
        return files;
    }

    /**
     * 功能说明：解析任务模型文件。
     *
     * 业务背景：将JSON格式的任务模型文件转换为DTO对象，用于后续持久化。
     *
     * @param file 需要解析的文件，不能为空。
     * @return 解析后的任务模型DTO对象，非空对象。
     *
     * @throws DaipBaseException 当文件解析失败或结果为空时抛出。
     */
    private TaskModelDto parseTaskModel(File file) throws DaipBaseException {
        TaskModel taskModel;
        try {
            taskModel = JSON.parseObject(FileUtils.readFileToByteArray(file), TaskModel.class);
        } catch (IOException e) {
            log.error("Failed to parse file: {}!", file.getName());
            throw new DaipBaseException(
                String.format("Failed to parse file: %s!, IOException %s", file.getName(), e.getMessage()));
        }
        if (taskModel != null) {
            TaskModelDto taskModelDo = new TaskModelDto();
            taskModelDo.setDefinition(taskModel).setModelName(taskModel.getModelName())
                .setVersion(taskModel.getVersion()).setService(taskModel.getServiceId());
            return taskModelDo;
        }
        throw new DaipBaseException(String.format("Parse file %s result is null", file.getName()));
    }
}