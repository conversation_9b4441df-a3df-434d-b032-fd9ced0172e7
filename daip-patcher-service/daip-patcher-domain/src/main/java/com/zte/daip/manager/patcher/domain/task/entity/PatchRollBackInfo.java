package com.zte.daip.manager.patcher.domain.task.entity;

import com.zte.daip.manager.patcher.api.dto.PatchHostInfo;
import com.zte.daip.manager.patcher.inner.api.dto.PatchHostInfoDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class PatchRollBackInfo
{
    private String serviceName;
    private String serviceInstanceId;
    private String roleName;
    private String patchName;
    private PatchHostInfoDto patchHostInfo;
}