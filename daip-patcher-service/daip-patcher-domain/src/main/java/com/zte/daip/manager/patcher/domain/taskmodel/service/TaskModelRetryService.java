package com.zte.daip.manager.patcher.domain.taskmodel.service;

import com.zte.daip.manager.common.cache.lock.LockUtils;
import com.zte.daip.manager.response.CommonResponse;
import com.zte.daip.manager.task.api.model.api.TaskModelApi;
import com.zte.daip.manager.task.api.model.dto.TaskModelDto;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class TaskModelRetryService {
    private static final String LOCK_KEY_PREFIX = "PATCHER_LOAD_TASK_MODEL";
    private static final int LOCK_LEASE_TIME = 60;

    @Autowired
    private TaskModelApi taskModelApi;

    @Autowired
    private LockUtils lockUtils;

    @Async
    @Retryable(value = {Exception.class}, backoff = @Backoff(delay = 3000L), maxAttempts = Integer.MAX_VALUE)
    public void saveTaskModelRetry(List<TaskModelDto> taskModelDto) {
        RLock lock = lockUtils.getLock(LOCK_KEY_PREFIX);

        if (lock.isLocked()) {
            return;
        }

        try {
            lock.lock(LOCK_LEASE_TIME, TimeUnit.SECONDS);
            String taskModelNames = taskModelDto.stream().map(TaskModelDto::getModelName).collect(Collectors.joining(", "));
            CommonResponse<Boolean> response = taskModelApi.loadModel(taskModelDto);
            if (response.getData()) {
                log.info("success to load task model: {}", taskModelNames);
            } else {
                log.warn("failed to load task model: {}", taskModelNames);
            }
        } finally {
            lock.unlock();
        }
    }
}
