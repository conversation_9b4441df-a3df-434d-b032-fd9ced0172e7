/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: AbstactPatchParamRequester.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/6
 * </p>
 * <p>
 * 完成日期：2021/4/6
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.executor;

import java.io.File;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;

import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import com.zte.daip.manager.patcher.domain.update.service.PatchesNeedUpdateQueryService;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public abstract class AbstractPatchParamRequester {

    @Autowired
    protected PatchHomeQueryService patchHomeQueryService;
    @Autowired
    protected PatchesNeedUpdateQueryService patchesNeedUpdateQueryService;

    @Autowired
    private DeploymentInstanceServiceControllerApi instanceControllerApi;

    protected String queryPatchHome(String clusterId, String serviceInstanceId) {
        List<ConfigInstance> configInstances = patchHomeQueryService.queryPatchHome(clusterId, serviceInstanceId);
        return CollectionUtils.isEmpty(configInstances) ? ""
            : configInstances.get(0).getConfigValue() + File.separator + "patch";
    }

    protected Predicate<ServiceRoleInfo> ipFilter(Set<String> wantIps) {
        return e -> CollectionUtils.isEmpty(wantIps) ? false : wantIps.contains(e.getIpAddress());
    }

    protected Predicate<ServiceRoleInfo> serviceIdFilter(String wantServiceId) {
        return e -> StringUtils.equalsIgnoreCase(e.getServiceId(), wantServiceId);
    }

    protected Predicate<ServiceRoleInfo> roleIdPredicate(String wantRoleId) {
        return e -> StringUtils.equalsIgnoreCase(e.getRoleId(), wantRoleId);
    }

    protected String queryInstanceName(String clusterId, String serviceInstanceId) {
        DeploymentServiceInstance deploymentServiceInstance =
            instanceControllerApi.queryByClusterIdAndServiceInstanceId(clusterId, serviceInstanceId);
        if (deploymentServiceInstance != null) {
            return deploymentServiceInstance.getServiceInstanceName();
        }
        return "";
    }

}