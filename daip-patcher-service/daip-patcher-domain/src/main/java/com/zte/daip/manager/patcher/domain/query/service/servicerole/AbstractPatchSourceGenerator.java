/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchSourceGenerator.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/8
 * </p>
 * <p>
 * 完成日期：2021/4/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service.servicerole;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.api.dto.PatchHostDto;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.domain.query.valobj.NeedPatchServiceInfo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.domain.query.valobj.UnpatchedParam;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@Service
public abstract class AbstractPatchSourceGenerator {

    @Autowired
    private PatchInfoService patchInfoService;
    @Autowired
    private PatchHistoryService patchHistoryService;

    protected UnpatchedParam unpatchedParam;

    protected Map<String, NeedPatchServiceInfo> patch2ServiceInfo = new ConcurrentHashMap<>();

    public void setParams(UnpatchedParam unpatchedParam) {
        this.unpatchedParam = unpatchedParam;
    }

    protected final static String PATCH_HOME = File.separator + "patch";

    /**
     * 功能说明：获取未打补丁的服务角色列表。
     *
     * 业务背景：根据补丁详情和服务模型，查询当前未应用该补丁的服务角色，用于后续的补丁分发和状态跟踪。
     *
     * @param patchDetailDto 补丁详情数据传输对象，包含补丁名称、服务名等关键信息。
     * @param serviceModel 服务模型，描述服务的配置和元数据。
     * @return 返回未打补丁的服务角色列表。
     */
    public abstract List<ServiceRoleInfo> obtainUnpatchedRoles(PatchDetailDto patchDetailDto,
        ServiceModel serviceModel);

    /**
     * 功能说明：查询补丁的存储路径。
     *
     * 业务背景：确定补丁文件在服务器上的存储位置，确保补丁能够正确部署到目标主机。
     *
     * @param patchHomeParam 包含集群ID、服务名等参数，用于定位补丁存储路径。
     * @return 返回补丁的根目录路径。
     * @throws DaipBaseException 当无法确定补丁路径时抛出异常。
     */
    public abstract String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException;

    /**
     * 功能说明：生成滚动自动更新的补丁信息列表。
     *
     * 业务背景：根据滚动更新参数和已更新的容器补丁列表，构建完整的补丁更新信息，用于自动化更新流程。
     *
     * @param rollingPatchUpdateParam 滚动更新参数，包含服务名、主机名等信息。
     * @param version 补丁版本号。
     * @param updatedContainerPatches 已更新的容器补丁列表。
     * @return 返回包含补丁更新信息的列表。
     * @throws DaipBaseException 当查询补丁路径失败时抛出异常。
     */
    public List<RollAutoPatchUpdateInfo> obtainPatchUpdateInfos(RollAutoPatchUpdateParam rollingPatchUpdateParam,
        String version, List<String> updatedContainerPatches) throws DaipBaseException {
        String serviceName = rollingPatchUpdateParam.getServiceName();
        String host = rollingPatchUpdateParam.getHostName();
        PatchHomeParam patchHomeParam = new PatchHomeParam(rollingPatchUpdateParam.getClusterId(), serviceName, "", "");
        RollAutoPatchUpdateInfo rollAutoPatchUpdateInfo = new RollAutoPatchUpdateInfo();
        rollAutoPatchUpdateInfo.setPatchHome(queryPatchHome(patchHomeParam));

        List<ServicePatchInfo> servicePatchInfoList = Lists.newArrayList();
        ServicePatchInfo servicePatchInfo = new ServicePatchInfo();
        servicePatchInfo.setServiceName(serviceName);
        servicePatchInfo.setVersion(version);
        servicePatchInfo.setUpdatedContainerPatches(updatedContainerPatches);
        servicePatchInfo.setPatches(obtainSimplePatchInfos(serviceName, version, host));
        servicePatchInfoList.add(servicePatchInfo);
        rollAutoPatchUpdateInfo.setServicePatchInfos(servicePatchInfoList);
        return Lists.newArrayList(rollAutoPatchUpdateInfo);
    }

    /**
     * 功能说明：获取指定服务、版本和IP的简单补丁信息列表。
     *
     * 业务背景：根据服务名、版本和主机IP，筛选出需要应用但尚未更新的补丁列表，用于补丁状态监控。
     *
     * @param serviceName 服务名称。
     * @param version 补丁版本号。
     * @param ip 主机IP地址。
     * @return 返回未更新的简单补丁信息列表。
     */
    public List<SimplePatchInfo> obtainSimplePatchInfos(String serviceName, String version, String ip) {
        List<PatchDetailPo> servicePatches =
            patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(serviceName, version);
        List<PatchHistory> patchHistories =
            patchHistoryService.queryPatchHistoryInfoByServiceNameAndIp(serviceName, ip);
        Set<String> updatedPatches = patchHistories.stream().map(patchHistory -> patchHistory.getId().getPatchName())
            .collect(Collectors.toSet());
        List<SimplePatchInfo> simplePatchInfoList = Lists.newArrayList();
        servicePatches.forEach(patchDetailPo -> {
            if (!updatedPatches.contains(patchDetailPo.getPatchName())) {
                SimplePatchInfo simplePatchInfo = new SimplePatchInfo();
                simplePatchInfo.setPatchName(patchDetailPo.getPatchName());
                simplePatchInfo.setContainer(patchDetailPo.getIsContainerPatch() == 1);
                simplePatchInfoList.add(simplePatchInfo);
            }
        });
        return simplePatchInfoList;
    }

    /**
     * 功能说明：计算未打补丁的主机数量。
     *
     * 业务背景：统计当前补丁未应用的主机数量，用于评估补丁覆盖范围和更新进度。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceModel 服务模型。
     * @return 返回未打补丁的主机数量。
     */
    public int obtainUnpatchedHostNum(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {

        if (StringUtils.containsIgnoreCase(patchDetailDto.getPatchName(), Constants.SCHEMA_PATCH)
            || StringUtils.containsIgnoreCase(patchDetailDto.getPatchName(), Constants.REPOSITORY_PATCH)) {
            return 0;
        }
        final NeedPatchServiceInfo needPatchServiceInfo = patch2ServiceInfo.computeIfAbsent(
            generatePatch2ServiceInfoKey(patchDetailDto), k -> obtainAllRoles(patchDetailDto, serviceModel));

        final List<ServiceRoleInfo> roleInfos = needPatchServiceInfo.getServiceRoleInfos();

        if (CollectionUtils.isEmpty(roleInfos)) {
            return 0;
        }
        return needPatchServiceInfo.getIpAddressSet().size()
            - calPatchedHostSize(patchDetailDto.getPatchName(), patchDetailDto.getService());
    }

    /**
     * 功能说明：获取未打补丁的服务与主机映射关系。
     *
     * 业务背景：根据补丁详情和服务模型，收集每个服务对应的未打补丁主机列表，用于批量更新操作。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceModel 服务模型。
     * @return 返回服务名到未打补丁主机集合的映射。
     */
    public Map<String, Set<PatchHistoryDto>> obtainUnpatchedService2Hosts(PatchDetailDto patchDetailDto,
        ServiceModel serviceModel) {
        Map<String, Set<PatchHistoryDto>> service2Hosts = Maps.newHashMap();
        String service = patchDetailDto.getService();

        List<ServiceRoleInfo> roleInfos = obtainUnpatchedRolesByDispatchUnpatchedHosts(patchDetailDto, serviceModel);
        Set<PatchHistoryDto> hosts = convertRole2HistorySet(roleInfos, serviceModel);
        if (!CollectionUtils.isEmpty(hosts)) {
            service2Hosts.put(service, hosts);
        }
        return service2Hosts;
    }

    /**
     * 功能说明：获取未打补丁的补丁与主机信息列表。
     *
     * 业务背景：根据补丁详情、服务模型和集群ID，筛选出需要更新的主机列表，用于定向补丁部署。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceModel 服务模型。
     * @param service2Hosts 服务到主机集合的映射。
     * @param clusterId 集群标识符。
     * @return 返回包含补丁和主机信息的列表。
     */
    public List<PatchHostDto> obtainUnpatchedPatchAndHost(PatchDetailDto patchDetailDto, ServiceModel serviceModel,
        Map<String, Set<PatchHistoryDto>> service2Hosts, String clusterId) {
        String service = patchDetailDto.getService();

        Set<String> selectedServices = service2Hosts.keySet();
        if (!selectedServices.contains(service)) {
            return Lists.newArrayList();
        }
        final Set<PatchHistoryDto> selectedHosts = service2Hosts.get(service);

        patchDetailDto.setServiceInstanceId(service);

        List<ServiceRoleInfo> roleInfos = obtainUnpatchedRolesByDispatchUnpatchedHosts(patchDetailDto, serviceModel);

        List<ServiceRoleInfo> roleInfoList = filterUnpatchedRolesByClusterId(roleInfos, clusterId, selectedHosts);

        Set<PatchHistoryDto> hosts = convertRole2HostSet(roleInfoList);

        if (CollectionUtils.isEmpty(hosts)) {
            return Lists.newArrayList();
        }

        return Lists.newArrayList(new PatchHostDto(patchDetailDto, hosts));

    }

    /**
     * 功能说明：收集未打补丁的集群和服务实例信息。
     *
     * 业务背景：根据补丁详情和服务模型，记录每个集群中未打补丁的服务实例信息，用于多集群管理。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceModel 服务模型。
     * @param clustersAndServiceInstanceInfo 集群到服务实例集合的映射。
     */
    public void obtainUnpatchedClustersAndServiceInstanceInfo(PatchDetailDto patchDetailDto, ServiceModel serviceModel,
        Map<String, Set<ServiceInstanceInfo>> clustersAndServiceInstanceInfo) {

        final List<ServiceRoleInfo> unUpdatePatchRoles =
            obtainUnpatchedRolesByDispatchUnpatchedHosts(patchDetailDto, serviceModel);

        for (ServiceRoleInfo roleInfo : unUpdatePatchRoles) {

            clustersAndServiceInstanceInfo.computeIfAbsent(roleInfo.getClusterId(), k -> Sets.newConcurrentHashSet())
                .add(new ServiceInstanceInfo(patchDetailDto.getService(), patchDetailDto.getBaseVersion(),
                    roleInfo.getServiceId(), roleInfo.getServiceInstanceId(), Lists.newArrayList()));
        }
    }

    /**
     * 功能说明：获取未打补丁的集群与服务映射关系。
     *
     * 业务背景：根据补丁详情和服务模型，统计每个集群中未打补丁的服务列表，用于集群级更新策略制定。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceModel 服务模型。
     * @param clusterServiceLstMap 集群到服务集合的映射。
     */
    public void obtainUnpatchedClustersAndServices(PatchDetailDto patchDetailDto, ServiceModel serviceModel,
        Map<String, Set<String>> clusterServiceLstMap) {

        final List<ServiceRoleInfo> unUpdatePatchRoles =
            obtainUnpatchedRolesByDispatchUnpatchedHosts(patchDetailDto, serviceModel);

        Set<String> clusterIds =
            unUpdatePatchRoles.stream().map(ServiceRoleInfo::getClusterId).collect(Collectors.toSet());

        for (String clusterId : clusterIds) {
            clusterServiceLstMap.computeIfAbsent(clusterId, k -> Sets.newConcurrentHashSet())
                .add(patchDetailDto.getService());
        }
    }

    /**
     * 功能说明：获取所有需要打补丁的服务角色信息。
     *
     * 业务背景：根据补丁详情和服务模型，收集所有需要应用该补丁的服务角色信息，为后续处理提供数据支持。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceModel 服务模型。
     * @return 返回包含服务角色信息的NeedPatchServiceInfo对象。
     */
    protected NeedPatchServiceInfo obtainAllRoles(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {

        List<String> serviceVersion2Ip = queryVersionIps(patchDetailDto, serviceModel);

        if (CollectionUtils.isEmpty(serviceVersion2Ip)) {
            return new NeedPatchServiceInfo();
        }

        List<ServiceRoleInfo> roleInfos =
            filterByVersionIps(queryServiceRoleInfos(), serviceVersion2Ip, serviceModel.getServiceId());

        final Set<String> ipAddressSet =
            roleInfos.stream().map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        return new NeedPatchServiceInfo(roleInfos, ipAddressSet);
    }

    /**
     * 功能说明：查询指定服务版本对应的IP列表。
     *
     * 业务背景：根据补丁详情和服务模型，获取该服务版本下所有相关主机的IP地址，用于定位目标主机。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceModel 服务模型。
     * @return 返回IP地址列表。
     */
    protected List<String> queryVersionIps(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {
        return queryServiceVersion2Ip().get(generateServiceVersionKey(patchDetailDto, serviceModel));
    }

    /**
     * 功能说明：按集群ID过滤未打补丁的服务角色。
     *
     * 业务背景：从服务角色列表中筛选出属于指定集群且未打补丁的角色，用于集群内定向更新。
     *
     * @param roleInfos 服务角色信息列表。
     * @param clusterId 集群标识符。
     * @return 返回过滤后的服务角色列表。
     */
    protected List<ServiceRoleInfo> filterUnpatchedRolesByClusterId(List<ServiceRoleInfo> roleInfos, String clusterId) {
        if (CollectionUtils.isEmpty(roleInfos) || StringUtils.isBlank(clusterId)) {
            return roleInfos;
        }
        return roleInfos.stream()
            .filter(serviceRoleInfo -> StringUtils.equals(clusterId, serviceRoleInfo.getClusterId()))
            .collect(Collectors.toList());
    }

    /**
     * 功能说明：按集群ID和选定主机过滤未打补丁的服务角色。
     *
     * 业务背景：结合集群ID和已选择的主机列表，精确筛选出需要更新的服务角色，提高更新效率。
     *
     * @param roleInfos 服务角色信息列表。
     * @param clusterId 集群标识符。
     * @param selectedHosts 已选择的主机集合。
     * @return 返回过滤后的服务角色列表。
     */
    protected List<ServiceRoleInfo> filterUnpatchedRolesByClusterId(List<ServiceRoleInfo> roleInfos, String clusterId,
        Set<PatchHistoryDto> selectedHosts) {
        if (CollectionUtils.isEmpty(roleInfos) || StringUtils.isBlank(clusterId) || !queryFilterHost()) {
            return filterUnpatchedRolesByClusterId(roleInfos, clusterId);
        }
        List<String> selectedHostIps = Optional.ofNullable(selectedHosts).orElse(Sets.newHashSet()).stream()
            .map(PatchHistoryDto::getIp).collect(Collectors.toList());
        return roleInfos.stream()
            .filter(serviceRoleInfo -> StringUtils.equals(clusterId, serviceRoleInfo.getClusterId())
                && selectedHostIps.contains(serviceRoleInfo.getIpAddress()))
            .collect(Collectors.toList());
    }

    /**
     * 功能说明：通过调度未打补丁的主机获取服务角色列表。
     *
     * 业务背景：根据补丁类型（如模式补丁或仓库补丁）决定是否需要获取服务角色，避免无效处理。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceModel 服务模型。
     * @return 返回未打补丁的服务角色列表。
     */
    protected List<ServiceRoleInfo> obtainUnpatchedRolesByDispatchUnpatchedHosts(PatchDetailDto patchDetailDto,
        ServiceModel serviceModel) {
        if (StringUtils.containsIgnoreCase(patchDetailDto.getPatchName(), Constants.SCHEMA_PATCH)
            || StringUtils.containsIgnoreCase(patchDetailDto.getPatchName(), Constants.REPOSITORY_PATCH)) {
            return Lists.newArrayList();
        }
        return obtainUnpatchedRoles(patchDetailDto, serviceModel);
    }

    /**
     * 功能说明：计算已打补丁的主机数量。
     *
     * 业务背景：统计指定补丁在特定服务下的已更新主机数量，用于评估补丁覆盖率。
     *
     * @param patchName 补丁名称。
     * @param serviceName 服务名称。
     * @return 返回已打补丁的主机数量。
     */
    protected int calPatchedHostSize(String patchName, String serviceName) {
        return queryPatchHistoriesInfoMap()
            .getOrDefault(new PatchKeyDo(patchName, serviceName, ""), Lists.newArrayList()).stream()
            .map(patchHistory -> patchHistory.getId().getIp()).collect(Collectors.toSet()).size();
    }

    /**
     * 功能说明：按服务版本IP列表过滤服务角色。
     *
     * 业务背景：根据服务版本对应的IP列表，筛选出需要处理的服务角色，确保补丁仅应用于目标主机。
     *
     * @param roleInfos 服务角色信息列表。
     * @param serviceVersion2Ip 服务版本到IP列表的映射。
     * @param serviceId 服务标识符。
     * @return 返回过滤后的服务角色列表。
     */
    protected List<ServiceRoleInfo> filterByVersionIps(List<ServiceRoleInfo> roleInfos, List<String> serviceVersion2Ip,
        String serviceId) {
        return Optional.ofNullable(roleInfos).orElse(Lists.newArrayList()).stream()
            .filter(roleInfo -> serviceVersion2Ip.contains(roleInfo.getIpAddress())
                && StringUtils.equals(roleInfo.getServiceId(), serviceId))
            .collect(Collectors.toList());
    }

    /**
     * 功能说明：过滤已更新补丁的服务角色。
     *
     * 业务背景：排除已更新指定补丁的服务角色，确保仅处理未更新的角色。
     *
     * @param patchName 补丁名称。
     * @param serviceName 服务名称。
     * @param roleInfos 服务角色信息列表。
     * @return 返回未更新指定补丁的服务角色列表。
     */
    protected List<ServiceRoleInfo> filterRolesUpdatedPatch(String patchName, String serviceName,
        List<ServiceRoleInfo> roleInfos) {
        Map<PatchKeyDo, List<PatchHistory>> patchKeyDoListMap = queryPatchHistoriesInfoMap();
        if (CollectionUtils.isEmpty(roleInfos) || patchKeyDoListMap == null || patchKeyDoListMap.isEmpty()) {
            return roleInfos;
        }
        final List<String> updatedHost = queryPatchHostByServiceAndPatch(serviceName, patchName);

        return Optional.ofNullable(roleInfos).orElse(Lists.newArrayList()).stream()
            .filter(roleInfo -> !updatedHost.contains(roleInfo.getIpAddress())).collect(Collectors.toList());
    }

    /**
     * 功能说明：过滤已更新补丁的服务角色。
     *
     * 业务背景：根据补丁详情和服务ID，排除已更新的角色，确保处理未更新的主机。
     *
     * @param roleInfos 服务角色信息列表。
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceId 服务标识符。
     * @return 返回未更新补丁的服务角色列表。
     */
    protected List<ServiceRoleInfo> filterRolesUpdatedPatch(List<ServiceRoleInfo> roleInfos,
        PatchDetailDto patchDetailDto, String serviceId) {
        Map<PatchKeyDo, List<PatchHistory>> patchKeyDoListMap = queryPatchHistoriesInfoMap();
        if (CollectionUtils.isEmpty(roleInfos) || patchKeyDoListMap == null || patchKeyDoListMap.isEmpty()) {
            return roleInfos;
        }
        List<String> historyKeys = patchKeyDoListMap
            .getOrDefault(new PatchKeyDo(patchDetailDto.getPatchName(), patchDetailDto.getService(), ""),
                Lists.newArrayList())
            .stream().map(patchHistory -> generateKey(serviceId, patchHistory)).collect(Collectors.toList());

        return roleInfos.stream().filter(roleInfo -> notInHistory(roleInfo, historyKeys)).collect(Collectors.toList());
    }

    /**
     * 功能说明：生成补丁到服务信息的唯一键。
     *
     * 业务背景：基于服务名和版本号生成唯一键，用于缓存和快速查找服务角色信息。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @return 返回生成的唯一键字符串。
     */
    protected String generatePatch2ServiceInfoKey(PatchDetailDto patchDetailDto) {
        return String.format("%s_%s", patchDetailDto.getService(), patchDetailDto.getBaseVersion());
    }

    /**
     * @param serviceModel This ServiceModel may be use for further computation in overriding classes
     */
    protected String generateServiceVersionKey(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {
        return String.format("%s_%s", patchDetailDto.getService(), patchDetailDto.getBaseVersion());
    }

    /**
     * 功能说明：生成服务ID和补丁历史记录的唯一键。
     *
     * 业务背景：组合服务ID、角色ID、IP地址和实例ID，生成唯一标识符，用于跟踪补丁状态。
     *
     * @param serviceId 服务标识符。
     * @param patchHistory 补丁历史记录。
     * @return 返回生成的唯一键字符串。
     */
    protected String generateKey(String serviceId, PatchHistory patchHistory) {
        return generateKey(serviceId, "", patchHistory.getId().getIp(), "");
    }

    /**
     * 功能说明：生成服务角色信息的唯一键。
     *
     * 业务背景：基于服务角色信息中的关键字段生成唯一键，用于缓存和状态管理。
     *
     * @param roleInfo 服务角色信息。
     * @return 返回生成的唯一键字符串。
     */
    protected String generateKey(ServiceRoleInfo roleInfo) {
        return generateKey(roleInfo.getServiceId(), roleInfo.getRoleId(), roleInfo.getIpAddress(),
            roleInfo.getServiceInstanceId());
    }

    /**
     * 功能说明：判断服务角色是否未在历史记录中。
     *
     * 业务背景：检查服务角色是否未被标记为已更新，确保仅处理未更新的主机。
     *
     * @param roleInfo 服务角色信息。
     * @param historyKeys 历史记录键列表。
     * @return 如果不在历史记录中返回true，否则返回false。
     */
    protected boolean notInHistory(ServiceRoleInfo roleInfo, List<String> historyKeys) {
        return !historyKeys.contains(generateKey(roleInfo));
    }

    protected String generateKey(String serviceId, String roleId, String ipAddress, String serviceInstanceId) {
        return serviceId + "_" + roleId + "_" + ipAddress + "_" + serviceInstanceId;
    }

    /**
     * 功能说明：查询指定服务和补丁的主机列表。
     *
     * 业务背景：获取已应用指定补丁的主机IP列表，用于更新状态验证和冲突检测。
     *
     * @param serviceName 服务名称。
     * @param patchName 补丁名称。
     * @return 返回主机IP列表。
     */
    protected List<String> queryPatchHostByServiceAndPatch(final String serviceName, final String patchName) {
        return queryPatchHistoriesInfoMap()
            .getOrDefault(new PatchKeyDo(patchName, serviceName, ""), Lists.newArrayList()).stream()
            .map(patchHistory -> patchHistory.getId().getIp()).collect(Collectors.toList());
    }

    /**
     * 功能说明：获取补丁历史信息映射。
     *
     * 业务背景：从参数中提取补丁历史信息映射，用于补丁状态查询和更新。
     *
     * @return 返回补丁历史信息映射。
     */
    protected Map<PatchKeyDo, List<PatchHistory>> queryPatchHistoriesInfoMap() {
        return unpatchedParam == null ? Maps.newHashMap() : unpatchedParam.getPatchHistoryInfoMap();
    }

    /**
     * 功能说明：获取服务模型列表。
     *
     * 业务背景：从参数中提取服务模型列表，用于多服务处理。
     *
     * @return 返回服务模型列表。
     */
    protected List<ServiceModel> queryServiceModels() {
        return unpatchedParam == null ? Lists.newArrayList() : unpatchedParam.getServiceModels();
    }

    /**
     * 功能说明：获取主机IP到主机名的映射。
     *
     * 业务背景：从参数中提取主机IP到主机名的映射，用于主机信息展示和日志记录。
     *
     * @return 返回主机IP到主机名的映射。
     */
    protected Map<String, String> queryHostIp2HostName() {
        return unpatchedParam == null ? Maps.newHashMap() : unpatchedParam.getHostIp2HostName();
    }

    /**
     * 功能说明：获取服务角色信息列表。
     *
     * 业务背景：从参数中提取服务角色信息列表，用于角色级别的补丁处理。
     *
     * @return 返回服务角色信息列表。
     */
    protected List<ServiceRoleInfo> queryServiceRoleInfos() {
        return unpatchedParam == null ? Lists.newArrayList() : unpatchedParam.getServiceRoleInfos();
    }

    /**
     * 功能说明：获取服务版本到IP列表的映射。
     *
     * 业务背景：从参数中提取服务版本到IP列表的映射，用于定位目标主机。
     *
     * @return 返回服务版本到IP列表的映射。
     */
    protected Map<String, List<String>> queryServiceVersion2Ip() {
        return unpatchedParam == null ? Maps.newHashMap() : unpatchedParam.getServiceVersion2Ip();
    }

    /**
     * 功能说明：判断是否需要过滤主机。
     *
     * 业务背景：从参数中提取过滤主机的标志，控制是否进行主机过滤逻辑。
     *
     * @return 如果需要过滤返回true，否则返回false。
     */
    protected boolean queryFilterHost() {
        return unpatchedParam == null ? false : unpatchedParam.isFilterHost();
    }

    /**
     * 功能说明：清除缓存的数据。
     *
     * 业务背景：重置缓存中的服务角色信息，确保数据最新性。
     */
    public void clearCache() {
        patch2ServiceInfo.clear();
    }

    /**
     * 功能说明：将服务角色信息转换为主机集合。
     *
     * 业务背景：将服务角色列表转换为补丁历史DTO集合，用于统一管理和展示。
     *
     * @param roleInfos 服务角色信息列表。
     * @return 返回补丁历史DTO集合。
     */
    private Set<PatchHistoryDto> convertRole2HostSet(List<ServiceRoleInfo> roleInfos) {
        if (CollectionUtils.isEmpty(roleInfos)) {
            return Sets.newHashSet();
        }

        return roleInfos.stream().map(roleInfo -> new PatchHistoryDto(roleInfo.getIpAddress(),
            queryHostIp2HostName().getOrDefault(roleInfo.getIpAddress(), ""))).collect(Collectors.toSet());
    }

    /**
     * 功能说明：将服务角色信息转换为带主机名的补丁历史集合。
     *
     * 业务背景：根据服务模型类型（角色或实例），生成包含主机名的补丁历史集合，增强可读性。
     *
     * @param roleInfos 服务角色信息列表。
     * @param serviceModel 服务模型。
     * @return 返回带主机名的补丁历史集合。
     */
    private Set<PatchHistoryDto> convertRole2HistorySet(List<ServiceRoleInfo> roleInfos, ServiceModel serviceModel) {
        if (CollectionUtils.isEmpty(roleInfos)) {
            return Sets.newHashSet();
        }

        if (StringUtils.equalsIgnoreCase(serviceModel.getPatchType(), "role")) {
            return roleInfos.stream().map(roleInfo -> new PatchHistoryDto(getIpAndRoleName(roleInfo),
                queryHostIp2HostName().getOrDefault(roleInfo.getIpAddress(), ""))).collect(Collectors.toSet());
        } else if (StringUtils.equalsIgnoreCase(serviceModel.getPatchType(), "instance")) {
            return roleInfos.stream()
                .map(roleInfo -> new PatchHistoryDto(
                    roleInfo.getIpAddress() + "(" + roleInfo.getServiceInstanceId() + ")",
                    queryHostIp2HostName().getOrDefault(roleInfo.getIpAddress(), "")))
                .collect(Collectors.toSet());
        }

        return roleInfos.stream().map(roleInfo -> new PatchHistoryDto(roleInfo.getIpAddress(),
            queryHostIp2HostName().getOrDefault(roleInfo.getIpAddress(), ""))).collect(Collectors.toSet());
    }

    /**
     * 功能说明：生成IP和角色名称的组合字符串。
     *
     * 业务背景：将主机IP和角色名称组合，用于日志记录和用户界面展示。
     *
     * @param roleInfo 服务角色信息。
     * @return 返回组合后的字符串。
     */
    private String getIpAndRoleName(ServiceRoleInfo roleInfo) {
        return roleInfo.getIpAddress() + "(" + roleInfo.getRoleId().substring(roleInfo.getRoleId().lastIndexOf(".") + 1)
            + ")";
    }
}