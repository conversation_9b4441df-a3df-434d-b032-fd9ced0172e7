/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UpdateRollbackResultScheduler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/22
 * </p>
 * <p>
 * 完成日期：2023/3/22
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.rollback.strategy;

import java.util.List;

import com.zte.daip.manager.patcher.domain.rollback.cache.PatchRollbackProgressQueue;
import com.zte.daip.manager.patcher.domain.rollback.entity.RollbackProgressDo;
import com.zte.daip.manager.patcher.domain.rollback.progress.RollbackProgressService;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class UpdateRollbackProgressScheduler {

    @Autowired
    private PatchRollbackProgressQueue patchRollbackProgressQueue;
    @Autowired
    private RollbackProgressService rollbackProgressService;

    @Scheduled(cron = "0/2 * * * * ?")
    public void cron() {
        List<RollbackProgressDo> rollbackProgressDos = Lists.newArrayList();

        while (!patchRollbackProgressQueue.isEmptyDispatchCache()) {
            RollbackProgressDo rollbackProgressDo = patchRollbackProgressQueue.poll();
            rollbackProgressDos.add(rollbackProgressDo);
        }
        if (!CollectionUtils.isEmpty(rollbackProgressDos)) {
            rollbackProgressService.updateRollbackProgress(rollbackProgressDos);
        }

    }

}