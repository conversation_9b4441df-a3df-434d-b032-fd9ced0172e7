/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: HostInfoCache.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/12/22
 * </p>
 * <p>
 * 完成日期：2021/12/22
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zte.daip.manager.patcher.domain.cache.ResourceLocalCacheConfig.HOST_RESOURCE_INFO;
import static com.zte.daip.manager.patcher.domain.cache.ResourceLocalCacheConfig.RESOURCE_CAFFEINE_CACHE_MANAGER;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@Service
@CacheConfig(cacheManager = RESOURCE_CAFFEINE_CACHE_MANAGER)
public class HostResourceInfoCache {

    @Autowired
    private HostResourceControllerApi hostResourceControllerApi;

    private LoadingCache<String, List<HostInfo>> hostInfoCache = CacheBuilder.newBuilder().maximumSize(10000)
        .refreshAfterWrite(5, TimeUnit.MINUTES).build(new CacheLoader<String, List<HostInfo>>() {
            @Override
            public List<HostInfo> load(String key) {
                List<HostInfo> hostInfos = hostResourceControllerApi.queryAll();
                if (!CollectionUtils.isEmpty(hostInfos)) {
                    return hostInfos;
                }
                return Lists.newArrayList();
            }
        });

    public String queryIpAddress(String hostName) {
        try {
            List<HostInfo> hostInfos = hostInfoCache.get("all");
            if (!CollectionUtils.isEmpty(hostInfos)) {
                List<HostInfo> host = hostInfos.stream()
                    .filter(hostInfo -> StringUtils.equalsIgnoreCase(hostName, hostInfo.getHostName()))
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(host)) {
                    return host.get(0).getIpAddress();
                }
            }
        } catch (Exception e) {
            log.error("Query ipAddress by hostName error:", e);
        }
        return hostName;
    }

    public List<String> queryHostNames(List<String> ipAddresses) {
        try {
            List<HostInfo> hostInfos = hostInfoCache.get("all");
            if (!CollectionUtils.isEmpty(hostInfos)) {
                List<String> hostnames =
                    hostInfos.stream().filter(hostInfo -> ipAddresses.contains(hostInfo.getIpAddress()))
                        .map(HostInfo::getHostName).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(hostnames)) {
                    return hostnames;
                }
            }
        } catch (Exception e) {
            log.error("Query ipAddress by hostName error:", e);
        }
        return Lists.newArrayList();
    }

    @Cacheable(cacheNames = HOST_RESOURCE_INFO, key = "'ipaddress:resource:'+#clusterId")
    public List<String> queryIpAddressByClusterId(String clusterId) {
        List<HostInfo> hostInfoList = hostResourceControllerApi.queryByClusterId(clusterId);
        if (!CollectionUtils.isEmpty(hostInfoList)) {
            return hostInfoList.stream().map(HostInfo::getIpAddress).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }

    public List<HostInfo> queryHostInfoByIps(Set<String> ipAddresses) {
        try {
            List<HostInfo> allHost = hostInfoCache.get("all");
            if (!CollectionUtils.isEmpty(allHost)) {
                List<HostInfo> hostInfos = allHost.stream()
                    .filter(hostInfo -> ipAddresses.contains(hostInfo.getIpAddress())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(hostInfos)) {
                    return hostInfos;
                }
            }
        } catch (Exception e) {
            log.error("Query hostInfos by ipAddress error:", e);
        }
        return Lists.newArrayList();
    }

    @Cacheable(cacheNames = HOST_RESOURCE_INFO, key = "'host:resource:'+#clusterId")
    public List<HostInfo> queryHostByClusterId(String clusterId) {
        return hostResourceControllerApi.queryByClusterId(clusterId);
    }

    @CacheEvict(allEntries = true, beforeInvocation = true)
    public void clear() {
        log.debug(String.format("clear local cache:%s", HOST_RESOURCE_INFO));
    }
}