package com.zte.daip.manager.patcher.domain.common;

import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchTaskRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CacheConfig(cacheNames = "patchTask")
@Slf4j
public class PatchTaskService
{
    @Autowired
    private PatchTaskRepository patchTaskRepository;

    @CacheEvict(allEntries = true)
    public PatchTaskPo addPatchTask(PatchTaskPo patchTaskPo) {
        return patchTaskRepository.save(patchTaskPo);
    }

    @CacheEvict(allEntries = true)
    public void updatePatchTask(PatchTaskPo patchTaskPo) {
        patchTaskRepository.save(patchTaskPo);
    }

    @CacheEvict(allEntries = true)
    public void updateAllowModifyByTaskId(long taskId, int allowModify) {
        patchTaskRepository.updateAllowModifyByTaskId(taskId, allowModify);
    }

    @CacheEvict(allEntries = true)
    public void deleteByTaskId(long taskId) {
        patchTaskRepository.deleteByTaskId(taskId);
    }

    @CacheEvict(allEntries = true)
    public void deleteByTaskIds(List<Long> taskIds) {
        patchTaskRepository.deleteByTaskIds(taskIds);
    }

    @Cacheable(key = "'allTask'")
    public List<PatchTaskPo> queryAllPatchTasks() {
        return patchTaskRepository.findAll();
    }

    @Cacheable(key = "#p0")
    public PatchTaskPo queryByTaskId(long taskId) {
        return patchTaskRepository.findByTaskId(taskId);
    }

    public List<PatchTaskPo> queryByTaskIds(List<Long> taskIds) {
        return patchTaskRepository.findByTaskIds(taskIds);
    }

    @CacheEvict(allEntries = true)
    public void updateRelationTaskIdByTaskId(long oldTaskId, long relationTaskId) {
        patchTaskRepository.updateRelationTaskIdTaskId(oldTaskId, relationTaskId);
    }

    @CacheEvict(allEntries = true)
    public void clearRelationTaskId(List<Long> relationTaskIds) {
        patchTaskRepository.updateRelationTaskIdsToZero(relationTaskIds);
    }
}