package com.zte.daip.manager.patcher.domain.upload.service.listener;

import com.zte.daip.manager.patcher.domain.upload.event.PatchFullValidateEvent;
import com.zte.daip.manager.patcher.domain.upload.service.PatchFullValidateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PatchFullValidateListener implements ApplicationListener<PatchFullValidateEvent> {

    @Autowired
    private PatchFullValidateService patchFullValidateService;

    @Override
    @Async
    public void onApplicationEvent(PatchFullValidateEvent event) {
        String batchId = event.getSource().toString();
        patchFullValidateService.validateFullPatches(batchId);
    }
}
