/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchUpdateSendMesage.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/1
 * </p>
 * <p>
 * 完成日期：2021/4/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.sender;

import com.google.common.collect.Sets;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.reply.producer.ReplyingEventPublisher;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.event.reporter.api.annotation.BusinessDomainEvent;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateRequest;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateBeansConvertor;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateCacheService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchUpdateResultKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchUpdateMessageSender {

    @Autowired
    private ReplyingEventPublisher replyingEventPublisher;
    @Autowired
    private HostResourceInfoCache hostResourceInfoCache;
    @Autowired
    private PatchUpdateCacheService patchUpdateCacheService;
    @Autowired
    private PatchUpdateBeansConvertor patchUpdateBeansConvertor;

    @BusinessDomainEvent(eventName = I18nKeyConstants.PUBLISH_UPGRADE_PATCH_MSG, clusterId = "#clusterId")
    public void publishUpdatePatchesMessage(String clusterId, List<PatchUpdateRequest> requests) {
        log.debug("all patch request:{}",requests);
        initPatchProgressCache(clusterId, requests);
        patchUpdateCacheService.putPatchInfoToCache(requests);
        requests.forEach(request -> {
            RequestMessageBody<PatchUpdateRequest> requestMessageBody = new RequestMessageBody();
            Set<String> targetIps = request.getIp2Patches().keySet();
            Set<String> targetHostNames = queryTargetHostNames(clusterId,targetIps);
            requestMessageBody.setTargetHosts(targetHostNames);
            requestMessageBody.setBody(new PatchUpdateRequest(request.getClusterId(), request.getPatchHome(), request.getPatchType()));
            requestMessageBody.setTimeOut(30 * 60 * 1000L);
            replyingEventPublisher.send("patchUpdate", requestMessageBody, "patchesUpdateAckHandler");
            log.info("success to send patchUpdate message for patchHome:" + request.getPatchHome());
        });
    }

    private void initPatchProgressCache(String clusterId, List<PatchUpdateRequest> requests) {
        log.info("start to init patch info into cache for update progress.");
        Set<String> patchResultKeys = Sets.newConcurrentHashSet();
        if (!CollectionUtils.isEmpty(requests)) {
            patchResultKeys = requests.stream().map(r -> {
                PatchUpdateResultKey p = new PatchUpdateResultKey(r.getClusterId(), r.getPatchHome(), r.getPatchType());
                return p.generateUId();
            }).collect(Collectors.toSet());
        }

        patchUpdateCacheService.clearPatchUpdateCache(clusterId, patchResultKeys);
        PatchUpdateCacheDto patchUpdateCacheDto =patchUpdateBeansConvertor.convertUpdateRequest2CacheDto(clusterId, requests);
        if (patchUpdateCacheDto != null && !CollectionUtils.isEmpty(patchUpdateCacheDto.getPatchResults())) {
            patchUpdateCacheService.initUpdateProgress(clusterId, patchUpdateCacheDto, patchResultKeys);
        }
        log.info("finished to init patch info into cache for update progress.");
    }

    private Set<String> queryTargetHostNames(String clusterId, Set<String> targetHostIps) {
        List<HostInfo> hosts = hostResourceInfoCache.queryHostByClusterId(clusterId);
        Map<String, String> ip2HostNameMaps = hosts.stream().collect(Collectors.toMap(HostInfo::getIpAddress, HostInfo::getHostName));
        return targetHostIps.stream().map(ip2HostNameMaps::get).collect(Collectors.toSet());
    }
}