/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: InstancePatchSourceGenerator.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/8
 * </p>
 * <p>
 * 完成日期：2021/4/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service.servicerole;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.api.dto.PatchHostDto;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.valobj.NeedPatchServiceInfo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service("InstancePatchSourceGenerator")
@Slf4j
public class InstancePatchSourceGenerator extends AbstractPatchSourceGenerator {

    private static final PatchTypeEnum patchTypeEnum = PatchTypeEnum.INSTANCE;

    @Override
    public List<ServiceRoleInfo> obtainUnpatchedRoles(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {

        final NeedPatchServiceInfo needPatchServiceInfo = patch2ServiceInfo.computeIfAbsent(
            generatePatch2ServiceInfoKey(patchDetailDto), k -> obtainAllRoles(patchDetailDto, serviceModel));

        List<ServiceRoleInfo> roleInfos = needPatchServiceInfo.getServiceRoleInfos();

        if (1 == patchDetailDto.getIsContainerPatch()) {
            return getRolesForContainerPatch(roleInfos, patchDetailDto);
        }

        return filterRolesUpdatedPatch(roleInfos, patchDetailDto, serviceModel.getServiceId());
    }

    @Override
    public String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException {
        return "";
    }

    @Override
    public List<RollAutoPatchUpdateInfo> obtainPatchUpdateInfos(RollAutoPatchUpdateParam rollingPatchUpdateParam,
        String version, List<String> updatedContainerPatches) {
        return Lists.newArrayList();
    }

    @Override
    public int obtainUnpatchedHostNum(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {
        final Map<String, Set<PatchHistoryDto>> unpatchedService2Hosts =
            obtainUnpatchedService2Hosts(patchDetailDto, serviceModel);
        if (unpatchedService2Hosts == null || unpatchedService2Hosts.isEmpty()) {
            return 0;
        }
        return unpatchedService2Hosts.values().stream().mapToInt(Set::size).sum();
    }

    @Override
    public Map<String, Set<PatchHistoryDto>> obtainUnpatchedService2Hosts(PatchDetailDto patchDetailDto,
        ServiceModel serviceModel) {
        Map<String, Set<PatchHistoryDto>> service2Hosts = Maps.newConcurrentMap();

        List<ServiceRoleInfo> roleInfos = obtainUnpatchedRolesByDispatchUnpatchedHosts(patchDetailDto, serviceModel);

        for (ServiceRoleInfo roleInfo : roleInfos) {

            String serviceInstanceId = roleInfo.getServiceInstanceId();

            service2Hosts.computeIfAbsent(serviceInstanceId, k -> Sets.newHashSet())
                .add(new PatchHistoryDto(roleInfo.getIpAddress() + "(" + roleInfo.getServiceInstanceId() + ")",
                    queryHostIp2HostName().getOrDefault(roleInfo.getIpAddress(), "")));
        }
        return service2Hosts;
    }

    @Override
    public void obtainUnpatchedClustersAndServices(PatchDetailDto patchDetailDto, ServiceModel serviceModel,
        Map<String, Set<String>> clusterServiceLstMap) {
        List<ServiceRoleInfo> roleInfos = obtainUnpatchedRolesByDispatchUnpatchedHosts(patchDetailDto, serviceModel);
        for (ServiceRoleInfo roleInfo : roleInfos) {

            clusterServiceLstMap.computeIfAbsent(roleInfo.getClusterId(), k -> Sets.newConcurrentHashSet())
                .add(roleInfo.getServiceInstanceId());
        }
    }

    @Override
    public List<PatchHostDto> obtainUnpatchedPatchAndHost(PatchDetailDto patchDetailDto, ServiceModel serviceModel,
        Map<String, Set<PatchHistoryDto>> service2Hosts, String clusterId) {

        List<ServiceRoleInfo> roleInfos = obtainUnpatchedRolesByDispatchUnpatchedHosts(patchDetailDto, serviceModel);

        List<ServiceRoleInfo> roleInfoList = filterUnpatchedRolesByClusterId(roleInfos, clusterId);

        Map<PatchDetailDto, Set<PatchHistoryDto>> patch2Host = Maps.newHashMap();

        Set<String> selectedServices = service2Hosts.keySet();
        for (ServiceRoleInfo serviceRoleInfo : roleInfoList) {
            String serviceInstanceId = serviceRoleInfo.getServiceInstanceId();
            Set<PatchHistoryDto> selectedHosts = service2Hosts.get(serviceInstanceId);
            List<String> selectedHostIps = Optional.ofNullable(selectedHosts).orElse(Sets.newHashSet()).stream()
                .map(PatchHistoryDto::getIp).collect(Collectors.toList());
            if (!selectedServices.contains(serviceInstanceId) || (!CollectionUtils.isEmpty(selectedHostIps)
                && !selectedHostIps.contains(serviceRoleInfo.getIpAddress()))) {
                continue;
            }

            PatchDetailDto instancePatchDetail = new PatchDetailDto(patchDetailDto);
            instancePatchDetail.setServiceInstanceId(serviceInstanceId);
            patch2Host.computeIfAbsent(instancePatchDetail, k -> Sets.newHashSet())
                .add(new PatchHistoryDto(serviceRoleInfo.getIpAddress(),
                    queryHostIp2HostName().getOrDefault(serviceRoleInfo.getIpAddress(), "")));
        }

        return patch2Host.entrySet().stream().filter(entry -> !CollectionUtils.isEmpty(entry.getValue()))
            .map(entry -> new PatchHostDto(entry.getKey(), entry.getValue())).collect(Collectors.toList());

    }

    private List<ServiceRoleInfo> getRolesForContainerPatch(List<ServiceRoleInfo> roleInfos,
        PatchDetailDto patchDetailDto) {
        final String patchName = patchDetailDto.getPatchName();

        List<String> updatedIps = queryPatchHostByServiceAndPatch(patchDetailDto.getService(), patchName);

        return roleInfos.stream().filter(roleInfo -> !updatedIps.contains(roleInfo.getIpAddress()))
            .collect(Collectors.toList());
    }

    @Override
    protected String generateKey(String serviceId, PatchHistory patchHistory) {
        return super.generateKey(serviceId, "", patchHistory.getId().getIp(),
            patchHistory.getId().getServiceInstanceId());
    }

    @Override
    protected String generateKey(ServiceRoleInfo roleInfo) {
        return super.generateKey(roleInfo.getServiceId(), "", roleInfo.getIpAddress(), roleInfo.getServiceInstanceId());
    }

}