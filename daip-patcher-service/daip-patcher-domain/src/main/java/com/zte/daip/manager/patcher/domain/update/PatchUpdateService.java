/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchUpdateService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/1
 * </p>
 * <p>
 * 完成日期：2021/4/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update;

import static com.zte.daip.manager.patcher.domain.utils.Constants.BIG_DATA_SERVICE_ID;

import java.util.*;
import java.util.stream.Collectors;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.event.reporter.api.annotation.BusinessDomainEvent;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.api.update.request.OrganizeKey;
import com.zte.daip.manager.patcher.api.update.request.PatchRequestKey;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateRequest;
import com.zte.daip.manager.patcher.domain.update.bean.ServiceNeedUpdatePatchInfo;
import com.zte.daip.manager.patcher.domain.update.executor.api.MergeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.api.OrganizeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.depend.DependResourceLocalUtil;
import com.zte.daip.manager.patcher.domain.update.sender.PatchUpdateMessageSender;
import com.zte.daip.manager.patcher.domain.update.service.PatchQueryService;
import com.zte.daip.manager.patcher.domain.update.service.PatchRequestCheckService;
import com.zte.daip.manager.patcher.domain.update.service.PatchTypeQueryService;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@Service
public class PatchUpdateService {

    @Autowired
    private PatchQueryService patchQueryService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private PatchTypeQueryService patchTypeQueryService;
    @Autowired
    private PatchUpdateMessageSender patchUpdateMessageSender;
    @Autowired
    private PatchRequestCheckService patchRequestCheckService;
    @Autowired
    private DaipEventReporter daipEventReporter;
    @Autowired
    private DependResourceLocalUtil dependResourceLocalUtil;

    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @BusinessDomainEvent(eventName = I18nKeyConstants.DOMAIN_UPGRADE_PATCH, clusterId = "#updateRequest.clusterId")
    public void update(UpdateRequest updateRequest) {
        String clusterId = updateRequest.getClusterId();

        List<ServiceInstanceInfo> serviceInstanceInfos = updateRequest.getServiceInstanceInfos();
        if (CollectionUtils.isEmpty(serviceInstanceInfos)) {
            final String msg = "Failed to update patch: service instances is empty.";
            log.warn(msg);
            daipEventReporter.info(I18nKeyConstants.UPGRADE_PATCH, msg);
            return;
        }
        // pre check zdh common service.
        patchRequestCheckService.checkUpdateRequest(updateRequest);

        List<PatchUpdateRequest> allPatchRequests = organizePatchUpdateRequests(updateRequest);

        // merge all patch requests if same patchHome.
        List<PatchUpdateRequest> finalMessage = mergePatchRequest(allPatchRequests);

        // send message and callback
        patchUpdateMessageSender.publishUpdatePatchesMessage(clusterId, finalMessage);
    }

    public List<ServiceInstancePatchInfo> queryRollbackPoints(String clusterId, List<String> instanceIds) {
        List<ServiceNeedUpdatePatchInfo> serviceNeedUpdatePatchInfos =
            getServiceNeedUpdatePatchInfos(clusterId, instanceIds);

        return serviceNeedUpdatePatchInfos.stream().flatMap(info -> info.getPatchInfoList().stream())
            .collect(Collectors.toList());
    }

    public Map<String, List<String>> queryNeedUpdatePatchs(String clusterId, List<String> instanceIds) {
        List<ServiceNeedUpdatePatchInfo> serviceNeedUpdatePatchInfos =
            getServiceNeedUpdatePatchInfos(clusterId, instanceIds);
        Map<String, List<String>> needUpdatePatchMap = new HashMap<>();
        for (ServiceNeedUpdatePatchInfo serviceNeedUpdatePatchInfo : serviceNeedUpdatePatchInfos) {
            if (!StringUtils.equals(serviceNeedUpdatePatchInfo.getServiceInstanceId(),
                PatchTypeQueryService.DEFAULT_ZDH_BASE_SERVICE_NAME)) {
                needUpdatePatchMap.put(serviceNeedUpdatePatchInfo.getServiceInstanceId(),
                    serviceNeedUpdatePatchInfo.getNeedUpdatePatchList());
            }
        }

        return needUpdatePatchMap;
    }

    private List<ServiceNeedUpdatePatchInfo> getServiceNeedUpdatePatchInfos(String clusterId,
        List<String> instanceIds) {
        List<String> tmpInstanceIds = Lists.newArrayList(instanceIds);
        if (CollectionUtils.isEmpty(tmpInstanceIds)) {
            log.info("Query need update patchs: service instances is empty.");
            return Lists.newArrayList();
        }

        Set<String> bigDataServiceIds = patchTypeQueryService.queryBigDataServiceIds(clusterId);
        Set<String> bigData = Sets.newHashSet(bigDataServiceIds);
        List<ServiceRoleInfo> serviceRoles = dependResourceLocalUtil.getClusterServiceRoleInfo(clusterId);
        return getPatchRollbackHostInfos(clusterId, tmpInstanceIds, bigData, serviceRoles);
    }

    private List<ServiceNeedUpdatePatchInfo> getPatchRollbackHostInfos(String clusterId, List<String> instanceIds,
        Set<String> bigData, List<ServiceRoleInfo> serviceRoles) {
        List<ServiceNeedUpdatePatchInfo> serviceNeedUpdatePatchInfos = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(serviceRoles)) {
            Set<String> serviceIds =
                serviceRoles.stream().map(ServiceRoleInfo::getServiceId).collect(Collectors.toSet());
            bigData.retainAll(serviceIds);
            List<ServiceModel> serviceModels = productModelInfoControllerApi.queryByClusterId(clusterId);
            if (!CollectionUtils.isEmpty(serviceModels)) {
                String zdhVersion = generateZdhService(instanceIds, bigData, serviceModels);
                for (String instanceId : instanceIds) {
                    ServiceRoleInfo serviceRoleInfo = getServiceRoleInfo(clusterId, serviceRoles, instanceId);

                    OrganizeKey key = generateOrganizeKey(instanceId, zdhVersion, serviceRoleInfo, serviceModels);

                    String patchType =
                        patchTypeQueryService.queryPatchTypeByServiceName(clusterId, key.getServiceName());
                    OrganizeRequest requester = applicationContext.getBean(patchType, OrganizeRequest.class);
                    ServiceNeedUpdatePatchInfo serviceNeedUpdatePatchInfo = requester.queryRollbackPoints(serviceRoleInfo, key);
                    if (serviceNeedUpdatePatchInfo != null){
                        serviceNeedUpdatePatchInfos.add(serviceNeedUpdatePatchInfo);
                    }
                }
            }
        }
        return serviceNeedUpdatePatchInfos;
    }

    private OrganizeKey generateOrganizeKey(String instanceId, String zdhVersion, ServiceRoleInfo serviceRoleInfo,
        List<ServiceModel> serviceModels) {
        OrganizeKey key;
        if (StringUtils.equals(instanceId, PatchTypeQueryService.DEFAULT_ZDH_BASE_SERVICE_NAME)
            && !zdhVersion.equals("")) {
            key = new OrganizeKey(PatchTypeQueryService.DEFAULT_ZDH_BASE_SERVICE_NAME, serviceRoleInfo.getServiceId(),
                zdhVersion);
        } else {
            ServiceModel serviceModel =
                serviceModels.stream().filter(s -> StringUtils.equals(s.getServiceId(), serviceRoleInfo.getServiceId()))
                    .findFirst().orElse(new ServiceModel());
            key = new OrganizeKey(serviceModel.getServiceName(), serviceRoleInfo.getServiceId(),
                serviceModel.getVersion());
        }
        return key;
    }

    private String generateZdhService(List<String> instanceIds, Set<String> bigData, List<ServiceModel> serviceModels) {
        String zdhVersion = "";
        if (!bigData.isEmpty() && serviceModels.stream().anyMatch(s -> bigData.contains(s.getServiceId()))) {
            if (!instanceIds.contains(PatchTypeQueryService.DEFAULT_ZDH_BASE_SERVICE_NAME)) {
                instanceIds.add(PatchTypeQueryService.DEFAULT_ZDH_BASE_SERVICE_NAME);
            }
            if (serviceModels.stream().noneMatch(
                s -> StringUtils.equals(s.getServiceId(), PatchTypeQueryService.DEFAULT_ZDH_BASE_SERVICE_NAME))) {
                zdhVersion =
                    serviceModels.stream().filter(s -> StringUtils.equals(s.getComponentType(), BIG_DATA_SERVICE_ID))
                        .map(ServiceModel::getVersion).findFirst().orElse("");
            }
        }
        return zdhVersion;
    }

    private ServiceRoleInfo getServiceRoleInfo(String clusterId, List<ServiceRoleInfo> serviceRoles,
        String instanceId) {
        if (StringUtils.equals(PatchTypeQueryService.DEFAULT_ZDH_BASE_SERVICE_NAME, instanceId)) {
            ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
            serviceRoleInfo.setClusterId(clusterId);
            serviceRoleInfo.setServiceId(instanceId);
            serviceRoleInfo.setServiceInstanceId(instanceId);
            return serviceRoleInfo;
        } else {
            return serviceRoles.stream().filter(s -> StringUtils.equals(instanceId, s.getServiceInstanceId()))
                .findFirst().orElse(new ServiceRoleInfo());
        }
    }

    private List<PatchUpdateRequest> organizePatchUpdateRequests(UpdateRequest updateRequest) {
        log.info(String.format("start to organize request:%s", JSON.toJSON(updateRequest)));
        daipEventReporter.info(I18nKeyConstants.ORGANIZE_PATCH_UPDATE_REQUESTS,
            String.format("updateRequest is %s", JSONObject.toJSONString(updateRequest)));

        String clusterId = updateRequest.getClusterId();
        initDependResource(clusterId);

        // group by service name
        List<ServiceInstanceInfo> serviceInstances = updateRequest.getServiceInstanceInfos();
        Map<String, List<ServiceInstanceInfo>> serviceName2Instances =
            serviceInstances.stream().collect(Collectors.groupingBy(ServiceInstanceInfo::getServiceName));

        List<PatchUpdateRequest> allPatchRequests = Lists.newArrayList();
        for (Map.Entry<String, List<ServiceInstanceInfo>> entry : serviceName2Instances.entrySet()) {
            String serviceName = entry.getKey();
            List<ServiceInstanceInfo> serviceInstanceInfos = entry.getValue();
            //
            OrganizeKey key = new OrganizeKey(serviceName, "", "");
            Optional<ServiceInstanceInfo> first = entry.getValue().stream().findFirst();
            if (first.isPresent()) {
                key = new OrganizeKey(serviceName, first.get().getServiceId(), first.get().getVersion(),
                    patchQueryService.queryUpdatedContainerPatches(serviceName, first.get().getVersion()));
            }
            // service patchType
            String patchType = patchTypeQueryService.queryPatchTypeByServiceName(clusterId, key.getServiceName());
            OrganizeRequest requester = applicationContext.getBean(patchType, OrganizeRequest.class);
            List<PatchUpdateRequest> requests = requester.organizeRequest(clusterId, key, serviceInstanceInfos);
            log.debug(serviceName + ":organizeRequest:" + JSONObject.toJSONString(requests));

            allPatchRequests.addAll(requests);
            log.info(String.format("finish organize service_name:%s:patchType:%s", serviceName, patchType));

        }
        clearDependResource();
        log.info("finished to organize all PatchUpdateRequests.");
        return allPatchRequests;
    }

    private List<PatchUpdateRequest> mergePatchRequest(List<PatchUpdateRequest> updateRequests) {
        log.info("start to merge PatchUpdateRequests.");
        // group by patchRequestKey.
        Map<PatchRequestKey, List<PatchUpdateRequest>> groupedRequest = updateRequests.stream().collect(
            Collectors.groupingBy(e -> new PatchRequestKey(e.getClusterId(), e.getPatchHome(), e.getPatchType())));

        List<PatchUpdateRequest> finalRequest = Lists.newArrayList();
        for (Map.Entry<PatchRequestKey, List<PatchUpdateRequest>> entry : groupedRequest.entrySet()) {
            PatchRequestKey requestKey = entry.getKey();
            MergeRequest requester = applicationContext.getBean(requestKey.getPatchType(), MergeRequest.class);
            // same patchRequestKey then merge.
            PatchUpdateRequest patchUpdateRequest = requester.mergeRequest(requestKey, entry.getValue());
            if (null != patchUpdateRequest) {
                finalRequest.add(patchUpdateRequest);
            }
            log.info(String.format("finished to merge requestKey:%s", requestKey));
        }

        log.info("finished to merge  all PatchUpdateRequests.");
        return finalRequest;
    }

    private void clearDependResource() {
        dependResourceLocalUtil.clear();
    }

    private void initDependResource(String clusterId) {
        dependResourceLocalUtil.initDependResource(clusterId);
    }

}