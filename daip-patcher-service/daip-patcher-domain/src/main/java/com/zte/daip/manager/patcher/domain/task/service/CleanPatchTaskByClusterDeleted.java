package com.zte.daip.manager.patcher.domain.task.service;

import java.util.List;
import java.util.stream.Collectors;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import com.zte.daip.manager.common.deployer.bean.cluster.ClusterBean;
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zte.daip.communication.api.ConsumerHandler;
import com.zte.daip.communication.api.EventListenerTo;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.exception.MessageCenterException;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchCategoryEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Service
@Slf4j
@EventListenerTo("clusterDeleted")
public class CleanPatchTaskByClusterDeleted implements ConsumerHandler<String> {

    @Autowired
    private PatchTaskService patchTaskService;

    @Override
    /* Started by AICoder, pid:db516o9d98o955a14ab90939c052681d0c196233 */
    public String handle(String body) throws MessageCenterException {
        log.info("Receive clean patch task msg.");
        RequestMessageBody<List<ClusterBean>> messageBody =
            JSON.parseObject(body, new TypeReference<RequestMessageBody<List<ClusterBean>>>() {});
        if (messageBody != null && messageBody.getBody() != null) {
            List<String> clusterIds = messageBody.getBody().stream().map(bean -> String.valueOf(bean.getClusterId())).collect(Collectors.toList());
            List<PatchTaskPo> patchTaskPos = patchTaskService.queryAllPatchTasks();
            List<Long> needDeleteTaskIds = patchTaskPos.stream()
                .filter(patchTaskPo -> clusterIds.contains(patchTaskPo.getClusterId())
                    && !StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.SCHEMA.getPatchCategory()))
                .map(PatchTaskPo::getTaskId).collect(Collectors.toList());
            if (!needDeleteTaskIds.isEmpty()) {
                log.info(String.format("need delete patch taskId=%s", needDeleteTaskIds));
                patchTaskService.deleteByTaskIds(needDeleteTaskIds);
            } else {
                log.info("No patch tasks need to be deleted.");
            }
        }
        return "success";
    }
    /* Ended by AICoder, pid:db516o9d98o955a14ab90939c052681d0c196233 */
}