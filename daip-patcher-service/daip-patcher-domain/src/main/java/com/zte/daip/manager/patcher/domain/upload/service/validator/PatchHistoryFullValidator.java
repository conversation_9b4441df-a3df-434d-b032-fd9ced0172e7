package com.zte.daip.manager.patcher.domain.upload.service.validator;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.operate.PatchTypeOperateFactory;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatchHistoryFullValidator implements PatchValidator {

    @Value("${dap.manager.patch.filter.fullPatch:VMAX}")
    private String ignoreList;

    @Autowired
    private PatchInfoService patchInfoService;

    @Autowired
    private PatchTypeOperateFactory patchTypeOperateFactory;

    @Override
    public PatchUploadResult checkPatch(PatchBean patchBean, File file, List<PatchDetailPo> patchDetailPos) {
        if (!ignore(file.getName())) {
            List<PatchDetailPo> fullPatches = patchDetailPos.stream()
                .filter(
                    patchDetailPo -> StringUtils.equalsIgnoreCase(patchDetailPo.getService(), patchBean.getService())
                        && StringUtils.containsIgnoreCase(patchDetailPo.getBaseVersion(), patchBean.getSrcVersion()))
                .filter(patchDetailPo -> patchDetailPo.getIsFullPatch() == 1).collect(Collectors.toList());
            List<PatchDetailPo> filterFullPatchList =
                patchTypeOperateFactory.getPatchType(patchBean).filterHistoryFullPatch(fullPatches);
            if (!CollectionUtils.isEmpty(filterFullPatchList)) {
                List<PatchDetailPo> sortedList = filterFullPatchList.stream()
                    .sorted(Comparator.comparing(PatchDetailPo::getPatchName).reversed()).collect(Collectors.toList());
                String fullPatchPatchName = sortedList.get(0).getPatchName();
                if (patchBean.getPatchName().compareToIgnoreCase(fullPatchPatchName) <= 0) {
                    log.error(" {} exist newer full patch:{}", patchBean.getPatchName(), fullPatchPatchName);
                    return new PatchUploadResult(false, "exist newer full-patch", patchBean.getPatchName(), true,
                        OnePatchProcess.NORMAL_CHECK);
                }
            }
        }
        return new PatchUploadResult(true, "upload patch success", patchBean.getPatchName(), false,
            OnePatchProcess.VALIDATING);
    }

    @Override
    public int order() {
        return 30;
    }

    private boolean ignore(String patchFileName) {
        String[] ignores = StringUtils.split(ignoreList, ",");
        for (String e : ignores) {
            if (StringUtils.lowerCase(patchFileName).contains(StringUtils.lowerCase(e))) {
                return true;
            }
        }
        return false;
    }
}
