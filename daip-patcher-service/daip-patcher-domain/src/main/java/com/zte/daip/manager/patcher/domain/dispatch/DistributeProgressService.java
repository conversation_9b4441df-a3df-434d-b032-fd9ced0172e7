/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UpdateDistributeProgressEvent.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/9
 * </p>
 * <p>
 * 完成日期：2023/3/9
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.dispatch;

import com.alibaba.fastjson.JSON;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;
import com.zte.daip.manager.filemanagement.api.FileDistributeQueryControllerApi;
import com.zte.daip.manager.filemanagement.api.bean.DistributeSuccessResult;
import com.zte.daip.manager.filemanagement.api.bean.FileDistributeResult;
import com.zte.daip.manager.filemanagement.api.progress.DistributeSummaryProgress;
import com.zte.daip.manager.filemanagement.common.util.exception.FileDistributeException;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.common.PatchDispatchService;
import com.zte.daip.manager.patcher.domain.constants.DomainI18nKey;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatch;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatchKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class DistributeProgressService {

    @Autowired
    private FileDistributeControllerApi fileDistributeControllerApi;

    @Autowired
    private FileDistributeQueryControllerApi fileDistributeQueryControllerApi;

    @Value("${fms.distribute.timeout.inMinutes:30}")
    private int distributeTimeout;

    @Autowired
    private HostResourceInfoCache hostResourceInfoCache;

    @Autowired
    private PatchDispatchService patchDispatchService;

    @Autowired
    private DaipEventReporter daipEventReporter;

    @Async("patchDispatchPool")
    public void checkAndUpdateDistributeProgress(String distributeId) throws DaipBaseException {
        try {
            int count = 0;
            while ((count++) < distributeTimeout * 6) {
                DistributeSummaryProgress distributeSummaryProgress =
                    fileDistributeQueryControllerApi.querySummaryProgress(distributeId);
                log.debug("distributeId:{}, distributeSummaryResult: {}", distributeId, distributeSummaryProgress);
                if (distributeSummaryProgress.isFinish()) {
                    dealDistributeFinished(distributeId);
                    if (!distributeSummaryProgress.isSuccess()) {
                        dealDistributeFailed(distributeId);
                    }
                    return;
                }
                TimeUnit.SECONDS.sleep(10);
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            throw new DaipBaseException("Executor auto distribute patch error.", e);
        }
    }

    private void dealDistributeFailed(String distributeId) throws FileDistributeException {
        List<FileDistributeResult> fileDistributeResults =
            fileDistributeQueryControllerApi.queryFailedDetailResult(distributeId);
        log.error("distributeId:{} finish,  distribute failed item: {}", distributeId, fileDistributeResults);
        daipEventReporter.error(DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH_FAILED,
            JSON.toJSONString(fileDistributeResults));
        fileDistributeResults.forEach(fileDistributeResult -> {
            String filePath = fileDistributeResult.getFile();
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            String patchName = fileName.substring(0, fileName.lastIndexOf('.'));
            List<PatchDispatch> failedResults = fileDistributeResult.getHostDistributeResults().stream()
                .map(hostDistributeResult -> new PatchDispatch(
                    new PatchDispatchKey(patchName,
                        hostResourceInfoCache.queryIpAddress(hostDistributeResult.getHost())),
                    System.currentTimeMillis(), false, hostDistributeResult.getMessages()))
                .collect(Collectors.toList());
            patchDispatchService.batchSave(failedResults);
        });
    }

    private void dealDistributeFinished(String distributeId) throws FileDistributeException {
        DistributeSuccessResult distributeSuccessResult =
            fileDistributeQueryControllerApi.querySuccessResult(distributeId);
        log.info("distributeId:{} finish, distribute success item: {}", distributeId, distributeSuccessResult);
        Map<String, List<String>> successHost = distributeSuccessResult.getSuccessHost();
        successHost.forEach((filePath, hostList) -> {
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            String patchName = fileName.substring(0, fileName.lastIndexOf('.'));
            List<PatchDispatch> patchDispatchList = hostList.stream()
                .map(host -> new PatchDispatch(
                    new PatchDispatchKey(patchName, hostResourceInfoCache.queryIpAddress(host)),
                    System.currentTimeMillis(), true, ""))
                .collect(Collectors.toList());
            patchDispatchService.batchSave(patchDispatchList);
        });
    }
}