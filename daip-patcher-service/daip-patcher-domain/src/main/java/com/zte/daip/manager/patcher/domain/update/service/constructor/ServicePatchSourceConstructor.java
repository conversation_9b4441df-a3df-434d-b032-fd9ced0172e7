package com.zte.daip.manager.patcher.domain.update.service.constructor;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.api.update.response.OfflinePatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.bean.PatchServiceInfo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service("ServicePatchSourceConstructor")
@Slf4j
public class ServicePatchSourceConstructor extends AbstractPatchTypeSourceConstructor {

    @Autowired
    private DeploymentInstanceServiceControllerApi instanceServiceControllerApi;

    private static final PatchTypeConstructorEnum PATCH_TYPE_CONSTRUCTOR_ENUM = PatchTypeConstructorEnum.SERVICE;
    private static final String BIG_DATA_SERVICE_ID = "dap.manager.common.bigdata";

    @Override
    public String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException {
        String clusterId = patchHomeParam.getClusterId();
        ServiceModel serviceModel =
            serviceModelInfoCache.queryByClusterIdAndServiceName(clusterId, patchHomeParam.getServiceName());
        if (serviceModel != null) {
            List<DeploymentServiceInstance> serviceInstances =
                instanceServiceControllerApi.queryByClusterIdAndServiceId(clusterId, serviceModel.getServiceId());
            if (!CollectionUtils.isEmpty(serviceInstances)) {
                String instanceId = serviceInstances.get(0).getServiceInstanceId();
                List<ConfigInstance> configInstances = patchHomeQueryService.queryPatchHome(clusterId, instanceId);
                if (!CollectionUtils.isEmpty(configInstances)) {
                    return configInstances.get(0).getConfigValue() + PATCH_HOME;
                }
            }
        }
        return "";
    }

    @Override
    public List<OfflinePatchUpdateInfo> obtainOfflinePatchUpdateInfo(PatchServiceParam updateParam, String clusterId,
        String hostIp) throws DaipBaseException {
        List<OfflinePatchUpdateInfo> finalUpdateInfo = Lists.newArrayList();
        String serviceName = updateParam.getServiceName();
        ServiceModel serviceModel = serviceModelInfoCache.queryByClusterIdAndServiceName(clusterId, serviceName);
        log.info("obtain serviceType services patch updateInfo:{}:{}", clusterId, serviceName);
        if (serviceModel != null && !StringUtils.equals(serviceModel.getPatchType(), "")
            && StringUtils.endsWithIgnoreCase(serviceModel.getComponentType(), BIG_DATA_SERVICE_ID)) {
            updateParam.setServiceName("zdh");
            List<OfflinePatchUpdateInfo> zdhUpdateInfo =
                getOfflinePatchUpdateInfos(updateParam, clusterId, hostIp).stream().peek(offlinePatchUpdateInfo -> {
                    offlinePatchUpdateInfo.setPatchHome("${ZDH_HOME}" + PATCH_HOME);
                    offlinePatchUpdateInfo.setPatchType(PatchTypeConstructorEnum.ZDH.getType());
                }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(zdhUpdateInfo)) {
                finalUpdateInfo.addAll(zdhUpdateInfo);
            }
        }
        updateParam.setServiceName(serviceName);
        List<OfflinePatchUpdateInfo> serviceUpdateInfo = getOfflinePatchUpdateInfos(updateParam, clusterId, hostIp);
        if (!CollectionUtils.isEmpty(serviceUpdateInfo)) {
            finalUpdateInfo.addAll(serviceUpdateInfo);
        }
        return finalUpdateInfo;
    }

    private List<OfflinePatchUpdateInfo> getOfflinePatchUpdateInfos(PatchServiceParam updateParam, String clusterId,
        String hostIp) throws DaipBaseException {
        String serviceName = updateParam.getServiceName();
        OfflinePatchUpdateInfo offlinePatchUpdateInfo = new OfflinePatchUpdateInfo();
        offlinePatchUpdateInfo.setPatchType(PATCH_TYPE_CONSTRUCTOR_ENUM.getType());
        offlinePatchUpdateInfo.setPatchHome(
            queryPatchHome(PatchHomeParam.builder().clusterId(clusterId).serviceName(serviceName).build()));
        ServicePatchInfo servicePatchInfo = obtainServicePatchInfo(updateParam, hostIp, serviceName);
        offlinePatchUpdateInfo.setServicePatchInfos(Lists.newArrayList(servicePatchInfo));
        return Lists.newArrayList(offlinePatchUpdateInfo);
    }

    private ServicePatchInfo obtainServicePatchInfo(PatchServiceParam updateParam, String hostIp, String serviceName) {
        ServicePatchInfo servicePatchInfo = new ServicePatchInfo();
        servicePatchInfo.setServiceName(serviceName);
        String version = updateParam.getVersion();
        servicePatchInfo.setVersion(version);
        List<String> updatedContainerPatches =
            queryIp2UpdatedContainerPatches(serviceName, version).computeIfAbsent(hostIp, k -> Lists.newArrayList())
                .stream().map(patchHistory -> patchHistory.getId().getPatchName()).collect(Collectors.toList());
        servicePatchInfo.setUpdatedContainerPatches(updatedContainerPatches);
        servicePatchInfo.setServiceInstances(updateParam.getServiceInstances());
        servicePatchInfo.setPatches(obtainSimplePatchInfo(
            PatchServiceInfo.builder().serviceName(serviceName).version(version).host(hostIp).build()));
        return servicePatchInfo;
    }

    private List<SimplePatchInfo> obtainSimplePatchInfo(PatchServiceInfo patchServiceInfo) {
        List<PatchHistory> patchHistories = patchHistoryService
            .queryPatchHistoryInfoByServiceNameAndIp(patchServiceInfo.getServiceName(), patchServiceInfo.getHost());
        Set<String> updatedPatches = patchHistories.stream().map(patchHistory -> patchHistory.getId().getPatchName())
            .collect(Collectors.toSet());
        return buildSimplePatchInfo(patchServiceInfo.getServiceName(), patchServiceInfo.getVersion(),
            patchServiceInfo.getRoleName(), updatedPatches);
    }
}
