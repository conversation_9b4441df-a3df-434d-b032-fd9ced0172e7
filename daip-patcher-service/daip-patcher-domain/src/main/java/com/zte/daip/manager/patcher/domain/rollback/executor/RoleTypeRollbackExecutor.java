/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: RoleTypeRollbackExecutor.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/6
 * </p>
 * <p>
 * 完成日期：2023/4/6
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.rollback.executor;

import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.RoleModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.model.controller.api.RoleModelControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service("roleRollback")
public class RoleTypeRollbackExecutor implements PatchRollbackExecutor {

    @Autowired
    private PatchHomeQueryService patchHomeQueryService;
    @Autowired
    private ServiceResourceControllerApi serviceResourceControllerApi;
    @Autowired
    private PublishRollbackMsg publishRollbackMsg;
    @Autowired
    private RoleModelControllerApi roleModelControllerApi;

    @Override
    public void executeRollback(String clusterId, PatchRollbackHostDto rollbackHostDto) {
        log.info("exec rollback patch:roleType");
        List<ServiceRoleInfo> serviceRoles = serviceResourceControllerApi.queryByClusterId(clusterId);
        if (!CollectionUtils.isEmpty(serviceRoles)) {
            for(ServiceRoleInfo serviceRoleInfo: serviceRoles){
                RoleModel roleModel = roleModelControllerApi
                    .queryByRoleId(clusterId, serviceRoleInfo.getServiceId(),
                        serviceRoleInfo.getRoleId());
                if(roleModel != null){
                    serviceRoleInfo.setRoleName(roleModel.getRoleName());
                }
            }
            List<ServiceRoleInfo> filtedRoles = serviceRoles.stream().filter(serviceRoleInfo -> StringUtils
                .equalsIgnoreCase(serviceRoleInfo.getRoleName(), rollbackHostDto.getRoleName()))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(filtedRoles)) {
                String roleId = filtedRoles.get(0).getRoleId();
                String serviceInstanceId = filtedRoles.get(0).getServiceInstanceId();
                String patchHome = queryPatchHome(clusterId, serviceInstanceId, roleId);
                String rollbackShell =
                    "sh " + patchHome + "/patchtool.sh recover" + " " + rollbackHostDto.getPatchName();
                publishRollbackMsg.publishRollbackMsg(clusterId, rollbackHostDto, patchHome, rollbackShell, false);
            }
        }
    }

    private String queryPatchHome(String clusterId, String anyServiceInstanceId, String roleId) {
        List<ConfigInstance> configInstances = patchHomeQueryService.queryPatchHome(clusterId, anyServiceInstanceId);
        if (CollectionUtils.isEmpty(configInstances)) {
            return "";
        }
        String configValue = configInstances.stream().filter(c -> StringUtils.equals(c.getRoleId(), roleId)).findFirst()
            .orElse(new ConfigInstance()).getConfigValue();
        return configValue + File.separator + "patch";
    }
}