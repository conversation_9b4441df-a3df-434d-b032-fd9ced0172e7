/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: FileUtils.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/15
 * </p>
 * <p>
 * 完成日期：2021/3/15
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.upload.utils;

import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class PatcherFileUtils {

    private PatcherFileUtils() {}

    public static void deletePatch(File unzipPatch) {
        if (unzipPatch.exists()) {
            try {
                FileUtils.deleteDirectory(unzipPatch);
            } catch (IOException e) {
                log.debug("delete temp patch error");
            }
        }
    }

    public static void copyFileToTempDir(MultipartFile[] patchMultipartFiles, String patchUploadTemp) {
        for (MultipartFile patch : patchMultipartFiles) {
            try {
                String filename = patch.getOriginalFilename();
                File destination = FilePathCleaner.newFile(patchUploadTemp, filename);
                FileUtils.copyInputStreamToFile(patch.getInputStream(), destination);
            } catch (IOException e) {
                log.error("copy upload-file error : ", e);
            }
        }
    }

}