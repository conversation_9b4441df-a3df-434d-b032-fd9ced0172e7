/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDetailAsyncQueryService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/12
 * </p>
 * <p>
 * 完成日期：2021/4/12
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.patcher.api.dto.*;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchDetailDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.AbstractPatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchDetailParam;
import com.zte.daip.manager.patcher.domain.query.valobj.UnpatchedParam;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatch;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class PatchDetailAsyncQueryService {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private PatchDetailDtoAssembler patchDetailDtoAssembler;

    @Async("asyncExecutor")
    public void calcOnePatchClustersAndServices(Map<String, Set<String>> clusterServiceLstMap,
        UnpatchedParam unpatchedParam, PatchDetailDto patchDetailDto, CountDownLatch countDownLatch) {

        final ServiceModel serviceModel = queryServiceModelByNameAndVersion(unpatchedParam.getServiceModels(),
            patchDetailDto.getService(), patchDetailDto.getBaseVersion());

        AbstractPatchSourceGenerator abstractPatchSourceGenerator =
            generatePatchSourceService(unpatchedParam, patchDetailDto, serviceModel);

        if (abstractPatchSourceGenerator == null) {
            countDown(countDownLatch);
            return;
        }

        abstractPatchSourceGenerator.obtainUnpatchedClustersAndServices(patchDetailDto, serviceModel,
            clusterServiceLstMap);

        countDown(countDownLatch);

    }

    @Async("asyncExecutor")
    public void calcOnePatchUnpatchedServiceInstanceInfo(
        Map<String, Set<ServiceInstanceInfo>> clustersAndServiceInstanceInfo, UnpatchedParam unpatchedParam,
        PatchDetailDto patchDetailDto, CountDownLatch countDownLatch) {

        final ServiceModel serviceModel = queryServiceModelByNameAndVersion(unpatchedParam.getServiceModels(),
            patchDetailDto.getService(), patchDetailDto.getBaseVersion());

        AbstractPatchSourceGenerator abstractPatchSourceGenerator =
            generatePatchSourceService(unpatchedParam, patchDetailDto, serviceModel);

        if (abstractPatchSourceGenerator == null) {
            countDown(countDownLatch);
            return;
        }

        abstractPatchSourceGenerator.obtainUnpatchedClustersAndServiceInstanceInfo(patchDetailDto, serviceModel,
            clustersAndServiceInstanceInfo);

        countDown(countDownLatch);
    }

    private void countDown(CountDownLatch countDownLatch) {
        if (countDownLatch != null) {
            countDownLatch.countDown();
        }
    }

    @Async("asyncExecutor")
    public Future<List<PatchHostDto>> calcOnePatchPatchAndHost(UnpatchedParam unpatchedParam,
        PatchDetailDto patchDetailDto, Map<String, Set<PatchHistoryDto>> service2Hosts, String clusterId) {

        if (patchDetailDto == null) {
            return new AsyncResult<>(null);
        }

        final ServiceModel serviceModel = queryServiceModelByNameAndVersion(unpatchedParam.getServiceModels(),
            patchDetailDto.getService(), patchDetailDto.getBaseVersion());

        AbstractPatchSourceGenerator abstractPatchSourceGenerator =
            generatePatchSourceService(unpatchedParam, patchDetailDto, serviceModel);

        if (abstractPatchSourceGenerator == null) {
            return new AsyncResult<>(null);
        }

        final List<PatchHostDto> patchHostDtoList =
            abstractPatchSourceGenerator.obtainUnpatchedPatchAndHost(patchDetailDto, serviceModel, service2Hosts, clusterId);
        return new AsyncResult<>(patchHostDtoList);
    }

    @Async("queryPatchesThreadPool")
    public Future<PatchDetailDto> calcOnePatchPatchDetail(PatchDetailParam patchDetailParam,
        UnpatchedParam unpatchedParam, PatchDetailDto patchDetailDto) {

        if (patchDetailDto == null) {
            return new AsyncResult<>(null);
        }

        final List<PatchHistory> patchHistoryList = patchDetailParam.getPatchHistoryInfoMap().getOrDefault(
            new PatchKeyDo(patchDetailDto.getPatchName(), patchDetailDto.getService(), ""), Lists.newArrayList());

        patchDetailDto.setCanDel(patchHistoryList.size() == 0);

        if (StringUtils.isNotBlank(patchDetailDto.getDependPatch())
            && !patchDetailParam.getPatchDetailPatchNameList().contains(patchDetailDto.getDependPatch())) {
            patchDetailDto.setDependPatchIsOkFlg(false);
            patchDetailDto.setSupplementDesc("no dependPatch: " + patchDetailDto.getDependPatch());
        }

        patchDetailDto.setPatchDispatchResultDto(generatePatchDispatchResultDto(
            patchDetailParam.getPatchDispatchStatisticsMap(), patchDetailDto.getPatchName()));

        patchDetailDto.setPatchUpdateResultDto(new PatchUpdateResultDto(patchHistoryList.size(),
            calcOnePatchUnpatchedHostNum(unpatchedParam, patchDetailDto)));
        return new AsyncResult<>(patchDetailDto);
    }

    public Map<String, Set<PatchHistoryDto>> calcOnePatchUnpatchedService2Hosts(UnpatchedParam unpatchedParam,
        PatchDetailDto patchDetailDto) {

        final String service = patchDetailDto.getService();

        final ServiceModel serviceModel = queryServiceModelByNameAndVersion(unpatchedParam.getServiceModels(), service,
            patchDetailDto.getBaseVersion());

        AbstractPatchSourceGenerator abstractPatchSourceGenerator =
            generatePatchSourceService(unpatchedParam, patchDetailDto, serviceModel);

        if (abstractPatchSourceGenerator == null) {
            return Maps.newHashMap();
        }

        return abstractPatchSourceGenerator.obtainUnpatchedService2Hosts(patchDetailDto, serviceModel);

    }

    private int calcOnePatchUnpatchedHostNum(UnpatchedParam unpatchedParam, PatchDetailDto patchDetailDto) {

        final String service = patchDetailDto.getService();

        final ServiceModel serviceModel = queryServiceModelByNameAndVersion(unpatchedParam.getServiceModels(), service,
            patchDetailDto.getBaseVersion());

        AbstractPatchSourceGenerator abstractPatchSourceGenerator =
            generatePatchSourceService(unpatchedParam, patchDetailDto, serviceModel);

        if (abstractPatchSourceGenerator == null) {
            return 0;
        }

        return abstractPatchSourceGenerator.obtainUnpatchedHostNum(patchDetailDto, serviceModel);

    }

    private PatchDispatchResultDto generatePatchDispatchResultDto(
        Map<PatchKeyDo, Map<Boolean, List<PatchDispatch>>> patchKeyDoListMap, String patchName) {
        PatchDispatchResultDto patchDispatchResultDto = new PatchDispatchResultDto();

        final Map<Boolean, List<PatchDispatch>> booleanListMap =
            patchKeyDoListMap.get(new PatchKeyDo(patchName, "", ""));

        if (null == booleanListMap || booleanListMap.isEmpty()) {
            return patchDispatchResultDto;
        }
        return new PatchDispatchResultDto(booleanListMap.getOrDefault(true, Lists.newArrayList()).size(),
            booleanListMap.getOrDefault(false, Lists.newArrayList()).size());
    }

    private AbstractPatchSourceGenerator generatePatchSourceService(UnpatchedParam unpatchedParam,
        PatchDetailDto patchDetailDto, ServiceModel serviceModel) {
        final PatchTypeEnum patchTypeEnum =
            PatchTypeEnum.queryPatchTypeByServiceName(patchDetailDto.getService(), serviceModel);

        AbstractPatchSourceGenerator abstractPatchSourceGenerator;
        try {
            abstractPatchSourceGenerator =
                (AbstractPatchSourceGenerator)applicationContext.getBean(patchTypeEnum.getServiceName());
        } catch (NoSuchBeanDefinitionException e) {
            return null;
        }

        abstractPatchSourceGenerator.setParams(unpatchedParam);
        return abstractPatchSourceGenerator;
    }

    private ServiceModel queryServiceModelByNameAndVersion(List<ServiceModel> serviceModels, String serviceName,
        String version) {

        return serviceModels.stream().filter(model -> StringUtils.equals(serviceName, model.getServiceName())
            && StringUtils.equals(model.getVersion(), version)).findFirst().orElse(new ServiceModel());
    }
}