package com.zte.daip.manager.patcher.domain.update.service.constructor;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.bean.model.RoleModel;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.ServiceInstance;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.api.update.response.OfflinePatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.bean.PatchServiceInfo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service("RolePatchSourceConstructor")
@Slf4j
public class RolePatchSourceConstructor extends AbstractPatchTypeSourceConstructor {

    private static final PatchTypeConstructorEnum PATCH_TYPE_CONSTRUCTOR_ENUM = PatchTypeConstructorEnum.ROLE;

    @Override
    public String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException {
        List<ConfigInstance> configInstances =
            patchHomeQueryService.queryPatchHome(patchHomeParam.getClusterId(), patchHomeParam.getServiceInstanceId());
        if (CollectionUtils.isEmpty(configInstances)) {
            return "";
        }
        String configValue =
            configInstances.stream().filter(c -> StringUtils.equals(c.getRoleId(), patchHomeParam.getRoleName()))
                .findFirst().orElse(new ConfigInstance()).getConfigValue();
        return configValue + PATCH_HOME;
    }

    @Override
    public List<OfflinePatchUpdateInfo> obtainOfflinePatchUpdateInfo(PatchServiceParam updateParam, String clusterId,
        String hostIp) throws DaipBaseException {
        log.info("obtain roleType services patch updateInfo");
        Map<String, List<ServicePatchInfo>> patchHome2ServicePatchInfo = Maps.newHashMap();
        ServiceModel serviceModel = Optional
            .ofNullable(serviceModelInfoCache.queryByClusterIdAndServiceName(clusterId, updateParam.getServiceName()))
            .orElse(new ServiceModel());
        Map<String, String> roleId2Name =
            serviceModel.getRoles().stream().collect(Collectors.toMap(RoleModel::getRoleId, RoleModel::getRoleName));
        for (ServiceInstance serviceInstance : updateParam.getServiceInstances()) {
            for (String roleId : serviceInstance.getRoles()) {
                String roleName = roleId2Name.computeIfAbsent(roleId, key -> roleId);
                String patchHome = queryPatchHome(PatchHomeParam.builder().clusterId(clusterId).roleName(roleId)
                    .serviceInstanceId(serviceInstance.getServiceInstanceId()).build());
                ServicePatchInfo servicePatchInfo = obtainServicePatchInfo(updateParam, roleName, hostIp);
                ServiceInstance newServiceInstance =
                    JSON.parseObject(JSON.toJSONString(serviceInstance), ServiceInstance.class);
                newServiceInstance.setRoles(Lists.newArrayList(roleId));
                servicePatchInfo.setServiceInstances(Lists.newArrayList(newServiceInstance));
                List<ServicePatchInfo> servicePatchInfos =
                    patchHome2ServicePatchInfo.computeIfAbsent(patchHome, k -> Lists.newArrayList());
                servicePatchInfos.add(servicePatchInfo);
            }
        }
        return getOfflinePatchUpdateInfoList(patchHome2ServicePatchInfo, PATCH_TYPE_CONSTRUCTOR_ENUM);
    }

    private ServicePatchInfo obtainServicePatchInfo(PatchServiceParam updateParam, String roleName, String hostIp) {
        String serviceName = updateParam.getServiceName();
        String version = updateParam.getVersion();
        ServicePatchInfo servicePatchInfo = new ServicePatchInfo();
        servicePatchInfo.setServiceName(serviceName);
        servicePatchInfo.setVersion(version);
        List<String> updatedContainerPatches = queryIp2UpdatedContainerPatches(serviceName, version)
            .computeIfAbsent(hostIp, k -> Lists.newArrayList()).stream()
            .filter(patchHistory -> StringUtils.equalsIgnoreCase(roleName, patchHistory.getId().getRoleName()))
            .map(patchHistory -> patchHistory.getId().getPatchName()).collect(Collectors.toList());
        servicePatchInfo.setUpdatedContainerPatches(updatedContainerPatches);
        servicePatchInfo.setPatches(obtainSimplePatchInfo(PatchServiceInfo.builder().serviceName(serviceName)
            .version(version).roleName(roleName).host(hostIp).build()));
        return servicePatchInfo;
    }

    private List<SimplePatchInfo> obtainSimplePatchInfo(PatchServiceInfo patchServiceInfo) {
        List<PatchHistory> patchHistories = patchHistoryService
            .queryPatchHistoryInfoByServiceNameAndIp(patchServiceInfo.getServiceName(), patchServiceInfo.getHost());
        Set<String> updatedPatches = patchHistories.stream()
            .filter(patchHistory -> StringUtils.equalsIgnoreCase(patchHistory.getId().getRoleName(),
                patchServiceInfo.getRoleName()))
            .map(patchHistory -> patchHistory.getId().getPatchName()).collect(Collectors.toSet());
        return buildSimplePatchInfo(patchServiceInfo.getServiceName(), patchServiceInfo.getVersion(),
            patchServiceInfo.getRoleName(), updatedPatches);
    }
}
