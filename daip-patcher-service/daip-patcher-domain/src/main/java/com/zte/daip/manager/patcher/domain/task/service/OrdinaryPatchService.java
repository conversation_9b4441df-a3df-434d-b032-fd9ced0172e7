package com.zte.daip.manager.patcher.domain.task.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.domain.task.api.ServiceInstancePatchResource;
import com.zte.daip.manager.patcher.domain.task.assembler.CurrentPatchAssembler;
import com.zte.daip.manager.patcher.inner.api.PatchUpdateInnerControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service("ordinary")
@Slf4j
public class OrdinaryPatchService implements ServiceInstancePatchResource {
    @Autowired
    private PatchUpdateInnerControllerApi patchUpdateInnerControllerApi;

    @Autowired
    private CurrentPatchAssembler currentPatchAssembler;

    @Override
    public List<ServiceInstancePatchInfo> organizeServiceInstancePatchInfo(PatchTaskDto patchTaskDto) {
        try {
            String clusterId = patchTaskDto.getClusterId();
            List<ServiceInstancePatchInfo> context = patchTaskDto.getContext();
            if (CollectionUtils.isEmpty(context)) {
                return Lists.newArrayList();
            }
            List<String> instanceIds = context.stream().filter(p -> p.getServiceInstance() != null)
                .map(p -> p.getServiceInstance().getServiceInstanceId()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(instanceIds)) {
                log.info("queryTaskRollBackResource: instance ids is empty");
                return Lists.newArrayList();
            }
            log.info("start to organize ordinary patch info: cluster:{} instance: {}", clusterId, instanceIds);
            List<ServiceInstancePatchInfo> serviceInstancePatchInfos =
                patchUpdateInnerControllerApi.queryRollbackPoints(clusterId, instanceIds);

            currentPatchAssembler.organizeDisplayPatchPoints(serviceInstancePatchInfos);

            log.info("finished to organize ordinary patch info: cluster:{} instance: {}", clusterId, instanceIds);
            return CollectionUtils.isEmpty(serviceInstancePatchInfos) ? context : serviceInstancePatchInfos;
        } catch (DaipBaseException e) {
            log.error("query patch task rollback resource error:", e);
            return Lists.newArrayList();
        }
    }
}