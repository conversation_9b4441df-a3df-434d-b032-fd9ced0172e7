package com.zte.daip.manager.patcher.domain.upload.operate;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RepositoryVersionPatchOperator implements PatchTypeOperator {

    @Autowired
    private PatchInfoService patchInfoService;

    @Override
    public String generateFullKey(PatchBean patchBean) {
        return patchBean.getService() + ":" + patchBean.getSrcVersion() + ":" + Constants.REPOSITORY_VERSION_PATCH;
    }

    @Override
    public void deleteByFullPatch(List<PatchDetailPo> patchDetailPos, String patchName) {
        List<String> needDeletePatches = patchDetailPos.stream()
            .filter(patchDetailPo -> patchDetailPo.getPatchName().contains(Constants.REPOSITORY_VERSION_PATCH)
                && patchName.compareToIgnoreCase(patchDetailPo.getPatchName()) > 0)
            .map(PatchDetailPo::getPatchName).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needDeletePatches)) {
//            patchInfoService.deleteByPatchNames(needDeletePatches);
        }
    }

    @Override
    public List<PatchDetailPo> filterHistoryFullPatch(List<PatchDetailPo> fullPatchList) {
        return fullPatchList.stream()
            .filter(patchDetailPo -> patchDetailPo.getPatchName().contains(Constants.REPOSITORY_VERSION_PATCH))
            .collect(Collectors.toList());
    }

    @Override
    public String patchType() {
        return Constants.REPOSITORY_VERSION_PATCH;
    }
}
