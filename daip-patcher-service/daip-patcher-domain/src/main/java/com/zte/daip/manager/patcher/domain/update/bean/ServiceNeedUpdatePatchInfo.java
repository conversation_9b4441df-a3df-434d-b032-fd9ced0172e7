package com.zte.daip.manager.patcher.domain.update.bean;

import java.util.List;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceNeedUpdatePatchInfo {
    private String serviceInstanceId;
    private List<ServiceInstancePatchInfo> patchInfoList = Lists.newArrayList();
    private List<String> needUpdatePatchList = Lists.newArrayList();

}
