package com.zte.daip.manager.patcher.domain.upload.pool;

import java.util.concurrent.Executor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@EnableAsync
@Slf4j
public class PatchSetValidateExecutor {
    @Value("${dapmanager.patcher.check.patchset.pool.corePoolSize:0}")
    private int corePoolSize;

    @Value("${dapmanager.patcher.check.patchset.pool.maxPoolSize:10}")
    private int maxPoolSize;

    @Value("${dapmanager.patcher.check.patchset.pool.keepAliveSeconds:600}")
    private int keepAliveSeconds;

    @Value("${dapmanager.patcher.check.patchset.pool.queueCapacity:3}")
    private int queueCapacity;

    private ThreadPoolTaskExecutor patcherExecutor;

    public boolean checkThread() {
        return patcherExecutor.getActiveCount() < patcherExecutor.getMaxPoolSize();
    }

    @Bean
    public Executor patchSetValidatePool() {
        patcherExecutor = new GenerateValidatePool().generate(corePoolSize, maxPoolSize, queueCapacity,
            keepAliveSeconds, "PatchSetValidatePool-");
        return patcherExecutor;
    }

}
