/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchLoaclUploadDomService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/20
 * </p>
 * <p>
 * 完成日期：2021/3/20
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.upload.service;

import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.miniagent.seed.bean.SeedUploadParam;
import com.zte.daip.manager.miniagent.seed.controller.LocalUploadController;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchLocalUploadService {

    @Autowired
    private PatchEnvApi patchEnvApi;

    @Autowired
    private LocalUploadController localUploadController;

    private static final String PATCH_PROJECT = "patch";

    public boolean uploadToLocalRepository(List<PatchBean> needPublishPatch) {
        log.info("Start upload valid patch to local repository. ");

        String patchSrcParDir = patchEnvApi.getRepositoryHomeEnv() + "/upload/";

        List<SeedUploadParam> seedUploadParams = needPublishPatch.stream().map(patchBean -> {
            String patchSrcDir = patchSrcParDir + patchBean.getPatchName() + ".zip";
            return new SeedUploadParam(PATCH_PROJECT, patchBean.getService(), patchBean.getPatchName(), patchSrcDir,
                "false");
        }).collect(Collectors.toList());
        try {
            localUploadController.uploadVersionListToLocalRepository(seedUploadParams);
            deleteSrcFile(needPublishPatch);
            return true;
        } catch (Exception e) {
            log.info("Fail upload valid patch to local repository ", e);
            return false;
        }

    }

    private void deleteSrcFile(List<PatchBean> patchBeans) {
        String patchSrcParDir = patchEnvApi.getRepositoryHomeEnv() + "/upload/";
        for (PatchBean patchBean : patchBeans) {
            File patchFile = FilePathCleaner.newFile(patchSrcParDir, patchBean.getPatchName() + ".zip");
            if (patchFile != null && patchFile.exists()) {
                try {
                    FileUtils.delete(patchFile);
                } catch (IOException e) {
                    log.error("delete file error.", e);
                }
            }
        }
    }

}