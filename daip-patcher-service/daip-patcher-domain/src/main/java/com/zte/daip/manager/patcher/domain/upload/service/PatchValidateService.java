package com.zte.daip.manager.patcher.domain.upload.service;

import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.common.utils.xml.JaxbUtil;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatedResultCache;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchValidateBean;
import com.zte.daip.manager.patcher.domain.upload.service.validator.LoadPatchValidators;
import com.zte.daip.manager.patcher.domain.upload.service.validator.PatchValidator;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import com.zte.daip.manager.patcher.domain.upload.utils.PatcherFileUtils;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.UnzipFileApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

@Service
@Slf4j
public class PatchValidateService {

    @Autowired
    private LoadPatchValidators loadPatchValidators;
    @Autowired
    private UnzipFileApi unzipFileApi;
    @Autowired
    private PatchEnvApi patchEnvApi;
    @Autowired
    private PatchValidatedResultCache patchValidatedResultCache;

    @Async("patchFileValidatePool")
    public void validatePatch(PatchValidateBean patchValidateBean, List<PatchDetailPo> patchDetailPos) {
        log.info("validatePatch: {}", patchValidateBean.getPatchFile().getName());
        List<PatchValidator> patchValidators = loadPatchValidators.loadValidators();
        File patchFile = patchValidateBean.getPatchFile();
        String fileName = patchFile.getName().substring(0, patchFile.getName().indexOf(".zip"));
        PatchBean patchBean = getPatchBean(patchFile, fileName);
        PatchUploadResult patchUploadResult = new PatchUploadResult();
        /* Started by AICoder, pid:9c41783c02644c71ac65802f090bf8f2 */
        for (PatchValidator validator : patchValidators) {
            if (validator != null) { // 添加对 patchBean 的检查
                patchUploadResult = validator.checkPatch(patchBean, patchFile, patchDetailPos);
                if (!patchUploadResult.isSuccess()) {
                    log.info("Delete File :{} {}", fileName, patchFile.delete());
                    patchValidatedResultCache.updateNormalValidateResult(patchValidateBean, patchUploadResult);
                    return;
                }
            }
        }
        /* Ended by AICoder, pid:9c41783c02644c71ac65802f090bf8f2 */
        patchUploadResult = new PatchUploadResult(true, "upload patch success", fileName, "", false,
            OnePatchProcess.NORMAL_CHECK, patchBean);
        patchUploadResult.setOriginSetName(patchValidateBean.getOriginName());
        patchValidatedResultCache.updateNormalValidateResult(patchValidateBean, patchUploadResult);
    }

    private PatchBean getPatchBean(File file, String fileName) {
        String patchTempDir = patchEnvApi.getRepositoryHomeEnv() + "/upload/" + fileName;
        File patchPath = FilePathCleaner.newFile(patchTempDir);
        final String updateXmlFileName = "patch-update-config.xml";
        try {
            FileUtils.deleteDirectory(patchPath);
            unzipFileApi.unzipFile(file, updateXmlFileName, patchTempDir);
            String xmlPath = patchTempDir + File.separator + updateXmlFileName;
            File xmlFile = new File(xmlPath);
            if (xmlFile.exists() && xmlFile.isFile()) {
                PatchBean patchBean = JaxbUtil.convertToJavaBean(xmlPath, PatchBean.class);
                patchBean.setPatchSize(file.length());
                PatcherFileUtils.deletePatch(patchPath);
                return patchBean;
            }
        } catch (Exception e) {
            log.error("convert to patch bean fail", e);
        }
        PatcherFileUtils.deletePatch(patchPath);
        return null;
    }
}
