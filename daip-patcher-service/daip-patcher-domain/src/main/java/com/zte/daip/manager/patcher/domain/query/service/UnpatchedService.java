/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UnUpdatePatchService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/8
 * </p>
 * <p>
 * 完成日期：2021/4/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceVersionResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.version.ServiceVersionModel;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.utils.pool.DaipThreadPoolExecutor;
import com.zte.daip.manager.patcher.api.dto.*;
import com.zte.daip.manager.patcher.domain.common.PatchDispatchService;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchDetailDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchHistoryDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.AbstractPatchSourceGenerator;
import com.zte.daip.manager.patcher.domain.query.valobj.UnpatchedParam;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.glassfish.jersey.internal.guava.Sets;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class UnpatchedService {
    @Autowired
    private PatchInfoService patchInfoService;

    @Autowired
    private PatchHistoryService patchHistoryService;

    @Autowired
    private PatchDispatchService patchDispatchService;

    @Autowired
    private HostResourceControllerApi hostResourceControllerApi;

    @Autowired
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @Autowired
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;

    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Autowired
    private ServiceVersionResourceControllerApi serviceVersionResourceControllerApi;

    @Autowired
    private PatchDetailAsyncQueryService patchDetailAsyncQueryService;

    @Autowired
    private PatchDetailDtoAssembler patchDetailDtoAssembler;

    @Autowired
    private PatchHistoryDtoAssembler patchHistoryDtoAssembler;

    @Autowired
    private ApplicationContext applicationContext;

    @Value("${daip.patcher.query.timeout:180}")
    private long timeout;

    public ClustersAndServicesBean queryUnpatchedClusterAndServicesInfo() {

        final List<PatchDetailPo> patchDetailPos = patchInfoService.queryAllPatchExceptScheme();

        UnpatchedParam unpatchedParam = getUnpatchedParam();

        Map<String, Set<String>> clusterServiceLstMap = Maps.newConcurrentMap();

        CountDownLatch countDownLatch = new CountDownLatch(patchDetailPos.size());

        List<PatchDetailDto> patchDetailDtos = patchDetailPos.stream().filter(Objects::nonNull)
            .map(patchDetailDtoAssembler::patchDetail2Dto).collect(Collectors.toList());

        for (PatchDetailDto patchDetailDto : patchDetailDtos) {
            patchDetailAsyncQueryService.calcOnePatchClustersAndServices(clusterServiceLstMap, unpatchedParam,
                patchDetailDto, countDownLatch);
        }

        waitAllThreadFinish(countDownLatch);

        return new ClustersAndServicesBean(clusterServiceLstMap);
    }

    public ClustersAndServiceInstanceBean queryUnpatchedClusterAndServiceInstance() {

        final List<PatchDetailPo> patchDetailPos = patchInfoService.queryAllPatchExceptScheme();

        UnpatchedParam unpatchedParam = getUnpatchedParam();

        Map<String, Set<ServiceInstanceInfo>> clusterServiceInstanceMap = Maps.newConcurrentMap();

        CountDownLatch countDownLatch = new CountDownLatch(patchDetailPos.size());

        List<PatchDetailDto> patchDetailDtos = patchDetailPos.stream().filter(Objects::nonNull)
            .map(patchDetailDtoAssembler::patchDetail2Dto).collect(Collectors.toList());

        for (PatchDetailDto patchDetailDto : patchDetailDtos) {
            patchDetailAsyncQueryService.calcOnePatchUnpatchedServiceInstanceInfo(clusterServiceInstanceMap,
                unpatchedParam, patchDetailDto, countDownLatch);
        }

        waitAllThreadFinish(countDownLatch);

        return new ClustersAndServiceInstanceBean(clusterServiceInstanceMap);
    }

    public List<PatchHostDto> queryUnpatchedPatchesAndHost(ClusterAndServiceHostBean clusterAndServiceHostBean,
        boolean isFilterHost) {

        if (clusterAndServiceHostBean == null
            || clusterAndServiceHostBean.getClusterServiceHostMap() == null || clusterAndServiceHostBean.getClusterServiceHostMap().isEmpty()) {
            log.error("clusterAndServiceHostBean param is empty.");
            return Lists.newArrayList();
        }
        final List<PatchDetailPo> patchDetailPos = patchInfoService.queryAllPatchExceptScheme();

        final Map<String, Set<PatchServiceHostDto>> clusterServiceHostMap =
            clusterAndServiceHostBean.getClusterServiceHostMap();

        List<String> clusterIds = new ArrayList<>(clusterServiceHostMap.keySet());
        String clusterId = !CollectionUtils.isEmpty(clusterIds) ? clusterIds.get(0) : "";

        Set<PatchServiceHostDto> patchServiceHostDtos = clusterServiceHostMap.get(clusterId);

        Map<String, Set<PatchHistoryDto>> service2Hosts = patchServiceHostDtos.stream().collect(
            Collectors.toMap(PatchServiceHostDto::getService, PatchServiceHostDto::getUnpatchedHosts, (o1, o2) -> o2));

        UnpatchedParam unpatchedParam = getUnpatchedParam();
        unpatchedParam.setFilterHost(isFilterHost);

        List<Future<List<PatchHostDto>>> futureList = new ArrayList<>();

        List<PatchHostDto> patchHostDtoList = Lists.newArrayList();

        List<PatchDetailDto> patchDetailDtos = patchDetailPos.stream().filter(Objects::nonNull)
            .map(patchDetailDtoAssembler::patchDetail2Dto).collect(Collectors.toList());

        for (PatchDetailDto patchDetailDto : patchDetailDtos) {

            final Future<List<PatchHostDto>> future = patchDetailAsyncQueryService
                .calcOnePatchPatchAndHost(unpatchedParam, patchDetailDto, service2Hosts, clusterId);
            futureList.add(future);
        }

        final List<List<PatchHostDto>> lists = DaipThreadPoolExecutor.waitThreadsEnd(futureList);

        lists.stream().filter(p -> !CollectionUtils.isEmpty(p)).forEach(patchHostDtoList::addAll);

        return patchHostDtoList;
    }

    public List<PatchServiceHostDto>
        queryUnpatchedPatchesAndServiceAndHost(ClustersAndServicesBean clustersAndServicesBean) {
        if (clustersAndServicesBean == null || clustersAndServicesBean.getClusterServicesMap() == null || clustersAndServicesBean.getClusterServicesMap().isEmpty()) {
            log.error("clustersAndServicesBean param is empty.");
            return Lists.newArrayList();
        }
        Map<String, Set<String>> clusterServicesMap = clustersAndServicesBean.getClusterServicesMap();

        Map<String, Set<PatchServiceHostDto>> clusterServiceHostMap = clusterServicesMap.entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> serviceSet2PatchServiceHostDto(entry.getValue())));

        ClusterAndServiceHostBean clusterAndServiceHostBean = new ClusterAndServiceHostBean(clusterServiceHostMap);

        List<PatchHostDto> patchHostDtos = queryUnpatchedPatchesAndHost(clusterAndServiceHostBean, false);

        Map<String, Set<PatchHistoryDto>> patchServiceHostsDtos = Maps.newHashMap();
        for (PatchHostDto patchHostDto : patchHostDtos) {
            PatchDetailDto patch = patchHostDto.getPatch();
            if (patch != null) {
                String service = StringUtils.isBlank(patch.getServiceInstanceId()) ? patch.getService()
                    : patch.getServiceInstanceId();
                patchServiceHostsDtos.computeIfAbsent(service, k -> Sets.newHashSet()).addAll(patchHostDto.getHosts());
            }
        }
        return patchServiceHostsDtos.entrySet().stream().map(e -> new PatchServiceHostDto(e.getKey(), e.getValue()))
            .collect(Collectors.toList());
    }

    protected UnpatchedParam getUnpatchedParam() {
        final List<PatchHistory> patchHistories = patchHistoryService.queryAllPatchHistoryInfo();

        final List<ServiceModel> serviceModels = productModelInfoControllerApi.queryAllServiceModels();

        final List<HostInfo> hostInfos = hostResourceControllerApi.queryAll();

        final Map<String, String> hostIp2HostName =
            hostInfos.stream().collect(Collectors.toMap(HostInfo::getIpAddress, HostInfo::getHostName));

        final List<ServiceRoleInfo> serviceRoleInfos = serviceResourceControllerApi.queryAll();

        final List<DeploymentServiceInstance> deploymentServiceInstances =
            deploymentInstanceServiceControllerApi.queryAll();

        final Map<String,
            String> instanceId2ServiceId = deploymentServiceInstances.stream()
                .collect(Collectors.toMap(DeploymentServiceInstance::getServiceInstanceId,
                    DeploymentServiceInstance::getServiceId, (key1, key2) -> key2));

        Map<String, List<String>> serviceVersion2Ip = queryServiceVersions(serviceModels, instanceId2ServiceId);

        clearAllCache();

        return new UnpatchedParam(generatePatchHistoryInfoMap(patchHistories), serviceModels, hostIp2HostName,
            serviceRoleInfos, serviceVersion2Ip, false);
    }

    private void clearAllCache() {
        for (PatchTypeEnum patchType : PatchTypeEnum.values()) {
            try {
                AbstractPatchSourceGenerator abstractPatchSourceGenerator =
                    (AbstractPatchSourceGenerator)applicationContext.getBean(patchType.getServiceName());
                abstractPatchSourceGenerator.clearCache();
            } catch (NoSuchBeanDefinitionException e) {
                log.error("NoSuchBeanDefinitionException by patchType ", e);
            }
        }
    }

    protected Map<PatchKeyDo, List<PatchHistory>> generatePatchHistoryInfoMap(List<PatchHistory> patchHistories) {
        if (CollectionUtils.isEmpty(patchHistories)) {
            return Maps.newHashMap();
        }
        return patchHistories.stream().collect(Collectors.groupingBy(patchHistoryDtoAssembler::patchHistory2Do));
    }

    private void waitAllThreadFinish(CountDownLatch countDownLatch) {
        try {
            log.info("countDownLatch timeout is {}", timeout);
            final boolean await = countDownLatch.await(timeout, TimeUnit.SECONDS);
            if (!await) {
                log.warn("search countDownLatch wait error!");
            }
        } catch (InterruptedException e) {
            log.info("countDownLatch wait error!", e);
            Thread.currentThread().interrupt();
        }
    }

    private Map<String, List<String>> queryServiceVersions(List<ServiceModel> serviceModels,
        Map<String, String> instanceId2ServiceId) {

        Map<String, List<String>> serviceHostsVersion = Maps.newHashMap();

        final List<ServiceVersionModel> serviceVersionModels =
            serviceVersionResourceControllerApi.queryAllServiceVersions();

        for (ServiceVersionModel curServiceVersion : serviceVersionModels) {

            final String serviceInstanceId = curServiceVersion.getId().getServiceInstanceId();

            if (StringUtils.equals(serviceInstanceId, Constants.HOST_SERVICE_ID)
                || StringUtils.equals(serviceInstanceId, Constants.BIG_DATA_SERVICE_ID)) {

                String key = String.format("%s_%s", StringUtils.equals(serviceInstanceId, Constants.BIG_DATA_SERVICE_ID)
                    ? Constants.ZDH_SERVICE : serviceInstanceId, curServiceVersion.getVersion());
                serviceHostsVersion.computeIfAbsent(key, k -> Lists.newArrayList())
                    .add(curServiceVersion.getId().getIp());
                continue;
            }
            String serviceId = instanceId2ServiceId.getOrDefault(serviceInstanceId, serviceInstanceId);
            ServiceModel serviceModel = queryServiceModelByServiceId(serviceModels, serviceId);
            if (null != serviceModel && StringUtils.equals(serviceModel.getServiceId(), serviceId)) {
                String roleName = StringUtils.isEmpty(curServiceVersion.getId().getRoleName()) ? ""
                    : curServiceVersion.getId().getRoleName();
                String key = serviceModel.getServiceName() + roleName + "_" + curServiceVersion.getVersion();

                serviceHostsVersion.computeIfAbsent(key, k -> Lists.newArrayList())
                    .add(curServiceVersion.getId().getIp());
            }
        }
        return serviceHostsVersion;
    }

    private ServiceModel queryServiceModelByServiceId(List<ServiceModel> serviceModels, String serviceId) {

        return serviceModels.stream().filter(model -> StringUtils.equals(serviceId, model.getServiceId())).findFirst()
            .orElse(new ServiceModel());
    }

    private Set<PatchServiceHostDto> serviceSet2PatchServiceHostDto(Set<String> serviceList) {

        return Optional.ofNullable(serviceList).orElse(Sets.newHashSet()).stream()
            .map(e -> new PatchServiceHostDto(e, Sets.newHashSet())).collect(Collectors.toSet());
    }
}