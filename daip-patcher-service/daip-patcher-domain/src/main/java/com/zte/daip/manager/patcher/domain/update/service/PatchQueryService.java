/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchContainerQueryService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/5/17
 * </p>
 * <p>
 * 完成日期：2021/5/17
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.AbstractPatchSourceGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Maps;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class PatchQueryService {
    @Autowired
    private PatchInfoService patchInfoService;
    @Autowired
    private PatchHistoryService patchHistoryService;
    @Autowired
    private PatchTypeQueryService patchTypeQueryService;
    @Autowired
    private ServiceModelInfoCache serviceModelInfoCache;
    @Autowired
    private ApplicationContext applicationContext;

    public Map<String, List<String>> queryUpdatedContainerPatches(String serviceName, String version) {
        List<PatchDetailPo> servicePatches = patchInfoService.findByServiceAndBaseVersion(serviceName, version);
        List<PatchDetailPo> containerPatches =
            servicePatches.stream().filter(patch -> patch.getIsContainerPatch() == 1).collect(Collectors.toList());
        Set<String> containerPatchNames =
            containerPatches.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(containerPatchNames)) {
            return Maps.newHashMap();
        }
        //
        List<PatchHistory> patchHistories = patchHistoryService.queryPatchHistoryInfoByServiceName(serviceName);
        List<PatchHistory> updatedContainerPatches = patchHistories.stream()
            .filter(patchHistory -> containerPatchNames.contains(patchHistory.getId().getPatchName()))
            .collect(Collectors.toList());
        Map<String, List<PatchHistory>> ip2UpdatedContainerHistory = updatedContainerPatches.stream()
            .collect(Collectors.groupingBy(patchHistory -> patchHistory.getId().getIp()));
        if (CollectionUtils.isEmpty(ip2UpdatedContainerHistory)) {
            return Maps.newHashMap();
        }
        //
        Map<String, List<String>> ip2UpdatedPatchNames = Maps.newHashMap();
        for (Map.Entry<String, List<PatchHistory>> entry : ip2UpdatedContainerHistory.entrySet()) {
            List<String> collect = entry.getValue().stream().map(patchHistory -> patchHistory.getId().getPatchName())
                .collect(Collectors.toList());
            ip2UpdatedPatchNames.put(entry.getKey(), collect);
        }
        return ip2UpdatedPatchNames;
    }

    public List<RollAutoPatchUpdateInfo> queryUpdatePatchInfo(RollAutoPatchUpdateParam rollAutoPatchUpdateParam)
        throws DaipBaseException {
        ServiceModel serviceModel = serviceModelInfoCache.queryByClusterIdAndServiceName(
            rollAutoPatchUpdateParam.getClusterId(), rollAutoPatchUpdateParam.getServiceName());
        if (serviceModel != null) {
            return generatePatchUpdateInfos(rollAutoPatchUpdateParam, serviceModel.getVersion());
        }
        return Lists.newArrayList();
    }

    private List<RollAutoPatchUpdateInfo> generatePatchUpdateInfos(RollAutoPatchUpdateParam updateParam, String version)
        throws DaipBaseException {
        Map<String, List<String>> ip2UpdatedPatchNames =
            queryUpdatedContainerPatches(updateParam.getServiceName(), version);
        List<String> updatedContainerPatches =
            ip2UpdatedPatchNames.computeIfAbsent(updateParam.getHostName(), k -> Lists.newArrayList());
        String patchType =
            patchTypeQueryService.queryPatchTypeByServiceName(updateParam.getClusterId(), updateParam.getServiceName());
        PatchTypeEnum patchTypeEnum = PatchTypeEnum.queryPatchType(patchType);
        AbstractPatchSourceGenerator abstractPatchSourceGenerator =
            (AbstractPatchSourceGenerator)applicationContext.getBean(patchTypeEnum.getServiceName());
        return abstractPatchSourceGenerator.obtainPatchUpdateInfos(updateParam, version, updatedContainerPatches);
    }
}