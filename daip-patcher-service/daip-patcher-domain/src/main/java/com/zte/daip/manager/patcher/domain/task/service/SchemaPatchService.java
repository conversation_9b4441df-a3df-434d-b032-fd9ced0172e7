package com.zte.daip.manager.patcher.domain.task.service;

import java.util.List;

import com.zte.daip.manager.patcher.domain.task.assembler.CurrentPatchAssembler;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.ums.zenap.httpclient.retrofit.common.Constant;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.domain.task.api.ServiceInstancePatchResource;
import com.zte.daip.manager.patcher.inner.api.dto.*;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service("schema")
@Slf4j
public class SchemaPatchService implements ServiceInstancePatchResource {

    @Autowired
    private CurrentPatchAssembler currentPatchAssembler;

    @Override
    public List<ServiceInstancePatchInfo> organizeServiceInstancePatchInfo(PatchTaskDto patchTaskDto) {
        if (patchTaskDto == null) {
            log.info("organize schema patch info: patch task dto is empty");
            return Lists.newArrayList();
        }
        List<ServiceInstancePatchInfo> tmpServiceInstancePatchs = Lists.newArrayList(patchTaskDto.getContext());
        if (CollectionUtils.isEmpty(tmpServiceInstancePatchs)) {
            log.info("organize schema patch info: context is empty");
            return Lists.newArrayList();
        }
        return generateSchemaServiceInstances(tmpServiceInstancePatchs);
    }

    private List<ServiceInstancePatchInfo>
        generateSchemaServiceInstances(List<ServiceInstancePatchInfo> serviceInstancePatchs) {
        List<ServiceInstancePatchInfo> serviceInstancePatchInfos = Lists.newArrayList();
        for (ServiceInstancePatchInfo serviceInstancePatchInfo : serviceInstancePatchs) {
            if (serviceInstancePatchInfo == null) {
                log.info("organize schema patch info: service instance patch info is empty");
                return Lists.newArrayList();
            }
            List<RollBackPatchPointInfo> rollBackPatchPoints = serviceInstancePatchInfo.getRollBackPatchPoints();
            if (CollectionUtils.isEmpty(rollBackPatchPoints)) {
                log.info("organize schema patch info: roll back patch points is empty");
                return Lists.newArrayList();
            }
            ServiceInstance serviceInstance = serviceInstancePatchInfo.getServiceInstance();
            if (serviceInstance == null) {
                log.info("organize schema patch info: service instances is empty");
                return Lists.newArrayList();
            }
            serviceInstancePatchInfo.setPatchType(Constants.SCHEMA_PATCH);
            serviceInstancePatchInfo.setServiceInstance(assembleInstanceId(serviceInstance));
            serviceInstancePatchInfos.add(serviceInstancePatchInfo);
        }
        currentPatchAssembler.organizeDisplayPatchPoints(serviceInstancePatchInfos);
        return serviceInstancePatchInfos;
    }

    private ServiceInstance assembleInstanceId(ServiceInstance serviceInstance) {
        if (serviceInstance.getServiceInstanceId() == null
            || StringUtils.equals(serviceInstance.getServiceInstanceId(), "")) {
            serviceInstance.setServiceInstanceId(serviceInstance.getServiceName());
        }
        if (serviceInstance.getServiceInstanceName() == null
            || StringUtils.equals(serviceInstance.getServiceInstanceName(), "")) {
            serviceInstance.setServiceInstanceName(serviceInstance.getServiceName());
        }
        return serviceInstance;
    }
}