package com.zte.daip.manager.patcher.domain.update.schedule;

import java.util.List;

import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.domain.update.cache.PatchRollbackCacheQueue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateResultService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RollbackPatchScheduler {

    @Autowired
    private PatchRollbackCacheQueue patchRollbackCacheQueue;
    @Autowired
    private PatchUpdateResultService patchUpdateResultService;

    @Scheduled(cron = "0/6 * * * * ?")
    public void cron() {
        List<PatchServiceParam> serviceParamArrayList = Lists.newArrayList();
        while (!patchRollbackCacheQueue.isEmptyResultCache()) {
            serviceParamArrayList.addAll(patchRollbackCacheQueue.poll());
        }
        patchUpdateResultService.deleteRollbackServicePatchHistory(serviceParamArrayList);
    }

}
