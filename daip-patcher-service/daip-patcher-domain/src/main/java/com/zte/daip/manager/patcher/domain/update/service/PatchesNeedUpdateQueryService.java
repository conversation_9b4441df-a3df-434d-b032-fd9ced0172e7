/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchesNeedUpdateQueryService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/6
 * </p>
 * <p>
 * 完成日期：2021/4/6
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@Service
public class PatchesNeedUpdateQueryService {

    @Autowired
    private PatchInfoService patchInfoService;
    @Autowired
    private PatchHistoryService patchHistoryService;

    public Map<String, List<SimplePatchInfo>> queryNeedUpdateRolePatches(String serviceName, String version,
        Set<String> needUpdateIps, String roleName) {
        List<PatchDetailPo> rolePatches = queryPatchesInfoByRoleName(serviceName, version, roleName);
        if (CollectionUtils.isEmpty(rolePatches)) {
            log.warn("No need to update role patch: none simple or container patches");
            return Maps.newHashMap();
        }
        return buildNeedUpdateRolePatches(roleName, needUpdateIps, rolePatches);
    }

    private Map<String, List<SimplePatchInfo>> buildNeedUpdateRolePatches(String roleName, Set<String> needUpdateIps,
        List<PatchDetailPo> patches) {
        Set<String> patchNames = patches.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toSet());
        List<PatchHistory> patchUpdated = queryPatchHistoryByRoleAndPatchName(roleName, patchNames);
        Map<String, Set<String>> needUpdatePatches =
            buildIp2NeedUpdatePatchedInfo(needUpdateIps, patchNames, patchUpdated);
        return convert2SimplePatchInfo(patches, needUpdatePatches);
    }

    private List<PatchHistory> queryPatchHistoryByRoleAndPatchName(String roleName,
        Set<String> patchNames)
    {
        return queryPatchHistoryByPatchNames(patchNames).stream()
            .filter(e -> StringUtils.equalsIgnoreCase(roleName, e.getId().getRoleName())).collect(
                Collectors.toList());
    }

    public Map<String, List<SimplePatchInfo>> queryNeedUpdatePatchesByInstance(String serviceName, String version,
        String serviceInstanceId, Set<String> needUpdateIps) {
        List<PatchDetailPo> patchDetailPos = queryPatchDetailsByServiceAndVersion(serviceName, version);
        if (CollectionUtils.isEmpty(patchDetailPos)) {
            log.warn("No need to update patch: none simple or container patches");
            return Maps.newHashMap();
        }
        return buildNeedUpdateInstancePatches(serviceInstanceId, needUpdateIps, patchDetailPos);
    }

    private Map<String, List<SimplePatchInfo>> buildNeedUpdateInstancePatches(String serviceInstanceId,
        Set<String> needUpdateIps, List<PatchDetailPo> patchDetailPos) {
        Set<String> patchNames = patchDetailPos.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toSet());
        List<PatchHistory> patchUpdated = queryPatchHistoryByPatchNames(patchNames).stream()
            .filter(e -> StringUtils.equalsIgnoreCase(serviceInstanceId, e.getId().getServiceInstanceId()))
            .collect(Collectors.toList());
        Map<String, Set<String>> needUpdatePatches =
            buildIp2NeedUpdatePatchedInfo(needUpdateIps, patchNames, patchUpdated);
        return convert2SimplePatchInfo(patchDetailPos, needUpdatePatches);
    }

    private Map<String, Set<String>> buildIp2NeedUpdatePatchedInfo(Set<String> needUpdateIps,
        Set<String> needUpdatePatchNames, List<PatchHistory> patchUpdatedHistory) {
        Map<String, List<PatchHistory>> ip2patchHistory =
            patchUpdatedHistory.stream().collect(Collectors.groupingBy(patch -> patch.getId().getIp()));
        Set<String> updatedIps = ip2patchHistory.keySet();
        //
        log.debug("history patch:" + JSON.toJSONString(ip2patchHistory));
        Map<String, Set<String>> needUpdatePatches = Maps.newHashMap();
        for (String ip : needUpdateIps) {
            if (updatedIps.contains(ip)) {
                List<PatchHistory> patchesUpdated = ip2patchHistory.get(ip);
                Set<String> updatedPatchNames =
                    patchesUpdated.stream().map(patch -> patch.getId().getPatchName()).collect(Collectors.toSet());
                Set<String> allLeftPatches = Sets.newHashSet(needUpdatePatchNames);
                // 差集
                allLeftPatches.removeAll(updatedPatchNames);
                needUpdatePatches.put(ip, allLeftPatches);
            } else {
                needUpdatePatches.put(ip, needUpdatePatchNames);
            }
        }
        log.debug("fina:" + JSON.toJSONString(needUpdatePatches));
        return needUpdatePatches;
    }

    public List<PatchDetailPo> queryPatchesInfoByRoleName(String serviceName, String version, String roleName) {
        List<PatchDetailPo> allPatchesOfService = queryPatchDetailsByServiceAndVersion(serviceName, version);
        if (CollectionUtils.isEmpty(allPatchesOfService)) {
            log.warn("No need to update role patch: none simple or container patches");
            return Lists.newArrayList();
        }
        List<PatchDetailPo> rolePatches = allPatchesOfService.stream().filter(p -> {
            String[] roles = StringUtils.split(p.getRoles(), ",");
            return Arrays.stream(roles).anyMatch(r -> StringUtils.equals(r, roleName));
        }).collect(Collectors.toList());
        return rolePatches;
    }

    public Map<String, List<SimplePatchInfo>> queryNeedUpdatePatchesByService(String serviceName, String version,
        Set<String> needUpdateIps) {
        List<PatchDetailPo> allPatchesOfService = queryPatchDetailsByServiceAndVersion(serviceName, version);
        if (CollectionUtils.isEmpty(allPatchesOfService)) {
            log.warn("No need to update patch: none simple or container patches");
            return Maps.newHashMap();
        }
        return genNeedUpdatePatches(needUpdateIps, null, allPatchesOfService);
    }

    private List<PatchDetailPo> queryPatchDetailsByServiceAndVersion(String serviceName, String version) {
        return patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(serviceName, version);
    }

    private Map<String, Set<String>> queryNeedPatches(Set<String> needUpdateIps, String serviceInstanceId,
        List<PatchDetailPo> allPatchesOfService) {
        Set<String> allPatchNames =
            allPatchesOfService.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toSet());
        Map<String, Set<String>> needUpdatePatches =
            queryNeedUpdatePatchGroupByIp(needUpdateIps, serviceInstanceId, allPatchNames);
        return needUpdatePatches;
    }

    private List<PatchHistory> queryPatchHistoryByPatchNames(Set<String> allPatchNames) {
        List<PatchHistory> patchHistories = patchHistoryService.queryAllPatchHistoryInfo();
        return patchHistories.stream().filter(patch -> allPatchNames.contains(patch.getId().getPatchName()))
            .collect(Collectors.toList());
    }

    private Map<String, Set<String>> queryNeedUpdatePatchGroupByIp(Set<String> needUpdateIps, String serviceInstanceId,
        Set<String> allPatchNames) {
        // 已经打过的补丁
        List<PatchHistory> patchHistories = patchHistoryService.queryAllPatchHistoryInfo();
        Stream<PatchHistory> patchHistoryStream =
            patchHistories.stream().filter(patch -> allPatchNames.contains(patch.getId().getPatchName()));

        if (!StringUtils.isEmpty(serviceInstanceId)) {
            patchHistoryStream
                .filter(patch -> StringUtils.equalsIgnoreCase(patch.getId().getServiceInstanceId(), serviceInstanceId));
        }
        List<PatchHistory> patchUpdated = patchHistoryStream.collect(Collectors.toList());
        return buildIp2NeedUpdatePatchedInfo(needUpdateIps, allPatchNames, patchUpdated);
    }

    private Map<String, List<SimplePatchInfo>> genNeedUpdatePatches(Set<String> needUpdateIps, String serviceInstanceId,
        List<PatchDetailPo> patchDetails) {
        Map<String, Set<String>> ip2NeedUpdatePatches =
            queryNeedPatches(needUpdateIps, serviceInstanceId, patchDetails);
        // convert
        Map<String, List<SimplePatchInfo>> finalPatches = convert2SimplePatchInfo(patchDetails, ip2NeedUpdatePatches);
        return finalPatches;
    }

    private Map<String, List<SimplePatchInfo>> convert2SimplePatchInfo(List<PatchDetailPo> patchDetails,
        Map<String, Set<String>> ip2NeedUpdatePatches) {
        Map<String, SimplePatchInfo> patchName2SimplePatches =
            patchDetails.stream().collect(Collectors.toMap(PatchDetailPo::getPatchName,
                o -> new SimplePatchInfo(o.getPatchName(), o.getIsContainerPatch() == 1)));
        Map<String, List<SimplePatchInfo>> finalPatches = Maps.newHashMap();
        ip2NeedUpdatePatches.forEach((ip, value) -> {
            List<SimplePatchInfo> simplePatches =
                value.stream().map(patchName2SimplePatches::get).collect(Collectors.toList());
            finalPatches.put(ip, simplePatches);
        });
        return finalPatches;
    }
}