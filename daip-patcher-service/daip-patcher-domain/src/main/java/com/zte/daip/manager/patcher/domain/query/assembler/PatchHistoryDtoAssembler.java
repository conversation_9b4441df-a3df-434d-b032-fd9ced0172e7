/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchHistroyDtoAssembler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/23
 * </p>
 * <p>
 * 完成日期：2021/3/23
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.assembler;

import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.stereotype.Service;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchHistoryDtoAssembler {

    public PatchKeyDo patchHistory2Do(PatchHistory patchHistory) {

        final PatchHistoryKey patchHistoryId = patchHistory.getId();

        return new PatchKeyDo(patchHistoryId.getPatchName(), patchHistoryId.getServiceName(), "");
    }

    public PatchHistoryDto patchDispatch2Dto(PatchHistory patchHistory) {

        final PatchHistoryKey patchHistoryId = patchHistory.getId();

        if(StringUtils.isNotBlank(patchHistoryId.getServiceName())
                && StringUtils.isNotBlank(patchHistoryId.getServiceInstanceId())
                && StringUtils.isBlank(patchHistoryId.getRoleName())){
            return new PatchHistoryDto(patchHistory.getPatchUptime(),
                    patchHistoryId.getIp() + "(" + patchHistoryId.getServiceInstanceId() + ")", "");
        } else if (StringUtils.isNotBlank(patchHistoryId.getServiceName())
                && StringUtils.isBlank(patchHistoryId.getServiceInstanceId())
                && StringUtils.isNotBlank(patchHistoryId.getRoleName())) {
            return new PatchHistoryDto(patchHistory.getPatchUptime(),
                    patchHistoryId.getIp() + "(" + patchHistoryId.getRoleName() + ")", "");
        }
        return new PatchHistoryDto(patchHistory.getPatchUptime(), patchHistoryId.getIp(), "");
    }
}