/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: LocalUtil.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/12/2
 * </p>
 * <p>
 * 完成日期：2021/12/2
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.executor.depend;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
public class DependResourceLocalUtil
{
    @Autowired
    private HostResourceControllerApi hostResourceControllerApi;
    @Autowired
    private ServiceResourceControllerApi serviceResourceControllerApi;

    private final InheritableThreadLocal<DependResource> localeThreadLocal = new InheritableThreadLocal<>();

    public void initDependResource(String clusterId)
    {
        List<HostInfo> hosts = hostResourceControllerApi.queryByClusterId(clusterId);
        List<ServiceRoleInfo> serviceRoles = serviceResourceControllerApi
            .queryByClusterId(clusterId);
        DependResource dependResource = new DependResource(hosts, serviceRoles);
        localeThreadLocal.set(dependResource);
    }

    public List<HostInfo> getClusterHostInfo(String clusterId)
    {
        DependResource dependResource = localeThreadLocal.get();
        return (Objects.isNull(dependResource)) ?
            hostResourceControllerApi.queryByClusterId(clusterId) :
            dependResource.getHostInfos();
    }

    public List<ServiceRoleInfo> getClusterServiceRoleInfo(String clusterId)
    {
        DependResource dependResource = localeThreadLocal.get();
        return (Objects.isNull(dependResource)) ?
            serviceResourceControllerApi.queryByClusterId(clusterId) :
            dependResource.getServiceRoles();
    }

    public void clear()
    {
        localeThreadLocal.remove();
    }

    public List<ServiceRoleInfo> queryServiceRoleInfoByServiceInstanceId(String clusterId, String serviceInstanceId) {
        List<ServiceRoleInfo> collect = getClusterServiceRoleInfo(clusterId).stream()
            .filter(e -> StringUtils.equals(e.getServiceInstanceId(), serviceInstanceId)).collect(
                Collectors.toList());

        return (CollectionUtils.isEmpty(collect))
            ? serviceResourceControllerApi.queryByClusterIdAndserviceInstanceId(clusterId, serviceInstanceId) : collect;
    }
}