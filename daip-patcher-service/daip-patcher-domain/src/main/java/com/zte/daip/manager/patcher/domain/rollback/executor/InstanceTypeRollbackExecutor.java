/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: InstanceTypeRollbackExecutor.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/24
 * </p>
 * <p>
 * 完成日期：2023/3/24
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.rollback.executor;

import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.domain.cache.ServiceModelInfoCache;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchHomeQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service("instanceRollback")
public class InstanceTypeRollbackExecutor implements PatchRollbackExecutor {
    @Autowired
    private PublishRollbackMsg publishRollbackMsg;
    @Autowired
    private ServiceModelInfoCache serviceModelInfoCache;

    @Autowired
    private PatchHomeQueryService patchHomeQueryService;
    @Autowired
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;

    private final static String PATCH_HOME = File.separator + "patch";

    @Override
    public void executeRollback(String clusterId, PatchRollbackHostDto rollbackHostDto) throws DaipBaseException {
        log.info("exec rollback patch:instanceType");
        ServiceModel serviceModel =
            serviceModelInfoCache.queryByClusterIdAndServiceName(clusterId, rollbackHostDto.getServiceName());
        if (serviceModel != null) {
            List<DeploymentServiceInstance> instances = deploymentInstanceServiceControllerApi
                .queryByClusterIdAndServiceId(clusterId, serviceModel.getServiceId());
            if (!CollectionUtils.isEmpty(instances)) {
                String patchHome = queryPatchHome(clusterId, instances.get(0).getServiceInstanceId());
                String rollbackShell = "sh " + patchHome + "/patchtool.sh recover" + " "
                    + rollbackHostDto.getPatchName() + " " + rollbackHostDto.getServiceInstanceId();
                publishRollbackMsg.publishRollbackMsg(clusterId, rollbackHostDto, patchHome, rollbackShell, false);
            }
        }
    }

    private String queryPatchHome(String clusterId, String serviceInstanceId) {
        List<ConfigInstance> configInstances = patchHomeQueryService.queryPatchHome(clusterId, serviceInstanceId);
        if (!CollectionUtils.isEmpty(configInstances)) {
            return configInstances.get(0).getConfigValue() + PATCH_HOME;
        }
        return "";
    }

}