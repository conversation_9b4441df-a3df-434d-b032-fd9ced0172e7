/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServicePatchUpdate.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/1
 * </p>
 * <p>
 * 完成日期：2021/4/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.executor;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.*;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.update.bean.ServiceNeedUpdatePatchInfo;
import com.zte.daip.manager.patcher.inner.api.dto.PatchHostInfoDto;
import com.zte.daip.manager.patcher.inner.api.dto.RollBackPatchPointInfo;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.zte.daip.manager.common.deployer.api.VersionSettingControllerApi;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.update.request.*;
import com.zte.daip.manager.patcher.domain.update.executor.api.MergeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.api.OrganizeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.depend.DependResourceLocalUtil;
import com.zte.daip.manager.patcher.domain.update.service.PatchTypeQueryService;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service("ZDH")
@Slf4j
public class ZdhTypePatchParamRequester extends AbstractPatchParamRequester implements OrganizeRequest, MergeRequest {
    private static final String ZDH_DEFAULT_PATCH_TYPE = "ZDH";
    private static final String ZDH_DEFAULT_BASE_SERVICE_NAME = "zdh";

    @Autowired
    private PatchTypeQueryService patchTypeQueryService;
    @Autowired
    private VersionSettingControllerApi versionSettingControllerApi;
    @Autowired
    private DependResourceLocalUtil dependResourceLocalUtil;

    @Override
    public List<PatchUpdateRequest> organizeRequest(String clusterId, OrganizeKey key,
        List<ServiceInstanceInfo> serviceInstances) {
        Set<String> requestIps =
            serviceInstances.stream().flatMap(s -> s.getIps().stream()).collect(Collectors.toSet());
        Set<String> targetIps = buildTargetIps(clusterId, key.getServiceId(), key.getServiceName(), requestIps);
        log.info("ZDH service: {}, ips: {}", key.getServiceName(), targetIps);

        List<ServiceInstance> instances = serviceInstances.stream()
            .map(e -> new ServiceInstance(e.getServiceInstanceId(), Lists.newArrayList())).collect(Collectors.toList());
        Map<String, List<SimplePatchInfo>> ip2NeedPatches = patchesNeedUpdateQueryService
            .queryNeedUpdatePatchesByService(key.getServiceName(), key.getVersion(), targetIps);
        log.debug(String.format("ZDH level service %s:finished to queryNeedUpdatePatches: %s", key.getServiceName(),
            JSON.toJSONString(ip2NeedPatches)));

        Map<String, List<ServicePatchInfo>> hostIp2Patches = Maps.newHashMap();
        for (String ip : targetIps) {
            ServicePatchInfo servicePatchInfo = new ServicePatchInfo(key.getServiceName(), key.getVersion());
            servicePatchInfo.setServiceInstances(instances);
            List<SimplePatchInfo> patchesOfTheHost = ip2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(patchesOfTheHost)) {
                servicePatchInfo.setPatches(patchesOfTheHost);
                servicePatchInfo.setUpdatedContainerPatches(key.getUpdatedContainerPatches().get(ip));
                hostIp2Patches.put(ip, Lists.newArrayList(servicePatchInfo));
            }
        }
        if (CollectionUtils.isEmpty(hostIp2Patches)) {
            return Lists.newArrayList();
        }
        return genPatchUpdateRequests(clusterId, hostIp2Patches);
    }

    private List<PatchUpdateRequest> genPatchUpdateRequests(String clusterId,
        Map<String, List<ServicePatchInfo>> hostName2Patches) {
        String patchHome = queryPatchHome(null, null);
        PatchUpdateRequest patchUpdateRequest = new PatchUpdateRequest(clusterId, patchHome, ZDH_DEFAULT_PATCH_TYPE);
        patchUpdateRequest.setIp2Patches(hostName2Patches);
        return Lists.newArrayList(patchUpdateRequest);
    }

    @Override
    protected String queryPatchHome(String clusterId, String serviceInstanceId) {
        return patchHomeQueryService.queryDefaultZdhPatchHome();
    }

    @Override
    public PatchUpdateRequest mergeRequest(PatchRequestKey key, List<PatchUpdateRequest> patchUpdateRequests) {
        log.debug("zdhType merge before:" + JSON.toJSONString(patchUpdateRequests));
        ArrayListMultimap<String, List<ServicePatchInfo>> ip2ServicePatches = ArrayListMultimap.create();
        //
        for (PatchUpdateRequest request : patchUpdateRequests) {
            Map<String, List<ServicePatchInfo>> hostName2Patches = request.getIp2Patches();
            hostName2Patches.forEach(ip2ServicePatches::put);
        }
        //
        Map<String, List<ServicePatchInfo>> hostName2Patches = Maps.newHashMap();
        Map<String, Collection<List<ServicePatchInfo>>> stringCollectionMap = ip2ServicePatches.asMap();
        stringCollectionMap.forEach((hostIp, values) -> {
            List<ServicePatchInfo> servicePatchInfos = Lists.newArrayList();
            values.forEach(servicePatchInfos::addAll);
            hostName2Patches.put(hostIp, servicePatchInfos);
        });
        //
        PatchUpdateRequest finalZdhRequest =
            new PatchUpdateRequest(key.getClusterId(), key.getPatchHome(), key.getPatchType());
        finalZdhRequest.setIp2Patches(hostName2Patches);
        log.debug("zdhType merge after:" + JSON.toJSONString(finalZdhRequest));

        return finalZdhRequest;
    }

    @Override
    public ServiceNeedUpdatePatchInfo queryRollbackPoints(ServiceRoleInfo serviceRoleInfo, OrganizeKey key) {
        List<ServiceRoleInfo> zdhServiceRoles =
            dependResourceLocalUtil.getClusterServiceRoleInfo(serviceRoleInfo.getClusterId());
        List<HostInfo> zdhHostInfos = dependResourceLocalUtil.getClusterHostInfo(serviceRoleInfo.getClusterId());
        if (CollectionUtils.isEmpty(zdhHostInfos) || CollectionUtils.isEmpty(zdhServiceRoles)) {
            log.warn("query rollback points ZDH service: depend resource local cache info is empty.");
            return null;
        }
        Set<String> zdhRequestIps;
        if (StringUtils.equalsIgnoreCase(key.getServiceName(), ZDH_DEFAULT_BASE_SERVICE_NAME)) {
            Set<String> bigDataServices = patchTypeQueryService.queryBigDataServiceIds(serviceRoleInfo.getClusterId());
            zdhRequestIps = zdhServiceRoles.stream().filter(e -> bigDataServices.contains(e.getServiceId()))
                .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        } else {
            zdhRequestIps = zdhServiceRoles.stream()
                .filter(s -> StringUtils.equals(s.getServiceId(), serviceRoleInfo.getServiceId())
                    && StringUtils.equals(s.getServiceInstanceId(), serviceRoleInfo.getServiceInstanceId()))
                .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        }
        Set<String> zdhTargetIps = buildTargetIps(serviceRoleInfo.getClusterId(), serviceRoleInfo.getServiceId(),
            key.getServiceName(), zdhRequestIps);
        return generateServiceInstancePatchInfos(key, serviceRoleInfo, zdhHostInfos, zdhTargetIps);
    }

    private ServiceNeedUpdatePatchInfo generateServiceInstancePatchInfos(OrganizeKey key,
        ServiceRoleInfo serviceRoleInfo, List<HostInfo> hostInfos, Set<String> targetIps) {
        List<ServiceInstancePatchInfo> serviceInstancePatchInfos = Lists.newArrayList();
        Map<String, List<SimplePatchInfo>> zdhIp2NeedPatches = patchesNeedUpdateQueryService
            .queryNeedUpdatePatchesByService(key.getServiceName(), key.getVersion(), targetIps);

        List<RollBackPatchPointInfo> zdhTaskPatchRollbackPoints =
            generateRollbackPoints(hostInfos, zdhIp2NeedPatches, targetIps);

        if (!CollectionUtils.isEmpty(zdhTaskPatchRollbackPoints)) {
            ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
            String targetPatchPoint = generateTargetPatch(zdhIp2NeedPatches, targetIps);
            serviceInstancePatchInfo.setRollBackPatchPoints(zdhTaskPatchRollbackPoints);
            serviceInstancePatchInfo.setPatchType(PatchTypeEnum.ZDH.getType());
            serviceInstancePatchInfo.setTargetPatchPoint(targetPatchPoint);
            com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance zdhServiceInstance =
                new com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance();
            zdhServiceInstance.setVersion(key.getVersion());
            zdhServiceInstance.setServiceInstanceId(serviceRoleInfo.getServiceInstanceId());
            zdhServiceInstance.setServiceName(key.getServiceName());
            zdhServiceInstance.setServiceId(key.getServiceId());
            zdhServiceInstance.setServiceInstanceName(
                queryInstanceName(serviceRoleInfo.getClusterId(), serviceRoleInfo.getServiceInstanceId()));
            serviceInstancePatchInfo.setServiceInstance(zdhServiceInstance);
            serviceInstancePatchInfos.add(serviceInstancePatchInfo);
        }
        /* Started by AICoder, pid:0d60av5b47188371488f0a715024f70e1e759eb3 */
        List<String> needUpdatePatch = targetIps.stream()
                .flatMap(ip -> zdhIp2NeedPatches.getOrDefault(ip, Collections.emptyList()).stream())
                .map(SimplePatchInfo::getPatchName)
                .distinct()
                .collect(Collectors.toList());
        /* Ended by AICoder, pid:0d60av5b47188371488f0a715024f70e1e759eb3 */
        ServiceNeedUpdatePatchInfo serviceNeedUpdatePatchInfo = new ServiceNeedUpdatePatchInfo();
        serviceNeedUpdatePatchInfo.setServiceInstanceId(serviceRoleInfo.getServiceInstanceId());
        serviceNeedUpdatePatchInfo.setPatchInfoList(serviceInstancePatchInfos);
        serviceNeedUpdatePatchInfo.setNeedUpdatePatchList(needUpdatePatch);
        return serviceNeedUpdatePatchInfo;
    }

    private List<RollBackPatchPointInfo> generateRollbackPoints(List<HostInfo> hostInfos,
        Map<String, List<SimplePatchInfo>> zdhIp2NeedPatches, Set<String> targetIps) {
        ListMultimap<String, PatchHostInfoDto> zdhPatchName2Hosts = ArrayListMultimap.create();
        List<RollBackPatchPointInfo> rollBackPatchPointInfos = Lists.newArrayList();
        for (String ip : targetIps) {
            List<SimplePatchInfo> servicePatchesOfTheHost = zdhIp2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(servicePatchesOfTheHost)) {
                String rollbackPatchPoint = servicePatchesOfTheHost.stream().filter(p -> !p.isContainer())
                    .map(SimplePatchInfo::getPatchName).min(String::compareToIgnoreCase).orElse("");
                PatchHostInfoDto patchHostInfoDto = new PatchHostInfoDto();
                patchHostInfoDto.setHostName(hostInfos.stream().filter(h -> StringUtils.equals(h.getIpAddress(), ip))
                    .map(HostInfo::getHostName).findFirst().orElse(""));
                patchHostInfoDto.setIp(ip);
                zdhPatchName2Hosts.put(rollbackPatchPoint, patchHostInfoDto);
            }
        }
        for (String key : zdhPatchName2Hosts.keySet()) {
            RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
            rollBackPatchPointInfo.setRollBackPatchPoint(key);
            rollBackPatchPointInfo.setPatchHostInfos(zdhPatchName2Hosts.get(key));
            rollBackPatchPointInfos.add(rollBackPatchPointInfo);
        }
        return rollBackPatchPointInfos;
    }

    private String generateTargetPatch(Map<String, List<SimplePatchInfo>> zdhIp2NeedPatches, Set<String> targetIps) {
        for (String ip : targetIps) {
            List<SimplePatchInfo> zdhPatchesOfTheHost = zdhIp2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(zdhPatchesOfTheHost)) {
                return zdhPatchesOfTheHost.stream().filter(p -> !p.isContainer()).map(SimplePatchInfo::getPatchName)
                    .max(String::compareToIgnoreCase).orElse("");
            }
        }
        return "";
    }

    private Set<String> buildTargetIps(String clusterId, String serviceId, String serviceName, Set<String> requestIps) {
        List<ServiceRoleInfo> serviceRoles = dependResourceLocalUtil.getClusterServiceRoleInfo(clusterId);
        Set<String> targetIps;
        if (StringUtils.equalsIgnoreCase(serviceName, ZDH_DEFAULT_BASE_SERVICE_NAME)) {
            Set<String> bigDataServices = patchTypeQueryService.queryBigDataServiceIds(clusterId);
            targetIps = serviceRoles.stream().filter(e -> bigDataServices.contains(e.getServiceId()))
                .filter(ipFilter(requestIps)).map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        } else {
            targetIps = serviceRoles.stream().filter(serviceIdFilter(serviceId)).filter(ipFilter(requestIps))
                .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        }
        return targetIps;
    }
}