/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: RollbackProgressService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/22
 * </p>
 * <p>
 * 完成日期：2023/3/22
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.rollback.progress;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.cache.delaytask.DelayTaskExecutor;
import com.zte.daip.manager.common.cache.delaytask.DelayTaskManager;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.api.dto.RollBackPatchProgress;
import com.zte.daip.manager.patcher.api.dto.RollbackHostProgressDto;
import com.zte.daip.manager.patcher.api.dto.RollbackServiceProgressDto;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.rollback.entity.HostRollbackProgress;
import com.zte.daip.manager.patcher.domain.rollback.entity.RollbackProgressDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class RollbackProgressService {

    @Autowired
    private RollbackProgressApi rollbackProgressApi;
    @Autowired
    private HostResourceInfoCache hostResourceInfoCache;
    @Autowired
    private DelayTaskManager delayTaskManager;
    @Autowired
    private DelayTaskExecutor rollbackTimeOutExecutor;

    @Value("${daip.patcher.rollback.timeout.inMinutes:30}")
    private int rollbackTimeout;

    public RollBackPatchProgress queryRollBackProgress(String clusterId) {

        try {
            List<String> services = rollbackProgressApi.queryRollbackServices(clusterId);
            List<RollbackServiceProgressDto> rollbackServiceProgressDtos = Lists.newArrayList();
            services.forEach(service -> {
                List<HostRollbackProgress> hostRollbackProgresses =
                    rollbackProgressApi.queryProgress(clusterId, service);
                log.info("hostRollbackProgresses:{}", hostRollbackProgresses.toString());
                List<RollbackHostProgressDto> rollbackHostProgresses = hostRollbackProgresses.stream()
                    .map(hostRollbackProgress -> new RollbackHostProgressDto(hostRollbackProgress.getIpAddress(),
                        hostRollbackProgress.isSuccess(), hostRollbackProgress.isFinish(),
                        hostRollbackProgress.getMessage()))
                    .collect(Collectors.toList());
                int total = hostRollbackProgresses.size();
                int finish = hostRollbackProgresses.stream().filter(HostRollbackProgress::isFinish)
                    .collect(Collectors.toList()).size();
                int failedSize =
                    hostRollbackProgresses.stream().filter(hostRollbackProgress -> !hostRollbackProgress.isSuccess())
                        .collect(Collectors.toList()).size();
                RollbackServiceProgressDto rollbackServiceProgressDto = new RollbackServiceProgressDto(service,
                    rollbackHostProgresses, total, finish - failedSize, failedSize);
                rollbackServiceProgressDtos.add(rollbackServiceProgressDto);
            });
            return getRollBackProgress(rollbackServiceProgressDtos);
        } catch (Exception e) {
            log.error("query rollback progress error", e);
        }
        return new RollBackPatchProgress();
    }

    public void initRollbackProgress(String clusterId, List<PatchRollbackHostDto> patchRollbackHostDtos) {
        log.info("init rollback progress :{}", clusterId);
        patchRollbackHostDtos.forEach(patchRollbackDo -> {
            String serviceName = patchRollbackDo.getServiceName();
            rollbackProgressApi.deleteRollbackProgress(clusterId, serviceName);
            patchRollbackDo.getPatchHostInfoList().forEach(patchHostInfo -> {
                HostRollbackProgress hostRollbackProgress =
                    new HostRollbackProgress(patchHostInfo.getIp(), true, false, "");
                rollbackProgressApi.initAndUpdateRollbackProgress(clusterId, serviceName, patchHostInfo.getIp(),
                    hostRollbackProgress);
                String mapKey = "ROLLBACK_PROGRESS:" + clusterId + serviceName;
                delayTaskManager.addDelayTask(TimeUnit.MINUTES.toSeconds(rollbackTimeout), mapKey,
                    rollbackTimeOutExecutor, mapKey);
            });
        });
        Set<String> services =
            patchRollbackHostDtos.stream().map(PatchRollbackHostDto::getServiceName).collect(Collectors.toSet());
        rollbackProgressApi.initRollbackServices(clusterId, Lists.newArrayList(services));
    }

    public void updateRollbackProgress(List<RollbackProgressDo> rollbackProgressDos) {
        log.info("updateRollbackProgress:{}", rollbackProgressDos);
        rollbackProgressDos.forEach(rollbackProgressDo -> {
            String clusterId = rollbackProgressDo.getClusterId();
            String serviceName = rollbackProgressDo.getServiceName();
            String ipAddress = hostResourceInfoCache.queryIpAddress(rollbackProgressDo.getHostName());
            String message = rollbackProgressDo.getMessage();
            boolean success = rollbackProgressDo.isSuccess();
            HostRollbackProgress hostRollbackProgress = new HostRollbackProgress(ipAddress, success, true, message);
            rollbackProgressApi.initAndUpdateRollbackProgress(clusterId, serviceName, ipAddress, hostRollbackProgress);
        });
    }

    private RollBackPatchProgress getRollBackProgress(List<RollbackServiceProgressDto> rollbackServiceProgressDtos) {
        boolean success = true;
        boolean finish = false;
        List<RollbackServiceProgressDto> failedService = rollbackServiceProgressDtos.stream()
            .filter(rollbackServiceProgressDto -> rollbackServiceProgressDto.getFailed() > 0)
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(failedService)) {
            success = false;
        }
        List<RollbackServiceProgressDto> finishService = rollbackServiceProgressDtos.stream()
            .filter(rollbackServiceProgressDto -> rollbackServiceProgressDto.getSuccess()
                + rollbackServiceProgressDto.getFailed() == rollbackServiceProgressDto.getTotal())
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(finishService) && finishService.size() == rollbackServiceProgressDtos.size()) {
            finish = true;
        }
        return new RollBackPatchProgress(rollbackServiceProgressDtos, success, finish);
    }
}