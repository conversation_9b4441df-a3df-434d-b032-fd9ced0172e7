package com.zte.daip.manager.patcher.domain.upload.pool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Configuration
@EnableAsync
public class PatchFileValidateExecutor {
    @Value("${dapmanager.patcher.check.patchfile.pool.corePoolSize:50}")
    private int corePoolSize;

    @Value("${dapmanager.patcher.check.patchfile.pool.maxPoolSize:50}")
    private int maxPoolSize;

    @Value("${dapmanager.patcher.check.patchfile.pool.keepAliveSeconds:600}")
    private int keepAliveSeconds;

    @Value("${dapmanager.patcher.check.patchfile.pool.queueCapacity:50}")
    private int queueCapacity;

    private ThreadPoolTaskExecutor patcherExecutor;

    @Bean
    public Executor patchFileValidatePool() {
        GenerateValidatePool generateValidatePool = new GenerateValidatePool();
        patcherExecutor = generateValidatePool.generate(corePoolSize, maxPoolSize, queueCapacity, keepAliveSeconds,
            "PatchFileValidatePool-");
        return patcherExecutor;
    }

    public boolean checkThread() {
        return patcherExecutor.getMaxPoolSize() > patcherExecutor.getActiveCount();
    }
}
