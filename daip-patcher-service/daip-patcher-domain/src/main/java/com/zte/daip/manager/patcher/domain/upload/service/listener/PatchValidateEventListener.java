package com.zte.daip.manager.patcher.domain.upload.service.listener;

import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatingQueue;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchValidateBean;
import com.zte.daip.manager.patcher.domain.upload.event.PatchValidateEvent;
import com.zte.daip.manager.patcher.domain.upload.pool.PatchFileValidateExecutor;
import com.zte.daip.manager.patcher.domain.upload.service.PatchValidateService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class PatchValidateEventListener implements ApplicationListener<PatchValidateEvent> {
    @Autowired
    private PatchFileValidateExecutor patchFileValidateExecutor;

    @Autowired
    private PatchValidatingQueue patchValidatingQueue;

    @Autowired
    private PatchValidateService patchValidateService;

    @Autowired
    private PatchInfoService patchInfoService;

    @Override
    @Async
    public void onApplicationEvent(PatchValidateEvent event) {
        List<PatchDetailPo> patchDetailPos = patchInfoService.queryAllPatchInfos();
        while (!patchValidatingQueue.isEmpty()) {
            if (patchFileValidateExecutor.checkThread()) {
                PatchValidateBean checkPatchFile = patchValidatingQueue.pollOnePatch();
                if (null == checkPatchFile) {
                    break;
                }
                patchValidateService.validatePatch(checkPatchFile, patchDetailPos);
            }
        }
    }
}
