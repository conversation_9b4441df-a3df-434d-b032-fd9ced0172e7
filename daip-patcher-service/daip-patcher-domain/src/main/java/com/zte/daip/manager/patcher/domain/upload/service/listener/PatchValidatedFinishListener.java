package com.zte.daip.manager.patcher.domain.upload.service.listener;

import com.alibaba.fastjson.JSON;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatedResultCache;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.event.PatchValidatedFinishEvent;
import com.zte.daip.manager.patcher.domain.upload.event.UploadPatchEvent;
import com.zte.daip.manager.patcher.domain.upload.operate.PatchTypeOperateFactory;
import com.zte.daip.manager.patcher.domain.upload.operate.PatchTypeOperator;
import com.zte.daip.manager.patcher.domain.upload.service.PatchDetailDomService;
import com.zte.daip.manager.patcher.domain.upload.service.PatchLocalUploadService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatchValidatedFinishListener implements ApplicationListener<PatchValidatedFinishEvent> {

    @Autowired
    private PatchValidatedResultCache patchValidatedResultCache;

    @Autowired
    private PatchLocalUploadService patchLocalUploadService;

    @Autowired
    private PatchDetailDomService patchDetailDomService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private PatchInfoService patchInfoService;

    @Autowired
    private PatchTypeOperateFactory patchTypeOperateFactory;

    @Value("${dap.manager.patch.filter.fullPatch:VMAX}")
    private String ignoreList;

    @Override
    public void onApplicationEvent(PatchValidatedFinishEvent event) {
        String batchId = event.getSource().toString();
        List<PatchBean> needPublishPatch = patchValidatedResultCache.querySuccessResultByBatchId(batchId).stream()
            .map(PatchUploadResult::getPatchBean).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needPublishPatch)) {
            if (patchLocalUploadService.uploadToLocalRepository(needPublishPatch)) {
                patchDetailDomService.saveValidPatches(needPublishPatch);
                deleteUploadedPatchesByFullPatch(needPublishPatch);
                patchValidatedResultCache.updateAllFinish(batchId);
                applicationContext.publishEvent(new UploadPatchEvent("", needPublishPatch));
            } else {
                patchValidatedResultCache.updateUploadFailMsg(batchId);
            }
        } else {
            patchValidatedResultCache.updateAllFinish(batchId);
        }
    }

    private void deleteUploadedPatchesByFullPatch(List<PatchBean> needPublishPatch) {
        List<PatchBean> fullPatchBeans =
            needPublishPatch.stream().filter(PatchBean::isFullpatch).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(fullPatchBeans)) {
            fullPatchBeans.forEach(fullPatch -> {
                PatchTypeOperator patchType = patchTypeOperateFactory.getPatchType(fullPatch);
                String currentPatchName = fullPatch.getPatchName();
                List<PatchDetailPo> patchDetailPos = patchInfoService
                    .findByServiceAndBaseVersion(fullPatch.getService(), fullPatch.getSrcVersion()).stream()
                    .filter(patchDetailPo -> !ignore(patchDetailPo.getPatchName())).collect(Collectors.toList());
                patchType.deleteByFullPatch(patchDetailPos, currentPatchName);
            });
        }
    }

    private boolean ignore(String patchFileName) {
        String[] ignores = StringUtils.split(ignoreList, ",");
        for (String e : ignores) {
            if (StringUtils.lowerCase(patchFileName).contains(StringUtils.lowerCase(e))) {
                return true;
            }
        }
        return false;
    }
}
