/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UpdateRollbackResultScheduler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/22
 * </p>
 * <p>
 * 完成日期：2023/3/22
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.rollback.strategy;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.update.response.OnePatchUpdateInfo;
import com.zte.daip.manager.patcher.api.update.response.PatchRecordAndRecoverInfos;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.common.PatchRollbackService;
import com.zte.daip.manager.patcher.domain.rollback.cache.PatchRollbackResultQueue;
import com.zte.daip.manager.patcher.domain.rollback.entity.PatchRollBackResult;
import com.zte.daip.manager.patcher.infrastructure.po.*;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class UpdateRollbackResultScheduler {

    @Autowired
    private PatchRollbackResultQueue patchRollbackResultQueue;
    @Autowired
    private PatchRollbackService patchRollbackService;
    @Autowired
    private PatchHistoryService patchHistoryService;
    @Autowired
    private PatchInfoService patchInfoService;

    @Scheduled(cron = "0/2 * * * * ?")
    public void cron() {
        List<PatchRollBackResult> patchRollBackResults = Lists.newArrayList();

        while (!patchRollbackResultQueue.isEmptyDispatchCache()) {
            PatchRollBackResult patchRollBackResult = patchRollbackResultQueue.poll();
            patchRollBackResults.add(patchRollBackResult);
        }
        updateRollbackResult(patchRollBackResults);

    }

    public void updateRollbackResult(List<PatchRollBackResult> patchRollBackResults) {
        patchRollBackResults.forEach(patchRollBackResult -> {
            log.info("update rollback result:{}", patchRollBackResult.toString());
            List<PatchRecordAndRecoverInfos> patchRecordAndRecoverInfos =
                patchRollBackResult.getPatchRecordAndRecoverInfos();
            patchRecordAndRecoverInfos.forEach(
                patchRecordAndRecoverInfo -> updateOneRollbackResult(patchRollBackResult, patchRecordAndRecoverInfo));
        });
    }

    private void updateOneRollbackResult(PatchRollBackResult patchRollBackResult,
        PatchRecordAndRecoverInfos patchRecordAndRecoverInfo) {

        String serviceName = patchRollBackResult.getServiceName();
        String serviceInstanceId = patchRollBackResult.getServiceInstanceId();
        String roleName = patchRollBackResult.getRoleName();
        String ip = patchRollBackResult.getIp();
        deleteOldPatchInfo(serviceName, serviceInstanceId, roleName, ip);

        List<OnePatchUpdateInfo> filterHistoryInfos =
            filteredPatchInfo(patchRecordAndRecoverInfo.getPatchHistory(), serviceName, serviceInstanceId, roleName);
        updateHistoryInfo(filterHistoryInfos);
        List<OnePatchUpdateInfo> filterRollbackInfos =
            filteredPatchInfo(patchRecordAndRecoverInfo.getRecoverPatch(), serviceName, serviceInstanceId, roleName);
        patchRollbackService.saveRollbackPatches(filterRollbackInfos);

    }

    private void deleteOldPatchInfo(String serviceName, String serviceInstanceId, String roleName, String ip) {
        List<String> containerPatchNames = patchInfoService.findContainerPatchByService(serviceName).stream()
            .map(PatchDetailPo::getPatchName).collect(Collectors.toList());
        log.info("containerPatchNames:{}", containerPatchNames.toString());
        patchRollbackService.deleteByRollbackKeyWithoutContainer(serviceName, serviceInstanceId, roleName, ip,
            containerPatchNames);
        patchHistoryService.deleteByHistoryKeyWithoutContainer(serviceName, serviceInstanceId, roleName, ip,
            containerPatchNames);
    }

    private void updateHistoryInfo(List<OnePatchUpdateInfo> filterHistoryInfos) {
        List<PatchHistory> patchHistories = Lists.newArrayList();
        for (OnePatchUpdateInfo onePatch : filterHistoryInfos) {
            PatchHistory patchHistory = new PatchHistory();
            patchHistory.setPatchUptime(onePatch.getTime());
            PatchHistoryKey patchHistoryKey = new PatchHistoryKey();
            patchHistoryKey.setIp(onePatch.getHostIp());
            patchHistoryKey.setServiceName(onePatch.getService());
            patchHistoryKey.setPatchName(onePatch.getPatchName());
            patchHistoryKey.setServiceInstanceId(onePatch.getInstance());
            patchHistoryKey.setRoleName(onePatch.getRoleName());
            patchHistory.setId(patchHistoryKey);
            patchHistories.add(patchHistory);
        }
        log.info("patchHistories:{}", patchHistories.toString());
        patchHistoryService.saveBatch(patchHistories);
    }

    private List<OnePatchUpdateInfo> filteredPatchInfo(List<OnePatchUpdateInfo> patchUpdateInfos, String serviceName,
        String serviceInstanceId, String roleName) {
        return patchUpdateInfos.stream()
            .filter(onePatchUpdateInfo -> checkEqual(onePatchUpdateInfo.getService(), serviceName)
                && checkEqual(onePatchUpdateInfo.getInstance(), serviceInstanceId)
                && checkEqual(onePatchUpdateInfo.getRoleName(), roleName))
            .collect(Collectors.toList());
    }

    private boolean checkEqual(String srcStr, String tagetStr) {
        if (StringUtils.isBlank(srcStr) && StringUtils.isBlank(tagetStr)) {
            return true;
        }
        return StringUtils.equalsIgnoreCase(srcStr, tagetStr);
    }

}