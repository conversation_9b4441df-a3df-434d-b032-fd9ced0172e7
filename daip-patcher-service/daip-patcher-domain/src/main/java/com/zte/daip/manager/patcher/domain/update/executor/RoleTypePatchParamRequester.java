/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServicePatchUpdate.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/1
 * </p>
 * <p>
 * 完成日期：2021/4/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.executor;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.*;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.patcher.domain.update.bean.ServiceNeedUpdatePatchInfo;
import com.zte.daip.manager.patcher.inner.api.dto.PatchHostInfoDto;
import com.zte.daip.manager.patcher.inner.api.dto.RollBackPatchPointInfo;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.deployer.bean.model.RoleModel;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.update.request.*;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.update.executor.api.MergeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.api.OrganizeRequest;
import com.zte.daip.manager.patcher.domain.update.executor.depend.DependResourceLocalUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service("role")
public class RoleTypePatchParamRequester extends AbstractPatchParamRequester implements OrganizeRequest, MergeRequest {
    @Autowired
    private DependResourceLocalUtil dependResourceLocalUtil;
    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Override
    public List<PatchUpdateRequest> organizeRequest(String clusterId, OrganizeKey key,
        List<ServiceInstanceInfo> serviceInstances) {
        List<ServiceRoleInfo> serviceRoles = dependResourceLocalUtil.getClusterServiceRoleInfo(clusterId);
        Set<String> roleIds = serviceRoles.stream().filter(serviceIdFilter(key.getServiceId()))
            .map(ServiceRoleInfo::getRoleId).collect(Collectors.toSet());

        return queryPatchUpdateRequests(clusterId, key, serviceRoles, roleIds, serviceInstances);
    }

    private List<PatchUpdateRequest> queryPatchUpdateRequests(String clusterId, OrganizeKey key,
        List<ServiceRoleInfo> serviceRoles, Set<String> roleIds, List<ServiceInstanceInfo> serviceInstances) {
        List<PatchUpdateRequest> patchUpdateRequests = Lists.newArrayList();
        // 参数传递过来的ips
        Set<String> requestIps =
            serviceInstances.stream().flatMap(s -> s.getIps().stream()).collect(Collectors.toSet());
        List<RoleModel> roleModels = queryRoleModels(clusterId, key.getServiceId());
        if (CollectionUtils.isEmpty(roleModels)) {
            log.warn("query role models is empty!");
            return patchUpdateRequests;
        }
        for (String roleId : roleIds) {
            String roleName = getRoleNameByRoleId(roleModels, roleId);
            Set<String> theRoleIps = serviceRoles.stream().filter(roleIdPredicate(roleId)).filter(ipFilter(requestIps))
                .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
            Map<String, List<SimplePatchInfo>> ip2NeedPatches = patchesNeedUpdateQueryService
                .queryNeedUpdateRolePatches(key.getServiceName(), key.getVersion(), theRoleIps, roleName);
            log.debug(String.format("role level role %s:finished to queryNeedUpdatePatches: %s", roleName,
                JSON.toJSONString(ip2NeedPatches)));
            //
            Map<String, List<ServicePatchInfo>> hostName2RolePatches = Maps.newHashMap();
            for (String ip : theRoleIps) {
                Set<ServiceInstance> instances = genServiceInstances(serviceRoles, roleId, roleName, ip);
                ServicePatchInfo servicePatchInfo = genServicePatchInfo(key, ip2NeedPatches, ip, instances);
                if (servicePatchInfo != null) {
                    hostName2RolePatches.put(ip, Lists.newArrayList(servicePatchInfo));
                }
            }
            if (!CollectionUtils.isEmpty(hostName2RolePatches)) {
                PatchUpdateRequest patchUpdateRequest =
                    genPatchUpdateRequest(clusterId, serviceRoles, roleId, hostName2RolePatches);
                patchUpdateRequests.add(patchUpdateRequest);
            }
        }
        return patchUpdateRequests;
    }

    private String getRoleNameByRoleId(List<RoleModel> roleModels, String roleId) {
        return roleModels.stream().filter(r -> StringUtils.equals(r.getRoleId(), roleId)).map(RoleModel::getRoleName)
            .findFirst().orElse("");
    }

    private PatchUpdateRequest genPatchUpdateRequest(String clusterId, List<ServiceRoleInfo> serviceRoles,
        String roleId, Map<String, List<ServicePatchInfo>> hostName2Patches) {
        String serviceInstanceId = serviceRoles.stream().filter(s -> StringUtils.equals(s.getRoleId(), roleId))
            .findFirst().orElse(new ServiceRoleInfo()).getServiceInstanceId();
        String patchHome = queryPatchHome(clusterId, serviceInstanceId, roleId);
        PatchUpdateRequest patchUpdateRequest =
            new PatchUpdateRequest(clusterId, patchHome, PatchTypeEnum.ROLE.getType());
        patchUpdateRequest.setIp2Patches(hostName2Patches);
        return patchUpdateRequest;
    }

    private Set<ServiceInstance> genServiceInstances(List<ServiceRoleInfo> serviceRoles, String roleId, String roleName,
        String ip) {
        Set<ServiceInstance> instances = serviceRoles.stream()
            .filter(s -> StringUtils.equals(s.getRoleId(), roleId) && StringUtils.equals(s.getIpAddress(), ip))
            .map(e -> new ServiceInstance(e.getServiceInstanceId(), Lists.newArrayList(roleName)))
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(instances)) {
            return Sets.newHashSet();
        }
        return instances;
    }

    private ServicePatchInfo genServicePatchInfo(OrganizeKey key, Map<String, List<SimplePatchInfo>> ip2NeedPatches,
        String ip, Set<ServiceInstance> instances) {
        List<SimplePatchInfo> rolePatches = ip2NeedPatches.get(ip);
        if (CollectionUtils.isEmpty(rolePatches)) {
            return null;
        }
        ServicePatchInfo servicePatchInfo = new ServicePatchInfo(key.getServiceName(), key.getVersion());
        servicePatchInfo.setServiceInstances(Lists.newArrayList(instances));
        servicePatchInfo.setPatches(rolePatches);
        return servicePatchInfo;
    }

    private List<RoleModel> queryRoleModels(String clusterId, String serviceId) {
        List<ServiceModel> serviceModels = productModelInfoControllerApi.queryByClusterId(clusterId);
        if (CollectionUtils.isEmpty(serviceModels)) {
            return Lists.newArrayList();
        }
        List<RoleModel> roles = serviceModels.stream().filter(s -> StringUtils.equals(s.getServiceId(), serviceId))
            .findFirst().orElse(new ServiceModel()).getRoles();
        if (CollectionUtils.isEmpty(roles)) {
            return Lists.newArrayList();
        }
        return roles;
    }

    private String queryPatchHome(String clusterId, String anyServiceInstanceId, String roleId) {
        List<ConfigInstance> configInstances = patchHomeQueryService.queryPatchHome(clusterId, anyServiceInstanceId);
        if (CollectionUtils.isEmpty(configInstances)) {
            return "";
        }
        String configValue = configInstances.stream().filter(c -> StringUtils.equals(c.getRoleId(), roleId)).findFirst()
            .orElse(new ConfigInstance()).getConfigValue();
        return configValue + File.separator + "patch";
    }

    // if patchHome same,then merge.
    @Override
    public PatchUpdateRequest mergeRequest(PatchRequestKey patchRequestKey,
        List<PatchUpdateRequest> patchUpdateRequests) {
        if ((!CollectionUtils.isEmpty(patchUpdateRequests)) && patchUpdateRequests.size() == 1) {
            return patchUpdateRequests.get(0);
        }
        log.debug("roleType merge before:" + JSON.toJSONString(patchUpdateRequests));
        if (CollectionUtils.isEmpty(patchUpdateRequests)) {
            return new PatchUpdateRequest();
        }
        ArrayListMultimap<String, List<ServicePatchInfo>> ip2ServicePatches = ArrayListMultimap.create();
        for (PatchUpdateRequest request : patchUpdateRequests) {
            Map<String, List<ServicePatchInfo>> hostName2Patches = request.getIp2Patches();
            hostName2Patches.forEach(ip2ServicePatches::put);
        }
        //
        Map<String, List<ServicePatchInfo>> hostName2Patches = Maps.newHashMap();
        Map<String, Collection<List<ServicePatchInfo>>> stringCollectionMap = ip2ServicePatches.asMap();
        stringCollectionMap.forEach((hostIp, values) -> {
            List<ServicePatchInfo> servicePatchInfos = Lists.newArrayList();
            values.forEach(servicePatchInfos::addAll);
            hostName2Patches.put(hostIp, Lists.newArrayList(mergeForRoleTypePatch(servicePatchInfos)));
        });
        //
        PatchUpdateRequest finalPatchRequest = new PatchUpdateRequest(patchRequestKey.getClusterId(),
            patchRequestKey.getPatchHome(), patchRequestKey.getPatchType());
        finalPatchRequest.setIp2Patches(hostName2Patches);
        log.debug("roleType merge after:" + JSON.toJSONString(finalPatchRequest));
        return finalPatchRequest;
    }

    private ServicePatchInfo mergeForRoleTypePatch(List<ServicePatchInfo> patchInfos) {
        if (CollectionUtils.isEmpty(patchInfos)) {
            return new ServicePatchInfo();
        }
        Set<String> updatedContainerPatches = Sets.newHashSet();
        Set<SimplePatchInfo> patches = Sets.newHashSet();
        List<ServiceInstance> serviceInstances = new ArrayList();
        for (ServicePatchInfo servicePatchInfo : patchInfos) {
            serviceInstances.addAll(servicePatchInfo.getServiceInstances());
            updatedContainerPatches.addAll(servicePatchInfo.getUpdatedContainerPatches());
            patches.addAll(servicePatchInfo.getPatches());
        }
        return new ServicePatchInfo(patchInfos.get(0).getServiceName(), patchInfos.get(0).getVersion(),
            serviceInstances, Lists.newArrayList(patches), Lists.newArrayList(updatedContainerPatches));
    }

    @Override
    public ServiceNeedUpdatePatchInfo queryRollbackPoints(ServiceRoleInfo serviceRoleInfo, OrganizeKey key) {
        List<ServiceRoleInfo> serviceRoles =
            dependResourceLocalUtil.getClusterServiceRoleInfo(serviceRoleInfo.getClusterId());
        List<HostInfo> hostInfos = dependResourceLocalUtil.getClusterHostInfo(serviceRoleInfo.getClusterId());
        if (CollectionUtils.isEmpty(hostInfos) || CollectionUtils.isEmpty(serviceRoles)) {
            log.warn("query rollback points service: depend resource local cache info is empty.");
            return null;
        }
        return generateServiceInstancePatchInfos(serviceRoleInfo, key, serviceRoles, hostInfos);
    }

    private ServiceNeedUpdatePatchInfo generateServiceInstancePatchInfos(ServiceRoleInfo serviceRoleInfo,
        OrganizeKey key, List<ServiceRoleInfo> serviceRoles, List<HostInfo> hostInfos) {
        List<RoleModel> roleModels = queryRoleModels(serviceRoleInfo.getClusterId(), serviceRoleInfo.getServiceId());
        if (CollectionUtils.isEmpty(roleModels)) {
            log.warn("query rollback points service: role models is empty!");
            return null;
        }
        Set<String> roleIds = serviceRoles.stream().filter(serviceIdFilter(serviceRoleInfo.getServiceId()))
            .map(ServiceRoleInfo::getRoleId).collect(Collectors.toSet());
        Set<String> requestIps = queryRequestIps(serviceRoles, serviceRoleInfo);
        List<ServiceInstancePatchInfo> patchRollbackHostInfos = Lists.newArrayList();
        List<String> needUpdatePatchList = Lists.newArrayList();
        String instanceName = queryInstanceName(serviceRoleInfo.getClusterId(), serviceRoleInfo.getServiceInstanceId());
        for (String roleId : roleIds) {
            String roleName = roleModels.stream().filter(r -> StringUtils.equals(r.getRoleId(), roleId))
                .map(RoleModel::getRoleName).findFirst().orElse("");
            Set<String> theRoleIps = serviceRoles.stream().filter(roleIdPredicate(roleId)).filter(ipFilter(requestIps))
                .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
            Map<String, List<SimplePatchInfo>> ip2NeedPatches = patchesNeedUpdateQueryService
                .queryNeedUpdateRolePatches(key.getServiceName(), key.getVersion(), theRoleIps, roleName);
            /* Started by AICoder, pid:0d60av5b47188371488f0a715024f70e1e759eb3 */
            List<String> needUpdatePatch = theRoleIps.stream()
                    .flatMap(ip -> ip2NeedPatches.getOrDefault(ip, Collections.emptyList()).stream())
                    .map(SimplePatchInfo::getPatchName)
                    .collect(Collectors.toList());
            /* Ended by AICoder, pid:0d60av5b47188371488f0a715024f70e1e759eb3 */
            needUpdatePatchList.addAll(needUpdatePatch);
            List<RollBackPatchPointInfo> rollBackPatchPointInfos =
                generateRollbackPoints(hostInfos, ip2NeedPatches, theRoleIps);
            if (!CollectionUtils.isEmpty(rollBackPatchPointInfos)) {
                ServiceInstancePatchInfo serviceInstancePatchInfo =
                    organizeServiceInstancePatchInfo(ip2NeedPatches, theRoleIps, rollBackPatchPointInfos);
                organizeServiceInstance(key, serviceRoleInfo, roleName, instanceName, serviceInstancePatchInfo);
                patchRollbackHostInfos.add(serviceInstancePatchInfo);
            }
        }
        ServiceNeedUpdatePatchInfo serviceNeedUpdatePatchInfo = new ServiceNeedUpdatePatchInfo();
        serviceNeedUpdatePatchInfo.setServiceInstanceId(serviceRoleInfo.getServiceInstanceId());
        serviceNeedUpdatePatchInfo.setNeedUpdatePatchList(needUpdatePatchList.stream().distinct().collect(Collectors.toList()));
        serviceNeedUpdatePatchInfo.setPatchInfoList(patchRollbackHostInfos);
        return serviceNeedUpdatePatchInfo;
    }

    private Set<String> queryRequestIps(List<ServiceRoleInfo> serviceRoles, ServiceRoleInfo serviceRoleInfo) {
        return serviceRoles.stream()
            .filter(s -> StringUtils.equals(s.getServiceId(), serviceRoleInfo.getServiceId())
                && StringUtils.equals(s.getServiceInstanceId(), serviceRoleInfo.getServiceInstanceId()))
            .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
    }

    private ServiceInstancePatchInfo organizeServiceInstancePatchInfo(Map<String, List<SimplePatchInfo>> ip2NeedPatches,
        Set<String> theRoleIps, List<RollBackPatchPointInfo> rollBackPatchPointInfos) {
        String targetPatchPoint = generateTargetPatch(ip2NeedPatches, theRoleIps);
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        serviceInstancePatchInfo.setRollBackPatchPoints(rollBackPatchPointInfos);
        serviceInstancePatchInfo.setPatchType(PatchTypeEnum.ROLE.getType());
        serviceInstancePatchInfo.setTargetPatchPoint(targetPatchPoint);
        return serviceInstancePatchInfo;
    }

    private void organizeServiceInstance(OrganizeKey key, ServiceRoleInfo serviceRoleInfo, String roleName,
        String instanceName, ServiceInstancePatchInfo serviceInstancePatchInfo) {
        com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance roleServiceInstance =
            new com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance();

        roleServiceInstance.setServiceInstanceId(serviceRoleInfo.getServiceInstanceId());
        roleServiceInstance.setRoleName(roleName);
        roleServiceInstance.setVersion(key.getVersion());
        roleServiceInstance.setServiceName(key.getServiceName());
        roleServiceInstance.setServiceId(key.getServiceId());
        roleServiceInstance.setServiceInstanceName(instanceName);
        serviceInstancePatchInfo.setServiceInstance(roleServiceInstance);
    }

    private List<RollBackPatchPointInfo> generateRollbackPoints(List<HostInfo> hostInfos,
        Map<String, List<SimplePatchInfo>> roleIp2NeedPatches, Set<String> targetIps) {
        ListMultimap<String, PatchHostInfoDto> rolePatchName2Hosts = ArrayListMultimap.create();
        List<RollBackPatchPointInfo> roleTaskPatchRollbackPoints = Lists.newArrayList();
        for (String ip : targetIps) {
            List<SimplePatchInfo> rolePatchesOfTheHost = roleIp2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(rolePatchesOfTheHost)) {
                String rollbackPatchPoint = rolePatchesOfTheHost.stream().filter(p -> !p.isContainer())
                    .map(SimplePatchInfo::getPatchName).min(String::compareToIgnoreCase).orElse("");
                PatchHostInfoDto patchHostInfoDto = new PatchHostInfoDto();
                patchHostInfoDto.setHostName(hostInfos.stream().filter(h -> StringUtils.equals(h.getIpAddress(), ip))
                    .map(HostInfo::getHostName).findFirst().orElse(""));
                patchHostInfoDto.setIp(ip);
                rolePatchName2Hosts.put(rollbackPatchPoint, patchHostInfoDto);
            }
        }
        for (String key : rolePatchName2Hosts.keySet()) {
            RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
            rollBackPatchPointInfo.setRollBackPatchPoint(key);
            rollBackPatchPointInfo.setPatchHostInfos(rolePatchName2Hosts.get(key));
            roleTaskPatchRollbackPoints.add(rollBackPatchPointInfo);
        }
        return roleTaskPatchRollbackPoints;
    }

    private String generateTargetPatch(Map<String, List<SimplePatchInfo>> roleIp2NeedPatches, Set<String> targetIps) {
        for (String ip : targetIps) {
            List<SimplePatchInfo> rolePatchesOfTheHost = roleIp2NeedPatches.get(ip);
            if (!CollectionUtils.isEmpty(rolePatchesOfTheHost)) {
                return rolePatchesOfTheHost.stream().filter(p -> !p.isContainer()).map(SimplePatchInfo::getPatchName)
                    .max(String::compareToIgnoreCase).orElse("");
            }
        }
        return "";
    }
}