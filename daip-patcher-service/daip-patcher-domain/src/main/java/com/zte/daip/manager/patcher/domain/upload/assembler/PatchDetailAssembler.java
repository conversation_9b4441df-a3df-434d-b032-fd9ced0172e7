/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDetailAssembler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/28
 * </p>
 * <p>
 * 完成日期：2021/3/28
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.upload.assembler;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Date;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service

public class PatchDetailAssembler {
    public PatchDetailPo patchBean2DbBean(PatchBean patchBean) {
        PatchDetailPo upgradePatchInfo = new PatchDetailPo();

        upgradePatchInfo.setPatchName(patchBean.getPatchName());
        upgradePatchInfo.setPatchDisplayNameZh(
            null != patchBean.getPatchDisplayname() ? patchBean.getPatchDisplayname().getZhCn() : "");
        upgradePatchInfo.setPatchDisplayNameEn(
            null != patchBean.getPatchDisplayname() ? patchBean.getPatchDisplayname().getEnUs() : "");
        upgradePatchInfo.setDependPatch(patchBean.getDependPatch());
        upgradePatchInfo.setBaseVersion(patchBean.getSrcVersion());
        upgradePatchInfo.setPatchCreateDate(getCreateDate(patchBean.getCreateDate()));
        upgradePatchInfo.setService(patchBean.getService());
        List<String> roles = patchBean.getRoles();
        if (CollectionUtils.isEmpty(roles)) {
            upgradePatchInfo.setRoles("");
        } else {
            upgradePatchInfo.setRoles(StringUtils.join(roles, ","));
        }
        upgradePatchInfo.setHotPatch(patchBean.isHotpatch() ? 1 : 0);
        upgradePatchInfo.setPatchSize(patchBean.getPatchSize());
        upgradePatchInfo.setPatchUploadTime(new Timestamp(System.currentTimeMillis()));
        upgradePatchInfo
            .setDescriptionZh(null != patchBean.getDescription() ? patchBean.getDescription().getZhCn() : "");
        upgradePatchInfo
            .setDescriptionEn(null != patchBean.getDescription() ? patchBean.getDescription().getEnUs() : "");
        upgradePatchInfo.setIsContainerPatch(patchBean.isContainPatch() ? 1 : 0);
        upgradePatchInfo.setIsFullPatch(patchBean.isFullpatch() ? 1 : 0);
        upgradePatchInfo.setPatchSize(patchBean.getPatchSize());

        return upgradePatchInfo;
    }

    private Date getCreateDate(String time) {
        try {
            return new Date(new SimpleDateFormat("yyyy-MM-dd").parse(time).getTime());
        } catch (ParseException e) {
            log.error("convert to Date fail", e);
        }
        return new Date(System.currentTimeMillis());
    }
}