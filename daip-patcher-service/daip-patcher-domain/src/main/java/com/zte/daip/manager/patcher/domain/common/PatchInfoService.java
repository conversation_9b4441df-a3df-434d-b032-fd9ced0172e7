/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchInfoService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/29
 * </p>
 * <p>
 * 完成日期：2021/3/29
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.common;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.paging.jpa.QueryParam;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.paging.JpaPagingQueryService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CacheConfig(cacheNames = "patchInfo")
@Slf4j
public class PatchInfoService {

    @Autowired
    private PatchInfoRepository patchInfoRepository;

    @Autowired
    private JpaPagingQueryService jpaPagingQueryService;

    @Cacheable(key = "'allInfo'")
    public List<PatchDetailPo> queryAllPatchInfos() {
        return patchInfoRepository.findAll();
    }

    public Page<PatchDetailPo> queryPatchInfoByPaging(QueryParam queryParam) {
        return patchInfoRepository.findAll(jpaPagingQueryService.getSpecificationByQueryParam(queryParam),
            jpaPagingQueryService.getPageRequest(queryParam));
    }

    @Cacheable(key = "'allPatchNames'")
    public List<String> queryPatchName() {
        return patchInfoRepository.queryPatchName();
    }

    @CacheEvict(allEntries = true)
    public void saveAllPatchDetail(List<PatchDetailPo> patchDetails) {
        patchInfoRepository.saveAll(patchDetails);
    }


    @Cacheable(key = "'allFullPatch'")
    public List<PatchDetailPo> findByIsFullPatch(int isFullPatch) {
        return patchInfoRepository.findByIsFullPatch(isFullPatch);
    }

    @Cacheable(key = "#p0+':'+#p1")
    public List<PatchDetailPo> findByServiceAndBaseVersion(String service, String baseVersion) {
        return patchInfoRepository.findByServiceAndBaseVersion(service, baseVersion);
    }

    @Cacheable(key = "#p0+':'+#p1+':'+#p2")
    public List<PatchDetailPo> findByPatchNameAnderviceAndBaseVersion(String patchName, String service,
        String baseVersion) {
        return patchInfoRepository.findByPatchNameAndServiceAndBaseVersion(patchName, service, baseVersion);
    }

    @Cacheable(key = "'allPatchExceptScheme'")
    public List<PatchDetailPo> queryAllPatchExceptScheme() {
        return patchInfoRepository.queryAllPatchExceptScheme();
    }

    @Cacheable(key = "#p0+':'+#p1+'no-scheme-no-repository'")
    public List<PatchDetailPo> findByServiceAndBaseVersionNotSchemeAndRepository(String service, String baseVersion) {
        List<PatchDetailPo> byServiceAndBaseVersion =
            patchInfoRepository.findByServiceAndBaseVersion(service, baseVersion);
        return byServiceAndBaseVersion.stream()
            .filter(e -> !StringUtils.containsIgnoreCase(e.getPatchName(), Constants.SCHEMA_PATCH)
                && !StringUtils.containsIgnoreCase(e.getPatchName(), Constants.REPOSITORY_PATCH))
            .collect(Collectors.toList());
    }

    @Cacheable(key = "#p0+':'+#p1+'scheme'")
    public List<PatchDetailPo> findSchemaByServiceAndBaseVersion(String service, String baseVersion) {
        List<PatchDetailPo> byServiceAndBaseVersion =
            patchInfoRepository.findByServiceAndBaseVersion(service, baseVersion);
        return byServiceAndBaseVersion.stream()
            .filter(e -> StringUtils.containsIgnoreCase(e.getPatchName(), Constants.SCHEMA_PATCH))
            .collect(Collectors.toList());
    }

    @Cacheable(key = "#p0+':container'")
    public List<PatchDetailPo> findContainerPatchByService(String service) {
        List<PatchDetailPo> patchDetailPos = patchInfoRepository.findByService(service);
        if (!CollectionUtils.isEmpty(patchDetailPos)) {
            List<PatchDetailPo> containerPatches = patchDetailPos.stream()
                .filter(patchDetailPo -> patchDetailPo.getIsContainerPatch() == 1).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(containerPatches)) {
                return containerPatches;
            }
        }
        return Lists.newArrayList();

    }
}