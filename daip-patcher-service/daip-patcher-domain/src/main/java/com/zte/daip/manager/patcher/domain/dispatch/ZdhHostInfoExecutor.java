/**
 * <p>
 * <owner>10242220</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ZdhHostInfoExecutor.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/13
 * </p>
 * <p>
 * 完成日期：2021/4/13
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.dispatch;

import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.patcher.domain.dispatch.bean.DispatcherParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

import static java.util.stream.Collectors.toSet;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10242220
 * @version 1.0
 */
@Service
@Slf4j
public class ZdhHostInfoExecutor extends HostInfoExecutor {

    @Autowired
    private DefaultHostInfoExecutor defaultHostInfoExecutor;

    @Autowired
    private SpectControllerApi spectControllerApi;

    @Override
    Set<HostInfo> query(String projectName, String version, DispatcherParam dispatcherParam) {
        List<ProductSpect> productSpects = spectControllerApi.queryByVersionAndProjectName(version, projectName);

        if (CollectionUtils.isEmpty(productSpects)) {
            log.info("Query Spects empty! projectName:{} version:{}", projectName, version);
            return Sets.newHashSet();
        }

        Set<String> serviceNames = productSpects.stream().map(ProductSpect::getServiceName).collect(toSet());

        return serviceNames.stream()
            .flatMap(serviceName -> defaultHostInfoExecutor.query(serviceName, version, dispatcherParam).stream())
            .collect(toSet());
    }
}