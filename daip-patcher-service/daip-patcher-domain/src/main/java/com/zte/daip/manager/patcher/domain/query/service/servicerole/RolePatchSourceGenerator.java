/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: RolePatchSourceGenerator.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/8
 * </p>
 * <p>
 * 完成日期：2021/4/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.service.servicerole;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.model.RoleModel;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.domain.query.valobj.NeedPatchServiceInfo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service("RolePatchSourceGenerator")
@Slf4j
public class RolePatchSourceGenerator extends AbstractPatchSourceGenerator {

    private Map<String, String> roleName2Id = new ConcurrentHashMap<>();

    private static final PatchTypeEnum patchTypeEnum = PatchTypeEnum.ROLE;

    @Override
    /**
     * 功能说明：获取未打补丁的服务角色列表。
     *
     * 业务背景：根据补丁详情和服务模型，查询当前未应用该补丁的服务角色，用于后续的补丁分发和状态跟踪。
     *
     * @param patchDetailDto 补丁详情数据传输对象，包含补丁名称、服务名等关键信息。
     * @param serviceModel 服务模型，描述服务的配置和元数据。
     * @return 返回未打补丁的服务角色列表。
     */
    public List<ServiceRoleInfo> obtainUnpatchedRoles(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {

        List<String> roleIds = getRoleIds(serviceModel, patchDetailDto.getRoles());

        final NeedPatchServiceInfo needPatchServiceInfo = patch2ServiceInfo.computeIfAbsent(
            generatePatch2ServiceInfoKey(patchDetailDto), k -> obtainAllRoles(patchDetailDto, serviceModel));

        List<ServiceRoleInfo> roleInfos = needPatchServiceInfo.getServiceRoleInfos();

        List<ServiceRoleInfo> filterResult =
            roleInfos.stream().filter(roleInfo -> roleIds.contains(roleInfo.getRoleId())).collect(Collectors.toList());

        return filterRolesUpdatedPatch(filterResult, patchDetailDto, serviceModel.getServiceId());
    }

    @Override
    /**
     * 功能说明：查询补丁的存储路径。
     *
     * 业务背景：确定补丁文件在服务器上的存储位置，确保补丁能够正确部署到目标主机。
     *
     * @param patchHomeParam 包含集群ID、服务名等参数，用于定位补丁存储路径。
     * @return 返回补丁的根目录路径。
     * @throws DaipBaseException 当无法确定补丁路径时抛出异常。
     */
    public String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException {
        return "";
    }

    @Override
    public List<RollAutoPatchUpdateInfo> obtainPatchUpdateInfos(RollAutoPatchUpdateParam rollingPatchUpdateParam,
        String version, List<String> updatedContainerPatches) {
        return Lists.newArrayList();
    }

    @Override
    /**
     * 功能说明：计算未打补丁的主机数量。
     *
     * 业务背景：统计当前补丁未应用的主机数量，用于评估补丁覆盖范围和更新进度。
     *
     * @param patchDetailDto 补丁详情数据传输对象。
     * @param serviceModel 服务模型。
     * @return 返回未打补丁的主机数量。
     */
    public int obtainUnpatchedHostNum(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {
        final List<ServiceRoleInfo> serviceRoleInfos = obtainUnpatchedRoles(patchDetailDto, serviceModel);

        return serviceRoleInfos.stream()
            .map(serviceRoleInfo -> serviceRoleInfo.getIpAddress() + getRoleName(serviceRoleInfo))
            .collect(Collectors.toSet()).size();
    }

    @Override
    /**
     * 功能说明：生成服务ID和补丁历史记录的唯一键。
     *
     * 业务背景：组合服务ID、角色ID、IP地址和实例ID，生成唯一标识符，用于跟踪补丁状态。
     *
     * @param serviceId 服务标识符。
     * @param patchHistory 补丁历史记录。
     * @return 返回生成的唯一键字符串。
     */
    protected String generateKey(String serviceId, PatchHistory patchHistory) {
        return super.generateKey(serviceId,
            roleName2Id.get(StringUtils.defaultIfEmpty(patchHistory.getId().getRoleName(), "")),
            patchHistory.getId().getIp(), "");
    }

    @Override
    /**
     * 功能说明：生成服务角色信息的唯一键。
     *
     * 业务背景：基于服务角色信息中的关键字段生成唯一键，用于缓存和状态管理。
     *
     * @param roleInfo 服务角色信息。
     * @return 返回生成的唯一键字符串。
     */
    protected String generateKey(ServiceRoleInfo roleInfo) {
        return generateKey(roleInfo.getServiceId(), roleInfo.getRoleId(), roleInfo.getIpAddress(), "");
    }

    @Override
    protected String generatePatch2ServiceInfoKey(PatchDetailDto patchDetailDto) {
        return String.format("%s_%s_%s", patchDetailDto.getService(), patchDetailDto.getRoles(),
            patchDetailDto.getBaseVersion());
    }

    @Override
    protected List<String> queryVersionIps(PatchDetailDto patchDetailDto, ServiceModel serviceModel) {
        final List<String> roleArr = Splitter.on(",").omitEmptyStrings().splitToList(patchDetailDto.getRoles());
        return queryVersionIps(roleArr, patchDetailDto);
    }

    private List<String> queryVersionIps(List<String> roleArr, PatchDetailDto patchDetailDto) {
        List<String> allIps = Lists.newArrayList();
        for (String roleName : roleArr) {
            List<String> oneRoleIps = queryServiceVersion2Ip()
                .get(patchDetailDto.getService() + roleName + "_" + patchDetailDto.getBaseVersion());
            if (!CollectionUtils.isEmpty(oneRoleIps)) {
                allIps.addAll(oneRoleIps);
            } else {
                List<String> serviceIps =
                    queryServiceVersion2Ip().get(patchDetailDto.getService() + "_" + patchDetailDto.getBaseVersion());
                if (!CollectionUtils.isEmpty(serviceIps)) {
                    allIps.addAll(serviceIps);
                }
            }
        }
        return allIps;
    }

    /**
     * 功能说明：获取角色名称对应的角色ID列表。
     *
     * 业务背景：根据服务模型和角色名称列表，提取对应的角色ID，用于后续的角色级处理。
     *
     * @param serviceModel 服务模型，包含角色信息。
     * @param roles 角色名称列表，逗号分隔。
     * @return 返回角色ID列表。
     */
    private List<String> getRoleIds(ServiceModel serviceModel, String roles) {
        for (RoleModel role : serviceModel.getRoles()) {
            roleName2Id.put(role.getRoleName(), role.getRoleId());
        }

        final List<String> roleNameArr = Splitter.on(",").omitEmptyStrings().splitToList(roles);
        List<String> roleIds = Lists.newArrayList();
        for (String roleName : roleNameArr) {
            String roleId = roleName2Id.get(roleName);
            if (StringUtils.isNotEmpty(roleId)) {
                roleIds.add(roleId);
            }
        }
        return roleIds;
    }

    /**
     * 功能说明：从服务角色信息中提取角色名称。
     *
     * 业务背景：若角色名称为空，则使用角色ID的最后部分作为显示名称，增强可读性。
     *
     * @param roleInfo 服务角色信息。
     * @return 返回角色名称或截取的角色ID部分。
     */
    private String getRoleName(ServiceRoleInfo roleInfo) {
        if (StringUtils.isNotBlank(roleInfo.getRoleName())) {
            return roleInfo.getRoleName();
        }
        return roleInfo.getRoleId().substring(roleInfo.getRoleId().lastIndexOf(".") + 1) + ")";
    }

}