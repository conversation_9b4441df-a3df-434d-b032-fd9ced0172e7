/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UnUpdatePatchParam.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/10
 * </p>
 * <p>
 * 完成日期：2021/4/10
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.valobj;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnpatchedParam {
    private Map<PatchKeyDo, List<PatchHistory>> patchHistoryInfoMap = Maps.newHashMap();
    private List<ServiceModel> serviceModels = Lists.newArrayList();
    private Map<String, String> hostIp2HostName = Maps.newHashMap();
    private List<ServiceRoleInfo> serviceRoleInfos = Lists.newArrayList();
    private Map<String, List<String>> serviceVersion2Ip = Maps.newHashMap();
    private boolean isFilterHost;

}