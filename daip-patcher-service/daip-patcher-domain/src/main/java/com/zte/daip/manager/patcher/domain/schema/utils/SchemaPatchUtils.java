/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: SchemaPatchUtils.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/30
 * </p>
 * <p>
 * 完成日期：2021/3/30
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.schema.utils;

import java.io.File;
import java.io.IOException;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.loadmodel.Product;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.schema.SchemaAction;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchRegisterRequest;
import com.zte.daip.manager.patcher.api.schema.UpdateSchemaPatchFile;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.UnzipFileApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Slf4j
@Service
public class SchemaPatchUtils {

    @Autowired
    private UnzipFileApi unzipFileApi;

    @Autowired
    private SchemaPatchApi schemaPatchApi;

    @Autowired
    protected SpectControllerApi spectControllerApi;

    @Autowired
    protected ProductControllerApi productControllerApi;

    @Autowired
    private PatchHistoryService patchHistoryService;

    @Autowired
    private PatchInfoService patchInfoService;

    public static final String ZDH_PRODUCT_SERVICE_NAME = "common";

    public void downloadAndUnzipSchemaPatch(PatchTaskInfo patchTaskInfo, SchemaAction schemaAction) throws IOException {
        String serviceName = patchTaskInfo.getServiceName();
        String version = patchTaskInfo.getVersion();
        List<String> schemaPatchNames = sortedSchemaPatchNames(patchTaskInfo.getUpdatePatchNames(), schemaAction);
        String targetPath = queryTargetPath(serviceName, version);
        for (String patchName : schemaPatchNames) {
            String patchFilePath = queryPatchPath(serviceName, patchName);
            unzipFileApi.unzipFile(FilePathCleaner.newFile(patchFilePath), targetPath);
        }
    }

    public List<String> sortedSchemaPatchNames(List<String> schemaPatchNames, SchemaAction schemaAction) {
        if (schemaAction.isUpdate()) {
            return schemaPatchNames.stream().sorted(Comparator.comparing(String::toLowerCase))
                .collect(Collectors.toList());
        } else {
            return schemaPatchNames.stream()
                .sorted(Comparator.comparing(String::toLowerCase, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        }
    }

    public void deleteSchemaPatch(String serviceName, String version) {
        File patchDir = FilePathCleaner.newFile(schemaPatchApi.queryPatchHome(serviceName, version));
        if (patchDir.isDirectory()) {
            FileUtils.deleteQuietly(patchDir);
        }
    }

    private String queryPatchPath(String serviceName, String patchName) {
        return schemaPatchApi.queryPatchPath(serviceName, patchName);
    }

    private String queryTargetPath(String serviceName, String version) {
        return schemaPatchApi.queryTargetPath(serviceName, version);
    }

    public void persistInDb(PatchTaskInfo patchTaskInfo, SchemaAction schemaAction) {
        List<PatchDetailPo> schemaPatchDetailPos = patchInfoService
            .findSchemaByServiceAndBaseVersion(patchTaskInfo.getServiceName(), patchTaskInfo.getVersion());

        List<PatchDetailPo> needUpdatePatches = schemaPatchDetailPos.stream()
            .filter(patchDetailPo -> patchTaskInfo.getUpdatePatchNames().contains(patchDetailPo.getPatchName()))
            .collect(Collectors.toList());

        List<PatchHistory> patchHistoryList =
            needUpdatePatches.stream().map(this::convertToPatchHistory).collect(Collectors.toList());

        if (schemaAction.isUpdate()) {
            patchHistoryService.saveBatch(patchHistoryList);
        } else {
            patchHistoryService.deleteByPatchHistoryList(patchHistoryList);
        }
    }

    private PatchHistory convertToPatchHistory(PatchDetailPo patchDetail) {
        PatchHistory history = new PatchHistory();
        history.setPatchUptime(System.currentTimeMillis());

        PatchHistoryKey historyKey = new PatchHistoryKey();
        historyKey.setIp("");
        historyKey.setServiceName(patchDetail.getService());
        historyKey.setPatchName(patchDetail.getPatchName());
        historyKey.setServiceInstanceId("");
        historyKey.setRoleName("");
        history.setId(historyKey);

        return history;
    }

    public PatchOperateResult registerSchemaPatchService(SchemaPatchRegisterRequest schemaPatchRegisterRequest) {
        try {
            schemaPatchApi.saveSchemaRegisterInfos(schemaPatchRegisterRequest);
            return new PatchOperateResult(true, "");
        } catch (Exception e) {
            return new PatchOperateResult(false, "");
        }
    }

    public String queryCurrentPatchName(String serviceName, String version) {
        List<PatchDetailPo> patchDetails = patchInfoService.queryAllPatchInfos();

        List<String> updatedSchemaPatch = patchHistoryService.querySchemaPatchHistory();
        List<String> updatedPatchNames = patchDetails.stream()
            .filter(patchDetail -> patchDetail.getPatchName().contains(Constants.SCHEMA_PATCH)
                && updatedSchemaPatch.contains(patchDetail.getPatchName())
                && StringUtils.equals(patchDetail.getService(), serviceName)
                && StringUtils.equals(patchDetail.getBaseVersion(), version))
            .sorted(Comparator.comparing(a -> a.getPatchName().toLowerCase())).map(PatchDetailPo::getPatchName)
            .collect(Collectors.toList());
        if (updatedPatchNames.size() > 0) {
            return updatedPatchNames.get(updatedPatchNames.size() - 1);
        } else {
            return "";
        }
    }

    public Set<UpdateSchemaPatchFile> queryAllSchemaPatchFiles(String serviceName, String version) {
        String targetPath = schemaPatchApi.queryTargetPath(serviceName, version);

        Set<UpdateSchemaPatchFile> allUpdateSchemaPatchFiles = Sets.newHashSet();

        searchSchemaFiles(targetPath, allUpdateSchemaPatchFiles);

        return allUpdateSchemaPatchFiles.stream().filter(FilterUpdateSchemaFiles::filterSchemaPatchFile)
            .collect(Collectors.toSet());
    }

    private void searchSchemaFiles(String directoryPath, Set<UpdateSchemaPatchFile> allSchemaPatchFiles) {

        File directory = new File(directoryPath);

        if (!directory.exists()) {
            return;
        }

        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    searchSchemaFiles(file.getAbsolutePath(), allSchemaPatchFiles);
                } else {
                    allSchemaPatchFiles.add(new UpdateSchemaPatchFile(file.getParent(), file.getName()));
                }
            }
        }
    }

    public Set<UpdateSchemaPatchFile> searchSchemaPatchFiles(SchemaPatchRegisterRequest schemaPatchRegisterRequest,
        Set<UpdateSchemaPatchFile> allUpdateSchemaPatchFiles) {

        List<UpdateSchemaPatchFile> registerSchemaPatchFiles = schemaPatchRegisterRequest.getRegisterSchemaPatchFiles();
        Set<UpdateSchemaPatchFile> updateSchemaPatchFiles = Sets.newHashSet();

        for (UpdateSchemaPatchFile registerSchemaPatchFile : registerSchemaPatchFiles) {
            for (UpdateSchemaPatchFile updateSchemaPatchFile : allUpdateSchemaPatchFiles) {

                boolean directoryExist = checkSchemaFileExist(registerSchemaPatchFile.getUpdatePatchFilePath(),
                    updateSchemaPatchFile.getUpdatePatchFilePath());
                if (!directoryExist) {
                    log.info("search schema patch path,thirdService={},registerPath={},schemaPath={},result={}",
                        schemaPatchRegisterRequest.getServiceName(), registerSchemaPatchFile.getUpdatePatchFilePath(),
                        updateSchemaPatchFile.getUpdatePatchFilePath(), false);
                    continue;
                }

                boolean fileExist = checkSchemaFileExist(registerSchemaPatchFile.getUpdatePatchFileName(),
                    updateSchemaPatchFile.getUpdatePatchFileName());

                log.info(
                    "search schema patch path and file,thirdService={},registerPath={},schemaPath={},registerFile={},schemaFile={},result={}",
                    schemaPatchRegisterRequest.getServiceName(), registerSchemaPatchFile.getUpdatePatchFilePath(),
                    updateSchemaPatchFile.getUpdatePatchFilePath(), registerSchemaPatchFile.getUpdatePatchFileName(),
                    updateSchemaPatchFile.getUpdatePatchFileName(), fileExist);

                if (fileExist) {
                    updateSchemaPatchFiles.add(updateSchemaPatchFile);
                }
            }
        }
        return updateSchemaPatchFiles;
    }

    private boolean checkSchemaFileExist(String registerPath, String schemaPath) {
        Pattern pattern = Pattern.compile(registerPath);

        if (StringUtils.isEmpty(registerPath)) {
            return true;
        }
        Matcher directorMatcher = pattern.matcher(schemaPath);
        return directorMatcher.matches();
    }

    public boolean isProductLoadSuccess(String serviceName, String version) {
        if (StringUtils.equals(Constants.ZDH_SERVICE, serviceName)){
            serviceName = ZDH_PRODUCT_SERVICE_NAME;
        }
        ProductSpect productSpect = spectControllerApi.queryByServiceNameAndVersionNo(serviceName, version);
        if (null == productSpect) {
            return false;
        }
        Product product = productControllerApi.queryByFileName(productSpect.getProductFileName());
        return null != product && StringUtils.equals(product.getCheckResult(), "true");
    }

    public boolean checkTaskPatchDependency(String serviceName, String version, List<String> needUpdatePatches) {
        List<PatchDetailPo> patchDetailList = patchInfoService.findByServiceAndBaseVersion(serviceName, version);

        List<PatchDetailPo> needUpdatePatchDetails =
            patchDetailList.stream().filter(patchDetailPo -> needUpdatePatches.contains(patchDetailPo.getPatchName()))
                .collect(Collectors.toList());

        Set<String> notUpdateTaskPatchNames = queryNotUpdateTaskPatchNames(serviceName, version, needUpdatePatches);

        Set<String> patchDetailNames =
            patchDetailList.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toSet());

        patchDetailNames.removeAll(notUpdateTaskPatchNames);

        return needUpdatePatchDetails.stream().allMatch(patchDetail -> StringUtils.isEmpty(patchDetail.getDependPatch())
            || patchDetailNames.contains(patchDetail.getDependPatch()));
    }

    private Set<String> queryNotUpdateTaskPatchNames(String serviceName, String version,
        List<String> needUpdatePatches) {
        List<String> taskPatchNames = schemaPatchApi.queryPatchInfosByService(serviceName, version);

        List<String> updatedPatchNames = patchHistoryService.querySchemaPatchHistory();

        return taskPatchNames.stream().filter(s -> !updatedPatchNames.contains(s) && !needUpdatePatches.contains(s))
            .collect(Collectors.toSet());
    }

    public PatchOperateResult checkBeforeUpdatePatches(PatchTaskInfo patchTaskInfo) {
        String serviceName = patchTaskInfo.getServiceName();
        String version = patchTaskInfo.getVersion();
        List<String> updatePatchNames = patchTaskInfo.getUpdatePatchNames();
        PatchOperateResult patchOperateResult = new PatchOperateResult();

        if (!isProductLoadSuccess(serviceName, version)) {
            patchOperateResult.setStatus(false);
            String msg = String.format("service=%s,version=%s,product not load", serviceName, version);
            patchOperateResult.setMessage(msg);
            log.error(msg);
            return patchOperateResult;
        }

        if (!checkTaskPatchDependency(serviceName, version, updatePatchNames)) {
            patchOperateResult.setStatus(false);
            String msg = String.format("service=%s,version=%s,patchNames=%s,check dependency failed", serviceName,
                version, updatePatchNames.toString());
            patchOperateResult.setMessage(msg);
            log.error(msg);
            return patchOperateResult;
        }
        patchOperateResult.setStatus(true);
        return patchOperateResult;
    }

    public PatchOperateResult checkBeforeRollbackPatches(PatchTaskInfo patchTaskInfo) {
        return checkBeforeUpdatePatches(patchTaskInfo);
    }

    public boolean checkPatchesHasUpdated(PatchTaskInfo patchTaskInfo) {
        List<String> allSchemaPatches = patchHistoryService.querySchemaPatchHistory();
        List<String> needUpdatePatches = patchTaskInfo.getUpdatePatchNames().stream()
            .filter(s -> !allSchemaPatches.contains(s)).collect(Collectors.toList());
        return needUpdatePatches.isEmpty();
    }
}