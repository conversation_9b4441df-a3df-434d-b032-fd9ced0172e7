/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDistributeService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/6
 * </p>
 * <p>
 * 完成日期：2023/3/6
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.dispatch;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.common.deployer.model.controller.api.ClusterVersionControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.event.reporter.api.annotation.BusinessDomainEvent;
import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;
import com.zte.daip.manager.filemanagement.api.bean.DistributeFileBean;
import com.zte.daip.manager.filemanagement.api.bean.DistributeFileParam;
import com.zte.daip.manager.filemanagement.api.bean.FileCommandBean;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchDistributeBean;
import com.zte.daip.manager.patcher.api.dto.PatchDistributeDto;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.constants.DomainI18nKey;
import com.zte.daip.manager.patcher.domain.query.service.PatchDetailService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatch;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchDispatchRepository;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchDistributeService {

    @Autowired
    private ClusterInfoControllerApi clusterInfoControllerApi;
    @Autowired
    private FileDistributeControllerApi fileDistributeControllerApi;
    @Autowired
    private HostResourceControllerApi hostResourceControllerApi;
    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;
    @Autowired
    private ClusterVersionControllerApi clusterVersionControllerApi;
    @Autowired
    private PatchDispatchRepository patchDispatchRepository;
    @Autowired
    private ServiceDeployedExecutor serviceDeployedExecutor;
    @Autowired
    private PatchDetailService patchDetailService;
    @Autowired
    private PatchEnvApi patchEnvApi;
    @Autowired
    private DistributeProgressService distributeProgressService;
    @Autowired
    private DaipEventReporter daipEventReporter;

    public String distributePatches(PatchDistributeDto patchDistributeDto) throws DaipBaseException {
        try {
            log.info("start distribute patches.");
            String clusterId = patchDistributeDto.getClusterId();
            List<PatchDistributeBean> patchDistributeBeans = patchDistributeDto.getPatchDistributeBeans();
            List<PatchDetailDto> patchDetailDtos = patchDetailService.queryAllPatch();
            List<PatchDetailDto> filterPatchDetailDtos = patchDetailDtos.stream()
                .filter(dto -> !dto.getPatchName().contains(Constants.REPOSITORY_PATCH)).collect(Collectors.toList());
            Map<String, List<String>> filterInfo = buildFilterParam(patchDistributeBeans);
            Map<PatchDetailDto, Set<HostInfo>> tmpInfo = buildDistributeInfo(filterPatchDetailDtos, clusterId);
            Map<PatchDetailDto, Set<HostInfo>> targetHostMap = filter4PatchHosts(filterInfo, tmpInfo);
            if (!targetHostMap.isEmpty()) {
                log.info("cluster:{} need distribute patch:{}", clusterId, targetHostMap.toString());
                DistributeFileParam distributeFileParam = getDistributeFileParam(patchDistributeDto, targetHostMap);
                return fileDistributeControllerApi.distributeFiles(distributeFileParam);
            } else {
                log.info("no patch need to distribute.");
                daipEventReporter.info(DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH,
                    DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH_NO_NEED);
            }
        } catch (Exception e) {
            throw new DaipBaseException("Executor distribute patch error.");
        }
        throw new DaipBaseException("Executor distribute patch error.");
    }

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH)
    public void autoUploadDistribute(List<PatchDetailDto> patchDetailDtos) throws DaipBaseException {
        try {
            log.info("start to auto distribute patches.");
            List<PatchDetailDto> filterPatchDetailDtos = patchDetailDtos.stream()
                .filter(dto -> !dto.getPatchName().contains(Constants.REPOSITORY_PATCH)).collect(Collectors.toList());
            List<String> clusterIds = clusterInfoControllerApi.queryAll().stream()
                .map(clusterBean -> clusterBean.getClusterId().toString()).collect(Collectors.toList());
            for (String clusterId : clusterIds) {
                Map<PatchDetailDto, Set<HostInfo>> patch2HostInfos =
                    buildDistributeInfo(filterPatchDetailDtos, clusterId);
                if (!patch2HostInfos.isEmpty()) {
                    log.info("cluster:{} need distribute patch:{}", clusterId, JSON.toJSONString(patch2HostInfos));
                    List<DistributeFileBean> distributeFileBeans = buildDistributeFileBeans(patch2HostInfos);
                    if (!CollectionUtils.isEmpty(distributeFileBeans)) {
                        DistributeFileParam distributeFileParam = new DistributeFileParam();
                        distributeFileParam.setDistributeFileBeans(distributeFileBeans);
                        String distributeId = fileDistributeControllerApi.distributeFiles(distributeFileParam);
                        distributeProgressService.checkAndUpdateDistributeProgress(distributeId);
                    }
                } else {
                    log.info("cluster:{} no patch need distribute.", clusterId);
                    daipEventReporter.info(DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH,DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH_NO_NEED);
                }
            }
        } catch (Exception e) {
            daipEventReporter.error(DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH, "");
            throw new DaipBaseException("Executor auto distribute patch error.", e);
        }

    }

    public PatchOperateResult dispatchPatchesPrecheck() {
        final List<PatchBean> dispatchPatchList = patchDetailService.queryNeedDispatchExceptSchemaPatch();

        List<ServiceModel> serviceModels = productModelInfoControllerApi.queryAllServiceModels();

        List<ClusterProject> clusterProjects = clusterVersionControllerApi.queryAllVersions();

        Set<String> allServiceVersion = clusterProjects.stream()
            .map(e -> e == null ? "" : String.format("%s_%s", e.getServiceName(), e.getVersion()))
            .collect(Collectors.toSet());

        Set<String> zdhServiceVersion = serviceModels.stream()
            .filter(e -> e != null && StringUtils.equals(e.getComponentType(), Constants.BIG_DATA_SERVICE_ID)
                && allServiceVersion.contains(String.format("%s_%s", e.getServiceName(), e.getVersion())))
            .map(o -> String.format("%s_%s", Constants.ZDH_SERVICE, o.getVersion())).collect(Collectors.toSet());

        allServiceVersion.addAll(zdhServiceVersion);

        String incorrectVersionPatch = dispatchPatchList.stream().filter(
            e -> e != null && !allServiceVersion.contains(String.format("%s_%s", e.getService(), e.getSrcVersion())))
            .map(PatchBean::getPatchName).collect(Collectors.joining(","));

        return new PatchOperateResult(StringUtils.isBlank(incorrectVersionPatch), incorrectVersionPatch);
    }

    private Map<PatchDetailDto, Set<HostInfo>> filter4PatchHosts(Map<String, List<String>> filterInfo,
        Map<PatchDetailDto, Set<HostInfo>> tmpInfo) {
        Map<PatchDetailDto, Set<HostInfo>> targetHosts = Maps.newHashMap();
        tmpInfo.forEach(((patchDetailDto, hostInfos) -> {
            if (filterInfo.containsKey(patchDetailDto.getService())) {
                Set<HostInfo> targetHost = hostInfos.stream()
                    .filter(hostInfo -> filterInfo.get(patchDetailDto.getService()).contains(hostInfo.getIpAddress()))
                    .collect(Collectors.toSet());
                targetHosts.put(patchDetailDto, targetHost);
            }
        }));
        return targetHosts;
    }

    private DistributeFileParam getDistributeFileParam(PatchDistributeDto patchDistributeDto,
        Map<PatchDetailDto, Set<HostInfo>> patch2HostInfos) throws DaipBaseException {
        DistributeFileParam distributeFileParam = new DistributeFileParam();
        distributeFileParam.setUserName(patchDistributeDto.getUserName());
        distributeFileParam.setPassword(patchDistributeDto.getPassword());
        distributeFileParam.setSshPort(patchDistributeDto.getSshPort());
        List<DistributeFileBean> distributeFileBeans = buildDistributeFileBeans(patch2HostInfos);
        distributeFileParam.setDistributeFileBeans(distributeFileBeans);
        return distributeFileParam;
    }

    private Map<String, List<String>> buildFilterParam(List<PatchDistributeBean> patchDistributeBeans) {
        Map<String, List<String>> service4Hosts = Maps.newHashMap();
        patchDistributeBeans.forEach(patchDispatchBean -> {
            String service = patchDispatchBean.getService();
            if (service4Hosts.containsKey(service)) {
                List<String> ipAddresses = Lists.newArrayList(service4Hosts.get(service));
                ipAddresses.addAll(patchDispatchBean.getIpAddresses());
                service4Hosts.put(service, ipAddresses);
            } else {
                service4Hosts.put(service, patchDispatchBean.getIpAddresses());
            }
        });
        log.debug("buildFilterParam result :{}", service4Hosts.toString());
        return service4Hosts;
    }

    private List<DistributeFileBean> buildDistributeFileBeans(Map<PatchDetailDto, Set<HostInfo>> patch2HostInfos)
        throws DaipBaseException {

        List<DistributeFileBean> distributeFileBeans = Lists.newArrayList();

        for (Map.Entry<PatchDetailDto, Set<HostInfo>> entry : patch2HostInfos.entrySet()) {
            PatchDetailDto patchDetailDto = entry.getKey();
            if (patchDetailDto == null || StringUtils.contains(patchDetailDto.getPatchName(), "-schema-")) {
                continue;
            }
            DistributeFileBean distributeFileBean = new DistributeFileBean();
            distributeFileBean
                .setTargethost(entry.getValue().stream().map(HostInfo::getHostName).collect(Collectors.toList()));
            distributeFileBean.setSrcBasePath(patchEnvApi.getRepositoryHomeEnv());
            String file = "patch" + File.separator + patchDetailDto.getService() + File.separator
                + patchDetailDto.getPatchName() + File.separator + patchDetailDto.getPatchName() + ".zip";
            distributeFileBean.setFiles(Lists.newArrayList(FileCommandBean.builder().file(file).build()));
            distributeFileBean.setTargetBasePath(patchEnvApi.getRepositoryHomeEnv());
            distributeFileBean.setUrl(patchEnvApi.getPatchUrl());
            distributeFileBeans.add(distributeFileBean);
        }

        return distributeFileBeans;
    }

    private Map<PatchDetailDto, Set<HostInfo>> buildDistributeInfo(List<PatchDetailDto> allPatches, String clusterId) {
        Map<PatchDetailDto, Set<HostInfo>> needDispatchPatch = new HashMap<>(20);
        Map<String, List<PatchDispatch>> distributedInfo = buildDistributedPatchMap(clusterId);
        Map<String, Map<String, Set<HostInfo>>> serviceInfo = serviceDeployedExecutor.getServiceInfo(clusterId);

        for (PatchDetailDto patchDetail : allPatches) {
            Set<HostInfo> targetHosts = buildTargetHosts(patchDetail, distributedInfo, serviceInfo);
            if (!CollectionUtils.isEmpty(targetHosts)) {
                needDispatchPatch.put(patchDetail, targetHosts);
            }
        }
        log.info("needDispatchPatch: {}", needDispatchPatch.toString());
        return needDispatchPatch;
    }

    private Set<HostInfo> buildTargetHosts(PatchDetailDto patchDetail, Map<String, List<PatchDispatch>> distributedInfo,
        Map<String, Map<String, Set<HostInfo>>> serviceInfo) {

        Set<HostInfo> targetHost = new HashSet<>();
        if (serviceInfo.containsKey(patchDetail.getService())) {
            Map<String, Set<HostInfo>> ipsByVersion = serviceInfo.get(patchDetail.getService());
            String version = patchDetail.getBaseVersion();
            if (ipsByVersion.containsKey(version)) {
                targetHost = ipsByVersion.get(version);
            }
        }
        if (distributedInfo.containsKey(patchDetail.getPatchName()) && !CollectionUtils.isEmpty(targetHost)) {
            Set<String> distributedIps = distributedInfo.get(patchDetail.getPatchName()).stream()
                .map(patchDispatch -> patchDispatch.getId().getIp()).collect(Collectors.toSet());
            targetHost = targetHost.stream().filter(hostInfo -> !distributedIps.contains(hostInfo.getIpAddress()))
                .collect(Collectors.toSet());
        }
        return targetHost;
    }

    private Map<String, List<PatchDispatch>> buildDistributedPatchMap(String clusterId) {
        List<String> ips = hostResourceControllerApi.queryByClusterId(clusterId).stream().map(HostInfo::getIpAddress)
            .collect(Collectors.toList());
        List<PatchDispatch> patchDispatches = patchDispatchRepository.queryByIpList(ips).stream()
            .filter(PatchDispatch::isSuccess).collect(Collectors.toList());
        return patchDispatches.stream()
            .collect(Collectors.groupingBy(patchDispatch -> patchDispatch.getId().getPatchName()));
    }

}