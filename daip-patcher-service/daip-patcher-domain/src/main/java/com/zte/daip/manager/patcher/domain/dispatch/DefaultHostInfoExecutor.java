/**
 * <p>
 * <owner>10242220</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: DefaultHostInfoExecutor.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/13
 * </p>
 * <p>
 * 完成日期：2021/4/13
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.dispatch;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.patcher.domain.dispatch.bean.DispatcherParam;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toSet;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10242220
 * @version 1.0
 */
@Service
@Slf4j
public class DefaultHostInfoExecutor extends HostInfoExecutor {

    @Override
    Set<HostInfo> query(String serviceName, String version, DispatcherParam dispatcherParam) {

        List<ServiceRoleInfo> serviceRoleInfos = queryServiceRoleInfo(serviceName, dispatcherParam);
        List<HostInfo> hostInfos = dispatcherParam.getHostInfos();

        if (CollectionUtils.isEmpty(serviceRoleInfos) || CollectionUtils.isEmpty(hostInfos)) {
            log.info("Query serviceRoleInfos or hostInfos empty.serviceName:{}", serviceName);
            return Sets.newHashSet();
        }
        Set<String> allClusterIds = queryClusterId(serviceName, version, dispatcherParam);

        Set<String> hostIps = serviceRoleInfos.stream().filter(e -> allClusterIds.contains(e.getClusterId()))
            .map(ServiceRoleInfo::getIpAddress).collect(toSet());

        return hostInfos.stream().filter(host -> hostIps.contains(host.getIpAddress())).collect(toSet());
    }

    private List<ServiceRoleInfo> queryServiceRoleInfo(String serviceName, DispatcherParam dispatcherParam) {
        List<ServiceModel> serviceModels = dispatcherParam.getServiceModels().stream()
            .filter(serviceModel -> StringUtils.equalsIgnoreCase(serviceName, serviceModel.getServiceName()))
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(serviceModels)) {
            String serviceId = serviceModels.get(0).getServiceId();
            return dispatcherParam.getServiceRoleInfos().stream()
                .filter(serviceRoleInfo -> StringUtils.equalsIgnoreCase(serviceId, serviceRoleInfo.getServiceId()))
                .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private Set<String> queryClusterId(String serviceName, String version, DispatcherParam dispatcherParam) {
        List<ClusterProject> clusterProjects = dispatcherParam.getClusterProjects();
        if (CollectionUtils.isEmpty(clusterProjects)) {
            return Sets.newHashSet();
        }
        return clusterProjects.stream()
            .filter(
                e -> StringUtils.equals(e.getServiceName(), serviceName) && StringUtils.equals(e.getVersion(), version))
            .map(ClusterProject::getClusterId).map(String::valueOf).collect(toSet());
    }
}