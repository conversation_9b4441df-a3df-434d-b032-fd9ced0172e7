package com.zte.daip.manager.patcher.domain.update.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.patcher.api.dto.OnePatchUpdateResult;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateResult;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateRequest;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateFinishedCacheApi;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateInfoCacheApi;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateProgressCacheApi;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateResultCacheApi;
import com.zte.daip.manager.patcher.domain.update.listener.PatchInfoCleanLocalCacheSender;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@Service
public class PatchUpdateCacheService {
    @Autowired
    private PatchUpdateProgressCacheApi patchUpdateProgressCacheApi;
    @Autowired
    private PatchUpdateResultCacheApi patchUpdateResultCacheApi;
    @Autowired
    private PatchUpdateFinishedCacheApi patchUpdateFinishedCacheApi;
    @Autowired
    private PatchUpdateInfoCacheApi patchUpdateInfoCacheApi;
    @Autowired
    private PatchInfoCleanLocalCacheSender patchInfoCleanLocalCacheSender;

    public void initUpdateProgress(String clusterId, PatchUpdateCacheDto patchUpdateCacheDto,
        Set<String> patchResultKeys) {
        String patchUpdateCache = JSONObject.toJSONString(patchUpdateCacheDto);
        patchUpdateProgressCacheApi.updateCache(clusterId, patchUpdateCache);

        List<PatchUpdateResult> patchResults = patchUpdateCacheDto.getPatchResults();
        if (!CollectionUtils.isEmpty(patchResults) && !CollectionUtils.isEmpty(patchResultKeys)) {
            patchResultKeys.forEach(patchKey -> updateFinishedCache(patchKey, false));
        }
    }

    public PatchUpdateCacheDto queryUpdatePatchProgress(String clusterId) {
        String patchResult = patchUpdateProgressCacheApi.queryCacheResult(clusterId);
        PatchUpdateCacheDto patchUpdateCacheDto = JSONObject.parseObject(patchResult, PatchUpdateCacheDto.class);
        if (patchUpdateCacheDto == null) {
            return null;
        }

        if (patchUpdateCacheDto.isAllFinished()) {
            patchUpdateCacheDto.setProgress(100);
        } else {
            List<PatchUpdateResult> patchResults = patchUpdateCacheDto.getPatchResults();
            if (!CollectionUtils.isEmpty(patchResults)) {
                long successCount = patchResults.stream().map(PatchUpdateResult::getHosts)
                    .filter(onePatchUpdateResults -> !CollectionUtils.isEmpty(onePatchUpdateResults))
                    .mapToLong(onePatchUpdateResults -> onePatchUpdateResults.stream()
                        .filter(OnePatchUpdateResult::isSuccess).count())
                    .sum();
                long sumCount = patchResults.stream().map(PatchUpdateResult::getHosts)
                    .filter(onePatchUpdateResults -> !CollectionUtils.isEmpty(onePatchUpdateResults))
                    .mapToLong(onePatchUpdateResults -> (long)onePatchUpdateResults.size()).sum();
                patchUpdateCacheDto.setProgress((int)(100 * successCount / sumCount));
            } else {
                log.warn("cluster: {} patch results is empty.", clusterId);
                return null;
            }
        }
        return patchUpdateCacheDto;
    }

    public PatchUpdateCacheDto queryUpdateProgress(String clusterId) {
        String patchResult = patchUpdateProgressCacheApi.queryCacheResult(clusterId);
        return JSONObject.parseObject(patchResult, PatchUpdateCacheDto.class);
    }

    public boolean isUpdatePatchPermit(String clusterId) {
        PatchUpdateCacheDto patchUpdateCacheDto = queryUpdateProgress(clusterId);
        return patchUpdateCacheDto == null || patchUpdateCacheDto.getStartTime() == 0
            || (patchUpdateCacheDto.getFinishTime() != 0 && patchUpdateCacheDto.isAllFinished());
    }

    public void updateProgressByHostResult(String clusterId, List<PatchUpdateResult> patchUpdateResults) {
        PatchUpdateCacheDto patchUpdateCacheDto = queryUpdateProgress(clusterId);
        if (CollectionUtils.isEmpty(patchUpdateResults)) {
            return;
        }
        if (patchUpdateCacheDto == null) {
            return;
        }
        List<PatchUpdateResult> pResults = patchUpdateCacheDto.getPatchResults();
        if (CollectionUtils.isEmpty(pResults)) {
            patchUpdateCacheDto.setPatchResults(patchUpdateResults);
        } else {
            for (PatchUpdateResult p1 : pResults) {
                Set<OnePatchUpdateResult> hosts = genOnePatchUpdateResults(patchUpdateResults, p1);
                p1.setHosts(Lists.newArrayList(hosts));
            }

            patchUpdateCacheDto.setPatchResults(pResults);
        }

        patchUpdateProgressCacheApi.updateCache(clusterId, JSONObject.toJSONString(patchUpdateCacheDto));
    }

    private Set<OnePatchUpdateResult> genOnePatchUpdateResults(List<PatchUpdateResult> patchUpdateResults,
        PatchUpdateResult p1) {
        Set<OnePatchUpdateResult> onePatchUpdateResults = Sets.newHashSet(p1.getHosts());
        if (CollectionUtils.isEmpty(patchUpdateResults)) {
            return onePatchUpdateResults;
        }
        patchUpdateResults.stream().filter(p2 -> StringUtils.equals(p1.getPatchName(), p2.getPatchName()))
            .forEach(patchUpdateResult -> {
                switch (PatchTypeEnum.queryPatchType(p1.getPatchType())) {
                    case INSTANCE:
                        genOnePatchUpdateResultsByInstance(onePatchUpdateResults, patchUpdateResult);
                        break;
                    case ROLE:
                        genOnePatchUpdateResultsByRole(onePatchUpdateResults, patchUpdateResult);
                        break;
                    default:
                        genOnePatchUpdateResultsByDefault(onePatchUpdateResults, patchUpdateResult);
                        break;
                }
            });
        return onePatchUpdateResults;
    }

    private void genOnePatchUpdateResultsByDefault(Set<OnePatchUpdateResult> onePatchUpdateResults,
        PatchUpdateResult patchUpdateResult) {
        Set<OnePatchUpdateResult> tmpHosts =
            onePatchUpdateResults.stream()
                .filter(p -> patchUpdateResult.getHosts().stream()
                    .noneMatch(o -> StringUtils.equals(o.getIpAddress(), p.getIpAddress())))
                .collect(Collectors.toSet());
        tmpHosts.addAll(patchUpdateResult.getHosts());
        onePatchUpdateResults.clear();
        onePatchUpdateResults.addAll(tmpHosts);
    }

    private void genOnePatchUpdateResultsByInstance(Set<OnePatchUpdateResult> onePatchUpdateResults,
        PatchUpdateResult patchUpdateResult) {
        Set<OnePatchUpdateResult> tmpHosts = onePatchUpdateResults.stream()
            .filter(p -> patchUpdateResult.getHosts().stream()
                .noneMatch(o -> StringUtils.equals(o.getIpAddress(), p.getIpAddress())
                    && StringUtils.equals(o.getInstanceId(), p.getInstanceId())))
            .collect(Collectors.toSet());
        tmpHosts.addAll(patchUpdateResult.getHosts());
        onePatchUpdateResults.clear();
        onePatchUpdateResults.addAll(tmpHosts);
    }

    private void genOnePatchUpdateResultsByRole(Set<OnePatchUpdateResult> onePatchUpdateResults,
        PatchUpdateResult patchUpdateResult) {
        Set<OnePatchUpdateResult> tmpHosts = onePatchUpdateResults.stream()
            .filter(p -> patchUpdateResult.getHosts().stream()
                .noneMatch(o -> StringUtils.equals(o.getIpAddress(), p.getIpAddress())
                    && StringUtils.equals(o.getRoleName(), p.getRoleName())))
            .collect(Collectors.toSet());
        tmpHosts.addAll(patchUpdateResult.getHosts());
        onePatchUpdateResults.clear();
        onePatchUpdateResults.addAll(tmpHosts);
    }

    public void updateFinishedCache(String patchKey, boolean isFinished) {
        patchUpdateFinishedCacheApi.updateCache(patchKey, String.valueOf(isFinished));
    }

    public boolean queryFinishedCache(String patchKey) {
        String finishedCache = patchUpdateFinishedCacheApi.queryCacheResult(patchKey);
        return finishedCache == null || Boolean.parseBoolean(finishedCache);
    }

    public void updateProgressFinishedState(String clusterId) {
        PatchUpdateCacheDto patchUpdateCacheDto = queryUpdateProgress(clusterId);
        if (patchUpdateCacheDto == null) {
            log.warn("Failed to update progress result: none cache exists.");
            return;
        }
        List<PatchUpdateResult> patchResults = patchUpdateCacheDto.getPatchResults();
        if (CollectionUtils.isEmpty(patchResults)) {
            log.warn("Failed to update progress result: none patch result.");
            return;
        }
        patchResults.stream().map(PatchUpdateResult::getHosts).flatMap(Collection::stream)
            .filter(onePatchUpdateResult -> !onePatchUpdateResult.isFinished())
            .forEach(onePatchUpdateResult -> onePatchUpdateResult.setFinished(true));
        patchUpdateCacheDto.setAllFinished(true);
        patchUpdateCacheDto.setFinishTime(System.currentTimeMillis());
        patchUpdateCacheDto.setSuccess(
            patchResults.stream().allMatch(p -> p.getHosts().stream().allMatch(OnePatchUpdateResult::isSuccess)));
        patchUpdateProgressCacheApi.updateCache(clusterId, JSONObject.toJSONString(patchUpdateCacheDto));
    }

    public void updateSuccessPatchResult(String clusterId, PatchResult patchResult) {
        String patchResultJson = JSONObject.toJSONString(patchResult);
        patchUpdateResultCacheApi.updateSuccessPatchResult(clusterId, patchResultJson);
    }

    public void updateFailedPatchResult(String clusterId, PatchUpdateResult patchUpdateResult) {
        String patchUpdateResultJson = JSONObject.toJSONString(patchUpdateResult);
        patchUpdateResultCacheApi.updateFailedPatchResult(clusterId, patchUpdateResultJson);
    }

    public List<PatchResult> pollSuccessPatchResult(String clusterId) {
        List<String> patchResultJson = patchUpdateResultCacheApi.pollSuccessPatchUpdateResult(clusterId);
        if (CollectionUtils.isEmpty(patchResultJson)) {
            return Lists.newArrayList();
        }
        return patchResultJson.stream().map(p -> JSONObject.parseObject(p, PatchResult.class))
            .collect(Collectors.toList());
    }

    public List<PatchUpdateResult> pollFailedPatchResult(String clusterId) {
        List<String> patchUpdateResultJson = patchUpdateResultCacheApi.pollFailedPatchUpdateResult(clusterId);
        if (CollectionUtils.isEmpty(patchUpdateResultJson)) {
            return Lists.newArrayList();
        }
        return patchUpdateResultJson.stream().map(p -> JSONObject.parseObject(p, PatchUpdateResult.class))
            .collect(Collectors.toList());
    }

    public boolean isPatchUpdateResultFinished(String clusterId) {
        int successResultSize = patchUpdateResultCacheApi.querySuccessPatchUpdateResultSize(clusterId);
        int failedResultSize = patchUpdateResultCacheApi.queryFailedPatchUpdateResultSize(clusterId);
        return successResultSize == 0 && failedResultSize == 0;
    }

    public void clearPatchUpdateCache(String clusterId, Set<String> patchResultKeys) {
        log.warn("clear patch update cache: clusterId is {}", clusterId);
        patchUpdateProgressCacheApi.clearCache(clusterId);
        patchUpdateResultCacheApi.clearCache(clusterId);
        if (!CollectionUtils.isEmpty(patchResultKeys)) {
            patchResultKeys.forEach(patchKey -> patchUpdateFinishedCacheApi.clearCache(patchKey));
        }
    }

    public void putPatchInfoToCache(List<PatchUpdateRequest> requests) {
        log.info("start to put patch info into cache.");
        requests.stream().forEach(request -> {
            Map<String, List<ServicePatchInfo>> ip2Patches = request.getIp2Patches();
            Map<String, String> patchInfo = new HashMap<>();
            request.getIp2Patches().keySet().stream()
                .forEach(ip -> patchInfo.put(ip, JSON.toJSONString(ip2Patches.get(ip))));
            patchUpdateInfoCacheApi.putPatchInfo(request.generateKey(), patchInfo);
            patchInfoCleanLocalCacheSender.sendMsgToClearLocalCache(request.generateKey());
        });
        log.info("finished to put patch info into cache.");
    }

    public String queryPatchInfo(String patchRequestKey, String hostIp) {
        return patchUpdateInfoCacheApi.queryPatchInfo(patchRequestKey, hostIp);
    }
}