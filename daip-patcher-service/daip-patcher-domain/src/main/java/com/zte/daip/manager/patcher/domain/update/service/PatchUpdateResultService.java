package com.zte.daip.manager.patcher.domain.update.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.*;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.response.OnePatchUpdateInfo;
import com.zte.daip.manager.patcher.api.update.response.PatchRecordAndRecoverInfos;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.common.PatchRollbackService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PatchUpdateResultService {

    @Autowired
    private PatchHistoryService patchHistoryService;
    @Autowired
    private PatchInfoService patchInfoService;
    @Autowired
    private PatchRollbackService patchRollbackService;

    public void saveSuccessPatchResults(List<PatchResult> successPatchResults) {
        savePatchRollback(successPatchResults);
        savePatchHistory(successPatchResults);
    }

    /* Started by AICoder, pid:y2ea8i6a70f1e48147e90a73108cf73fbcf85e58 */
    public void deleteRollbackServicePatchHistory(List<PatchServiceParam> patchServiceParams) {
        Map<String, Set<String>> service2PatchNames = Maps.newHashMap();
        Map<String, Set<String>> service2Ips = Maps.newHashMap();
        patchServiceParams.forEach(patchServiceParam -> {
            String serviceName = patchServiceParam.getServiceName();
            service2Ips.computeIfAbsent(serviceName, k -> Sets.newHashSet()).add(patchServiceParam.getHostIp());
            List<String> patchNames =
                patchInfoService.findByServiceAndBaseVersion(serviceName, patchServiceParam.getVersion()).stream()
                    .map(PatchDetailPo::getPatchName).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(patchNames)) {
                service2PatchNames.computeIfAbsent(serviceName, k -> Sets.newHashSet()).addAll(patchNames);
            }
        });
        service2Ips.forEach((serviceName, ipSet) -> {
            if (!CollectionUtils.isEmpty(ipSet) && service2PatchNames.containsKey(serviceName)) {
                Set<String> patchNames = service2PatchNames.get(serviceName);
                if (!CollectionUtils.isEmpty(patchNames)) {
                    log.error("deleteRollbackServicePatchHistory:{},{},{}", serviceName, JSON.toJSONString(patchNames),
                        JSON.toJSONString(ipSet));
                    patchHistoryService.deleteByServiceNameAndPatchNamesAndIps(serviceName,
                        Lists.newArrayList(patchNames), Lists.newArrayList(ipSet));
                }
            }
        });
    }
    /* Ended by AICoder, pid:y2ea8i6a70f1e48147e90a73108cf73fbcf85e58 */

    private void savePatchRollback(List<PatchResult> successPatchResults) {
        successPatchResults.forEach(patchResult -> {
            List<PatchRecordAndRecoverInfos> patchRecordAndRecoverInfos = patchResult.getPatchRecordAndRecoverInfos();
            patchRecordAndRecoverInfos.forEach(patchRecordAndRecoverInfo -> {
                List<OnePatchUpdateInfo> recoverPatches = patchRecordAndRecoverInfo.getRecoverPatch();
                ListMultimap<String, OnePatchUpdateInfo> rollbackPatchMap =
                    Multimaps.index(recoverPatches, recoverPatch -> recoverPatch.getService()
                        + recoverPatch.getInstance() + recoverPatch.getRoleName() + recoverPatch.getHostIp());
                for (String key : rollbackPatchMap.keySet()) {
                    List<OnePatchUpdateInfo> rollbackInfo = rollbackPatchMap.get(key);
                    String serviceName = rollbackInfo.get(0).getService();
                    String serviceInstanceId = rollbackInfo.get(0).getInstance();
                    String roleName = rollbackInfo.get(0).getRoleName();
                    String ip = rollbackInfo.get(0).getHostIp();
                    List<String> containerPatchNames = patchInfoService.findContainerPatchByService(serviceName)
                        .stream().map(PatchDetailPo::getPatchName).collect(Collectors.toList());
                    patchRollbackService.deleteByRollbackKeyWithoutContainer(serviceName, serviceInstanceId, roleName,
                        ip, containerPatchNames);
                    patchRollbackService.saveRollbackPatches(rollbackInfo);
                }

            });
        });
    }

    private void savePatchHistory(List<PatchResult> successPatchResults) {
        List<PatchHistory> patchHistories = organizePatchHistories(successPatchResults);
        patchHistoryService.saveBatch(patchHistories);
    }

    private List<PatchHistory> organizePatchHistories(List<PatchResult> patchResults) {
        List<PatchHistory> patchHistoryList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(patchResults)) {
            return patchHistoryList;
        }
        List<PatchResult> patches = patchResults.stream().filter(Objects::nonNull).collect(Collectors.toList());
        for (PatchResult patchResult : patches) {
            List<PatchRecordAndRecoverInfos> patchRecordAndRecoverInfos = patchResult.getPatchRecordAndRecoverInfos();
            for (PatchRecordAndRecoverInfos info : patchRecordAndRecoverInfos) {
                List<PatchHistory> patchHistories = convertToPatchHistory(info.getPatchHistory());
                patchHistoryList.addAll(patchHistories);
            }
        }

        return patchHistoryList;
    }

    private List<PatchHistory> convertToPatchHistory(List<OnePatchUpdateInfo> updateInfos) {

        List<PatchHistory> patchHistories = Lists.newArrayList();
        for (OnePatchUpdateInfo onePatch : updateInfos) {
            PatchHistory patchHistory = new PatchHistory();
            patchHistory.setPatchUptime(onePatch.getTime());
            PatchHistoryKey patchHistoryKey = new PatchHistoryKey(onePatch.getHostIp(), onePatch.getPatchName(),
                onePatch.getService(), onePatch.getRoleName(), onePatch.getInstance());
            patchHistory.setId(patchHistoryKey);
            patchHistories.add(patchHistory);
        }
        return patchHistories;
    }

}
