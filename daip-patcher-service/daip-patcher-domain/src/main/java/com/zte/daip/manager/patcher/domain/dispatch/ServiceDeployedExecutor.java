/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServiceHostInfoExecutor.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/7
 * </p>
 * <p>
 * 完成日期：2023/3/7
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.dispatch;

import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.api.HostResourceControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModelInfo;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class ServiceDeployedExecutor {

    @Autowired
    private HostResourceControllerApi hostResourceControllerApi;

    @Autowired
    private ClusterInfoControllerApi clusterInfoControllerApi;

    @Autowired
    private ServiceResourceControllerApi serviceResourceControllerApi;

    public Map<String, Map<String, Set<HostInfo>>> getServiceInfo(String clusterId) {
        log.info("Init service deployment map. ");
        Map<String, Map<String, Set<HostInfo>>> serviceDeployMap = new HashMap<>(20);
        List<ServiceRoleInfo> serviceRoleInfos = serviceResourceControllerApi.queryByClusterId(clusterId);
        if (!CollectionUtils.isEmpty(serviceRoleInfos)) {
            List<HostInfo> hostInfoList = hostResourceControllerApi.queryByClusterId(clusterId);

            Map<String, List<ServiceRoleInfo>> serviceMap =
                serviceRoleInfos.stream().collect(Collectors.groupingBy(ServiceRoleInfo::getServiceId));

            serviceMap.forEach((serviceId, serviceRoleList) -> {
                Set<String> ips =
                    serviceRoleList.stream().map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
                Set<HostInfo> hostsWithService = hostInfoList.stream()
                    .filter(hostInfo -> ips.contains(hostInfo.getIpAddress())).collect(Collectors.toSet());
                ServiceModelInfo serviceModelInfo =
                    clusterInfoControllerApi.queryServiceModelInfo(Integer.valueOf(clusterId), serviceId);
                generateServiceDeployMap(serviceDeployMap, hostsWithService, serviceModelInfo);
            });
        }
        return serviceDeployMap;
    }

    private void generateServiceDeployMap(Map<String, Map<String, Set<HostInfo>>> serviceDeployMap,
        Set<HostInfo> hostInfos, ServiceModelInfo serviceModelInfo) {
        String version = serviceModelInfo.getVersion();
        String serviceName = serviceModelInfo.getServiceName();
        String projectName = serviceModelInfo.getProjectname();
        addServiceToMap(serviceDeployMap, hostInfos, version, serviceName);
        addServiceToMap(serviceDeployMap, hostInfos, version, projectName);
    }

    private void addServiceToMap(Map<String, Map<String, Set<HostInfo>>> serviceDeployMap, Set<HostInfo> hostInfoList,
        String version, String serviceName) {
        Set<HostInfo> hostInfos = new HashSet<>(hostInfoList);
        if (serviceDeployMap.containsKey(serviceName)) {
            Map<String, Set<HostInfo>> version4Ips = serviceDeployMap.get(serviceName);
            if (version4Ips.containsKey(version)) {
                Set<HostInfo> ipList = new HashSet<>(version4Ips.get(version));
                ipList.addAll(hostInfos);
                version4Ips.replace(version, ipList);
            } else {
                version4Ips.put(version, hostInfos);
            }
            serviceDeployMap.replace(serviceName, version4Ips);
        } else {
            Map<String, Set<HostInfo>> version4Ips = new HashMap<>();
            version4Ips.put(version, hostInfos);
            serviceDeployMap.put(serviceName, version4Ips);
        }
    }
}