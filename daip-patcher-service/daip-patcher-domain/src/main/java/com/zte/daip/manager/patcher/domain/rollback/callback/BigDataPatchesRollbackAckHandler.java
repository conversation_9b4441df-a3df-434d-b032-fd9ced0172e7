/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchesRollbackAckHandler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/21
 * </p>
 * <p>
 * 完成日期：2023/3/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.rollback.callback;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zte.daip.communication.api.ConsumerAckHandler;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.bean.ResponseMessageBody;
import com.zte.daip.manager.patcher.api.dto.BigDataPatchRollbackMsg;
import com.zte.daip.manager.patcher.api.update.response.PatchRecordAndRecoverInfos;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.rollback.cache.PatchRollbackProgressQueue;
import com.zte.daip.manager.patcher.domain.rollback.cache.PatchRollbackResultQueue;
import com.zte.daip.manager.patcher.domain.rollback.entity.PatchRollBackResult;
import com.zte.daip.manager.patcher.domain.rollback.entity.RollbackProgressDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

/* Started by AICoder, pid:bb7b6wd842r90b61403f0bb921404a01c7c030db */
@Slf4j
@Service
public class BigDataPatchesRollbackAckHandler implements ConsumerAckHandler {

    @Autowired
    private PatchRollbackResultQueue patchRollbackResultQueue;
    @Autowired
    private PatchRollbackProgressQueue patchRollbackProgressQueue;

    @Override
    public String response(String hostName, String body, RequestMessageBody requestMessageBody) {
        log.info("receive response msg from host:" + hostName);
        ResponseMessageBody<PatchResult> responseMessageBody =
            JSONObject.parseObject(body, new TypeReference<ResponseMessageBody<PatchResult>>() {});
        PatchResult patchResult = responseMessageBody.getResult();
        List<BigDataPatchRollbackMsg> bigDataPatchRollbackMsgs =
            (List<BigDataPatchRollbackMsg>)requestMessageBody.getBody();

        List<BigDataPatchRollbackMsg> bigDataPatchRollbackHostMsgs = bigDataPatchRollbackMsgs.stream()
            .filter(bigDataPatchRollbackMsg -> bigDataPatchRollbackMsg.getHostNames().contains(hostName))
            .collect(Collectors.toList());

        if (patchResult != null && !CollectionUtils.isEmpty(bigDataPatchRollbackHostMsgs)) {
            BigDataPatchRollbackMsg bigDataPatchRollbackMsg = bigDataPatchRollbackHostMsgs.get(0);
            List<String> serviceNames = bigDataPatchRollbackMsg.getServiceNames();
            String clusterId = bigDataPatchRollbackMsg.getClusterId();
            for (String serviceName : serviceNames) {
                if (!patchResult.isUpdateSuccess()) {
                    log.error("Rollback patch {}:{} failed.", hostName, serviceName);
                    updateProgress(hostName, serviceName, clusterId, false, "rollback failed.");
                    return "failed";
                }
                List<PatchRecordAndRecoverInfos> patchRecordAndRecoverInfos =
                    patchResult.getPatchRecordAndRecoverInfos();
                if (!CollectionUtils.isEmpty(patchRecordAndRecoverInfos)) {
                    PatchRollBackResult patchRollBackResult = new PatchRollBackResult();
                    patchRollBackResult.setIp(patchResult.getHostIp());
                    patchRollBackResult.setServiceName(serviceName);
                    patchRollBackResult.setServiceInstanceId("");
                    patchRollBackResult.setRoleName("");
                    patchRollBackResult.setPatchRecordAndRecoverInfos(patchRecordAndRecoverInfos);
                    patchRollbackResultQueue.add(patchRollBackResult);
                }
                updateProgress(hostName, serviceName, clusterId, true, "rollback success");
            }
        }
        return "success";
    }

    @Override
    public void onFailure(Set<String> hostNames, Throwable ex, RequestMessageBody requestMessageBody) {
        log.error("hostNames:{}", hostNames.toString(), ex);
        List<BigDataPatchRollbackMsg> bigDataPatchRollbackMsgs =
            (List<BigDataPatchRollbackMsg>)requestMessageBody.getBody();
        hostNames.forEach(hostName -> {
            List<BigDataPatchRollbackMsg> bigDataPatchRollbackHostMsgs = bigDataPatchRollbackMsgs.stream()
                .filter(bigDataPatchRollbackMsg -> bigDataPatchRollbackMsg.getHostNames().contains(hostName))
                .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(bigDataPatchRollbackHostMsgs)) {
                BigDataPatchRollbackMsg bigDataPatchRollbackMsg = bigDataPatchRollbackHostMsgs.get(0);
                List<String> serviceNames = bigDataPatchRollbackMsg.getServiceNames();
                String clusterId = bigDataPatchRollbackMsg.getClusterId();
                for (String serviceName : serviceNames) {
                    updateProgress(hostName, serviceName, clusterId, false, ex.getMessage());
                }
            }
        });
    }

    @Override
    public void onFailure(String hostName, Throwable ex, RequestMessageBody requestMessageBody) {
        log.error("hostNames:{}", hostName, ex);
        List<BigDataPatchRollbackMsg> bigDataPatchRollbackMsgs =
            (List<BigDataPatchRollbackMsg>)requestMessageBody.getBody();
        List<BigDataPatchRollbackMsg> bigDataPatchRollbackHostMsgs = bigDataPatchRollbackMsgs.stream()
            .filter(bigDataPatchRollbackMsg -> bigDataPatchRollbackMsg.getHostNames().contains(hostName))
            .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(bigDataPatchRollbackHostMsgs)) {
            BigDataPatchRollbackMsg bigDataPatchRollbackMsg = bigDataPatchRollbackHostMsgs.get(0);
            List<String> serviceNames = bigDataPatchRollbackMsg.getServiceNames();
            String clusterId = bigDataPatchRollbackMsg.getClusterId();
            for (String serviceName : serviceNames) {
                updateProgress(hostName, serviceName, clusterId, false, ex.getMessage());
            }
        }
    }

    @Override
    public void onFinish(RequestMessageBody requestMessageBody) {

    }

    private void updateProgress(String hostName, String serviceName, String clusterId, boolean success,
        String message) {
        RollbackProgressDo rollbackProgressDo =
            new RollbackProgressDo(clusterId, hostName, serviceName, message, success);
        patchRollbackProgressQueue.add(rollbackProgressDo);

    }
}

/* Ended by AICoder, pid:bb7b6wd842r90b61403f0bb921404a01c7c030db */