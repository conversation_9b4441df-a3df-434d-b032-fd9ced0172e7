/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchesUpdateAckHandler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/7
 * </p>
 * <p>
 * 完成日期：2021/4/7
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.callback;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.communication.api.ConsumerAckHandler;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.bean.ResponseMessageBody;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.patcher.api.dto.OnePatchUpdateResult;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateResult;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateRequest;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateBeansConvertor;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateCacheService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchUpdateResultKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchesUpdateAckHandler implements ConsumerAckHandler {

    @Autowired
    private PatchUpdateCacheService patchUpdateCacheService;
    @Autowired
    private PatchUpdateBeansConvertor patchUpdateBeansConvertor;
    @Autowired
    private HostResourceInfoCache hostResourceInfoCache;

    @Override
    public String response(String hostName, String body, RequestMessageBody requestMessageBody) {

        log.info("receive response msg from host:" + hostName);
        ResponseMessageBody<PatchResult> responseMessageBody =
            JSONObject.parseObject(body, new TypeReference<ResponseMessageBody<PatchResult>>() {});
        PatchResult patchResult = responseMessageBody.getResult();
        //
        if (!Objects.isNull(patchResult) && (!CollectionUtils.isEmpty(patchResult.getPatchRecordAndRecoverInfos()))) {
            PatchUpdateRequest patchUpdateRequest = (PatchUpdateRequest)requestMessageBody.getBody();
            if (patchUpdateRequest != null) {
                patchUpdateCacheService.updateSuccessPatchResult(patchUpdateRequest.getClusterId(), patchResult);
            } else {
                log.warn("patchUpdateRequest is empty for hostname: {}", hostName);
            }
        } else {
            log.warn("patchResult content is empty for hostname: {}", hostName);
        }
        //
        return "success";
    }

    @Override
    public void onFailure(Set<String> hostNames, Throwable ex, RequestMessageBody requestMessageBody) {
        log.error("update patch send failed for:" + hostNames);
        PatchUpdateRequest patchUpdateRequest = (PatchUpdateRequest)requestMessageBody.getBody();
        updateFailedPatchResult(patchUpdateRequest, hostNames, String.valueOf(ex));
    }

    @Override
    public void onFailure(String hostName, Throwable ex, RequestMessageBody requestMessageBody) {
        log.error("update patch message deal exception for:" + hostName);
        PatchUpdateRequest patchUpdateRequest = (PatchUpdateRequest)requestMessageBody.getBody();
        updateFailedPatchResult(patchUpdateRequest, Sets.newHashSet(hostName), String.valueOf(ex));
    }

    @Override
    public void onFinish(RequestMessageBody requestMessageBody) {
        log.info("update patch message send and deal finished.");
        PatchUpdateRequest patchUpdateRequest = (PatchUpdateRequest)requestMessageBody.getBody();
        if (patchUpdateRequest != null) {
            PatchUpdateResultKey p = new PatchUpdateResultKey(patchUpdateRequest.getClusterId(),
                patchUpdateRequest.getPatchHome(), patchUpdateRequest.getPatchType());
            patchUpdateCacheService.updateFinishedCache(p.generateUId(), true);
        } else {
            log.warn("requestMessageBody: patchUpdateRequest is null.");
        }
    }

    private void updateFailedPatchResult(PatchUpdateRequest patchUpdateRequest, Set<String> hostNames, String message) {
        String clusterId = patchUpdateRequest.getClusterId();
        PatchUpdateCacheDto patchUpdateCacheDto =
            patchUpdateBeansConvertor.convertUpdateRequest2CacheDto(clusterId, Lists.newArrayList(patchUpdateRequest));
        List<PatchUpdateResult> patchResults = patchUpdateCacheDto.getPatchResults();
        List<HostInfo> hostInfos = hostResourceInfoCache.queryHostByClusterId(clusterId);
        if (CollectionUtils.isEmpty(hostInfos) || CollectionUtils.isEmpty(patchResults)) {
            return;
        }
        Map<String, String> host2Ip =
            hostInfos.stream().collect(Collectors.toMap(HostInfo::getHostName, HostInfo::getIpAddress));
        Set<String> ips = hostNames.stream().map(h -> host2Ip.getOrDefault(h, "")).collect(Collectors.toSet());
        patchResults.forEach(p -> {
            List<OnePatchUpdateResult> onePatchUpdateResults = p.getHosts();
            if (!CollectionUtils.isEmpty(onePatchUpdateResults)) {
                onePatchUpdateResults.stream().filter(o -> ips.contains(o.getIpAddress())).forEach(r -> {
                    r.setSuccess(false);
                    r.setFinished(true);
                    r.setMessage(message);
                });
                patchUpdateCacheService.updateFailedPatchResult(clusterId, p);
            }
        });
    }
}