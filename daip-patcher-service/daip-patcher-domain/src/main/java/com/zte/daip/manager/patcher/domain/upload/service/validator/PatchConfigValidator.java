/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchConfigValidator.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/12
 * </p>
 * <p>
 * 完成日期：2021/3/12
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.upload.service.validator;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchConfigValidator implements PatchValidator {
    @Override
    public PatchUploadResult checkPatch(PatchBean patchBean, File patchFile, List<PatchDetailPo> patchDetailPos) {
        String fileName = patchFile.getName().substring(0, patchFile.getName().indexOf(".zip"));
        if (patchBean == null) {
            log.error("{} patch-update-config.xml content is error", fileName);
            return new PatchUploadResult(false, "file patch-update-config.xml content is error", fileName, true,
                OnePatchProcess.NORMAL_CHECK);
        }
        if (!fileName.equals(patchBean.getPatchName())) {
            log.error("{}: The file name is inconsistent with the patch name", fileName);
            return new PatchUploadResult(false, "The file name is inconsistent with the patch name", fileName, true,
                OnePatchProcess.NORMAL_CHECK);
        }
        if (StringUtils.isBlank(patchBean.getSrcVersion())) {
            log.error("{}: patch version is empty", fileName);
            return new PatchUploadResult(false, "patch version is empty", fileName, true, OnePatchProcess.NORMAL_CHECK);
        }
        return new PatchUploadResult(true, "upload patch success", fileName, false, OnePatchProcess.VALIDATING);
    }

    @Override
    public int order() {
        return 10;
    }
}