package com.zte.daip.manager.patcher.domain.update.schedule;

import com.google.common.collect.*;
import com.zte.daip.manager.common.cache.lock.LockUtils;
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.ClusterBean;
import com.zte.daip.manager.patcher.api.dto.OnePatchUpdateResult;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateResult;
import com.zte.daip.manager.patcher.api.update.response.OnePatchUpdateInfo;
import com.zte.daip.manager.patcher.api.update.response.PatchRecordAndRecoverInfos;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.common.PatchRollbackService;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateCacheService;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateResultService;
import com.zte.daip.manager.patcher.infrastructure.po.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class UpdatePatchListenerScheduler {
    @Autowired
    private ClusterInfoControllerApi clusterInfoControllerApi;
    @Autowired
    private PatchUpdateCacheService patchUpdateCacheService;
    @Autowired
    private PatchHistoryService patchHistoryService;
    @Autowired
    private PatchInfoService patchInfoService;
    @Autowired
    private PatchRollbackService patchRollbackService;
    @Autowired
    private LockUtils lockUtils;

    @Autowired
    private PatchUpdateResultService patchUpdateResultService;

    @Scheduled(cron = "0/5 * * * * ?")
    public void cron() {
        try {
            List<ClusterBean> clusterBeanList = clusterInfoControllerApi.queryAll();

            for (ClusterBean clusterBean : clusterBeanList) {
                String clusterId = String.valueOf(clusterBean.getClusterId());
                lockUtils.executeWithLockAndReturnWithLocked(clusterId, 3 * 1000L, () -> savePatchRecord(clusterId));
            }

        } catch (Exception ex) {
            log.error("Fail to scan listener.", ex);
        }
    }

    void savePatchRecord(String clusterId) {
        PatchUpdateCacheDto patchUpdateCacheDto = patchUpdateCacheService.queryUpdateProgress(clusterId);
        if (patchUpdateCacheDto != null && !patchUpdateCacheDto.isAllFinished()) {
            List<PatchResult> successPatchResults = patchUpdateCacheService.pollSuccessPatchResult(clusterId);
            List<PatchUpdateResult> failedPatchResults = patchUpdateCacheService.pollFailedPatchResult(clusterId);
            log.info("successPatchResults:{}", successPatchResults);
            patchUpdateResultService.saveSuccessPatchResults(successPatchResults);
            updatePatchProgressResult(clusterId, convertSuccessPatchResult2UpdateResult(successPatchResults),
                failedPatchResults);

            if (isUpdateProgressFinished(clusterId)) {
                patchUpdateCacheService.updateProgressFinishedState(clusterId);
            }
        }
    }

    private Set<String> queryPatchKeys(String clusterId) {
        Set<String> patchKeys = Sets.newConcurrentHashSet();
        PatchUpdateCacheDto patchUpdateCacheDto = patchUpdateCacheService.queryUpdateProgress(clusterId);
        if (patchUpdateCacheDto == null) {
            return patchKeys;
        }
        List<PatchUpdateResult> patchResults = patchUpdateCacheDto.getPatchResults();
        if (CollectionUtils.isEmpty(patchResults)) {
            return patchKeys;
        }
        patchResults.forEach(updateResult -> {
            String patchType = updateResult.getPatchType();
            List<OnePatchUpdateResult> onePatchUpdateResults = updateResult.getHosts();
            if (!CollectionUtils.isEmpty(onePatchUpdateResults)) {
                onePatchUpdateResults.stream().map(OnePatchUpdateResult::getPatchHome).forEach(patchHome -> {
                    PatchUpdateResultKey patchUpdateResultKey =
                        new PatchUpdateResultKey(clusterId, patchHome, patchType);
                    patchKeys.add(patchUpdateResultKey.generateUId());
                });
            }
        });
        return patchKeys;
    }

    private boolean isUpdateProgressFinished(String clusterId) {
        Set<String> patchKeys = queryPatchKeys(clusterId);
        return !CollectionUtils.isEmpty(patchKeys)
            && patchKeys.stream().allMatch(patchKey -> patchUpdateCacheService.queryFinishedCache(patchKey))
            && patchUpdateCacheService.isPatchUpdateResultFinished(clusterId);
    }

    private List<PatchUpdateResult> convertSuccessPatchResult2UpdateResult(List<PatchResult> successPatchResults) {
        Map<String, List<OnePatchUpdateResult>> patchName2OnePatchResult =
            queryPatchName2OnePatchResult(successPatchResults);

        return queryMap2UpdateResult(patchName2OnePatchResult);
    }

    private List<PatchUpdateResult>
        queryMap2UpdateResult(Map<String, List<OnePatchUpdateResult>> patchName2OnePatchResult) {
        List<PatchUpdateResult> patchUpdateResults = Lists.newArrayList();
        patchName2OnePatchResult.forEach((patchName, o1) -> {
            PatchUpdateResult patchUpdateResult = new PatchUpdateResult();
            patchUpdateResult.setPatchName(patchName);
            for (OnePatchUpdateResult o2 : o1) {
                o2.setSuccess(true);
                o2.setFinished(true);
            }
            patchUpdateResult.setHosts(o1);
            patchUpdateResults.add(patchUpdateResult);
        });
        return patchUpdateResults;
    }

    private Map<String, List<OnePatchUpdateResult>>
        queryPatchName2OnePatchResult(List<PatchResult> successPatchResults) {
        Map<String, List<OnePatchUpdateResult>> patchName2OnePatchResult = Maps.newHashMap();
        successPatchResults.forEach(p1 -> {
            List<PatchRecordAndRecoverInfos> patchRecordAndRecoverInfos = p1.getPatchRecordAndRecoverInfos();
            patchRecordAndRecoverInfos.forEach(p2 -> {
                List<OnePatchUpdateInfo> patchHistory = p2.getPatchHistory();
                patchHistory.forEach(p3 -> {
                    String patchName = p3.getPatchName();
                    OnePatchUpdateResult o =
                        new OnePatchUpdateResult(p3.getHostIp(), p3.getInstance(), p3.getRoleName());
                    List<OnePatchUpdateResult> results =
                        patchName2OnePatchResult.getOrDefault(patchName, Lists.newArrayList());
                    results.add(o);
                    patchName2OnePatchResult.put(patchName, results);
                });
            });
        });
        return patchName2OnePatchResult;
    }

    private void updatePatchProgressResult(String clusterId, List<PatchUpdateResult> successPatchResults,
        List<PatchUpdateResult> failedPatchResults) {
        List<PatchUpdateResult> patchResults = Lists.newArrayList();
        patchResults.addAll(successPatchResults);
        patchResults.addAll(failedPatchResults);
        patchUpdateCacheService.updateProgressByHostResult(clusterId, patchResults);
    }

}