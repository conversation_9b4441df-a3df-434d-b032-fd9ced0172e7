package com.zte.daip.manager.patcher.domain.task.api;

import java.util.List;

import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

public interface ServiceInstancePatchResource
{
    List<ServiceInstancePatchInfo> organizeServiceInstancePatchInfo(PatchTaskDto patchTaskDto);
}
