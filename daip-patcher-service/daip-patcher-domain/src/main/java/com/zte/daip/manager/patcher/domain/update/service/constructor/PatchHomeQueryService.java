/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchHomeQueryService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/31
 * </p>
 * <p>
 * 完成日期：2021/3/31
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.service.constructor;

import java.io.File;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.configcenter.api.ServiceConfigControllerApi;
import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.configcenter.bean.ConfigRequest;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Service
public class PatchHomeQueryService {

    private static final String ZDH_HOME_ENV_KEY = "${ZDH_HOME}";

    @Autowired
    private ServiceConfigControllerApi serviceConfigControllerApi;

    public List<ConfigInstance> queryPatchHome(String clusterId, String serviceInstanceId) {
        List<String> itemTypes = Lists.newArrayList("InstallDir");
        ConfigRequest configRequest = ConfigRequest.builder().clusterId(clusterId).serviceInstanceId(serviceInstanceId)
            .itemTypes(itemTypes).build();
        return serviceConfigControllerApi.queryByCondition(configRequest);
    }

    public String queryDefaultZdhPatchHome() {
        return ZDH_HOME_ENV_KEY + File.separator + "patch";
    }
}