package com.zte.daip.manager.patcher.domain.update.service.constructor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.configcenter.bean.ConfigInstance;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.ServiceInstance;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.api.update.response.OfflinePatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.query.service.servicerole.bean.PatchServiceInfo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service("InstancePatchSourceConstructor")
@Slf4j
public class InstancePatchSourceConstructor extends AbstractPatchTypeSourceConstructor {

    private static final PatchTypeConstructorEnum PATCH_TYPE_CONSTRUCTOR_ENUM = PatchTypeConstructorEnum.INSTANCE;

    @Override
    public String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException {
        List<ConfigInstance> configInstances =
            patchHomeQueryService.queryPatchHome(patchHomeParam.getClusterId(), patchHomeParam.getServiceInstanceId());
        if (!CollectionUtils.isEmpty(configInstances)) {
            return configInstances.get(0).getConfigValue() + PATCH_HOME;
        }
        return "";
    }

    @Override
    public List<OfflinePatchUpdateInfo> obtainOfflinePatchUpdateInfo(PatchServiceParam updateParam, String clusterId,
        String hostIp) throws DaipBaseException {

        log.info("obtain instanceType services patch updateInfo");

        Map<String, List<ServicePatchInfo>> patchHome2ServicePatchInfo = Maps.newHashMap();
        for (ServiceInstance serviceInstance : updateParam.getServiceInstances()) {
            String patchHome = queryPatchHome(PatchHomeParam.builder().clusterId(clusterId)
                .serviceInstanceId(serviceInstance.getServiceInstanceId()).build());
            ServicePatchInfo servicePatchInfo = obtainServicePatchInfo(updateParam, serviceInstance, hostIp);
            List<ServicePatchInfo> servicePatchInfoList =
                patchHome2ServicePatchInfo.computeIfAbsent(patchHome, k -> Lists.newArrayList());
            servicePatchInfoList.add(servicePatchInfo);
        }
        return getOfflinePatchUpdateInfoList(patchHome2ServicePatchInfo, PATCH_TYPE_CONSTRUCTOR_ENUM);
    }

    private ServicePatchInfo obtainServicePatchInfo(PatchServiceParam updateParam, ServiceInstance serviceInstance,
        String hostIp) {
        String serviceName = updateParam.getServiceName();
        String version = updateParam.getVersion();
        String serviceInstanceId = serviceInstance.getServiceInstanceId();
        ServicePatchInfo servicePatchInfo = new ServicePatchInfo();
        servicePatchInfo.setServiceName(serviceName);
        servicePatchInfo.setVersion(version);
        List<String> updatedContainerPatches = queryIp2UpdatedContainerPatches(serviceName, version)
            .computeIfAbsent(hostIp, k -> Lists.newArrayList()).stream()
            .filter(patchHistory -> StringUtils.equalsIgnoreCase(serviceInstanceId,
                patchHistory.getId().getServiceInstanceId()))
            .map(patchHistory -> patchHistory.getId().getPatchName()).collect(Collectors.toList());
        servicePatchInfo.setUpdatedContainerPatches(updatedContainerPatches);
        servicePatchInfo.setPatches(obtainSimplePatchInfo(PatchServiceInfo.builder().serviceName(serviceName)
            .version(version).serviceInstanceId(serviceInstanceId).host(hostIp).build()));
        servicePatchInfo.setServiceInstances(Lists.newArrayList(serviceInstance));
        return servicePatchInfo;
    }

    private List<SimplePatchInfo> obtainSimplePatchInfo(PatchServiceInfo patchServiceInfo) {
        List<PatchHistory> patchHistories = patchHistoryService
            .queryPatchHistoryInfoByServiceNameAndIp(patchServiceInfo.getServiceName(), patchServiceInfo.getHost());
        Set<String> updatedPatches = patchHistories.stream()
            .filter(patchHistory -> StringUtils.equalsIgnoreCase(patchHistory.getId().getServiceInstanceId(),
                patchServiceInfo.getServiceInstanceId()))
            .map(patchHistory -> patchHistory.getId().getPatchName()).collect(Collectors.toSet());
        return buildSimplePatchInfo(patchServiceInfo.getServiceName(), patchServiceInfo.getVersion(),
            patchServiceInfo.getRoleName(), updatedPatches);
    }
}
