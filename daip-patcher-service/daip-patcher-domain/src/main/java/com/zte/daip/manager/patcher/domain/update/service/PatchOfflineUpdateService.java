package com.zte.daip.manager.patcher.domain.update.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.response.OfflinePatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.update.service.constructor.AbstractPatchTypeSourceConstructor;
import com.zte.daip.manager.patcher.domain.update.service.constructor.PatchTypeConstructorEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatchOfflineUpdateService {

    @Autowired
    private PatchTypeQueryService patchTypeQueryService;

    @Autowired
    private ApplicationContext applicationContext;

    public List<OfflinePatchUpdateInfo> queryUpdatePatchInfo(PatchUpdateParam offlinePatchUpdateParam)
        throws DaipBaseException {
        List<OfflinePatchUpdateInfo> offlinePatchUpdateInfoList = Lists.newArrayList();
        List<PatchServiceParam> serviceParams = offlinePatchUpdateParam.getServiceParams();
        for (PatchServiceParam patchServiceParam : serviceParams) {
            List<OfflinePatchUpdateInfo> oneServicePatchUpdateInfoList = generatePatchUpdateInfoList(patchServiceParam,
                offlinePatchUpdateParam.getClusterId(), offlinePatchUpdateParam.getHostIp());
            if (!CollectionUtils.isEmpty(oneServicePatchUpdateInfoList)) {
                log.info("queryUpdatePatchInfo:{}", JSON.toJSONString(oneServicePatchUpdateInfoList));
                offlinePatchUpdateInfoList.addAll(oneServicePatchUpdateInfoList);
            }
        }
        return offlinePatchUpdateInfoList;
    }

    private List<OfflinePatchUpdateInfo> generatePatchUpdateInfoList(PatchServiceParam updateParam, String clusterId,
        String hostIp) throws DaipBaseException {
        List<OfflinePatchUpdateInfo> needOfflineUpdateInfoList = Lists.newArrayList();
        String patchType = patchTypeQueryService.queryPatchTypeByServiceName(clusterId, updateParam.getServiceName());
        PatchTypeConstructorEnum patchTypeEnum = PatchTypeConstructorEnum.queryPatchType(patchType);
        AbstractPatchTypeSourceConstructor abstractPatchSourceGenerator =
            (AbstractPatchTypeSourceConstructor)applicationContext.getBean(patchTypeEnum.getConstructorName());
        List<OfflinePatchUpdateInfo> offlinePatchUpdateInfos =
            abstractPatchSourceGenerator.obtainOfflinePatchUpdateInfo(updateParam, clusterId, hostIp);

        offlinePatchUpdateInfos.forEach(offlinePatchUpdateInfo -> {
            List<ServicePatchInfo> servicePatchInfos = offlinePatchUpdateInfo.getServicePatchInfos();
            if (!CollectionUtils.isEmpty(servicePatchInfos)) {
                List<ServicePatchInfo> collect = servicePatchInfos.stream()
                    .filter(servicePatchInfo -> !CollectionUtils.isEmpty(servicePatchInfo.getPatches()))
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    offlinePatchUpdateInfo.setServicePatchInfos(collect);
                    needOfflineUpdateInfoList.add(offlinePatchUpdateInfo);
                }
            }
        });
        return needOfflineUpdateInfoList;
    }
}
