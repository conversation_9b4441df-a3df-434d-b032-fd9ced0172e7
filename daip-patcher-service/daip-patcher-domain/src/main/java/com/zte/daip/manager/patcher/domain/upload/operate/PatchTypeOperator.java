package com.zte.daip.manager.patcher.domain.upload.operate;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;

import java.util.List;

public interface PatchTypeOperator {
    String generateFullKey(PatchBean patchBean);

    void deleteByFullPatch(List<PatchDetailPo> patchDetailPos, String patchName);

    List<PatchDetailPo> filterHistoryFullPatch(List<PatchDetailPo> fullPatchList);

    String patchType();

}
