/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: DispatchPatchQueryService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/23
 * </p>
 * <p>
 * 完成日期：2021/3/23
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.common;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.dto.PatchDispatchDto;
import com.zte.daip.manager.patcher.api.dto.PatchDispatchResult;
import com.zte.daip.manager.patcher.domain.cache.HostResourceInfoCache;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchDispatchPoAssembler;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatch;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDispatchKey;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchDispatchRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CacheConfig(cacheNames = "patchDispatch")
@Slf4j
public class PatchDispatchService {
    private static final int BATCH_NUMBER = 2000;

    @Autowired
    private PatchDispatchRepository patchDispatchRepository;
    @Autowired
    private PatchDispatchPoAssembler patchDispatchPoAssembler;

    @Autowired
    private HostResourceInfoCache hostResourceInfoCache;

    @Cacheable(key = "'allDispatch'")
    public List<PatchDispatch> queryAllPatchDispatchInfo() {
        return patchDispatchRepository.findAll();
    }

    @Cacheable(key = "'allDispatch'+#p0")
    public List<PatchDispatch> queryPatchDispatchByResult(boolean success) {
        return patchDispatchRepository.findBySuccess(success);
    }

    @Cacheable(key = "'allDispatch'+#p0")
    public List<PatchDispatch> queryPatchDispatchByPatchnameAndResult(String patchName,
                                                                      boolean success) {
        return patchDispatchRepository.findByPatchNameAndSuccess(patchName, success);
    }

    public List<PatchDispatchDto> queryPatchDispatchInfoByPatchKey(PatchKeyDo patchKeyDo, String type) {
        final List<PatchDispatch> patchDispatches = queryPatchDispatchInfoByPatchKey(patchKeyDo.getPatchName());

        final List<PatchDispatchDto> collect =
                patchDispatches.stream().map(patchDispatchPoAssembler::patchDispatch2Dto).collect(Collectors.toList());

        if (StringUtils.isBlank(type)) {
            return collect;
        }
        boolean isSuccess = StringUtils.equalsIgnoreCase(type, "success");
        return collect.stream().filter(patchDispatchDto -> isSuccess == patchDispatchDto.isSuccess())
                .collect(Collectors.toList());

    }

    @CacheEvict(allEntries = true, beforeInvocation = true)
    public void deleteByPatchNamesAndIps(List<String> patchNames, List<String> ips) {
        if (ips.size() > BATCH_NUMBER) {
            List<List<String>> partitions = Lists.partition(ips, BATCH_NUMBER);
            partitions
                .forEach(dispatchList -> patchDispatchRepository.deleteByPatchNamesAndIps(patchNames, dispatchList));
        } else {
            patchDispatchRepository.deleteByPatchNamesAndIps(patchNames, ips);
        }
    }



    @Cacheable(key = "#p0")
    public List<PatchDispatch> queryPatchDispatchInfoByPatchKey(String patchName) {
        return patchDispatchRepository.queryPatchDispatchByPatchKey(patchName);
    }

    @CacheEvict(allEntries = true, beforeInvocation = true)
    public void insert(String patchName, List<String> hostIps, long timeStamp) {
        if (CollectionUtils.isEmpty(hostIps)) {
            return;
        }
        List<PatchDispatch> patchDispatches = patchDispatchRepository
                .queryPatchDispatchByPatchKey(patchName);

        Set<String> existIps = patchDispatches.stream().map(e -> e.getId().getIp())
                .collect(Collectors.toSet());

        List<PatchDispatch> toInsertPatchRecords = hostIps.stream()
                .filter(e -> !existIps.contains(e))
                .map(ip -> new PatchDispatch(new PatchDispatchKey(patchName, ip), timeStamp, false, ""))
                .collect(Collectors.toList());

        batchSave(toInsertPatchRecords);
    }

    @CacheEvict(allEntries = true, beforeInvocation = true)
    public void batchUpdate(List<PatchDispatchResult> dispatchResults) {
        if (CollectionUtils.isEmpty(dispatchResults)) {
            return;
        }
        Set<PatchDispatch> needSavePatchDispatches = dispatchResults.stream()
                .map(dispatchResult -> {
                    PatchDispatch needSavePatchDispatch = new PatchDispatch();
                    needSavePatchDispatch.setId(new PatchDispatchKey(dispatchResult.getPatchName(), hostResourceInfoCache.queryIpAddress(dispatchResult.getHostIp())));
                    needSavePatchDispatch.setPatchDispatchUptime(System.currentTimeMillis());
                    needSavePatchDispatch.setSuccess(dispatchResult.isSuccess());
                    needSavePatchDispatch.setReason(dispatchResult.getReason());
                    return needSavePatchDispatch;
                }).collect(Collectors.toSet());
        batchSave(Lists.newArrayList(needSavePatchDispatches));
    }

    @CacheEvict(allEntries = true, beforeInvocation = true)
    public void batchSave(List<PatchDispatch> patchDispatches) {
        if (patchDispatches.size() > BATCH_NUMBER) {
            List<List<PatchDispatch>> partitions = Lists.partition(patchDispatches, BATCH_NUMBER);
            partitions.forEach(dispatchList -> patchDispatchRepository.saveAll(dispatchList));
        } else {
            patchDispatchRepository.saveAll(patchDispatches);
        }
    }
}