package com.zte.daip.manager.patcher.domain.update.service.constructor;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.api.update.response.OfflinePatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.query.valobj.PatchHomeParam;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Service("ZdhSourceConstructor")
@Slf4j
public class ZdhSourceConstructor extends ServicePatchSourceConstructor {

    @Autowired
    private DeploymentInstanceServiceControllerApi instanceServiceControllerApi;


    private static final PatchTypeConstructorEnum PATCH_TYPE_CONSTRUCTOR_ENUM = PatchTypeConstructorEnum.ZDH;

    @Override
    public String queryPatchHome(PatchHomeParam patchHomeParam) throws DaipBaseException {
        return "${ZDH_HOME}" + PATCH_HOME;
    }

    @Override
    public List<OfflinePatchUpdateInfo> obtainOfflinePatchUpdateInfo(PatchServiceParam updateParam, String clusterId,
        String hostIp) throws DaipBaseException {

        log.info("obtain zdh services patch updateInfo");

        List<OfflinePatchUpdateInfo> offlinePatchUpdateInfoList =
            super.obtainOfflinePatchUpdateInfo(updateParam, clusterId, hostIp);
        List<OfflinePatchUpdateInfo> zdhServicesPatches = offlinePatchUpdateInfoList.stream()
            .peek(offlinePatchUpdateInfo -> offlinePatchUpdateInfo.setPatchType(PATCH_TYPE_CONSTRUCTOR_ENUM.getType()))
            .collect(Collectors.toList());
        if (!StringUtils.equalsIgnoreCase(updateParam.getServiceName(), "zdh")) {
            updateParam.setServiceName("zdh");
            List<OfflinePatchUpdateInfo> zdhPatchUpdateInfoLists =
                super.obtainOfflinePatchUpdateInfo(updateParam, clusterId, hostIp);
            if (!CollectionUtils.isEmpty(zdhPatchUpdateInfoLists)) {
                zdhServicesPatches.addAll(zdhPatchUpdateInfoLists);
            }
        }
        return zdhServicesPatches;
    }
}
