/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UpdateSchemaPatchImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/23
 * </p>
 * <p>
 * 完成日期：2021/3/23
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.schema;

import java.util.List;
import java.util.Set;
import java.util.concurrent.Future;

import com.zte.daip.manager.event.reporter.api.util.SpanUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import com.google.common.collect.Sets;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.schema.SchemaAction;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchRegisterRequest;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchUpdateRequest;
import com.zte.daip.manager.patcher.api.schema.UpdateSchemaPatchFile;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.domain.schema.utils.SchemaPatchUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Slf4j
@Service
public class UpdateSchemaPatchNew {

    @Autowired
    private SchemaPatchUtils schemaPatchUtils;

    @Autowired
    private SchemaPatchApi schemaPatchApi;

    @Async("asyncExecutor")
    public Future<PatchOperateResult> updatePatch(PatchTaskInfo patchTaskInfo) {
        String serviceName = patchTaskInfo.getServiceName();
        String version = patchTaskInfo.getVersion();
        PatchOperateResult updatePatchResult = new PatchOperateResult();
        try {
            SpanUtil.refreshTracing(patchTaskInfo.getTraceId());

            log.info(String.format("start to update schema patch serviceName=%s,version=%s", serviceName, version));

            if (schemaPatchUtils.checkPatchesHasUpdated(patchTaskInfo)) {
                updatePatchResult.setStatus(true);
                String msg = String.format("schema patches has all updated,patchNames=%s", patchTaskInfo.getUpdatePatchNames());
                updatePatchResult.setMessage(msg);
                log.info(msg);
                return new AsyncResult<>(updatePatchResult);
            }

            updatePatchResult = schemaPatchUtils.checkBeforeUpdatePatches(patchTaskInfo);

            if (!updatePatchResult.isStatus()) {
                return new AsyncResult<>(updatePatchResult);
            }

            schemaPatchUtils.downloadAndUnzipSchemaPatch(patchTaskInfo, SchemaAction.UPDATE);

            updatePatchResult = executeUpdate(patchTaskInfo, SchemaAction.UPDATE);

            if (updatePatchResult.isStatus()) {
                log.info(String.format("schema patch insert to db serviceName=%s,version=%s", serviceName, version));
                schemaPatchUtils.persistInDb(patchTaskInfo, SchemaAction.UPDATE);
            }
        } catch (Exception e) {
            log.error("failed to updatePatch", e);
            updatePatchResult.setStatus(false);
            updatePatchResult.setMessage(e.getMessage());
        } finally {
            schemaPatchUtils.deleteSchemaPatch(serviceName, version);
        }
        return new AsyncResult<>(updatePatchResult);
    }

    @Async("asyncExecutor")
    public Future<PatchOperateResult> rollbackPatch(PatchTaskInfo patchTaskInfo) {
        String serviceName = patchTaskInfo.getServiceName();
        String version = patchTaskInfo.getVersion();
        PatchOperateResult rollbackPatchResult = new PatchOperateResult();

        try {
            SpanUtil.refreshTracing(patchTaskInfo.getTraceId());

            log.info(String.format("start to rollback schema patch serviceName=%s,version=%s", serviceName, version));

            rollbackPatchResult = schemaPatchUtils.checkBeforeRollbackPatches(patchTaskInfo);

            if (!rollbackPatchResult.isStatus()) {
                return new AsyncResult<>(rollbackPatchResult);
            }

            schemaPatchUtils.downloadAndUnzipSchemaPatch(patchTaskInfo, SchemaAction.ROLLBACK);

            rollbackPatchResult = executeUpdate(patchTaskInfo, SchemaAction.ROLLBACK);

            if (rollbackPatchResult.isStatus()) {
                log.info(String.format("schema patch insert to db serviceName=%s,version=%s", serviceName, version));
                schemaPatchUtils.persistInDb(patchTaskInfo, SchemaAction.ROLLBACK);
            }
        } catch (Exception e) {
            log.error("failed to rollback schema patch", e);
            rollbackPatchResult.setStatus(false);
            rollbackPatchResult.setMessage(e.getMessage());
        } finally {
            schemaPatchUtils.deleteSchemaPatch(serviceName, version);
        }
        return new AsyncResult<>(rollbackPatchResult);
    }

    private PatchOperateResult executeUpdate(PatchTaskInfo patchTaskInfo, SchemaAction schemaAction) {
        Set<UpdateSchemaPatchFile> allSchemaPatchFiles = schemaPatchUtils.queryAllSchemaPatchFiles(patchTaskInfo.getServiceName(), patchTaskInfo.getVersion());

        Set<UpdateSchemaPatchFile> needUpdateSchemaFiles = Sets.newHashSet();

        PatchOperateResult operateResult = new PatchOperateResult();

        StringBuilder infos = new StringBuilder();

        boolean isSuccess;

        isSuccess = executeUpdateSchemaStep(patchTaskInfo, allSchemaPatchFiles, needUpdateSchemaFiles, schemaAction, infos);

        if (!checkServiceIsRegister(allSchemaPatchFiles, patchTaskInfo.getVersion(), needUpdateSchemaFiles, infos) && schemaAction.isUpdate()) {
            schemaPatchApi.notifyThirdServiceRegister();
            isSuccess = false;
        }
        operateResult.setStatus(isSuccess);
        operateResult.setMessage(infos.toString());
        return operateResult;
    }

    private boolean executeUpdateSchemaStep(PatchTaskInfo patchTaskInfo, Set<UpdateSchemaPatchFile> allSchemaPatchFiles, Set<UpdateSchemaPatchFile> needUpdateSchemaFiles, SchemaAction schemaAction, StringBuilder infos) {
        String serviceName = patchTaskInfo.getServiceName();
        String version = patchTaskInfo.getVersion();

        List<String> updatePatchNames = schemaPatchUtils.sortedSchemaPatchNames(patchTaskInfo.getUpdatePatchNames(), schemaAction);

        List<SchemaPatchRegisterRequest> schemaPatchRegisterRequests = schemaPatchApi.querySchemaRegisterInfos();

        String currentPatchName = schemaPatchUtils.queryCurrentPatchName(serviceName, version);

        boolean isSuccess = true;

        for (SchemaPatchRegisterRequest schemaPatchRegisterRequest : schemaPatchRegisterRequests) {
            Set<UpdateSchemaPatchFile> updateSchemaPatchFiles = schemaPatchUtils.searchSchemaPatchFiles(schemaPatchRegisterRequest, allSchemaPatchFiles);

            if (updateSchemaPatchFiles.isEmpty()) {
                log.info("{} not search schema files in patch:{}", schemaPatchRegisterRequest.toString(), updatePatchNames.toString());
                continue;
            }

            needUpdateSchemaFiles.addAll(updateSchemaPatchFiles);

            PatchOperateResult patchOperateResult = schemaPatchApi.notifyToThirdService(schemaPatchRegisterRequest, new SchemaPatchUpdateRequest(serviceName, version, currentPatchName, updatePatchNames.get(updatePatchNames.size() - 1), updatePatchNames, schemaAction));
            if (!patchOperateResult.isStatus()) {
                String failedMsg = String.format("failed to execute service=%s,version=%s,url=%s,thirdService=%s,result=%s", patchTaskInfo.getServiceName(), patchTaskInfo.getVersion(), schemaPatchRegisterRequest.getUrl(), schemaPatchRegisterRequest.getServiceName(), patchOperateResult.getMessage());
                log.error(failedMsg);
                infos.append(failedMsg);
                isSuccess = false;
            } else {
                log.info("success to execute service={},version={},url={},thirdService={},result={}", patchTaskInfo.getServiceName(), patchTaskInfo.getVersion(), schemaPatchRegisterRequest.getUrl(), schemaPatchRegisterRequest.getServiceName(), patchOperateResult.getMessage());
            }
        }
        return isSuccess;
    }

    private boolean checkServiceIsRegister(Set<UpdateSchemaPatchFile> allSchemaPatchFiles, String version, Set<UpdateSchemaPatchFile> needUpdateSchemaFiles, StringBuilder infos) {
        if (StringUtils.equals(version, "V20.19.40.R4.B2")) {
            log.info("schema patch version={}", version);
            return true;
        }

        if (needUpdateSchemaFiles.size() != allSchemaPatchFiles.size()) {
            allSchemaPatchFiles.removeAll(needUpdateSchemaFiles);
            log.error("schema file not register,files={}", allSchemaPatchFiles.toString());
            infos.append(String.format("schema file not register,files=%s", allSchemaPatchFiles.toString()));
            return false;
        }
        return true;
    }
}