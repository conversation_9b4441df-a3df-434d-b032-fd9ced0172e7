/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PublishRollbackMsg.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/6
 * </p>
 * <p>
 * 完成日期：2023/4/6
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.rollback.executor;

import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.reply.producer.ReplyingEventPublisher;
import com.zte.daip.manager.patcher.api.dto.BigDataPatchRollbackMsg;
import com.zte.daip.manager.patcher.api.dto.PatchHostInfo;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackMsg;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.internal.guava.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PublishRollbackMsg {

    @Autowired
    private ReplyingEventPublisher replyingEventPublisher;

    public void publishRollbackMsg(String clusterId, PatchRollbackHostDto rollbackHostDto, String servicePatchHome,
        String rollbackShell, boolean isBigData) {
        log.info("publish rollback msg:{},{}", clusterId, servicePatchHome);
        Set<String> targetHostNames =
            rollbackHostDto.getPatchHostInfoList().stream().map(PatchHostInfo::getHostName).collect(Collectors.toSet());
        RequestMessageBody<PatchRollbackMsg> requestMessageBody = new RequestMessageBody();
        requestMessageBody.setTargetHosts(targetHostNames);
        requestMessageBody.setBody(PatchRollbackMsg.builder().clusterId(clusterId).bigData(isBigData)
            .command(rollbackShell).patchHome(servicePatchHome).roleName(rollbackHostDto.getRoleName())
            .serviceInstanceId(rollbackHostDto.getServiceInstanceId()).serviceName(rollbackHostDto.getServiceName())
            .build());
        requestMessageBody.setTimeOut(30 * 60 * 1000L);
        log.debug("rollbackShell:{},targetHostNames:{}", rollbackShell, targetHostNames);
        replyingEventPublisher.send("patchRollback", requestMessageBody, "patchesRollbackAckHandler");
    }

    public void publishBigDataRollbackMsg(String clusterId, List<PatchRollbackHostDto> rollbackHostDtos,
        List<BigDataPatchRollbackMsg> bigDataPatchRollbackMsgs) {
        log.info("publish rollback big data msg:{}", clusterId);
        Set<String> targetHostNames = Sets.newHashSet();
        for (PatchRollbackHostDto patchRollbackHostDto : rollbackHostDtos) {
            targetHostNames.addAll(patchRollbackHostDto.getPatchHostInfoList().stream().map(PatchHostInfo::getHostName)
                .collect(Collectors.toSet()));
        }
        RequestMessageBody<List<BigDataPatchRollbackMsg>> requestMessageBody = new RequestMessageBody();
        requestMessageBody.setTargetHosts(targetHostNames);
        requestMessageBody.setBody(bigDataPatchRollbackMsgs);
        requestMessageBody.setTimeOut(30 * 60 * 1000L);
        replyingEventPublisher.send("bigDataPatchRollback", requestMessageBody, "bigDataPatchesRollbackAckHandler");
    }
}