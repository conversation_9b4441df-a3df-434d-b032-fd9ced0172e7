package com.zte.daip.manager.patcher.domain.upload.service;

import com.google.common.collect.Maps;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.upload.cache.PatchValidatedResultCache;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchValidateBean;
import com.zte.daip.manager.patcher.domain.upload.event.PatchValidatedFinishEvent;
import com.zte.daip.manager.patcher.domain.upload.operate.PatchTypeOperateFactory;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatchFullValidateService {

    @Autowired
    private PatchValidatedResultCache patchValidatedResultCache;

    @Autowired
    private PatchInfoService patchInfoService;

    @Autowired
    private PatchTypeOperateFactory patchTypeOperateFactory;

    @Autowired
    private ApplicationContext applicationContext;
    @Value("${dap.manager.patch.filter.fullPatch:VMAX}")
    private String ignoreList;

    @Async
    public void validateFullPatches(String batchId) {
        log.info("start to validate full patches :{}", batchId);
        Map<PatchValidateBean, PatchUploadResult> patchMap = patchValidatedResultCache.queryResultMapByBatchId(batchId);
        List<PatchBean> patchBeans =
            patchMap.values().stream().map(PatchUploadResult::getPatchBean).collect(Collectors.toList());
        Map<String, String> lastestFullPatch = lastestFullPatch(patchBeans);
        patchMap.forEach(((patchValidateKey, patchUploadResult) -> {
            String patchName = patchUploadResult.getPatchName();
            PatchBean patchBean = patchUploadResult.getPatchBean();
            String fullKey = patchTypeOperateFactory.getPatchType(patchBean).generateFullKey(patchBean);
            if (!ignore(patchName) && lastestFullPatch.containsKey(fullKey)) {
                String fullPatchName = lastestFullPatch.get(fullKey);
                if (patchName.compareToIgnoreCase(fullPatchName) < 0) {
                    patchUploadResult = new PatchUploadResult(false, "Exist lastest full-patch", patchName, true,
                        OnePatchProcess.FULL_PATCH_CHECK);
                } else {
                    patchUploadResult.setOneProcess(OnePatchProcess.FULL_PATCH_CHECK);
                }
            } else {
                patchUploadResult.setOneProcess(OnePatchProcess.FULL_PATCH_CHECK);
            }
            patchValidatedResultCache.updateValidateResult(patchValidateKey, patchUploadResult);
        }));
        applicationContext.publishEvent(new PatchValidatedFinishEvent(batchId));
    }

    private Map<String, String> lastestFullPatch(List<PatchBean> patchBeans) {

        Map<String, String> historyFullPatch = historyFullPatch();
        patchBeans.stream().filter(PatchBean::isFullpatch).forEach(patchBean -> {
            String fullPatchKey = patchTypeOperateFactory.getPatchType(patchBean).generateFullKey(patchBean);
            if (!historyFullPatch.containsKey(fullPatchKey)) {
                historyFullPatch.put(fullPatchKey, patchBean.getPatchName());
            } else if (patchBean.getPatchName().compareToIgnoreCase(historyFullPatch.get(fullPatchKey)) > 0) {
                historyFullPatch.replace(fullPatchKey, patchBean.getPatchName());
            }
        });
        return historyFullPatch;
    }

    private Map<String, String> historyFullPatch() {
        List<PatchDetailPo> fullPatchList = patchInfoService.findByIsFullPatch(1);
        Map<String, String> fullPatchCache = Maps.newHashMap();
        fullPatchList.forEach(patchDetailPo -> {
            String patchName = patchDetailPo.getPatchName();
            String fullPatchKey = generateFullKey(patchDetailPo);
            if (!fullPatchCache.containsKey(fullPatchKey)) {
                fullPatchCache.put(fullPatchKey, patchName);
            } else if (patchName.compareToIgnoreCase(fullPatchCache.get(fullPatchKey)) > 0) {
                fullPatchCache.replace(fullPatchKey, patchName);
            }
        });
        return fullPatchCache;
    }

    private String generateFullKey(PatchDetailPo patchDetailPo) {
        PatchBean patchBean = new PatchBean();
        patchBean.setContainPatch(patchDetailPo.getIsContainerPatch() == 1);
        patchBean.setPatchName(patchDetailPo.getPatchName());
        patchBean.setSrcVersion(patchDetailPo.getBaseVersion());
        patchBean.setService(patchDetailPo.getService());
        return patchTypeOperateFactory.getPatchType(patchBean).generateFullKey(patchBean);
    }

    private boolean ignore(String patchFileName) {
        String[] ignores = StringUtils.split(ignoreList, ",");
        for (String e : ignores) {
            if (StringUtils.lowerCase(patchFileName).contains(StringUtils.lowerCase(e))) {
                return true;
            }
        }
        return false;
    }

}
