/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchHistoryQueryService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/23
 * </p>
 * <p>
 * 完成日期：2021/3/23
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.common;

import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.domain.query.assembler.PatchHistoryDtoAssembler;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchHistoryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;


/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@CacheConfig(cacheNames = "patchHistory")
@Slf4j
public class PatchHistoryService {

    private static final int BATCH_NUMBER = Short.MAX_VALUE / 15;

    @Autowired
    private PatchHistoryRepository patchHistoryRepository;

    @Autowired
    private PatchHistoryDtoAssembler patchHistoryDtoAssembler;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final String SERVICE_PATCH_HISTORY_TABLE_NAME = "dapmanager_patch_history";


    @Cacheable(key = "'allHistory'")
    public List<PatchHistory> queryAllPatchHistoryInfo() {
        return patchHistoryRepository.findAll();
    }

    @CacheEvict(allEntries = true)
    public void deleteByServiceNameAndPatchNamesAndIps(String serviceName, List<String> patchNames,List<String> ips) {
        patchHistoryRepository.deleteByServiceNameAndPatchNamesAndIps(serviceName,patchNames, ips);
    }

    public void deleteByHistoryKeyWithoutContainer(String serviceName, String serviceInstanceId, String roleName,
        String ip, List<String> patchNames) {
        List<String> patchNameList =
            org.apache.commons.collections4.CollectionUtils.isEmpty(patchNames) ? Lists.newArrayList("") : patchNames;
        patchHistoryRepository.deleteByHistoryKeyWithoutContainer(confirmParam(serviceName),
            confirmParam(serviceInstanceId), confirmParam(roleName), ip, patchNameList);
    }

    private String confirmParam(String param) {
        return StringUtils.isBlank(param) ? "" : param;
    }

    @CacheEvict(allEntries = true)
    public void deleteByPatchHistoryList(List<PatchHistory> patchHistorieList) {
        patchHistoryRepository.deleteInBatch(patchHistorieList);
    }

    @Cacheable(key = "'allHistoryPatchNames'")
    public List<String> queryPatchHistoryName() {
        List<String> patchNames = patchHistoryRepository.queryPatchName();
        if (CollectionUtils.isEmpty(patchNames)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(patchNames.stream().collect(Collectors.toSet()));
    }

    public List<PatchHistoryDto> queryPatchDispatchInfoByPatchKey(PatchKeyDo patchKeyDo) {
        final List<PatchHistory> patchDispatches = queryPatchHistoryInfoByPatchKey(patchKeyDo.getPatchName(), patchKeyDo.getServiceName());

        return patchDispatches.stream().map(patchHistoryDtoAssembler::patchDispatch2Dto).collect(Collectors.toList());
    }

    @Cacheable(key = "#p0+'@'+#p1")
    public List<PatchHistory> queryPatchHistoryInfoByPatchKey(String patchName, String serviceName) {
        return patchHistoryRepository.queryPatchHistoryByPatchKey(patchName, serviceName);
    }

    @Cacheable(key = "#p0+'@'+#p1")
    public List<PatchHistory> queryPatchHistoryInfoByServiceNameAndIp(String serviceName, String ip) {
        return patchHistoryRepository.queryPatchHistoryInfoByServiceNameAndIp(serviceName, ip);
    }

    @CacheEvict(allEntries = true)
    public int batchDeleteByIpServiceName(List<PatchHistory> patchHistoryList) {
        String sql = String.format("delete from %s where ip=? and servicename=?", SERVICE_PATCH_HISTORY_TABLE_NAME);

        jdbcTemplate.batchUpdate(sql, patchHistoryList, patchHistoryList.size(), (preparedStatement, patchHistory) -> {
            preparedStatement.setString(1, patchHistory.getId().getIp());
            preparedStatement.setString(2, patchHistory.getId().getServiceName());
        });
        return patchHistoryList.size();
    }

    @CacheEvict(allEntries = true)
    public int batchDeleteByIpServiceNameRoleName(List<PatchHistory> patchHistoryList) {
        String sql = String.format("delete from %s where ip=? and servicename=? and rolename=?", SERVICE_PATCH_HISTORY_TABLE_NAME);

        jdbcTemplate.batchUpdate(sql, patchHistoryList, patchHistoryList.size(), (preparedStatement, patchHistory) -> {
            preparedStatement.setString(1, patchHistory.getId().getIp());
            preparedStatement.setString(2, patchHistory.getId().getServiceName());
            preparedStatement.setString(3, patchHistory.getId().getRoleName());
        });
        return patchHistoryList.size();
    }


    @Cacheable(key = "'serviceName'+'@'+#p1")
    public List<PatchHistory> queryPatchHistoryInfoByServiceName(String serviceName) {
        return patchHistoryRepository.queryPatchHistoryByServiceName(serviceName);
    }

    public List<String> querySchemaPatchHistory() {
        List<PatchHistory> allPatchHistory = patchHistoryRepository.findAll();
        return allPatchHistory.stream().filter(patchHistory -> StringUtils.contains(patchHistory.getId().getPatchName(), Constants.SCHEMA_PATCH)).map(patchHistory -> patchHistory.getId().getPatchName()).collect(Collectors.toList());
    }

    @CacheEvict(allEntries = true)
    public void save(PatchHistory patchHistory) {
        patchHistoryRepository.save(patchHistory);
    }

    @CacheEvict(allEntries = true)
    public void saveBatch(List<PatchHistory> patchHistories) {
        if (CollectionUtils.isEmpty(patchHistories)) {
            return;
        }

        if (patchHistories.size() > BATCH_NUMBER) {
            List<List<PatchHistory>> partitions = Lists.partition(patchHistories, BATCH_NUMBER);
            partitions.stream().forEach(patchHistory -> patchHistoryRepository.saveAll(patchHistory));
        } else {
            patchHistoryRepository.saveAll(patchHistories);
        }
    }
}