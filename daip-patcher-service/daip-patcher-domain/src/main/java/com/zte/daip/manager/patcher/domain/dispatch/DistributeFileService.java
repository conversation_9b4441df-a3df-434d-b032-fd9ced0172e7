/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UpdateDistributeProgressEvent.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/9
 * </p>
 * <p>
 * 完成日期：2023/3/9
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.dispatch;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.filemanagement.api.bean.DistributeFileBean;
import com.zte.daip.manager.filemanagement.api.bean.DistributeFileParam;
import com.zte.daip.manager.filemanagement.api.bean.FileCommandBean;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class DistributeFileService {
    @Autowired
    private FileDistributeControllerApi fileDistributeControllerApi;

    @Autowired
    private PatchEnvApi patchEnvApi;

    public String distributeFiles(Set<String> targetHosts, List<PatchDetailPo> distributePatches)
        throws DaipBaseException {
        try {
            log.info("Prepare distribute patches,targetHosts: {}, need distribute patches: {}", targetHosts.toString(),
                distributePatches.toString());
            List<DistributeFileBean> distributeFileBeans = organizeDistributeFileBeans(targetHosts, distributePatches);

            DistributeFileParam distributeFileParam = new DistributeFileParam();
            distributeFileParam.setDistributeFileBeans(distributeFileBeans);
            return fileDistributeControllerApi.distributeFiles(distributeFileParam);
        } catch (Exception e) {
            throw new DaipBaseException("prepare distribute param and distribute patch failed!", e);
        }
    }

    public List<DistributeFileBean> organizeDistributeFileBeans(Set<String> distributeHosts,
        List<PatchDetailPo> distributePatches) throws DaipBaseException {
        Map<String, List<PatchDetailPo>> service4Patches =
            distributePatches.stream().collect(Collectors.groupingBy(PatchDetailPo::getService));
        List<DistributeFileBean> distributeFileBeans = Lists.newArrayList();
        for (Map.Entry<String, List<PatchDetailPo>> entry : service4Patches.entrySet()) {
            String service = entry.getKey();
            List<PatchDetailPo> patches = entry.getValue();
            DistributeFileBean distributeFileBean = new DistributeFileBean();
            String basePath = patchEnvApi.getRepositoryHomeEnv() + File.separator + "patch" + File.separator + service;
            List<FileCommandBean> fileCommandBeans = patches.stream()
                .map(patchDetailPo -> FileCommandBean.builder()
                    .file(patchDetailPo.getPatchName() + File.separator + patchDetailPo.getPatchName() + ".zip")
                    .build())
                .collect(Collectors.toList());
            distributeFileBean.setSrcBasePath(basePath);
            distributeFileBean.setUrl(patchEnvApi.getPatchUrl());
            distributeFileBean.setTargetBasePath(basePath);
            distributeFileBean.setFiles(fileCommandBeans);
            distributeFileBean.setTargethost(Lists.newArrayList(distributeHosts));
            distributeFileBeans.add(distributeFileBean);
        }
        return distributeFileBeans;
    }
}