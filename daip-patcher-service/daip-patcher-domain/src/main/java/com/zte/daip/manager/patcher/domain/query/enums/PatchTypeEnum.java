/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchTypeEnum.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/8
 * </p>
 * <p>
 * 完成日期：2021/4/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.query.enums;

import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public enum PatchTypeEnum {

    /*服务级*/
    SERVICE("service", "ServicePatchSourceGenerator"),

    /*角色级*/
    ROLE("role", "RolePatchSourceGenerator"),

    /*实例级*/
    INSTANCE("instance", "InstancePatchSourceGenerator"),

    /*ZDH级*/
    ZDH("ZDH", "ZDHPatchSourceGenerator");

    private final String type;

    private final String serviceName;

    PatchTypeEnum(String type, String serviceName) {
        this.type = type;
        this.serviceName = serviceName;
    }

    public String getType() {
        return type;
    }

    public String getServiceName() {
        return serviceName;
    }

    public static PatchTypeEnum queryPatchType(String type) {
        PatchTypeEnum[] patchTypes = PatchTypeEnum.values();

        for (PatchTypeEnum patchType : patchTypes) {
            if (StringUtils.equalsIgnoreCase(type, patchType.getType())) {
                return patchType;
            }
        }
        return SERVICE;

    }

    public static PatchTypeEnum queryPatchTypeByServiceName(String serviceName, ServiceModel serviceModel) {

        final String zdh = "zdh";

        if (StringUtils.equals(serviceName, zdh)) {
            return ZDH;
        }

        return queryPatchType(serviceModel == null ? "" : serviceModel.getPatchType());
    }

}
