/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchRollbackModifyIpService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/21
 * </p>
 * <p>
 * 完成日期：2023/3/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.common;

import java.util.List;

import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.update.response.OnePatchUpdateInfo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRollbackKey;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRollbackPo;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchRollbackRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@CacheConfig(cacheNames = "patchRollback")
public class PatchRollbackService {

    private static final int BATCH_NUMBER = Short.MAX_VALUE / 15;

    @Autowired
    private PatchRollbackRepository patchRollbackRepository;

    public List<PatchRollbackPo> queryByIps(List<String> ips) {
        return patchRollbackRepository.queryByIps(ips);
    }

    public List<PatchRollbackPo> queryIpsByServiceAndInstanceAndRole(String serviceName, String serviceInstanceId,
        String roleName, String patchName) {
        return patchRollbackRepository.queryIpsByServiceAndInstanceAndRole(serviceName, serviceInstanceId, roleName,
            patchName);
    }

    public void deleteByRollbackKeyWithoutContainer(String serviceName, String serviceInstanceId, String roleName,
        String ip, List<String> patchNames) {
        log.info("serviceName:{},serviceInstanceId:{},roleName:{},ip:{},patchNames:{}", serviceName, serviceInstanceId,
            roleName, ip, patchNames.toString());
        List<String> patchNameList = CollectionUtils.isEmpty(patchNames) ? Lists.newArrayList("") : patchNames;
        patchRollbackRepository.deleteByRollbackKey(confirmParam(serviceName), confirmParam(serviceInstanceId),
            confirmParam(roleName), ip, patchNameList);
    }

    private String confirmParam(String param) {
        return StringUtils.isBlank(param) ? "" : param;
    }

    public void saveRollbackPatches(List<OnePatchUpdateInfo> onePatchUpdateInfoList) {
        List<PatchRollbackPo> patchRollbackPos = Lists.newArrayList();
        for (OnePatchUpdateInfo onePatch : onePatchUpdateInfoList) {
            PatchRollbackPo patchRollbackPo = new PatchRollbackPo();
            patchRollbackPo.setPatchUptime(onePatch.getTime());
            PatchRollbackKey patchRollbackKey = new PatchRollbackKey();
            patchRollbackKey.setIp(onePatch.getHostIp());
            patchRollbackKey.setServiceName(onePatch.getService());
            patchRollbackKey.setPatchName(onePatch.getPatchName());
            patchRollbackKey.setServiceInstanceId(onePatch.getInstance());
            patchRollbackKey.setRoleName(onePatch.getRoleName());
            patchRollbackPo.setId(patchRollbackKey);
            patchRollbackPos.add(patchRollbackPo);
        }
        log.info("patchRollbackPos:{}", patchRollbackPos.toString());
        saveBatch(patchRollbackPos);
    }

    @CacheEvict(allEntries = true)
    public void saveBatch(List<PatchRollbackPo> patchRollbackPos) {
        if (CollectionUtils.isEmpty(patchRollbackPos)) {
            return;
        }
        if (patchRollbackPos.size() > BATCH_NUMBER) {
            List<List<PatchRollbackPo>> partitions = Lists.partition(patchRollbackPos, BATCH_NUMBER);
            partitions.stream().forEach(rollbackPos -> patchRollbackRepository.saveAll(rollbackPos));
        } else {
            patchRollbackRepository.saveAll(patchRollbackPos);
        }
    }

}