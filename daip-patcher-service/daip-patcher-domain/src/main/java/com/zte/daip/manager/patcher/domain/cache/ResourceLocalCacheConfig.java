/**
 * <p><owner>10168351</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchLocalCacheConfig.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/11/29</p>
 * <p>完成日期：2021/11/29</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Configuration
@EnableCaching
@Slf4j
public class ResourceLocalCacheConfig
{
    public static final int DEFAULT_TTL = 5;
    private static final int DEFAULT_MAXSIZE = 50000;
    private static final int INITIAL_CAPACITY = 10000;

    public static final String RESOURCE_CAFFEINE_CACHE_MANAGER = "caffeineCacheManager";
    public static final String HOST_RESOURCE_INFO = "hostResourceInfo";


    @Bean (RESOURCE_CAFFEINE_CACHE_MANAGER)
    public CacheManager caffeineCacheManager()
    {
        Caffeine caffeine = Caffeine.newBuilder().initialCapacity(INITIAL_CAPACITY)
            .maximumSize(DEFAULT_MAXSIZE).expireAfterWrite(DEFAULT_TTL, TimeUnit.SECONDS);

        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(caffeine);
        cacheManager.setCacheNames(getCacheNames());
        cacheManager.setAllowNullValues(false);

        return cacheManager;
    }

    private List<String> getCacheNames()
    {
        List<String> cacheNames = Lists.newArrayList();

        cacheNames.add(HOST_RESOURCE_INFO);

        return cacheNames;
    }
}