/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: NotUpdateSchemaFiles.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2024/1/25
 * </p>
 * <p>
 * 完成日期：2024/1/25
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.schema.utils;

import com.zte.daip.manager.patcher.api.schema.UpdateSchemaPatchFile;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
public enum FilterUpdateSchemaFiles {
    patch_update_config("", "patch-update-config.xml"), patch_rollback_path(".*rollback.*", "");

    private String fileName;

    private String filePath;

    FilterUpdateSchemaFiles(String filePath, String fileName) {
        this.fileName = fileName;
        this.filePath = filePath;
    }

    public static boolean filterSchemaPatchFile(UpdateSchemaPatchFile schemaPatchFile) {
        for (FilterUpdateSchemaFiles schemaFile : values()) {
            if (StringUtils.isEmpty(schemaFile.filePath) && StringUtils.isNotEmpty(schemaFile.fileName)) {
                boolean fileNameMatch = filterSchemaFile(schemaFile.fileName, schemaPatchFile.getUpdatePatchFileName());
                if (fileNameMatch) {
                    return false;
                }
            }
            if (StringUtils.isNotEmpty(schemaFile.filePath) && StringUtils.isNotEmpty(schemaFile.fileName)) {
                boolean fileNameMatch = filterSchemaFile(schemaFile.fileName, schemaPatchFile.getUpdatePatchFileName());
                boolean filePathMatch = filterSchemaFile(schemaFile.filePath, schemaPatchFile.getUpdatePatchFilePath());
                if (fileNameMatch && filePathMatch) {
                    return false;
                }
            }

            if (StringUtils.isNotEmpty(schemaFile.filePath) && StringUtils.isEmpty(schemaFile.fileName)) {
                boolean filePathMatch = filterSchemaFile(schemaFile.filePath, schemaPatchFile.getUpdatePatchFilePath());
                if (filePathMatch) {
                    return false;
                }
            }
        }
        return true;
    }

    private static boolean filterSchemaFile(String filterName, String schemaName) {
        Pattern pattern = Pattern.compile(filterName);
        Matcher directorMatcher = pattern.matcher(schemaName);
        return directorMatcher.matches();
    }
}