package com.zte.daip.manager.patcher.domain.utils;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zte.daip.manager.event.beans.*;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;

import static com.zte.daip.manager.event.reporter.api.DaipEventReporter.*;
import static com.zte.daip.manager.event.reporter.api.DaipEventReporter.buildEventName;

@Component
public class LogEvent {

    @Autowired
    private DaipEventReporter daipEventReporter;

    public void logOpt(String operationNameI18nKey, String operationObjI18nKey, String failReason) {
        logOpt(operationNameI18nKey, null, operationObj<PERSON>18n<PERSON><PERSON>, OperationDesc.builder().build(), failReason);
    }

    public void logOpt(String operationNameI18nKey, String[] operationNameI18nArgs, String operationObjI18nKey,
        OperationDesc optDesc, String failReason) {
        logOpt(operationNameI18nKey, operationNameI18nArgs, operationObjI18nKey, optDesc,
            OperationResult.failed(failReason));
    }

    public void logOpt(String operationNameI18nKey, String operationObjI18nKey, String extraI18nKey,
        String[] extraI18nArgs) {
        logOpt(operationNameI18nKey, null, operationObjI18nKey,
            OperationDesc.builder().extraZhCn(getZhCnI18n(extraI18nKey, extraI18nArgs))
                .extraEnUs(getEnUsI18n(extraI18nKey, extraI18nArgs)).build());
    }

    public void logOpt(String operationNameI18nKey, String[] operationNameI18nArgs, String operationObjI18nKey,
        OperationDesc optDesc, OperationResult operationResult) {
        OperationName optName = OperationName.of(getEnUsI18n(operationNameI18nKey, operationNameI18nArgs),
            getZhCnI18n(operationNameI18nKey, operationNameI18nArgs));
        OperationObject optObj =
            OperationObject.of(getEnUsI18n(operationObjI18nKey, null), getZhCnI18n(operationObjI18nKey, null));
        AbstractEvent event = daipEventReporter.buildOperationLog(optName, optObj, optDesc, operationResult);

        daipEventReporter.report(event);
    }

    public void logOpt(String operationNameI18nKey, String[] operationNameI18nArgs, String operationObjI18nKey,
        OperationDesc optDesc) {
        logOpt(operationNameI18nKey, operationNameI18nArgs, operationObjI18nKey, optDesc, OperationResult.succeed());
    }

    public void logEvent(String eventName, String[] i18nArgs, String extraInfo, String[] extraInfoI18nArgs) {
        logEvent(eventName, i18nArgs, extraInfo, extraInfoI18nArgs, OperationResult.succeed());
    }

    public void logEvent(String eventName, String[] i18nArgs, String extraInfo, String[] extraInfoI18nArgs,
        String failReason) {
        logEvent(eventName, i18nArgs, extraInfo, extraInfoI18nArgs, OperationResult.failed(failReason));
    }

    public void logEvent(String eventName, String[] i18nArgs, String extraInfo, String[] extraInfoI18nArgs,
        OperationResult operationResult) {
        EventDesc eventDesc =
            StringUtils.isNotBlank(extraInfo) ? EventDesc.builder().extraEnUs(getEnUsI18n(extraInfo, extraInfoI18nArgs))
                .extraZhCn(getZhCnI18n(extraInfo, extraInfoI18nArgs)).build() : null;
        AbstractEvent event = buildEventDetail(buildEventName(eventName, i18nArgs), eventDesc, operationResult);

        daipEventReporter.report(event);
    }

    public void logEvent(String eventName, String[] i18nArgs, String extraInfo, OperationResult operationResult) {
        EventDesc eventDesc = StringUtils.isNotBlank(extraInfo)
            ? EventDesc.builder().extraEnUs(extraInfo).extraZhCn(extraInfo).build() : null;
        AbstractEvent event = buildEventDetail(buildEventName(eventName, i18nArgs), eventDesc, operationResult);
        daipEventReporter.report(event);
    }
}
