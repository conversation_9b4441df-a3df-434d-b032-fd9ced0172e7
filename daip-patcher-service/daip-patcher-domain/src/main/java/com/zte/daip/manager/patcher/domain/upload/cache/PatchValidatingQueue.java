package com.zte.daip.manager.patcher.domain.upload.cache;

import com.zte.daip.manager.patcher.domain.upload.entity.PatchValidateBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

@Service
@Slf4j
public class PatchValidatingQueue {
    private Queue<PatchValidateBean> patchQueue = new ConcurrentLinkedQueue<>();

    public void addValidatePatches(List<PatchValidateBean> patchValidateBeans) {
        if (!CollectionUtils.isEmpty(patchValidateBeans)) {
            patchQueue.addAll(patchValidateBeans);
        }
    }

    public PatchValidateBean pollOnePatch() {
        return patchQueue.poll();
    }

    public boolean isEmpty() {
        return patchQueue.isEmpty();
    }

}
