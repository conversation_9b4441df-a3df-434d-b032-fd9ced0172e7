package com.zte.daip.manager.patcher.domain.task.service;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.api.ServiceDependencyControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.ClusterBean;
import com.zte.daip.manager.common.deployer.bean.patch.ServiceDependencyBean;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.task.common.enums.WfInstanceStatus;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.i18n.DaipI18nService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.domain.schema.service.SchemaPatchTaskService;
import com.zte.daip.manager.patcher.domain.task.api.ServiceInstancePatchResource;
import com.zte.daip.manager.patcher.domain.task.assembler.CurrentPatchAssembler;
import com.zte.daip.manager.patcher.domain.task.assembler.PatchTaskAssembler;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.PatchUpdateInnerControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchCategoryEnum;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskModifyPermisionEnum;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskTypeEnum;
import com.zte.daip.manager.response.CommonResponse;
import com.zte.daip.manager.task.api.ability.api.TaskManagerPlatformApi;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowDeleteRequest;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowRequest;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowSummary;
import com.zte.daip.manager.task.api.ability.dto.NestWorkFlowSummaryRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchTaskOperateService {
    @Autowired
    private PatchTaskService patchTaskService;

    @Autowired
    private TaskManagerPlatformApi taskManagerPlatformApi;

    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Autowired
    private ClusterInfoControllerApi clusterInfoControllerApi;

    @Autowired
    private PatchTaskAssembler patchTaskAssembler;

    @Autowired
    private CurrentPatchAssembler currentPatchAssembler;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private SchemaPatchApi schemaPatchApi;

    @Autowired
    private SchemaPatchTaskService schemaPatchTaskService;

    @Autowired
    private DaipI18nService daipI18nService;

    @Autowired
    private PatchUpdateInnerControllerApi patchUpdateInnerControllerApi;

    @Autowired
    private ServiceDependencyControllerApi serviceDependencyControllerApi;

    @Autowired
    private PatchInfoService patchInfoService;

    private DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public long createPatchTask(PatchTaskDto patchTaskDto) throws DaipBaseException {
        PatchTaskPo patchTaskPo = initPatchTaskBeforeCreate(patchTaskDto);
        assembleTaskClusterName(patchTaskPo);
        PatchTaskPo patchTask = patchTaskService.addPatchTask(patchTaskPo);
        if (patchTask == null) {
            throw new DaipBaseException(String.format("patch task: %s is null.", patchTaskPo.getTaskName()));
        }
        try {
            NestWorkFlowRequest request = patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTask);
            CommonResponse<Boolean> result = taskManagerPlatformApi.createNestWorkflow(request);
            if (result == null || result.getData() == null || !result.getData()) {
                throw new DaipBaseException(
                    String.format("failed to sync to task management: %s", patchTask.getTaskName()));
            }
        } catch (DaipBaseException e) {
            patchTaskService.deleteByTaskId(patchTask.getTaskId());
            throw new DaipBaseException(String.format("create patch task: %s", e.getMessage()));
        }
        return patchTask.getTaskId();
    }

    public void modifyPatchTask(PatchTaskDto patchTaskDto) throws DaipBaseException {
        if (patchTaskDto == null) {
            throw new DaipBaseException("modify patch task: patch task is null.");
        }
        List<ServiceInstancePatchInfo> patchTaskRollbackInfos = queryPatchTaskRollBackResource(patchTaskDto);

        if (CollectionUtils.isEmpty(patchTaskRollbackInfos)) {
            throw new DaipBaseException("modify patch task: patch task context is empty.");
        }

        patchTaskDto.setContext(patchTaskRollbackInfos);
        PatchTaskPo modifyPatchTaskPo = patchTaskAssembler.convertPatchTaskDto2Po(patchTaskDto);
        PatchTaskPo oldPatchTaskInfo = convertOldPatchTaskPo(modifyPatchTaskPo.getTaskId());
        if (!StringUtils.equals(oldPatchTaskInfo.getTaskName(), modifyPatchTaskPo.getTaskName())
            && checkTaskNameDuplicate(modifyPatchTaskPo.getTaskName())) {
            log.error("Duplicate patch task name: {}", modifyPatchTaskPo.getTaskName());
            throw new DaipBaseException("Duplicate patch task name.");
        }
        assembleTaskClusterName(modifyPatchTaskPo);
        NestWorkFlowRequest request = patchTaskAssembler.patchTaskPo2NestWorkRequest(modifyPatchTaskPo);
        CommonResponse<Boolean> result = taskManagerPlatformApi.editNestWorkflow(request);
        if (result != null && result.getData() != null && result.getData()) {
            patchTaskService.updatePatchTask(modifyPatchTaskPo);
            deletePatchTaskWhenModifyOperateType(modifyPatchTaskPo, oldPatchTaskInfo);
        }
    }

    public void removePatchTask(List<Long> taskIds) throws DaipBaseException {
        List<PatchTaskPo> patchTaskPos = patchTaskService.queryByTaskIds(taskIds);
        if (CollectionUtils.isEmpty(patchTaskPos)) {
            throw new DaipBaseException(String.format("failed to remove patch task: %s is null.", taskIds));
        }
        List<NestWorkFlowDeleteRequest> requests = Lists.newArrayList();
        for (PatchTaskPo patchTaskPo : patchTaskPos) {
            String accessType = patchTaskAssembler.assemblePatchTaskPo2AccessType(patchTaskPo);
            NestWorkFlowDeleteRequest request = new NestWorkFlowDeleteRequest();
            request.setParentId(patchTaskPo.getTaskId());
            request.setParentName(patchTaskPo.getTaskName());
            request.setAccessType(accessType);
            requests.add(request);
        }
        CommonResponse<Boolean> result = taskManagerPlatformApi.delete(requests);
        if (result != null && result.getData()) {
            patchTaskService.deleteByTaskIds(taskIds);
            patchTaskService.clearRelationTaskId(taskIds);
            schemaPatchApi.removeSchemaPatchTaskInfos(patchTaskPos);
        }
    }

    public void triggerPatchTask(long taskId) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        if (patchTaskPo == null) {
            throw new DaipBaseException(String.format("failed to trigger patch task: %d is null.", taskId));
        }
        patchTaskService.updateAllowModifyByTaskId(patchTaskPo.getTaskId(),
            PatchTaskModifyPermisionEnum.DENIED.getModifyPermision());
        String accessType = patchTaskAssembler.assemblePatchTaskPo2AccessType(patchTaskPo);
        taskManagerPlatformApi.trigger(patchTaskPo.getTaskId(), patchTaskPo.getTaskName(), accessType);
    }

    public void retryPatchTask(long taskId) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        if (patchTaskPo == null) {
            throw new DaipBaseException(String.format("failed to retry patch task: %d is null.", taskId));
        }
        String accessType = patchTaskAssembler.assemblePatchTaskPo2AccessType(patchTaskPo);
        taskManagerPlatformApi.retry(patchTaskPo.getTaskId(), patchTaskPo.getTaskName(), accessType);
    }

    public void pausePatchTask(long taskId) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        if (patchTaskPo == null) {
            throw new DaipBaseException(String.format("failed to pause patch task: %d is null.", taskId));
        }
        String accessType = patchTaskAssembler.assemblePatchTaskPo2AccessType(patchTaskPo);
        taskManagerPlatformApi.pause(patchTaskPo.getTaskId(), patchTaskPo.getTaskName(), accessType);
    }

    public void resumePatchTask(long taskId) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        if (patchTaskPo == null) {
            throw new DaipBaseException(String.format("failed to resume patch task: %d is null.", taskId));
        }
        String accessType = patchTaskAssembler.assemblePatchTaskPo2AccessType(patchTaskPo);
        taskManagerPlatformApi.resume(patchTaskPo.getTaskId(), patchTaskPo.getTaskName(), accessType);
    }

    public List<PatchTaskDto> queryAll() throws DaipBaseException {
        List<PatchTaskPo> patchTaskPos = patchTaskService.queryAllPatchTasks();
        if (CollectionUtils.isEmpty(patchTaskPos)) {
            return Lists.newArrayList();
        }
        List<NestWorkFlowSummaryRequest> requests = Lists.newArrayList();
        for (PatchTaskPo p : patchTaskPos) {
            requests.add(patchTaskAssembler.patchTaskPo2NestWorkFlowSummaryRequest(p));
        }
        CommonResponse<List<NestWorkFlowSummary>> result = taskManagerPlatformApi.summaries(requests);
        if (result == null) {
            throw new DaipBaseException("failed to query all patch task: summaries from task management is null.");
        }
        List<PatchTaskDto> patchTaskDtos = Lists.newArrayList();
        List<NestWorkFlowSummary> summaries = result.getData();
        List<ClusterBean> clusterBeans = clusterInfoControllerApi.queryAll();
        if (CollectionUtils.isEmpty(clusterBeans)) {
            return Lists.newArrayList();
        }
        for (PatchTaskPo p : patchTaskPos) {
            String clusterName = clusterBeans.stream()
                .filter(c -> StringUtils.equals(String.valueOf(c.getClusterId()), p.getClusterId()))
                .map(ClusterBean::getClusterName).findFirst().orElse("");
            p.setClusterName(clusterName);
            PatchTaskDto patchTaskDto = assemblePatchTaskDto(summaries, p);
            patchTaskDtos.add(patchTaskDto);
        }
        return patchTaskDtos.stream().sorted(Comparator.comparingLong(PatchTaskDto::getTaskId).reversed())
            .collect(Collectors.toList());
    }

    public PatchTaskDto queryPatchDetailById(long taskId) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        if (patchTaskPo == null) {
            throw new DaipBaseException(String.format("failed to query patch task: %d is null.", taskId));
        }
        PatchTaskDto patchTaskDto = patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo);
        NestWorkFlowSummaryRequest request = patchTaskAssembler.patchTaskPo2NestWorkFlowSummaryRequest(patchTaskPo);
        CommonResponse<NestWorkFlowSummary> result = taskManagerPlatformApi.summary(request);
        if (result == null) {
            throw new DaipBaseException("failed to query patch task: summary from task management is null.");
        }
        NestWorkFlowSummary summary = result.getData();
        if (patchTaskDto.getTaskType() == 0 && (summary.getStatus() == null
            || StringUtils.equals(summary.getStatus(), WfInstanceStatus.TERMINATE.getStatus())
            || StringUtils.equals(summary.getStatus(), WfInstanceStatus.FAILURE.getStatus()))) {
            updateRollbackInfo(patchTaskDto);
        }
        currentPatchAssembler.organizeCurrentPatchPoints(patchTaskDto.getContext());

        return patchTaskDto;
    }

    public void updateRollbackInfo(PatchTaskDto patchTaskDto) {
        List<ServiceInstancePatchInfo> patchTaskRollbackInfos = queryPatchTaskRollBackResource(patchTaskDto);
        patchTaskDto.setContext(patchTaskRollbackInfos);
    }

    /* Started by AICoder, pid:pd93cif92b96a9e149d90bf4f03cc827f5c1e8ca */
    public PatchOperateResult checkTaskCanRollback(long taskId) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        PatchTaskPo rollbackPatchTaskPo = patchTaskService.queryByTaskId(patchTaskPo.getRelationTaskId());
        if (rollbackPatchTaskPo != null) {
            return PatchOperateResult.fail(daipI18nService.getLabel("patch_task_duplicate_fail",
                new String[] {rollbackPatchTaskPo.getTaskName()}));
        }

        PatchTaskDto patchTaskDto = patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo);

        if (currentPatchAssembler.checkTaskIsAllRollback(patchTaskDto.getContext())) {
            return PatchOperateResult.fail(daipI18nService.getLabel("rollback_patch_task_finish"));
        }

        StringBuilder infos = new StringBuilder();

        if (!currentPatchAssembler.checkTaskIsSequence(patchTaskDto.getContext(), PatchTaskTypeEnum.ROLLBACK, infos)) {
            return PatchOperateResult
                .fail(daipI18nService.getLabel("rollback_task_sequence_fail", new String[] {infos.toString()}));
        }
        return PatchOperateResult.success("");
    }
    /* Ended by AICoder, pid:pd93cif92b96a9e149d90bf4f03cc827f5c1e8ca */

    public PatchOperateResult checkTaskCanDuplicate(long taskId) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        String copyTaskName = patchTaskPo.getTaskName() + "_" + daipI18nService.getLabel("update_patch_copy");
        if (checkTaskNameDuplicate(copyTaskName)) {
            return PatchOperateResult
                .fail(daipI18nService.getLabel("patch_task_duplicate_fail", new String[] {copyTaskName}));
        }
        PatchTaskDto patchTaskDto = patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo);
        if (currentPatchAssembler.checkTaskIsAllUpdate(patchTaskDto.getContext())) {
            return PatchOperateResult.fail(daipI18nService.getLabel("duplicate_patch_task_finish"));
        }
        StringBuilder infos = new StringBuilder();
        if (!currentPatchAssembler.checkTaskIsSequence(patchTaskDto.getContext(), PatchTaskTypeEnum.UPDATE, infos)) {
            return PatchOperateResult
                .fail(daipI18nService.getLabel("copy_task_sequence_fail", new String[] {infos.toString()}));
        }
        return PatchOperateResult.success("");
    }

    public PatchTaskDto queryPatchTaskByTaskId(long taskId) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        if (patchTaskPo == null) {
            throw new DaipBaseException(String.format("failed to query patch task: %d is null.", taskId));
        }
        NestWorkFlowSummaryRequest request = patchTaskAssembler.patchTaskPo2NestWorkFlowSummaryRequest(patchTaskPo);
        CommonResponse<NestWorkFlowSummary> result = taskManagerPlatformApi.summary(request);
        if (result != null) {
            NestWorkFlowSummary summary = result.getData();
            return assemblePatchTaskDto(Lists.newArrayList(summary), patchTaskPo);
        }
        return patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo);
    }

    public void copyPatchTask(long taskId) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        String copyTaskName = patchTaskPo.getTaskName() + "_" + daipI18nService.getLabel("update_patch_copy");

        if (StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.ORDINARY.getPatchCategory())) {
            copyOrdinaryPatchTask(taskId, copyTaskName);
        } else {
            copySchemaPatchTask(taskId, copyTaskName);
        }
    }

    public void rollbackPatchTask(PatchTaskDto patchTaskDto) throws DaipBaseException {
        if (patchTaskDto == null) {
            throw new DaipBaseException("patch task is null.");
        }
        patchTaskDto.setTaskType(PatchTaskTypeEnum.ROLLBACK.getTaskType());
        PatchTaskPo patchTaskPo = patchTaskAssembler.convertPatchTaskDto2Po(patchTaskDto);
        if (checkTaskNameDuplicate(patchTaskPo.getTaskName())) {
            throw new DaipBaseException(String.format("Duplicate patch task name: %s", patchTaskPo.getTaskName()));
        }
        patchTaskPo.setAllowModify(PatchTaskModifyPermisionEnum.DENIED.getModifyPermision());
        patchTaskPo.setCreateTime(new Date());
        PatchTaskPo patchTask = patchTaskService.addPatchTask(patchTaskPo);
        if (patchTask == null) {
            throw new DaipBaseException(String.format("patch task: %s is null.", patchTaskPo.getTaskName()));
        }

        assembleTaskClusterName(patchTask);
        if (patchTask.getRelationTaskId() != 0L) {
            rollbackByRelationTaskId(patchTask);
        } else {
            throw new DaipBaseException(String
                .format("failed to rollback patch task: %s relation patch task id is empty.", patchTask.getTaskName()));
        }
    }

    public List<ServiceInstance> queryNeedRestartService(PatchTaskDto patchTaskDto) throws DaipBaseException {
        if (CollectionUtils.isEmpty(patchTaskDto.getContext())
            || CollectionUtils.isEmpty(patchTaskDto.getRelationServices())
            || org.springframework.util.StringUtils.isEmpty(patchTaskDto.getClusterId())) {
            throw new DaipBaseException("query param exist null value.");
        }
        List<ServiceInstancePatchInfo> updateInstance = patchTaskDto.getContext();
        String clusterId = patchTaskDto.getClusterId();
        List<ServiceInstance> relationServices = patchTaskDto.getRelationServices();
        List<String> serviceInstanceIds =
            updateInstance.stream().map(instance -> instance.getServiceInstance().getServiceInstanceId())
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<String> needRestartInstanceId = queryNeedRestartServiceInstance(clusterId, serviceInstanceIds);
        List<ServiceInstance> needRestartService = relationServices.stream()
            .filter(info -> needRestartInstanceId.contains(info.getServiceInstanceId())).collect(Collectors.toList());
        return needRestartService;
    }

    /* Started by AICoder, pid:i077eg3e81r25711472e0a2cf0a97c362d71dba3 */
    /**
     * 查询需要重启的服务实例列表
     * @param clusterId 集群ID
     * @param serviceInstanceIds 服务实例ID列表
     * @return 需要重启的服务实例ID列表
     * @throws DaipBaseException 如果发生错误
     */
    public List<String> queryNeedRestartServiceInstance(String clusterId, List<String> serviceInstanceIds)
        throws DaipBaseException {
        // 获取需要更新的补丁信息
        Map<String, List<String>> needUpdatePatchs =
            patchUpdateInnerControllerApi.queryNeedUpdatePatchs(clusterId, serviceInstanceIds);
        // 筛选出需要重启的服务实例
        List<String> needRestartService = needUpdatePatchs.entrySet().stream()
            .filter(entry -> needRestart(entry.getValue())).map(Map.Entry::getKey).collect(Collectors.toList());
        // 如果存在需要重启的服务实例
        if (!CollectionUtils.isEmpty(needRestartService)) {
            // 查询依赖于这些服务的其他服务实例
            List<ServiceDependencyBean> dependedServices =
                serviceDependencyControllerApi.queryDependedService(clusterId, needRestartService);
            // 将所有依赖于需要重启服务的服务实例合并，并去重
            List<String> finalRestartServices = new ArrayList<>();
            dependedServices.forEach(service -> {
                finalRestartServices.add(service.getInstanceId());
                finalRestartServices.addAll(service.getDependedInstanceIds());
            });

            return finalRestartServices.stream().distinct().collect(Collectors.toList());
        }

        // 如果没有需要重启的服务实例，返回空列表
        return new ArrayList<>();
    }

    /* Ended by AICoder, pid:i077eg3e81r25711472e0a2cf0a97c362d71dba3 */

    private boolean needRestart(List<String> patchNameList) {
        List<PatchDetailPo> patchDetailPos = patchInfoService.queryAllPatchExceptScheme();
        for (PatchDetailPo patchDetail : patchDetailPos) {
            if (patchNameList.contains(patchDetail.getPatchName()) && patchDetail.getHotPatch() == 0) {
                return true;
            }
        }
        return false;
    }

    private PatchTaskPo copyOrdinaryPatchTask(long taskId, String taskName) throws DaipBaseException {
        PatchTaskPo patchTaskPo = initBeforeCopyTask(taskId, taskName);
        if (checkTaskNameDuplicate(patchTaskPo.getTaskName())) {
            throw new DaipBaseException(String.format("Duplicate patch task name: %s", patchTaskPo.getTaskName()));
        }
        PatchTaskPo patchTask = patchTaskService.addPatchTask(patchTaskPo);
        if (patchTask == null) {
            throw new DaipBaseException(String.format("patch task: %s is null.", patchTaskPo.getTaskName()));
        }
        assembleTaskClusterName(patchTask);
        NestWorkFlowRequest request = patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTask);
        try {
            CommonResponse<Boolean> result = taskManagerPlatformApi.createNestWorkflow(request);
            if (result == null || result.getData() == null || !result.getData()) {
                throw new DaipBaseException(
                    String.format("failed to sync to task management: %s", patchTask.getTaskName()));
            }
        } catch (Exception e) {
            patchTaskService.deleteByTaskId(patchTask.getTaskId());
            throw new DaipBaseException(String.format("Failed to roll back patch task: %s", e.getMessage()));
        }
        return patchTask;
    }

    private PatchTaskPo initBeforeCopyTask(long taskId, String taskName) throws DaipBaseException {
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        patchTaskPo.setTaskName(taskName);
        patchTaskPo.setTaskId(0);
        patchTaskPo.setAllowModify(PatchTaskModifyPermisionEnum.DENIED.getModifyPermision());
        patchTaskPo.setCreateTime(new Date());
        patchTaskPo.setRelationTaskId(0);
        PatchTaskDto patchTaskDto = patchTaskAssembler.convertPatchTaskPo2Dto(patchTaskPo);
        patchTaskPo.setContext(JSON.toJSONString(queryPatchTaskRollBackResource(patchTaskDto)));
        return patchTaskPo;
    }

    private void copySchemaPatchTask(long taskId, String taskName) {
        List<PatchTaskInfo> copyTaskInfos = schemaPatchTaskService.queryCanCopyPatches(taskId);

        schemaPatchTaskService.createAndTriggerPatchTask(taskName, copyTaskInfos);
    }

    private PatchTaskPo initPatchTaskBeforeCreate(PatchTaskDto patchTaskDto) throws DaipBaseException {
        if (patchTaskDto == null) {
            throw new DaipBaseException("create patch task: patch task is null.");
        }

        if (checkTaskNameDuplicate(patchTaskDto.getTaskName())) {
            throw new DaipBaseException("Duplicate patch task name.");
        }
        List<ServiceInstancePatchInfo> patchTaskRollbackInfos = queryPatchTaskRollBackResource(patchTaskDto);

        if (CollectionUtils.isEmpty(patchTaskRollbackInfos)) {
            throw new DaipBaseException("create patch task: patch task context is empty.");
        }

        patchTaskDto.setContext(patchTaskRollbackInfos);
        PatchTaskPo patchTaskPo = patchTaskAssembler.convertPatchTaskDto2Po(patchTaskDto);
        patchTaskPo.setAllowModify(PatchTaskModifyPermisionEnum.ALLOW.getModifyPermision());
        patchTaskPo.setCreateTime(new Date());
        PatchTaskPo patchTaskInfo = new PatchTaskPo();
        BeanUtils.copyProperties(patchTaskPo, patchTaskInfo);
        return patchTaskInfo;
    }

    private void rollbackByRelationTaskId(PatchTaskPo patchTask) throws DaipBaseException {
        PatchTaskPo relationPatchTaskPo = patchTaskService.queryByTaskId(patchTask.getRelationTaskId());
        if (relationPatchTaskPo == null) {
            throw new DaipBaseException(String.format("failed to rollback patch task: %s relation patch task is null.",
                patchTask.getTaskName()));
        }
        NestWorkFlowRequest request = patchTaskAssembler.patchTaskPo2NestWorkRequest(patchTask, relationPatchTaskPo);
        try {
            CommonResponse<Boolean> result = taskManagerPlatformApi.createNestWorkflow(request);
            if (result == null || result.getData() == null || !result.getData()) {
                patchTaskService.deleteByTaskId(patchTask.getTaskId());
                throw new DaipBaseException(
                    String.format("failed to sync to task management: %s", patchTask.getTaskName()));
            } else {
                patchTaskService.updateRelationTaskIdByTaskId(patchTask.getRelationTaskId(), patchTask.getTaskId());
                if (StringUtils.equals(patchTask.getPatchCategory(), PatchCategoryEnum.SCHEMA.getPatchCategory())) {
                    schemaPatchApi.saveRollbackSchemaPatchInfos(patchTask.getRelationTaskId(), patchTask.getTaskId());
                }
                triggerPatchTask(patchTask.getTaskId());
            }
        } catch (Exception e) {
            patchTaskService.deleteByTaskId(patchTask.getTaskId());
            throw new DaipBaseException(String.format("Failed to roll back patch task: %s", e.getMessage()));
        }
    }

    private PatchTaskPo convertOldPatchTaskPo(long taskId) throws DaipBaseException {
        PatchTaskPo oldPatchTaskInfo = patchTaskService.queryByTaskId(taskId);
        if (oldPatchTaskInfo == null) {
            throw new DaipBaseException(String.format("failed to modify patch task: %d is null.", taskId));
        }
        PatchTaskPo patchTaskInfo = new PatchTaskPo();
        BeanUtils.copyProperties(oldPatchTaskInfo, patchTaskInfo);
        return patchTaskInfo;
    }

    private void deletePatchTaskWhenModifyOperateType(PatchTaskPo patchTaskPo, PatchTaskPo patchTaskInfo)
        throws DaipBaseException {
        if (patchTaskInfo.getOperateType() != patchTaskPo.getOperateType()) {
            String accessType = patchTaskAssembler.assemblePatchTaskPo2AccessType(patchTaskInfo);
            CommonResponse<Boolean> deleteResult =
                taskManagerPlatformApi.delete(patchTaskInfo.getTaskId(), patchTaskInfo.getTaskName(), accessType);
            if (deleteResult == null || deleteResult.getData() == null || !deleteResult.getData()) {
                log.warn("Modify: failed to delete patch task name: {}", patchTaskInfo.getTaskName());
            }
        }
    }

    private List<ServiceInstancePatchInfo> queryPatchTaskRollBackResource(PatchTaskDto patchTaskDto) {
        try {
            ServiceInstancePatchResource resource =
                applicationContext.getBean(patchTaskDto.getPatchCategory(), ServiceInstancePatchResource.class);
            return resource.organizeServiceInstancePatchInfo(patchTaskDto);
        } catch (Exception e) {
            log.error("query patch task rollback resource error:", e);
            return Lists.newArrayList();
        }
    }

    private PatchTaskDto assemblePatchTaskDto(List<NestWorkFlowSummary> summaries, PatchTaskPo p)
        throws DaipBaseException {
        PatchTaskDto patchTaskDto = patchTaskAssembler.convertPatchTaskPo2Dto(p);
        NestWorkFlowSummary nestWorkFlowSummary =
            summaries.stream().filter(s -> s.getParentId() == p.getTaskId()).findFirst().orElse(null);
        if (nestWorkFlowSummary != null) {
            patchTaskDto.setStatus(nestWorkFlowSummary.getStatus());
            patchTaskDto.setSuccessCnt(nestWorkFlowSummary.getSuccessCnt());
            patchTaskDto.setTotal(nestWorkFlowSummary.getTotal());
            if (nestWorkFlowSummary.getStartTime() != null) {
                patchTaskDto.setLatestExecutionTime(df.format(
                    nestWorkFlowSummary.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()));
            }
        }
        return patchTaskDto;
    }

    private List<String> queryAllPatchTaskNames() {
        List<PatchTaskPo> patchTaskPos = patchTaskService.queryAllPatchTasks();
        if (CollectionUtils.isEmpty(patchTaskPos)) {
            return Lists.newArrayList();
        }
        return patchTaskPos.stream().map(PatchTaskPo::getTaskName).collect(Collectors.toList());
    }

    private void assembleTaskClusterName(PatchTaskPo patchTask) {
        ClusterBean clusterBean = clusterInfoControllerApi.query(patchTask.getClusterId());
        if (clusterBean != null) {
            patchTask.setClusterName(clusterBean.getClusterName());
        }
    }

    private boolean checkTaskNameDuplicate(String taskName) {
        List<String> taskNames = queryAllPatchTaskNames();
        return taskNames.contains(taskName);
    }
}