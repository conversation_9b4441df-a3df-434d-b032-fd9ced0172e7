package com.zte.daip.manager.patcher.domain.update.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.patcher.api.dto.OnePatchUpdateResult;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateResult;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateRequest;
import com.zte.daip.manager.patcher.api.update.request.ServiceInstance;
import com.zte.daip.manager.patcher.api.update.request.ServicePatchInfo;
import com.zte.daip.manager.patcher.api.update.request.SimplePatchInfo;
import com.zte.daip.manager.patcher.domain.query.enums.PatchTypeEnum;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@Service
public class PatchUpdateBeansConvertor {
    private static final int PATCH_KEY_LENGTH = 2;

    public PatchUpdateCacheDto convertUpdateRequest2CacheDto(String clusterId, List<PatchUpdateRequest> requests) {
        PatchUpdateCacheDto patchUpdateCacheDto = new PatchUpdateCacheDto();
        patchUpdateCacheDto.setClusterId(clusterId);
        patchUpdateCacheDto.setStartTime(System.currentTimeMillis());
        Map<String, List<OnePatchUpdateResult>> key2OnePatchResult = genKey2OnePatchResult(requests);
        List<PatchUpdateResult> patchUpdateResults = genPatchUpdateResults(key2OnePatchResult);
        patchUpdateCacheDto.setPatchResults(patchUpdateResults);

        return patchUpdateCacheDto;

    }

    private List<PatchUpdateResult> genPatchUpdateResults(Map<String, List<OnePatchUpdateResult>> key2OnePatchResult) {
        List<PatchUpdateResult> patchUpdateResults = Lists.newArrayList();
        if (CollectionUtils.isEmpty(key2OnePatchResult)) {
            return patchUpdateResults;
        }
        key2OnePatchResult.forEach((key, onePatchUpdateResults) -> {
            String[] patchKey = StringUtils.split(key, "|");
            if (patchKey != null && patchKey.length == PATCH_KEY_LENGTH) {
                PatchUpdateResult patchUpdateResult = new PatchUpdateResult();
                patchUpdateResult.setPatchName(patchKey[0]);
                patchUpdateResult.setPatchType(patchKey[1]);
                patchUpdateResult.setHosts(onePatchUpdateResults);
                patchUpdateResults.add(patchUpdateResult);
            }
        });
        return patchUpdateResults;
    }

    private Map<String, List<OnePatchUpdateResult>> genKey2OnePatchResult(List<PatchUpdateRequest> requests) {
        Map<String, List<OnePatchUpdateResult>> key2OnePatchResult = Maps.newConcurrentMap();
        if (!CollectionUtils.isEmpty(requests)) {
            requests.forEach(p -> p.getIp2Patches().forEach((ip, patchInfos) -> {
                if (!CollectionUtils.isEmpty(patchInfos)) {
                    patchInfos
                        .forEach(servicePatchInfo -> genSimplePatchs(key2OnePatchResult, ip, servicePatchInfo, p));
                }
            }));
        }
        return key2OnePatchResult;
    }

    private void genSimplePatchs(Map<String, List<OnePatchUpdateResult>> key2OnePatchResult, String ip,
        ServicePatchInfo servicePatchInfo, PatchUpdateRequest patchUpdateRequest) {
        if (patchUpdateRequest == null) {
            return;
        }
        List<SimplePatchInfo> patches = servicePatchInfo.getPatches();
        List<ServiceInstance> serviceInstances = servicePatchInfo.getServiceInstances();
        String patchType = patchUpdateRequest.getPatchType();
        switch (PatchTypeEnum.queryPatchType(patchType)) {
            case INSTANCE:
                Set<String> instanceIds =
                    serviceInstances.stream().map(ServiceInstance::getServiceInstanceId).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(instanceIds)) {
                    instanceIds.forEach(instanceId -> {
                        OnePatchUpdateResult onePatchUpdateResult =
                            new OnePatchUpdateResult(ip, instanceId, "", patchUpdateRequest.getPatchHome());
                        genKey2OnePatchResult(key2OnePatchResult, onePatchUpdateResult, patches, patchType);
                    });
                }
                break;
            case ROLE:
                serviceInstances.forEach(s -> {
                    if (!CollectionUtils.isEmpty(s.getRoles())) {
                        s.getRoles().forEach(roleName -> {
                            OnePatchUpdateResult onePatchUpdateResult = new OnePatchUpdateResult(ip, "",
                                roleName, patchUpdateRequest.getPatchHome());
                            genKey2OnePatchResult(key2OnePatchResult, onePatchUpdateResult, patches, patchType);
                        });
                    }
                });
                break;
            default:
                OnePatchUpdateResult onePatchUpdateResult =
                    new OnePatchUpdateResult(ip, "", "", patchUpdateRequest.getPatchHome());
                genKey2OnePatchResultMergeByService(key2OnePatchResult, onePatchUpdateResult, patches, patchType);
                break;
        }
    }

    private void genKey2OnePatchResult(Map<String, List<OnePatchUpdateResult>> key2OnePatchResult,
        OnePatchUpdateResult onePatchUpdateResult, List<SimplePatchInfo> patches, String patchType) {
        if (CollectionUtils.isEmpty(patches)) {
            return;
        }
        patches.forEach(simplePatchInfo -> {
            String key = String.format("%s|%s", simplePatchInfo.getPatchName(), patchType);
            List<OnePatchUpdateResult> onePatchUpdateResults =
                key2OnePatchResult.getOrDefault(key, Lists.newArrayList());
            onePatchUpdateResults.add(onePatchUpdateResult);
            key2OnePatchResult.put(key, onePatchUpdateResults);
        });
    }

    private void genKey2OnePatchResultMergeByService(Map<String, List<OnePatchUpdateResult>> key2OnePatchResult,
        OnePatchUpdateResult onePatchUpdateResult, List<SimplePatchInfo> patches, String patchType) {
        if (CollectionUtils.isEmpty(patches)) {
            return;
        }
        patches.forEach(simplePatchInfo -> {
            String key = String.format("%s|%s", simplePatchInfo.getPatchName(), patchType);
            List<OnePatchUpdateResult> onePatchUpdateResults =
                key2OnePatchResult.getOrDefault(key, Lists.newArrayList());
            if (CollectionUtils.isEmpty(onePatchUpdateResults) || onePatchUpdateResults.stream()
                .noneMatch(p -> StringUtils.equals(p.getIpAddress(), onePatchUpdateResult.getIpAddress()))) {
                onePatchUpdateResults.add(onePatchUpdateResult);
                key2OnePatchResult.put(key, onePatchUpdateResults);
            }
        });
    }
}