package com.zte.daip.manager.patcher.domain.update.schedule;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.update.cache.PatchUpdateReportCacheQueue;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class UpdatePatchResultScheduler {

    @Autowired
    private PatchUpdateReportCacheQueue patchUpdateReportCacheQueue;
    @Autowired
    private PatchUpdateResultService patchUpdateResultService;

    @Scheduled(cron = "0/5 * * * * ?")
    public void cron() {
        List<PatchResult> patchResults = Lists.newArrayList();
        while (!patchUpdateReportCacheQueue.isEmptyResultCache()) {
            List<PatchResult> patchResultList = patchUpdateReportCacheQueue.poll();
            patchResults.addAll(patchResultList);
        }
        patchUpdateResultService.saveSuccessPatchResults(patchResults);
    }

}
