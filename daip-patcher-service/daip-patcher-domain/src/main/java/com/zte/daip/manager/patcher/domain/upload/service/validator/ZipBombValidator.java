/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ZipBombValidator.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/12
 * </p>
 * <p>
 * 完成日期：2021/4/12
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * <p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * <p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.upload.service.validator;

import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class ZipBombValidator implements PatchValidator {

    @Override
    public PatchUploadResult checkPatch(PatchBean patchBean, File file, List<PatchDetailPo> patchDetailPos) {
        if (isZipBomb(file)) {
            log.error("{} maybe is zip bomb", file.getName());
            return new PatchUploadResult(false, "patch maybe is zip bomb", file.getName(), true,
                OnePatchProcess.NORMAL_CHECK);
        }

        return new PatchUploadResult(true, "upload patch success", file.getName(), false, OnePatchProcess.VALIDATING);

    }

    private boolean isZipBomb(File file) {
        boolean isBomb = false;
        try (ZipFile zipFile = new ZipFile(file, Charset.forName("GBK"))) {
            final int maxSizeDiff = 100;
            long compressedSize = file.length();
            long uncompressedSize = 0;
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                uncompressedSize = uncompressedSize + entry.getSize();
            }
            if (compressedSize < 0 || uncompressedSize < 0) {
                log.error(file.getName() + ":Zip bomb attack detected, invalid sizes: compressed " + compressedSize
                    + ", uncompressed " + uncompressedSize);
                isBomb = true;
            }
            if (compressedSize * maxSizeDiff < uncompressedSize) {
                log.error(file.getName() + ":Zip bomb attack detected, invalid sizes: compressed " + compressedSize
                    + ", uncompressed " + uncompressedSize);
                isBomb = true;
            }
        } catch (Exception e) {
            log.error("Assert zip file is bomb error", e);
            isBomb = true;
        }
        return isBomb;
    }

    @Override
    public int order() {
        return 5;
    }
}