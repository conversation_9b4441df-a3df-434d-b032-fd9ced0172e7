package com.zte.daip.manager.patcher.domain.constants;

public class DomainI18nKey {

    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_EXECUTOR_DISTRIBUTE_PATCH = "com_zte_daip_manager_patcher_executor_distribute_patch";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_PUBLISH_DISTRIBUTE_PATCH_EVENT = "com_zte_daip_manager_patcher_publish_distribute_patch_event";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH = "com_zte_daip_manager_patcher_auto_distribute_patch";

    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH_FAILED = "com_zte_daip_manager_patcher_auto_distribute_patch_failed";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_AUTO_DISTRIBUTE_PATCH_NO_NEED = "com_zte_daip_manager_patcher_auto_distribute_patch_no_need";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_CREATE_PATCH_TASK = "com_zte_daip_manager_patcher_create_patch_task";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_MODIFY_PATCH_TASK = "com_zte_daip_manager_patcher_modify_patch_task";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_REMOVE_PATCH_TASK = "com_zte_daip_manager_patcher_remove_patch_task";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_TRIGGER_PATCH_TASK = "com_zte_daip_manager_patcher_trigger_patch_task";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_RETRY_PATCH_TASK = "com_zte_daip_manager_patcher_retry_patch_task";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_PAUSE_PATCH_TASK = "com_zte_daip_manager_patcher_pause_patch_task";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_RESUME_PATCH_TASK = "com_zte_daip_manager_patcher_resume_patch_task";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_ROLLBACK_PATCH_TASK = "com_zte_daip_manager_patcher_rollback_patch_task";
    public static final String COM_ZTE_DAIP_MANAGER_PATCHER_QUERY_SERVICE_INSTANCE = "com_zte_daip_manager_patcher_query_service_instance";

    private DomainI18nKey(){

    }
}
