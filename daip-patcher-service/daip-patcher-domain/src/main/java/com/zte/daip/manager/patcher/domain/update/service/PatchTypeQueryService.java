/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchHomeQueryService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/31
 * </p>
 * <p>
 * 完成日期：2021/3/31
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.domain.update.service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@Service
public class PatchTypeQueryService {

    private static final String DEFAULT_PATCH_TYPE = "service";
    private static final String DEFAULT_ZDH_PATCH_TYPE = "ZDH";
    public static final String DEFAULT_ZDH_BASE_SERVICE_NAME = "zdh";
    private static final String BIG_DATA_SERVICE_ID = "dap.manager.common.bigdata";

    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    public String queryPatchTypeByServiceName(String clusterId, String serviceName) {
        if (StringUtils.equalsIgnoreCase(serviceName, DEFAULT_ZDH_BASE_SERVICE_NAME)) {
            return DEFAULT_ZDH_PATCH_TYPE;
        }
        ServiceModel serviceModel = queryServiceModel(clusterId, serviceName);
        if (StringUtils.equals(serviceModel.getPatchType(), "") && StringUtils.endsWithIgnoreCase(serviceModel.getComponentType(), BIG_DATA_SERVICE_ID)) {
            return DEFAULT_ZDH_PATCH_TYPE;
        }
        return StringUtils.isEmpty(serviceModel.getPatchType()) ? DEFAULT_PATCH_TYPE : serviceModel.getPatchType();
    }

    public ServiceModel queryServiceModel(String clusterId, String serviceName) {
        List<ServiceModel> serviceModels = queryServiceModelByClusterId(clusterId);
        List<ServiceModel> models = serviceModels.stream()
            .filter(e -> StringUtils.equals(serviceName, e.getServiceName())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(models)) {
            log.warn(String.format("not find serviceModel for clusterId:%s,serviceName:%s", clusterId, serviceName));
            return new ServiceModel();
        }
        return models.get(0);
    }

    public List<ServiceModel> queryServiceModelByClusterId(String clusterId) {
        return productModelInfoControllerApi.queryByClusterId(clusterId);
    }

    public Set<String> queryBigDataServiceIds(String clusterId) {
        List<ServiceModel> serviceModels = queryServiceModelByClusterId(clusterId);
        return serviceModels.stream()
            .filter(e -> StringUtils.equalsIgnoreCase(e.getComponentType(), BIG_DATA_SERVICE_ID))
            .map(ServiceModel::getServiceId).collect(Collectors.toSet());
    }

}