package com.zte.daip.manager.patcher.application.service.i18n;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import static com.zte.daip.manager.patcher.application.service.i18n.DaipTaskModelI18nRegister.DAIPMANAGER_I18N_FILE;


/**
 * 功能描述:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/28
 */
@Slf4j
@Service
public class RegisterTaskServiceI18nEvent implements ApplicationListener<ApplicationReadyEvent> {



    @Autowired
    private DaipTaskModelI18nRegister daipTaskModelI18nRegister;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        try {
            log.info("start to register daip-task i18n file!");
            daipTaskModelI18nRegister.updateTaskServiceI18n(DAIPMANAGER_I18N_FILE);
            log.info("success to register daip-task i18n file!");
        } catch (Exception e) {
            log.error("fail to register daip-task i18n file!", e);
        }
    }
}
