/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchUpdateAppService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/9
 * </p>
 * <p>
 * 完成日期：2021/4/9
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.service;

import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.domain.update.PatchUpdateService;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateCacheService;
import com.zte.daip.manager.patcher.inner.api.dto.PatchRollbackHostInfo;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchUpdateAppService {

    @Autowired
    private PatchUpdateService patchUpdateService;
    @Autowired
    private PatchUpdateCacheService patchUpdateCacheService;
    @Autowired
    private DaipEventReporter daipEventReporter;

    public void updatePatch(UpdateRequest updateRequest) {
        log.info("receive update patch request:" + updateRequest.toString());
        if (!patchUpdateCacheService.isUpdatePatchPermit(updateRequest.getClusterId())) {
            log.warn("Patch is performing other operations.");
            daipEventReporter.info(I18nKeyConstants.UPGRADE_PATCH,
                String.format("Patch is performing other operations.clusterId: %s", updateRequest.getClusterId()));
            return;
        }
        patchUpdateService.update(updateRequest);
        log.info("update patch request end.");
    }

    public List<ServiceInstancePatchInfo> queryRollbackPoints(String clusterId, List<String> instanceIds) {
        log.info("start to query rollback points by cluster: {} and instanceIds: {}", clusterId, instanceIds);
        List<ServiceInstancePatchInfo> patchRollbackHostInfos =
            patchUpdateService.queryRollbackPoints(clusterId, instanceIds);
        log.info("finished to query rollback points by cluster: {} and instanceIds: {}", clusterId, instanceIds);
        return patchRollbackHostInfos;
    }

    public Map<String, List<String>> queryNeedUpdatePatchs(String clusterId, List<String> instanceIds) {
        log.info("start to query need update patchs by cluster: {} and instanceIds: {}", clusterId, instanceIds);
        Map<String, List<String>> needUpdatePatchs =
                patchUpdateService.queryNeedUpdatePatchs(clusterId, instanceIds);
        log.info("finished to query need update patchs by cluster: {} and instanceIds: {}", clusterId, instanceIds);
        return needUpdatePatchs;
    }
}