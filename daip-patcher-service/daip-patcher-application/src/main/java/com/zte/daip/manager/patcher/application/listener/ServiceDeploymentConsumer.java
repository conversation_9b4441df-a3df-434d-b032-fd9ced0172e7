/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServiceDeploymenetConsumer.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/6/7
 * </p>
 * <p>
 * 完成日期：2021/6/7
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.listener;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.zte.daip.communication.api.ConsumerHandler;
import com.zte.daip.communication.api.EventListenerTo;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.exception.MessageCenterException;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.application.bean.ServiceDeploymentInfo;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.dispatch.DistributeFileService;
import com.zte.daip.manager.patcher.domain.dispatch.DistributeProgressService;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@EventListenerTo("serviceDeployment")
@Slf4j
@Service
public class ServiceDeploymentConsumer implements ConsumerHandler<String> {

    @Autowired
    private PatchInfoService patchInfoService;

    @Autowired
    private DistributeProgressService distributeProgressService;

    @Autowired
    private DistributeFileService distributeFileService;

    @Override
    public String handle(String body) throws MessageCenterException {
        log.info("Receive serviceDeployment topic,start to dispatch patches.");
        try {
            ServiceDeploymentInfo serviceDeploymentInfo = JSONObject
                .parseObject(body, new TypeReference<RequestMessageBody<ServiceDeploymentInfo>>() {}).getBody();
            if (serviceDeploymentInfo != null) {
                Set<String> targetHosts = serviceDeploymentInfo.getTargetHostsName();
                List<PatchDetailPo> distributePatches = getPatchDetailPos(serviceDeploymentInfo);

                if (!CollectionUtils.isEmpty(distributePatches)) {
                    log.info("ServiceDeployment  auto distribute patches:{}", distributePatches.toString());
                    distributePatches(targetHosts, distributePatches);
                }
            }
        } catch (Exception e) {
            log.error("Distribute patches error", e);
        }
        return "Finish";
    }

    private void distributePatches(Set<String> targetHosts, List<PatchDetailPo> distributePatches)
        throws DaipBaseException {
        try {
            String distributeId = distributeFileService.distributeFiles(targetHosts, distributePatches);
            distributeProgressService.checkAndUpdateDistributeProgress(distributeId);
        } catch (Exception e) {
            throw new DaipBaseException("Failed distribute patches :", e);
        }

    }

    private List<PatchDetailPo> getPatchDetailPos(ServiceDeploymentInfo serviceDeploymentInfo) {
        List<PatchDetailPo> needDispatchAndUpdatePatches = Lists.newArrayList();
        List<PatchDetailPo> projectPatches = patchInfoService
            .findByServiceAndBaseVersion(serviceDeploymentInfo.getProjectName(), serviceDeploymentInfo.getVersion());
        List<PatchDetailPo> filterProjectPatches = projectPatches.stream()
                .filter(detail -> !StringUtils.containsIgnoreCase(detail.getPatchName(), Constants.SCHEMA_PATCH)
                        && !StringUtils.containsIgnoreCase(detail.getPatchName(), Constants.REPOSITORY_PATCH))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterProjectPatches)) {
            needDispatchAndUpdatePatches.addAll(filterProjectPatches);
        }
        List<PatchDetailPo> servicePatches = patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(
            serviceDeploymentInfo.getServiceName(), serviceDeploymentInfo.getVersion());
        if (!CollectionUtils.isEmpty(servicePatches)) {
            needDispatchAndUpdatePatches.addAll(servicePatches);
        }
        return needDispatchAndUpdatePatches;
    }

}