/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchStartAndStopAppService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/21
 * </p>
 * <p>
 * 完成日期：2023/4/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.infrastructure.PatchStartAndStopApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateServiceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchStartAndStopAppService {
    @Autowired
    private PatchStartAndStopApi patchStartAndStopApi;

    public List<String> queryCanOperateServices(String clusterId, boolean isStart,
        List<PatchOperateServiceDto> operateService) throws DaipBaseException {
        try {
            log.info("query can operate services :{},{},{}", clusterId, isStart, operateService.toString());
            Set<String> needOperateInstances = Sets.newHashSet();
            if (!CollectionUtils.isEmpty(operateService)) {
                for (PatchOperateServiceDto operateServiceDto : operateService) {
                    List<String> operateInstances = generateNeedOperateInstances(clusterId, operateServiceDto);
                    needOperateInstances.addAll(operateInstances);
                }
            }
            if (!CollectionUtils.isEmpty(needOperateInstances)) {
                List<String> canOperateInstances = patchStartAndStopApi.queryCanOperateInstances(clusterId, isStart);
                return needOperateInstances.stream().filter(canOperateInstances::contains).collect(Collectors.toList());
            }
            return Lists.newArrayList();
        } catch (Exception e) {
            throw new DaipBaseException("queryCanOperateServices exception", e);
        }

    }

    public void startCluster(String clusterId, List<String> needOperateInstanceIds) throws DaipBaseException {
        log.info("begin to start services after operate patches: {}", needOperateInstanceIds.toString());
        patchStartAndStopApi.startServiceByCluster(clusterId, needOperateInstanceIds);
    }

    public void stopCluster(String clusterId, List<String> needOperateInstanceIds) throws DaipBaseException {
        log.info("operate to stop service before operate patches: {}", needOperateInstanceIds.toString());
        patchStartAndStopApi.stopServiceByCluster(clusterId, needOperateInstanceIds);
    }

    public String queryOperatorProcess(String clusterId) throws DaipBaseException {
        return patchStartAndStopApi.queryOperateProgress(clusterId);
    }

    private List<String> generateNeedOperateInstances(String clusterId, PatchOperateServiceDto patchOperateServiceDto)
        throws DaipBaseException {
        try {
            log.info("generate need operate instance by {}:{}", patchOperateServiceDto.getServiceName(),
                patchOperateServiceDto.getServiceInstanceId());
            List<String> instanceIds = Lists.newArrayList();
            if (StringUtils.isBlank(patchOperateServiceDto.getServiceInstanceId())) {
                List<String> allInstances = patchStartAndStopApi.queryInstancesByServiceName(clusterId,
                    patchOperateServiceDto.getServiceName());
                if (!CollectionUtils.isEmpty(allInstances)) {
                    instanceIds.addAll(allInstances);
                }
            } else {
                instanceIds.add(patchOperateServiceDto.getServiceInstanceId());
            }
            List<String> needOperateInstanceIds = Lists.newArrayList();
            instanceIds.forEach(instanceId -> {
                List<String> dependInstanceIds = patchStartAndStopApi.queryDependInstances(clusterId, instanceId);
                if (!CollectionUtils.isEmpty(dependInstanceIds)) {
                    needOperateInstanceIds.addAll(dependInstanceIds);
                }
            });
            needOperateInstanceIds.addAll(instanceIds);
            log.info("generate need operate instance {}:{}:{} ", patchOperateServiceDto.getServiceName(),
                patchOperateServiceDto.getServiceInstanceId(), needOperateInstanceIds.toString());
            return needOperateInstanceIds;
        } catch (Exception e) {
            throw new DaipBaseException("generate need operate Instances error", e);
        }

    }
}