/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServiceInstallUpdatePatchConsumer.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/6/5
 * </p>
 * <p>
 * 完成日期：2021/6/5
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.listener;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zte.daip.communication.api.ConsumerHandler;
import com.zte.daip.communication.api.EventListenerTo;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.manager.common.deployer.bean.service.ServiceDeployBean;
import com.zte.daip.manager.patcher.application.service.PatchAutoUpdateAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@EventListenerTo("serviceInstall")
@Slf4j
@Service
public class ServiceInstallUpdatePatchConsumer implements ConsumerHandler<String> {
    @Autowired
    private PatchAutoUpdateAppService patchAutoUpdateAppService;

    @Override
    public String handle(String body) {
        log.info("Receive finish serviceInstall topic, start to update patch.");

        ServiceDeployBean serviceDeployBean =
            JSONObject.parseObject(body, new TypeReference<RequestMessageBody<ServiceDeployBean>>() {}).getBody();

        patchAutoUpdateAppService.update(serviceDeployBean);
        return "Finish";
    }
}