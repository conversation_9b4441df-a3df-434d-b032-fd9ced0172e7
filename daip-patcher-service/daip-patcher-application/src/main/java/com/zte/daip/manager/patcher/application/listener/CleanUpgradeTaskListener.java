package com.zte.daip.manager.patcher.application.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zte.daip.communication.api.ConsumerHandler;
import com.zte.daip.communication.api.EventListenerTo;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@EventListenerTo("TASKMANAGEMENT_CLEAN_MSG")
/* Started by AICoder, pid:37ae03635fd4427baf3f178e4a503c1d */
public class CleanUpgradeTaskListener implements ConsumerHandler<String> {
    private final PatchTaskOperateService patchTaskOperateService;

    // 通过构造函数注入 patchTaskOperateService
    public CleanUpgradeTaskListener(PatchTaskOperateService patchTaskOperateService) {
        this.patchTaskOperateService = patchTaskOperateService;
    }

    @Override
    public String handle(String body) {
        log.info("Receive clean task msg.");
        RequestMessageBody<List<Long>> messageBody =
                JSON.parseObject(body, new TypeReference<RequestMessageBody<List<Long>>>(){});
        if (messageBody != null && messageBody.getBody() != null) {
            List<Long> parentIds = messageBody.getBody();
            log.info("Start to clean task:{}", parentIds);
            try {
                patchTaskOperateService.removePatchTask(parentIds);
            } catch (Exception e) {
                log.error("Clean task error", e);
            }
        }
        return "success";
    }
}
/* Ended by AICoder, pid:37ae03635fd4427baf3f178e4a503c1d */
