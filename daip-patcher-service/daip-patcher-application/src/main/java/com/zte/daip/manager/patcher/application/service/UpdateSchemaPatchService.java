/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UpdateSchemaPatchService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/22
 * </p>
 * <p>
 * 完成日期：2021/3/22
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import com.zte.daip.manager.common.utils.pool.DaipThreadPoolExecutor;
import com.zte.daip.manager.event.reporter.api.util.SpanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.domain.schema.UpdateSchemaPatchNew;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.domain.utils.LogEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Slf4j
@Service
public class UpdateSchemaPatchService {

    @Autowired
    private SchemaPatchApi schemaPatchApi;

    @Autowired
    private UpdateSchemaPatchNew updateSchemaPatchNew;

    @Autowired
    private LogEvent logEvent;

    /* Started by AICoder, pid:h332e90244uc042140f90a6b60361c4940211228 */
    public List<PatchOperateResult> updateSchemaPatch(long taskId) {
        List<PatchTaskInfo> patchTaskInfos = schemaPatchApi.queryByUpdateTaskId(taskId);
        if (CollectionUtils.isNotEmpty(patchTaskInfos)) {
            logEvent.logOpt(I18nKeyConstants.UPDATE_SCHEMA_PATCH, I18nKeyConstants.PATCH_ZIP, I18nKeyConstants.PATCH_INFO, new String[]{patchTaskInfos.toString()});
            String traceId = SpanUtil.getTraceId();
            patchTaskInfos.forEach(patchTaskInfo -> patchTaskInfo.setTraceId(traceId));
            List<Future<PatchOperateResult>> futureList = new ArrayList<>();
            for (PatchTaskInfo patchTaskInfo : patchTaskInfos) {
                final Future<PatchOperateResult> future = updateSchemaPatchNew.updatePatch(patchTaskInfo);
                futureList.add(future);
            }
            final List<PatchOperateResult> patchOperateResults = DaipThreadPoolExecutor.waitThreadsEnd(futureList);
            return patchOperateResults.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    public List<PatchOperateResult> rollbackSchemaPatch(long taskId) {
        List<PatchTaskInfo> patchTaskInfos = schemaPatchApi.queryByRollbackTaskId(taskId);
        if (CollectionUtils.isNotEmpty(patchTaskInfos)) {
            logEvent.logOpt(I18nKeyConstants.ROLLBACK_SCHEMA_PATCH, I18nKeyConstants.PATCH_ZIP, I18nKeyConstants.PATCH_INFO, new String[]{patchTaskInfos.toString()});
            String traceId = SpanUtil.getTraceId();
            patchTaskInfos.forEach(patchTaskInfo -> patchTaskInfo.setTraceId(traceId));
            List<Future<PatchOperateResult>> futureList = new ArrayList<>();
            for (PatchTaskInfo patchTaskInfo : patchTaskInfos) {
                final Future<PatchOperateResult> future = updateSchemaPatchNew.rollbackPatch(patchTaskInfo);
                futureList.add(future);
            }
            final List<PatchOperateResult> patchOperateResults = DaipThreadPoolExecutor.waitThreadsEnd(futureList);
            return patchOperateResults.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /* Ended by AICoder, pid:h332e90244uc042140f90a6b60361c4940211228 */
}