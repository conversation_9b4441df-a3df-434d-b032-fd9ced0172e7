package com.zte.daip.manager.patcher.application.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.event.reporter.api.dto.EventLog;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.application.publish.UpdateRepositoryPatchPublish;
import com.zte.daip.manager.patcher.application.bean.RepositoryPatchUpdateInfo;
import com.zte.daip.manager.patcher.application.bean.RepositoryUpdateResult;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchDetailKey;
import com.zte.daip.manager.patcher.domain.schema.utils.SchemaPatchUtils;
import com.zte.daip.manager.patcher.domain.utils.Constants;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistory;
import com.zte.daip.manager.patcher.infrastructure.po.PatchHistoryKey;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UpdateRepositoryPatchService {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PatchHistoryService patchHistoryService;

    @Autowired
    private PatchInfoService patchInfoService;

    @Autowired
    private SpectControllerApi spectControllerApi;

    @Autowired
    private ProductControllerApi productControllerApi;

    @Autowired
    private UpdateRepositoryPatchPublish updateRepositoryPatchPublish;

    @Autowired
    private PatchEnvApi patchEnvApi;

    @Autowired
    private DaipEventReporter daipEventReporter;

    @Autowired
    private SchemaPatchUtils schemaPatchUtils;

    @Value("${dexcloud.base.microservice.name:daip-patcher-svr}")
    private String microserviceName;

    @Value("${server.servlet.port:56210}")
    private String microserviceport;

    @Value("${server.servlet.context-path:/api/daip-patcher-svr/v1}")
    private String microserviceurl;

    @Value("${repository.upgrade.timeout.inMinutes:15}")
    private int upgradeTimeout;

    private static final String UPDATE_PATCH_KEY = "UPDATE_REPOSITORY_PATCH_LOCK_KEY";

    public synchronized void updateRepositoryPatch() {
        RLock lock = redissonClient.getLock(UPDATE_PATCH_KEY);
        if (lock.isLocked()) {
            log.info("update repository patch not finished ");
            return;
        }
        try {
            lock.lock(upgradeTimeout * 60 * 1000L, TimeUnit.MILLISECONDS);
            Map<PatchDetailKey, List<PatchDetailPo>> service2Patches = queryRepositoryPatch();
            Map<PatchDetailKey, List<PatchDetailPo>> canUpdatePatch = filterCanUpdatePatch(service2Patches);

            if (canUpdatePatch.isEmpty()) {
                log.info("No repository version patch need update!");
                lock.unlock();
                return;
            }
            doUpdatePatchByKafka(canUpdatePatch);
        } catch (Exception e) {
            log.error("handle need upgrade repository patch failed", e);
            lock.unlock();
        }
    }

    public synchronized void handleRepositoryPatchResult(List<RepositoryUpdateResult> repositoryUpdateResults) {
        List<PatchHistory> successRepositoryHistory = new ArrayList<>();

        repositoryUpdateResults.forEach(result -> {
            List<String> successPatchList = result.getSuccessPatchList();
            List<PatchHistory> successHistory = successPatchList.stream()
                .map(patch -> constructPatchHistory(result.getServiceName(), patch)).collect(Collectors.toList());
            successRepositoryHistory.addAll(successHistory);
        });
        patchHistoryService.saveBatch(successRepositoryHistory);

        releaseLock();
    }

    public void releaseLock() {
        RLock lock = redissonClient.getLock(UPDATE_PATCH_KEY);
        if (lock.isLocked()) {
            lock.forceUnlock();
        }
    }

    public Map<PatchDetailKey, List<PatchDetailPo>>
        filterCanUpdatePatch(Map<PatchDetailKey, List<PatchDetailPo>> service2Patches) {
        Map<PatchDetailKey, List<PatchDetailPo>> canUpdatePatch = Maps.newHashMap();
        for (Map.Entry<PatchDetailKey, List<PatchDetailPo>> ser2Patch : service2Patches.entrySet()) {
            PatchDetailKey ser2PatchKey = ser2Patch.getKey();
            String serviceName = ser2PatchKey.getServiceName();
            String version = ser2PatchKey.getVersion();

            if (!schemaPatchUtils.isProductLoadSuccess(serviceName, version)) {
                log.error(String.format("check product load error,serviceName=%s,version=%s", serviceName, version));
                continue;
            }

            final List<PatchDetailPo> patches = ser2Patch.getValue();

            boolean result = checkDependency(serviceName, version, patches);

            if (result) {
                canUpdatePatch.put(ser2PatchKey, patches);
            } else {
                log.error(String.format("check dependency error,serviceName=%s,version=%s", serviceName, version));
            }
        }
        return canUpdatePatch;
    }

    private boolean checkDependency(String serviceName, String version, List<PatchDetailPo> patchDetails) {
        List<PatchDetailPo> patchDetailList = patchInfoService.findByServiceAndBaseVersion(serviceName, version);

        List<String> patchNames =
            patchDetailList.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toList());

        return patchDetails.stream().allMatch(patchDetail -> StringUtils.isEmpty(patchDetail.getDependPatch())
            || patchNames.contains(patchDetail.getDependPatch()));
    }

    private PatchHistory constructPatchHistory(String serviceName, String patchName) {
        PatchHistory patchHistory = new PatchHistory();
        PatchHistoryKey patchHistoryKey = new PatchHistoryKey();
        patchHistoryKey.setServiceName(serviceName);
        patchHistoryKey.setPatchName(patchName);
        patchHistoryKey.setServiceInstanceId("");
        patchHistoryKey.setRoleName("");
        patchHistoryKey.setIp("");
        patchHistory.setPatchUptime(System.currentTimeMillis());
        patchHistory.setId(patchHistoryKey);
        return patchHistory;
    }

    private void handleFailedPatch(RepositoryUpdateResult result) {
        String errorMessage = result.getErrorMessage();
        if (!StringUtils.equals("", errorMessage)) {
            log.error(errorMessage);
            EventLog eventLog = daipEventReporter
                .buildEvent(I18nKeyConstants.HANDLE_UPDATE_REPOSITORY_VERSION_RESULT, errorMessage).toBuilder()
                .targetHost(String.format("%s-%s", result.getServiceName(), result.getVersion())).build();
            daipEventReporter.error(eventLog, " repository version patch update failed");
        }

    }

    private Map<PatchDetailKey, List<PatchDetailPo>> queryRepositoryPatch() {

        List<PatchDetailPo> patchDetails = patchInfoService.queryAllPatchInfos();

        List<String> updatedPatchName = patchHistoryService.queryPatchHistoryName();

        return patchDetails.stream()
            .filter(patchDetail -> patchDetail.getPatchName().contains(Constants.REPOSITORY_PATCH)
                && !updatedPatchName.contains(patchDetail.getPatchName()))
            .sorted(Comparator.comparing(a -> a.getPatchName().toLowerCase()))
            .collect(Collectors.groupingBy(this::getKey));
    }

    private PatchDetailKey getKey(PatchDetailPo patchDetail) {
        return new PatchDetailKey(patchDetail.getService(), patchDetail.getBaseVersion());
    }

    private void doUpdatePatchByKafka(Map<PatchDetailKey, List<PatchDetailPo>> canUpdatePatch) {
        log.info("Start report update repository patch message.{}", canUpdatePatch.toString());
        List<RepositoryPatchUpdateInfo> patchUpdateInfos = Lists.newArrayList();
        try {
            for (Map.Entry<PatchDetailKey, List<PatchDetailPo>> ser2Patch : canUpdatePatch.entrySet()) {
                List<PatchDetailPo> patchDetails = ser2Patch.getValue();
                PatchDetailKey patchDetailKey = ser2Patch.getKey();
                if (!CollectionUtils.isEmpty(patchDetails)) {
                    RepositoryPatchUpdateInfo repositoryPatchUpdateInfo =
                        constructDownloadInfo(patchDetailKey, patchDetails);
                    patchUpdateInfos.add(repositoryPatchUpdateInfo);
                }
            }
            if (!CollectionUtils.isEmpty(patchUpdateInfos)) {
                log.info("need update repository version patch:{}", patchUpdateInfos.toString());
                updateRepositoryPatchPublish.publishUpdateRepositoryMsg(patchUpdateInfos);
            } else {
                releaseLock();
            }
        } catch (Exception e) {
            releaseLock();
            log.error("Report update repository patch message error", e);
        }
    }

    private RepositoryPatchUpdateInfo constructDownloadInfo(PatchDetailKey patchDetailKey,
        List<PatchDetailPo> patchDetails) {
        RepositoryPatchUpdateInfo repositoryPatchUpdateInfo = new RepositoryPatchUpdateInfo();
        repositoryPatchUpdateInfo.setPatchRepository(patchEnvApi.getRepositoryHomeEnv() + File.separator + "patch");
        repositoryPatchUpdateInfo.setServiceName(patchDetailKey.getServiceName());
        repositoryPatchUpdateInfo.setVersion(patchDetailKey.getVersion());
        repositoryPatchUpdateInfo.setSourceUrl(String.format("http://%s.%s:%s%s", microserviceName,
            System.getenv("OPENPALETTE_NAMESPACE"), microserviceport, microserviceurl + "/v1/fms/download/file"));
        List<String> patchNameList =
            patchDetails.stream().map(PatchDetailPo::getPatchName).collect(Collectors.toList());
        repositoryPatchUpdateInfo.setPatchNameList(patchNameList);
        return repositoryPatchUpdateInfo;
    }
}
