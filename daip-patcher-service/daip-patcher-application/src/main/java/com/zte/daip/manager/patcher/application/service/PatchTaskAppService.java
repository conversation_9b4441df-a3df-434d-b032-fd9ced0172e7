package com.zte.daip.manager.patcher.application.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.version.ServiceProject;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.common.task.common.enums.UpgradeModel;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.version.VersionUtil;
import com.zte.daip.manager.event.reporter.api.annotation.BusinessDomainEvent;
import com.zte.daip.manager.patcher.domain.constants.DomainI18nKey;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import com.zte.daip.manager.response.CommonResponse;
import com.zte.daip.manager.task.api.ability.api.TaskManagerPlatformApi;
import com.zte.daip.manager.task.api.ability.dto.TaskModelVersionSet;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchTaskAppService {
    @Autowired
    private PatchTaskOperateService patchTaskOperateService;

    @Autowired
    private TaskManagerPlatformApi taskManagerPlatformApi;

    @Autowired
    private SpectControllerApi spectControllerApi;

    @Autowired
    private ClusterInfoControllerApi clusterInfoControllerApi;

    private static final Pattern TASK_NAME_COMPILE = Pattern.compile("^[\u4e00-\u9fa5a-zA-Z][\\u4e00-\\u9fa50-9a-zA-Z-_]*$");

    private static final Pattern CHINESE_COMPILE = Pattern.compile("([\u4e00-\u9fa5]|[\u3000-\u303F]|[\uFF00-\uFF60])");

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_CREATE_PATCH_TASK)
    public long createPatchTask(PatchTaskDto patchTaskDto) throws DaipBaseException {
        if (patchTaskDto == null) {
            throw new DaipBaseException("patch task is null.");
        }
        log.info("start to create patch task: {}, cluster: {}", patchTaskDto.getTaskName(),
            patchTaskDto.getClusterId());
        long taskId = patchTaskOperateService.createPatchTask(patchTaskDto);
        log.info("finished to create patch task: {}, cluster: {}", patchTaskDto.getTaskName(),
            patchTaskDto.getClusterId());
        return taskId;
    }

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_MODIFY_PATCH_TASK)
    public void modifyPatchTask(PatchTaskDto patchTaskDto) throws DaipBaseException {
        if (patchTaskDto == null) {
            throw new DaipBaseException("patch task is null.");
        }
        if (!patchTaskDto.isAllowModify()) {
            log.error("failed to modify patch task: {}, modification operation is not allowed",
                patchTaskDto.getTaskName());
            throw new DaipBaseException("failed to modify patch task, modification operation is not allowed");
        }
        log.info("start to modify patch task: {}, cluster: {}", patchTaskDto.getTaskName(),
            patchTaskDto.getClusterId());
        patchTaskOperateService.modifyPatchTask(patchTaskDto);
        log.info("finished to modify patch task: {}, cluster: {}", patchTaskDto.getTaskName(),
            patchTaskDto.getClusterId());
    }

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_REMOVE_PATCH_TASK)
    public void removePatchTask(List<Long> taskIds) throws DaipBaseException {
        log.info("start to remove patch task: {}", taskIds);
        patchTaskOperateService.removePatchTask(taskIds);
        log.info("finished to remove patch task: {}", taskIds);
    }

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_TRIGGER_PATCH_TASK)
    public void triggerPatchTask(long taskId) throws DaipBaseException {
        log.info("start to trigger patch task: {}", taskId);
        patchTaskOperateService.triggerPatchTask(taskId);
        log.info("finished to trigger patch task: {}", taskId);
    }

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_RETRY_PATCH_TASK)
    public void retryPatchTask(long taskId) throws DaipBaseException {
        log.info("start to retry patch task: {}", taskId);
        patchTaskOperateService.retryPatchTask(taskId);
        log.info("finished to retry patch task: {}", taskId);
    }

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_PAUSE_PATCH_TASK)
    public void pausePatchTask(long taskId) throws DaipBaseException {
        log.info("start to pause patch task: {}", taskId);
        patchTaskOperateService.pausePatchTask(taskId);
        log.info("finished to pause patch task: {}", taskId);
    }

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_RESUME_PATCH_TASK)
    public void resumePatchTask(long taskId) throws DaipBaseException {
        log.info("start to resume patch task: {}", taskId);
        patchTaskOperateService.resumePatchTask(taskId);
        log.info("finished to resume patch task: {}", taskId);
    }

    public void copyPatchTask(long taskId) throws DaipBaseException {
        log.info("start to copy patch task: {}", taskId);
        patchTaskOperateService.copyPatchTask(taskId);
        log.info("finished to copy patch task: {}", taskId);
    }

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_QUERY_SERVICE_INSTANCE)
    public List<ServiceInstanceUpgrade> queryServiceInstanceUpgrade(VersionQueryRequestInfo versionQueryRequestInfo)
        throws DaipBaseException {
        String accessType = versionQueryRequestInfo.getAccessType();
        List<ServiceVersionInfo> serviceVersionInfoList = versionQueryRequestInfo.getServiceVersionInfoList();
        if (CollectionUtils.isEmpty(serviceVersionInfoList)) {
            return Lists.newArrayList();
        }
        Map<String, ServiceVersionInfo> serviceIdToServiceVersionInfo = serviceVersionInfoList.stream().distinct()
            .collect(Collectors.toMap(ServiceVersionInfo::getServiceId, e -> e));

        Map<String, List<String>> modelVersionMap =
            queryAndFilterModelVersion(accessType, serviceIdToServiceVersionInfo);
        if (StringUtils.equals(UpgradeModel.ROLLING_UPDATE_PATCH.getModel(), accessType)
            || StringUtils.equals(UpgradeModel.ROLLING_ROLLBACK_PATCH.getModel(), accessType)) {
            return convertInstanceBean(modelVersionMap);
        }

        Map<String, List<String>> serviceIdToVersions = obtainServiceIdToVersions(serviceVersionInfoList);
        if (MapUtils.isEmpty(serviceIdToVersions)) {
            return Lists.newArrayList();
        }
        Map<String, List<String>> serviceVersions = Maps.newHashMap();
        serviceIdToVersions.entrySet()
            .forEach(entry -> assembleServiceVersion(entry, modelVersionMap, serviceVersions));
        return convertInstanceBean(serviceVersions);
    }

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_ROLLBACK_PATCH_TASK)
    public void rollbackPatchTask(PatchTaskDto patchTaskDto) throws DaipBaseException {
        if (patchTaskDto == null) {
            throw new DaipBaseException("patch task is null.");
        }
        log.info("start to rollback patch task: {}, cluster: {}", patchTaskDto.getTaskName(),
            patchTaskDto.getClusterId());
        patchTaskOperateService.rollbackPatchTask(patchTaskDto);
        log.info("finished to rollback patch task: {}, cluster: {}", patchTaskDto.getTaskName(),
            patchTaskDto.getClusterId());
    }
    
    public boolean patchTaskParamValid(PatchTaskDto patchTaskDto) {
        if (patchTaskDto == null || StringUtils.isEmpty(patchTaskDto.getClusterId())
            || StringUtils.isEmpty(patchTaskDto.getTaskName())) {
            log.info("The task parameter exist empty value.");
            return false;
        }
        String taskName = patchTaskDto.getTaskName();
        Matcher matcher = TASK_NAME_COMPILE.matcher(taskName);
        if (!matcher.matches()) {
            log.info("The task name does not meet the specifications.");
            return false;
        }

        int length = taskName.length();
        matcher = CHINESE_COMPILE.matcher(taskName);
        while (matcher.find()) {
            length += 1;
        }
        if (length > 20) {
            log.info("The task name cannot exceed 20 characters.");
            return false;
        }
        return true;
    }

    public boolean patchNameNotRepeated(PatchTaskDto patchTaskDto) throws DaipBaseException {
        if (patchTaskDto == null || StringUtils.isEmpty(patchTaskDto.getTaskName())) {
            log.info("The task parameter exist empty value.");
            return false;
        }
        List<PatchTaskDto> patchTaskDtos = queryAllPatchTasks();
        List<PatchTaskDto> taskSummaries = patchTaskDtos.stream()
                .filter(task -> StringUtils.equals(patchTaskDto.getTaskName(), task.getTaskName()))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(taskSummaries);
    }

    public List<ServiceInstance> queryNeedRestartService(PatchTaskDto patchTaskDto) throws DaipBaseException {
        if (CollectionUtils.isEmpty(patchTaskDto.getContext())
                || CollectionUtils.isEmpty(patchTaskDto.getRelationServices())
                || StringUtils.isEmpty(patchTaskDto.getClusterId())) {
            throw new DaipBaseException("query param exist null value.");
        }
        log.info("start to query need restart service, patchTaskDto: {}", patchTaskDto);
        List<ServiceInstance> needRestartService = patchTaskOperateService.queryNeedRestartService(patchTaskDto);
        log.info("finished to query need restart service, result: {}", needRestartService);
        return needRestartService;
    }

    private List<ServiceInstanceUpgrade> convertInstanceBean(Map<String, List<String>> modelVersionMap) {
        List<ServiceInstanceUpgrade> serviceInstanceUpgrades = new ArrayList<>();
        modelVersionMap.forEach((key, value) -> serviceInstanceUpgrades.add(new ServiceInstanceUpgrade(key, value)));
        return serviceInstanceUpgrades;
    }

    public List<PatchTaskDto> queryAllPatchTasks() throws DaipBaseException {
        return patchTaskOperateService.queryAll();
    }

    public PatchTaskDto queryPatchTaskDetailByTaskId(long taskId) throws DaipBaseException {
        return patchTaskOperateService.queryPatchDetailById(taskId);
    }

    public PatchTaskDto queryPatchTaskByTaskId(long taskId) throws DaipBaseException {
        return patchTaskOperateService.queryPatchTaskByTaskId(taskId);
    }

    public PatchOperateResult checkTaskCanRollback(long taskId) throws DaipBaseException {
        return patchTaskOperateService.checkTaskCanRollback(taskId);
    }

    public PatchOperateResult checkTaskCanDuplicate(long taskId) throws DaipBaseException {
        return patchTaskOperateService.checkTaskCanDuplicate(taskId);
    }

    private Map<String, List<String>> queryAndFilterModelVersion(String accessType,
        Map<String, ServiceVersionInfo> serviceIdToServiceVersionInfo) throws DaipBaseException {
        Map<String, List<String>> map = Maps.newHashMap();
        CommonResponse<List<TaskModelVersionSet>> response = taskManagerPlatformApi.modelSet(accessType);
        if (response.getStatus() != 0) {
            throw new DaipBaseException("Fail to query model version, errMsg: " + response.getErrorMsgZhCn());
        }
        List<TaskModelVersionSet> taskModelVersionSets = response.getData();
        if (!CollectionUtils.isEmpty(taskModelVersionSets)) {
            map = taskModelVersionSets.stream().filter(e -> serviceIdToServiceVersionInfo.containsKey(e.getServiceId()))
                .collect(Collectors.toMap(TaskModelVersionSet::getServiceId,
                    e -> e.getVersions().stream().sorted(VersionUtil::compare).collect(Collectors.toList())));
        }
        return map;
    }

    private Map<String, List<String>> obtainServiceIdToVersions(List<ServiceVersionInfo> serviceVersionInfoList) {
        List<ProductSpect> productSpectList = spectControllerApi.queryAll();
        Set<ServiceProject> serviceProjects = productSpectList.stream()
            .map(productSpect -> new ServiceProject(productSpect.getServiceName(), productSpect.getVersionNo()))
            .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(serviceProjects)) {
            return Maps.newHashMap();
        }
        Map<String, List<String>> map = Maps.newHashMap();
        Map<String, String> serviceIdToServiceName = serviceVersionInfoList.stream().distinct()
            .collect(Collectors.toMap(ServiceVersionInfo::getServiceId, ServiceVersionInfo::getServiceName));
        Map<String, List<ServiceProject>> serviceNameToProjects =
            serviceProjects.stream().collect(Collectors.groupingBy(ServiceProject::getServiceName));
        for (Map.Entry<String, String> entry : serviceIdToServiceName.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            ServiceModel serviceModel = clusterInfoControllerApi.queryModelByServiceName(-1, value);
            if (serviceModel == null || !serviceModel.getServiceId().equals(key)) {
                continue;
            }
            if (serviceNameToProjects.containsKey(value)) {
                map.put(key, serviceNameToProjects.get(value).stream().map(ServiceProject::getVersion)
                        .sorted(VersionUtil::compare).collect(Collectors.toList()));
            }
        }
        return map;
    }

    private static void assembleServiceVersion(Map.Entry<String, List<String>> entry,
        Map<String, List<String>> modelVersionMap, Map<String, List<String>> serviceVersions) {
        if (modelVersionMap.containsKey(entry.getKey())) {
            serviceVersions.put(entry.getKey(), modelVersionMap.get(entry.getKey()));
            return;
        }
        serviceVersions.put(entry.getKey(), entry.getValue());
    }
}