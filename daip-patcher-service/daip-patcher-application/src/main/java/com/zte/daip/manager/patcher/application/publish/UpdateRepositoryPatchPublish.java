package com.zte.daip.manager.patcher.application.publish;
/* Started by AICoder, pid:5a959b0b70ub2a4145d50b1920be5f64c5a50bdb */
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.producer.EventPublisher;
import com.zte.daip.manager.common.utils.i18n.DaipI18nService;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.application.bean.RepositoryPatchUpdateInfo;
import lombok.extern.slf4j.Slf4j;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.zte.daip.communication.bean.MessageType.BROADCAST;

/**
 * 服务类，用于发布更新仓库补丁的事件消息。
 */
@Slf4j
@Service
public class UpdateRepositoryPatchPublish {

    /**
     * 事件发布者，用于发送事件消息。
     */
    @Autowired
    private EventPublisher eventPublisher;

    /**
     * 事件报告器，用于记录事件日志。
     */
    @Autowired
    private DaipEventReporter daipEventReporter;

    /**
     * 国际化服务，用于获取本地化信息。
     */
    @Autowired
    private DaipI18nService daipI18nService;

    /**
     * 发布更新仓库版本补丁的消息。
     *
     * @param patchUpdateInfos 补丁更新信息列表
     */
    public void publishUpdateRepositoryMsg(List<RepositoryPatchUpdateInfo> patchUpdateInfos) {
        log.info("publish update repository version patch message: {}", patchUpdateInfos.toString());

        // 创建消息体
        RequestMessageBody<List<RepositoryPatchUpdateInfo>> requestMessageBody = new RequestMessageBody<>();
        requestMessageBody.setBody(patchUpdateInfos);
        requestMessageBody.setType(BROADCAST.getType());

        // 发送消息
        eventPublisher.sendByGroup("updateRepositoryVersionPatch", requestMessageBody);

        // 记录事件日志
        String[] patchNames = patchUpdateInfos.stream()
                .flatMap(info -> info.getPatchNameList().stream())
                .toArray(String[]::new);
        daipEventReporter.info(I18nKeyConstants.PUBLISH_UPDATE_REPOSITORY_KAFKA_MESSAGE,
                daipI18nService.getLabel(I18nKeyConstants.WAITING_FOR_UPGRADE_PATCH_LIST,
                        new String[] {Arrays.toString(patchNames)}));
    }
}

/* Ended by AICoder, pid:5a959b0b70ub2a4145d50b1920be5f64c5a50bdb */
