/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDispatchAppService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/29
 * </p>
 * <p>
 * 完成日期：2021/3/29
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.service;

import com.zte.daip.manager.event.reporter.api.annotation.BusinessDomainEvent;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.application.publish.UploadPatchEventPublish;
import com.zte.daip.manager.patcher.domain.constants.DomainI18nKey;
import com.zte.daip.manager.patcher.domain.query.service.PatchDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchDispatchAppService {
    @Autowired
    private UploadPatchEventPublish uploadPatchEventPublish;

    @Autowired
    private PatchDetailService patchDetailService;

    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_PUBLISH_DISTRIBUTE_PATCH_EVENT)
    public void publishDispatchEvent() {
        final List<PatchBean> dispatchPatchListMap = patchDetailService.queryNeedDispatchPatchListMap();

        uploadPatchEventPublish.publishEvents(dispatchPatchListMap);
    }

}