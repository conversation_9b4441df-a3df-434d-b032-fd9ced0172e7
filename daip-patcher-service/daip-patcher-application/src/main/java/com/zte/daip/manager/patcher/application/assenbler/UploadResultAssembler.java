/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UploadResultAssembler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/5/21
 * </p>
 * <p>
 * 完成日期：2021/5/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.assenbler;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.dto.UploadPatchResult;
import com.zte.daip.manager.patcher.api.dto.UploadResponseResult;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Slf4j
public class UploadResultAssembler {
    public UploadResponseResult assemblerUploadPatchProcess(List<PatchUploadResult> patchUploadResults) {
        int finishNums =
            patchUploadResults.stream().filter(patchUploadResult -> StringUtils.equalsIgnoreCase(OnePatchProcess.FINISH,
                patchUploadResult.getOneProcess())).collect(Collectors.toList()).size();
        if (finishNums == patchUploadResults.size()) {
            return new UploadResponseResult(true, assembler(patchUploadResults));
        }
        return new UploadResponseResult(false);
    }

    public List<UploadPatchResult> assembler(List<PatchUploadResult> patchUploadResults) {
        List<UploadPatchResult> uploadPatchResults = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(patchUploadResults)) {
            uploadPatchResults =
                patchUploadResults.stream().map(this::generateUploadPatchResult).collect(Collectors.toList());
        }
        return uploadPatchResults;

    }

    private UploadPatchResult generateUploadPatchResult(PatchUploadResult patchUploadResult) {
        UploadPatchResult uploadResult = new UploadPatchResult();

        uploadResult.setPatchName(patchUploadResult.getPatchName());
        uploadResult.setNeedDelete(patchUploadResult.isNeedDelete());
        uploadResult.setOneProgress(patchUploadResult.getOneProcess());
        uploadResult.setOriginSetName(patchUploadResult.getOriginSetName());
        if (StringUtils.equalsIgnoreCase(OnePatchProcess.FINISH, patchUploadResult.getOneProcess())) {
            uploadResult.setMessage(patchUploadResult.getMessage());
            uploadResult.setSuccess(patchUploadResult.isSuccess());
        } else {
            uploadResult.setMessage("upload patch timeout");
            uploadResult.setSuccess(false);
        }
        return uploadResult;
    }
}