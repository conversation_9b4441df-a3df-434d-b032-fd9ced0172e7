package com.zte.daip.manager.patcher.application.service.i18n;

import com.zte.daip.manager.common.deployer.bean.patch.I18nInfoBean;
import com.zte.daip.manager.common.deployer.i18n.I18nControllerApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.URL;


/**
 * 功能描述:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/28
 */
@Slf4j
@Service
public class DaipTaskModelI18nRegister {

    private static final String TASK_SERVICE_NAME = "daip-task";
    private static final String DEFAULT_VERSION = "default";

    @Autowired
    private I18nControllerApi i18nControllerApi;
    public static final String DAIPMANAGER_I18N_FILE = "daip-patcher-task-server-i18n.xml";

    @Async
    @Retryable(value = Throwable.class, maxAttempts = Integer.MAX_VALUE, backoff = @Backoff(30000L))
    public void updateTaskServiceI18n(String path) throws IOException {
        URL url = getClass().getClassLoader().getResource(path);
        if (url == null) {
            log.error("Can't load the daip-task i18n file: " + path);
            return;
        }
        String i18nFileContent = FileUtils.readFileToString(new File(url.getPath()), "UTF-8");
        I18nInfoBean i18nInfoBean = new I18nInfoBean(TASK_SERVICE_NAME, DEFAULT_VERSION, i18nFileContent);
        i18nControllerApi.updateManagerService(DAIPMANAGER_I18N_FILE, i18nInfoBean);
    }

}

