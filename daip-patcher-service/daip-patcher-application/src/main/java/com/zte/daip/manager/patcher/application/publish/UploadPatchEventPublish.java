/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UploadPatchEventPublish.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/24
 * </p>
 * <p>
 * 完成日期：2021/3/24
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * <p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * <p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.publish;

import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.domain.upload.event.UploadPatchEvent;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.constants.DomainI18nKey;
import lombok.extern.slf4j.Slf4j;

import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Slf4j
public class UploadPatchEventPublish {
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private DaipEventReporter daipEventReporter;

    public void publishEvents(List<PatchBean> needPublishPatch) {
        if (CollectionUtils.isEmpty(needPublishPatch)) {
            daipEventReporter.info(DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_PUBLISH_DISTRIBUTE_PATCH_EVENT, "no patch need to distribute");
            return;
        }
        log.info("Publish distribute patch event, need distribute patch: {}", needPublishPatch.toString());
        applicationContext.publishEvent(new UploadPatchEvent("", needPublishPatch));
    }

}