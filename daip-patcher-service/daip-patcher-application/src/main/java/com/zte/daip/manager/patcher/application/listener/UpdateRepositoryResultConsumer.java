package com.zte.daip.manager.patcher.application.listener;
/* Started by AICoder, pid:e925ef8741b8cbd14fa40ba6e0079c8c1679bf08 */
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zte.daip.communication.api.ConsumerHandler;
import com.zte.daip.communication.api.EventListenerTo;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.exception.MessageCenterException;
import com.zte.daip.manager.common.utils.i18n.DaipI18nService;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.application.service.UpdateRepositoryPatchService;
import com.zte.daip.manager.patcher.application.bean.RepositoryUpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 处理更新仓库补丁结果的消费者。
 */
@EventListenerTo("reportRepositoryPatchResult")
@Slf4j
@Service
public class UpdateRepositoryResultConsumer implements ConsumerHandler<String> {

    /**
     * 更新仓库补丁服务。
     */
    @Autowired
    private UpdateRepositoryPatchService updateRepositoryPatchService;

    /**
     * 事件报告器，用于记录日志和发送事件。
     */
    @Autowired
    private DaipEventReporter daipEventReporter;

    /**
     * 国际化服务，用于获取本地化信息。
     */
    @Autowired
    private DaipI18nService daipI18nService;

    /**
     * 处理接收到的消息。
     *
     * @param body 消息体
     * @return 处理结果
     * @throws MessageCenterException 如果处理消息时发生异常
     */
    @Override
    public String handle(String body) throws MessageCenterException {
        // 将消息体解析为RequestMessageBody对象
        RequestMessageBody<List<RepositoryUpdateResult>> requestMessageBody =
                JSONObject.parseObject(body, new TypeReference<RequestMessageBody<List<RepositoryUpdateResult>>>() {});

        // 检查解析后的对象是否为空或消息体是否为空
        if (requestMessageBody == null || CollectionUtils.isEmpty(requestMessageBody.getBody())) {
            String msg = "receive kafka msg is not valid! or receive data size is empty!";
            log.info(msg);
            // 释放锁
            updateRepositoryPatchService.releaseLock();
            return "";
        }

        // 获取消息体中的RepositoryUpdateResult列表
        List<RepositoryUpdateResult> repositoryUpdateResults = requestMessageBody.getBody();

        // 记录接收到的更新仓库版本结果的日志
        log.info("receive update repository version result: {}", repositoryUpdateResults.toString());

        // 处理仓库补丁结果
        updateRepositoryPatchService.handleRepositoryPatchResult(repositoryUpdateResults);

        // 记录处理更新仓库版本结果的事件
        daipEventReporter.info(I18nKeyConstants.HANDLE_UPDATE_REPOSITORY_VERSION_RESULT,
                daipI18nService.getLabel(I18nKeyConstants.REPOSITORY_VERSION_PATCH_UPGRADE_RESULT,
                        new String[] {
                                Arrays.toString(repositoryUpdateResults.stream()
                                        .flatMap(info -> info.getSuccessPatchList().stream()).toArray(String[]::new)),
                                Arrays.toString(repositoryUpdateResults.stream().flatMap(info -> info.getFailedPatchList().stream())
                                        .toArray(String[]::new))}));

        // 返回处理成功的标识
        return "success";
    }
}

/* Ended by AICoder, pid:e925ef8741b8cbd14fa40ba6e0079c8c1679bf08 */