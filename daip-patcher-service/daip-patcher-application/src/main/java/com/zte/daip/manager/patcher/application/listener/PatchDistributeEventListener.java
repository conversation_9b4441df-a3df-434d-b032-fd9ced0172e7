/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDistributeEventListener.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/8
 * </p>
 * <p>
 * 完成日期：2023/3/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.listener;

import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.upload.event.UploadPatchEvent;
import com.zte.daip.manager.patcher.domain.dispatch.PatchDistributeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchDistributeEventListener implements ApplicationListener<UploadPatchEvent> {

    @Autowired
    private ClusterInfoControllerApi clusterInfoControllerApi;

    @Autowired
    private PatchDistributeService patchDistributeService;

    @Override
    public void onApplicationEvent(UploadPatchEvent uploadPatchEvent) {
        try {
            List<PatchDetailDto> patchDetailDtos = getPatchDetailDtos(uploadPatchEvent);
            patchDistributeService.autoUploadDistribute(patchDetailDtos);
        } catch (Exception e) {
            log.error("autoUploadDistribute error", e);
        }

    }

    private List<PatchDetailDto> getPatchDetailDtos(UploadPatchEvent uploadPatchEvent) {
        List<PatchBean> distributePatches = uploadPatchEvent.getNeedPublishPatch();
        return distributePatches.stream().map(patchBean -> {
            PatchDetailDto patchDetailDto = new PatchDetailDto();
            patchDetailDto.setBaseVersion(patchBean.getSrcVersion());
            patchDetailDto.setService(patchBean.getService());
            patchDetailDto.setPatchName(patchBean.getPatchName());
            return patchDetailDto;
        }).collect(Collectors.toList());
    }

}