package com.zte.daip.manager.patcher.application.listener;

import com.zte.daip.manager.patcher.application.service.UpdateRepositoryPatchService;
import com.zte.daip.manager.patcher.domain.upload.event.UploadPatchEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UpdateRepositoryPatchListener implements ApplicationListener<UploadPatchEvent> {
    @Autowired
    private UpdateRepositoryPatchService updateRepositoryPatchService;

    @Override
    @Async("asyncExecutor")
    public void onApplicationEvent(UploadPatchEvent uploadPatchEvent) {
        log.info("Receive update repository patch listener.");
        updateRepositoryPatchService.updateRepositoryPatch();
    }
}
