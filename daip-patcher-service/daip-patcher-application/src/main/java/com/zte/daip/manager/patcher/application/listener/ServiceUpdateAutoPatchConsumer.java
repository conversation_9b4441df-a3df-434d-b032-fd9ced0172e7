
package com.zte.daip.manager.patcher.application.listener;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.zte.daip.communication.api.ConsumerHandler;
import com.zte.daip.communication.api.EventListenerTo;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.exception.MessageCenterException;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceComponentType;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.upgrade.UpdateServiceParams;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import org.springframework.util.CollectionUtils;
import com.zte.daip.manager.event.reporter.api.annotation.BusinessDomainEvent;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.application.service.PatchUpdateAppService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@EventListenerTo("serviceUpdate")
@Slf4j
@Service
public class ServiceUpdateAutoPatchConsumer implements ConsumerHandler<String> {
    @Autowired
    private PatchUpdateAppService patchUpdateAppService;
    @Autowired
    private PatchInfoService patchInfoService;
    @Autowired
    private ServiceResourceControllerApi serviceResourceControllerApi;
    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;
    @Autowired
    private DaipEventReporter daipEventReporter;

    @Override
    @BusinessDomainEvent(eventName = I18nKeyConstants.DOMAIN_UPGRADE_PATCH)
    public String handle(String body) throws MessageCenterException {
        log.info("receive topic serviceUpdate:" + body);

        RequestMessageBody<UpdateServiceParams> requestMessageBody =
            JSONObject.parseObject(body, new TypeReference<RequestMessageBody<UpdateServiceParams>>() {});

        UpdateServiceParams updateServiceParams = requestMessageBody.getBody();

        List<PatchDetailPo> needDispatchAndUpdatePatches = Lists.newArrayList();

        Set<String> targetHostIps = updateServiceParams.getHosts().stream()
            .filter(hostInfo -> !StringUtils.equalsIgnoreCase(hostInfo.getStatus(), "false"))
            .map(HostInfo::getIpAddress).collect(Collectors.toSet());

        if (StringUtils.equalsIgnoreCase(updateServiceParams.getServiceInstanceId(),
            ServiceComponentType.DAP_COMMON_BIGDATA.getComponentType())) {
            updateBigDataComponentPatch(updateServiceParams, targetHostIps, needDispatchAndUpdatePatches);
        } else {
            updateNotBigDataComponentPatch(updateServiceParams, needDispatchAndUpdatePatches, targetHostIps);

        }

        return "success";
    }
    /* Started by AICoder, pid:99286re064wb6c61422f08b740e6242ec5e8bdba */
    private void updateBigDataComponentPatch(UpdateServiceParams updateServiceParams, Set<String> targetHostIps,
        List<PatchDetailPo> needDispatchAndUpdatePatches) {
        List<ServiceRoleInfo> serviceRoleInfos =
            serviceResourceControllerApi.queryByClusterId(updateServiceParams.getClusterId());
        List<ServiceModel> serviceModels =
            productModelInfoControllerApi.queryByClusterId(updateServiceParams.getClusterId());
        Set<String> bigdataServiceIds = serviceModels.stream()
            .filter(serviceModel -> StringUtils.equalsIgnoreCase(serviceModel.getComponentType(),
                ServiceComponentType.DAP_COMMON_BIGDATA.getComponentType()))
            .map(ServiceModel::getServiceId).collect(Collectors.toSet());
        List<ServiceRoleInfo> availableRoleInfos = serviceRoleInfos.stream()
            .filter(serviceRoleInfo -> bigdataServiceIds.contains(serviceRoleInfo.getServiceId())
                && targetHostIps.contains(serviceRoleInfo.getIpAddress()))
            .collect(Collectors.toList());
        dispatchAndUpdatePatches(updateServiceParams, needDispatchAndUpdatePatches, serviceModels, availableRoleInfos);
        if (!CollectionUtils.isEmpty(needDispatchAndUpdatePatches)) {
            log.info("Need update big data service patches sizes:{}", needDispatchAndUpdatePatches.size());
            updateAllBigDataComponentPatch(updateServiceParams, serviceModels, availableRoleInfos);
        } else {
            log.info("No big data service patches need to update!");
        }
    }
    /* Ended by AICoder, pid:99286re064wb6c61422f08b740e6242ec5e8bdba */
    private void updateNotBigDataComponentPatch(UpdateServiceParams updateServiceParams,
        List<PatchDetailPo> needDispatchAndUpdatePatches, Set<String> targetHostIps) {
        needDispatchAndUpdatePatches.addAll(patchInfoService
            .findByServiceAndBaseVersion(updateServiceParams.getProjectName(), updateServiceParams.getTargetVersion()));
        needDispatchAndUpdatePatches.addAll(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(
            updateServiceParams.getServiceName(), updateServiceParams.getTargetVersion()));

        if (!CollectionUtils.isEmpty(needDispatchAndUpdatePatches)) {
            log.info("Need update not big data patches sizes:{}", needDispatchAndUpdatePatches.size());
            ServiceInstanceInfo serviceInstanceInfo = new ServiceInstanceInfo(updateServiceParams.getServiceName(),
                updateServiceParams.getTargetVersion(), updateServiceParams.getServiceId(),
                updateServiceParams.getServiceInstanceId(), Lists.newArrayList(targetHostIps));
            UpdateRequest updateRequest =
                new UpdateRequest(updateServiceParams.getClusterId(), Arrays.asList(serviceInstanceInfo));
            patchUpdateAppService.updatePatch(updateRequest);

        } else {
            log.info("No not big data patches need to update!");
        }
    }

    private void updateAllBigDataComponentPatch(UpdateServiceParams updateServiceParams,
        List<ServiceModel> serviceModels, List<ServiceRoleInfo> availableRoleInfos) {
        Set<String> serviceInstanceIds = new HashSet<>();
        List<ServiceInstanceInfo> serviceInstanceInfos = new ArrayList<>();
        List<String> updateHostIps = null;
        for (ServiceRoleInfo serviceRoleInfo : availableRoleInfos) {
            if (!serviceInstanceIds.contains(serviceRoleInfo.getServiceInstanceId())) {
                serviceInstanceIds.add(serviceRoleInfo.getServiceInstanceId());
                updateHostIps = new ArrayList<>();
                ServiceInstanceInfo serviceInstanceInfo =
                    new ServiceInstanceInfo(getServiceName(serviceModels, serviceRoleInfo.getServiceId()),
                        updateServiceParams.getTargetVersion(), serviceRoleInfo.getServiceId(),
                        serviceRoleInfo.getServiceInstanceId(), updateHostIps);
                serviceInstanceInfos.add(serviceInstanceInfo);
            }
            if (null != updateHostIps && !updateHostIps.contains(serviceRoleInfo.getIpAddress())) {
                updateHostIps.add(serviceRoleInfo.getIpAddress());
            }

        }
        UpdateRequest updateRequest = new UpdateRequest(updateServiceParams.getClusterId(), serviceInstanceInfos);
        patchUpdateAppService.updatePatch(updateRequest);

    }

    private void dispatchAndUpdatePatches(UpdateServiceParams updateServiceParams,
        List<PatchDetailPo> needDispatchAndUpdatePatches, List<ServiceModel> serviceModels,
        List<ServiceRoleInfo> availableRoleInfos) {
        needDispatchAndUpdatePatches.addAll(patchInfoService
            .findByServiceAndBaseVersion(updateServiceParams.getProjectName(), updateServiceParams.getTargetVersion()));
        Set<String> serviceIds =
            availableRoleInfos.stream().map(ServiceRoleInfo::getServiceId).collect(Collectors.toSet());
        for (String serviceId : serviceIds) {
            needDispatchAndUpdatePatches.addAll(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(
                getServiceName(serviceModels, serviceId), updateServiceParams.getTargetVersion()));
        }
    }

    private String getServiceName(List<ServiceModel> serviceModels, String serviceId) {
        return serviceModels.stream()
            .filter(serviceModel -> StringUtils.equalsIgnoreCase(serviceId, serviceModel.getServiceId())).findFirst()
            .orElse(new ServiceModel()).getServiceName();
    }
}
