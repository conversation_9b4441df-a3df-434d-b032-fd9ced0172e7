/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchUploadAppService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/28
 * </p>
 * <p>
 * 完成日期：2021/3/28
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.dto.UploadResponseResult;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.application.assenbler.UploadResultAssembler;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.service.PatchUploadService;
import com.zte.daip.manager.patcher.domain.upload.utils.PatcherFileUtils;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchUploadAppService {

    @Autowired
    private PatchEnvApi patchEnvApi;
    @Autowired
    private PatchUploadService patchUploadService;

    @Autowired
    private UploadResultAssembler uploadResultAssembler;

    @Autowired
    private DaipEventReporter daipEventReporter;

    private static Long CYCLE_TIME = 3000L;

    private static Long TIME_OUT = 5 * 60 * 1000L;

    public UploadResponseResult uploadPatches(MultipartFile[] patchMultipartFiles) {

        dropFileToTempDir(patchMultipartFiles);
        return getUploadResponseResult(patchUploadService.uploadPatches(Lists.newArrayList(patchMultipartFiles).stream()
            .map(MultipartFile::getOriginalFilename).collect(Collectors.toList())));
    }

    public UploadResponseResult uploadPatches(List<String> pathNameList) {

        return getUploadResponseResult(patchUploadService.uploadPatches(pathNameList));

    }

    private UploadResponseResult getUploadResponseResult(String batchId) {
        int retryCount = 0;
        boolean isAllFinish = false;
        UploadResponseResult uploadResponseResult = new UploadResponseResult();
        while (!isAllFinish) {
            List<PatchUploadResult> patchUploadResults = patchUploadService.uploadPatchesProcess(batchId);
            UploadResponseResult uploadResponseTempResult =
                uploadResultAssembler.assemblerUploadPatchProcess(patchUploadResults);
            if (uploadResponseTempResult.isFinish()) {
                isAllFinish = true;
                uploadResponseResult = uploadResponseTempResult;
            } else if (retryCount > TIME_OUT / CYCLE_TIME) {
                log.error("upload patch timeout.");
                daipEventReporter.error(I18nKeyConstants.UPLOAD_PATCH_NOTIFICATION, "upload patch timeout");
                isAllFinish = true;
                uploadResponseTempResult.setUploadPatchResults(uploadResultAssembler.assembler(patchUploadResults));
                uploadResponseResult = uploadResponseTempResult;
            } else {
                retryCount++;
                try {
                    TimeUnit.MILLISECONDS.sleep(CYCLE_TIME);
                } catch (Exception e) {
                    log.error("wait error", e);
                    daipEventReporter.error(I18nKeyConstants.UPLOAD_PATCH_NOTIFICATION,
                        String.format("time sleep error %s", e.getMessage()));
                }
            }
        }
        return uploadResponseResult;
    }

    private void dropFileToTempDir(MultipartFile[] patchMultipartFiles) {
        String uploadDirTemp = patchEnvApi.getRepositoryHomeEnv() + "/upload/";
        PatcherFileUtils.copyFileToTempDir(patchMultipartFiles, uploadDirTemp);
        log.info("Finish drop upload file.");
    }
}