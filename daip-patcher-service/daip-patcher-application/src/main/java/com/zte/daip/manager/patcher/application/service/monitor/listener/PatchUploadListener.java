/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchUploadListener.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/2
 * </p>
 * <p>
 * 完成日期：2021/4/2
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.service.monitor.listener;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.zte.daip.manager.patcher.domain.upload.service.PatchUploadService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.monitor.FileAlterationListenerAdaptor;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.common.cache.lock.LockUtils;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
@NoArgsConstructor
@Slf4j
public class PatchUploadListener extends FileAlterationListenerAdaptor {

    @Autowired
    private PatchUploadService patchUploadService;
    @Autowired
    private PatchEnvApi patchEnvApi;

    @Autowired
    private LockUtils lockUtils;

    private Map<File, Long> fileLastModifiedMap = new HashMap<>();

    @Override
    public void onFileCreate(final File file) {
        log.debug("onFileCreate finish...");
        fileLastModifiedMap.put(file, 0L);
    }

    @Override
    public void onFileChange(final File file) {
        fileLastModifiedMap.put(file, 0L);
    }

    @Override
    public void onFileDelete(final File file) {
        fileLastModifiedMap.remove(file);
    }

    @Override
    public void onStop(FileAlterationObserver observer) {
        log.debug("change file stop...");
        boolean finishTransmit = isFinishTransmit();
        if (!finishTransmit) {
            return;
        }
        Set<File> files = fileLastModifiedMap.keySet();
        if (!files.isEmpty()) {
            uploadPatch(files);
        }
        fileLastModifiedMap.clear();
    }

    protected boolean isFinishTransmit() {
        return ListenerUtils.isFinishTransmit(fileLastModifiedMap);
    }

    private void uploadPatch(Set<File> files) {
        log.info("start to load patch  ");
        List<String> fileNames = Lists.newArrayList();
        File uploadDir = FilePathCleaner.newFile(queryRepositoryHomeEnv(), "upload");
        Lists.newArrayList(files).forEach(file -> {
            try {
                lockUtils.executeWithLockAndReturnWithLocked("upload:" + file.getName(), 180 * 1000L,
                    () -> copyPatchFile(file, uploadDir, fileNames));
            } catch (Exception e) {
                log.error("lock exception:", e);
            }
        });
        upload(fileNames);
    }

    public void copyPatchFile(File file, File uploadDir, List<String> fileNames) {
        try {
            if (file.exists()) {
                log.debug("copy patchFile ");
                FileUtils.copyFileToDirectory(file, uploadDir);
                fileNames.add(file.getName());
                log.debug("delete patchFile result:{} ", FileUtils.delete(file));
            }
        } catch (IOException e) {
            log.error("copy {} error.", file.getName());
        }
    }

    protected void upload(List<String> fileNames) {
        patchUploadService.uploadPatches(fileNames);
    }

    protected String queryRepositoryHomeEnv() {
        return patchEnvApi.getRepositoryHomeEnv();
    }

    protected Map<File, Long> getFileLastModifiedMap() {
        return fileLastModifiedMap;
    }

}