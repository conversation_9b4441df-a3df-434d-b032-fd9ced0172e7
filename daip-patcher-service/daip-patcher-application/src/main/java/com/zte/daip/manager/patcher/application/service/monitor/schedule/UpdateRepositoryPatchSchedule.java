package com.zte.daip.manager.patcher.application.service.monitor.schedule;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.zte.daip.manager.patcher.application.service.UpdateRepositoryPatchService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class UpdateRepositoryPatchSchedule {
    @Autowired
    private UpdateRepositoryPatchService updateRepositoryPatchService;

    @Scheduled(cron = "0 0/5 * * * ?")
    public void run() {
        log.info("begin to update repository patch");
        this.updateRepositoryPatchService.updateRepositoryPatch();
    }
}
