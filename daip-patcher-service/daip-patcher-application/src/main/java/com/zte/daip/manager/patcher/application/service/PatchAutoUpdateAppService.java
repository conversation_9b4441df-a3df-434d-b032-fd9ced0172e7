/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchAutoUpdateAppService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/6/5
 * </p>
 * <p>
 * 完成日期：2021/6/5
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.role.RolesToDeploy;
import com.zte.daip.manager.common.deployer.bean.service.ServiceDeployBean;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchAutoUpdateAppService {
    @Autowired
    private PatchInfoService patchInfoService;

    @Autowired
    private PatchUpdateAppService patchUpdateAppService;

    public void update(ServiceDeployBean serviceDeployBean) {

        List<PatchDetailPo> needDispatchAndUpdatePatches = Lists.newArrayList();

        needDispatchAndUpdatePatches.addAll(patchInfoService
            .findByServiceAndBaseVersion(serviceDeployBean.getProjectName(), serviceDeployBean.getVersion()));
        needDispatchAndUpdatePatches.addAll(patchInfoService
            .findByServiceAndBaseVersionNotSchemeAndRepository(serviceDeployBean.getServiceName(), serviceDeployBean.getVersion()));

        if (!CollectionUtils.isEmpty(needDispatchAndUpdatePatches)) {
            log.info("Need update patches sizes:{}", needDispatchAndUpdatePatches.size());
            try {
                updatePatches(serviceDeployBean);
            } catch (Exception e) {
                log.error("Update patches error", e);
            }
        }
    }

    private void updatePatches(ServiceDeployBean serviceDeployBean) {
        log.info("Auto update patches.");
        Set<String> targetHostIps =
            serviceDeployBean.getSelectedRoles().stream().map(RolesToDeploy::getNodeIp).collect(Collectors.toSet());

        ServiceInstanceInfo serviceInstanceInfo = new ServiceInstanceInfo(serviceDeployBean.getServiceName(),
            serviceDeployBean.getVersion(), serviceDeployBean.getServiceId(), serviceDeployBean.getServiceInstanceId(),
            Lists.newArrayList(targetHostIps));
        UpdateRequest updateRequest =
            new UpdateRequest(serviceDeployBean.getClusterId(), Lists.newArrayList(serviceInstanceInfo));

        patchUpdateAppService.updatePatch(updateRequest);
    }

}