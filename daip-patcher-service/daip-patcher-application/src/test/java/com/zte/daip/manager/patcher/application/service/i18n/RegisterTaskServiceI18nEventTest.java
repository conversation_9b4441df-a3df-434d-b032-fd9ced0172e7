package com.zte.daip.manager.patcher.application.service.i18n;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30
 */
@RunWith(MockitoJUnitRunner.class)
public class RegisterTaskServiceI18nEventTest {

    private static final Logger log = LoggerFactory.getLogger(RegisterTaskServiceI18nEventTest.class);

    @Mock
    private DaipTaskModelI18nRegister daipTaskModelI18nRegister;

    @InjectMocks
    private RegisterTaskServiceI18nEvent registerTaskServiceI18nEvent;

    @Mock
    private ApplicationReadyEvent applicationReadyEvent;

    // 测试正常注册的情况
    @Test
    public void should_RegisterI18nFile_when_ApplicationIsReady() throws IOException {
        doNothing().when(daipTaskModelI18nRegister).updateTaskServiceI18n(anyString());

        registerTaskServiceI18nEvent.onApplicationEvent(applicationReadyEvent);

        verify(daipTaskModelI18nRegister, times(1)).updateTaskServiceI18n(anyString());
    }

    // 测试注册过程中抛出异常的情况
    @Test
    public void should_LogError_when_RegistrationFails() throws IOException {
        doThrow(new RuntimeException("Registration failed")).when(daipTaskModelI18nRegister).updateTaskServiceI18n(anyString());

        registerTaskServiceI18nEvent.onApplicationEvent(applicationReadyEvent);

        verify(daipTaskModelI18nRegister, times(1)).updateTaskServiceI18n(anyString());
        // 验证日志记录了错误
        log.info("Expected exception logged: Registration failed");
    }
}