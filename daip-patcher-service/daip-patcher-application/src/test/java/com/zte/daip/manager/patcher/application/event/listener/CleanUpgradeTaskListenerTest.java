package com.zte.daip.manager.patcher.application.event.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.daip.communication.bean.MessageType;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.application.listener.CleanUpgradeTaskListener;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class CleanUpgradeTaskListenerTest {
    @InjectMocks
    private CleanUpgradeTaskListener cleanUpgradeTaskListener;

    @Mock
    private PatchTaskOperateService patchTaskOperateService;

    @Test
    public void handleTest() throws DaipBaseException {
        RequestMessageBody<List<Long>> messageBody = new RequestMessageBody<>();
        messageBody.setType(MessageType.BROADCAST.getType());
        messageBody.setBody(Lists.newArrayList(1L, 2L, 3L));
        String body = JSON.toJSONString(messageBody);
        Assert.assertEquals("success", cleanUpgradeTaskListener.handle(body));
        verify(patchTaskOperateService).removePatchTask(anyList());
    }
}
