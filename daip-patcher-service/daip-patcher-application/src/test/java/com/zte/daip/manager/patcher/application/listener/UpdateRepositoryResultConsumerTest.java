package com.zte.daip.manager.patcher.application.listener;
/* Started by AICoder, pid:a13e6p94864819014c400a1c612ef02f0d15a2a6 */
import static com.zte.daip.communication.bean.MessageType.MULICAST;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSONObject;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.exception.MessageCenterException;
import com.zte.daip.manager.common.utils.i18n.DaipI18nService;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.application.bean.RepositoryUpdateResult;
import com.zte.daip.manager.patcher.application.service.UpdateRepositoryPatchService;

/**
 * 测试类，用于测试更新仓库补丁结果的消费者。
 */
@RunWith(SpringRunner.class)
public class UpdateRepositoryResultConsumerTest {

    /**
     * 更新仓库补丁服务的模拟对象。
     */
    @Mock
    private UpdateRepositoryPatchService updateRepositoryPatchService;

    /**
     * 事件报告器的模拟对象。
     */
    @Mock
    private DaipEventReporter daipEventReporter;

    /**
     * 国际化服务的模拟对象。
     */
    @Mock
    private DaipI18nService daipI18nService;

    /**
     * 被测试的更新仓库补丁结果消费者对象。
     */
    @InjectMocks
    private UpdateRepositoryResultConsumer updateRepositoryResultConsumer;

    /**
     * 测试处理消息的方法。
     *
     * @throws MessageCenterException 如果发生消息中心异常，则抛出此异常。
     */
    @Test
    public void handle() throws MessageCenterException {
        // 模拟事件报告器的错误方法不执行任何操作
        doNothing().when(daipEventReporter).error(anyString(), anyString());

        // 模拟国际化服务的获取标签方法返回"test"
        Mockito.when(daipI18nService.getLabel(anyString(), (String[]) any())).thenReturn("test");

        // 创建一个空的仓库更新结果列表
        List<RepositoryUpdateResult> repositoryUpdateResults = new ArrayList<>();

        // 创建一个请求消息体对象
        RequestMessageBody requestMessageBody = new RequestMessageBody();
        requestMessageBody.setType(MULICAST.getType());
        requestMessageBody.setBody(repositoryUpdateResults);

        // 模拟更新仓库补丁服务的处理仓库补丁结果方法不执行任何操作
        doNothing().when(updateRepositoryPatchService).handleRepositoryPatchResult(repositoryUpdateResults);

        // 模拟更新仓库补丁服务的释放锁方法不执行任何操作
        doNothing().when(updateRepositoryPatchService).releaseLock();

        // 调用处理消息的方法，并断言返回的结果为空字符串
        String result = updateRepositoryResultConsumer.handle(JSONObject.toJSONString(requestMessageBody));
        assertEquals("", result);
    }

    /**
     * 测试处理消息的方法，当存在失败和成功的补丁列表时。
     *
     * @throws MessageCenterException 如果发生消息中心异常，则抛出此异常。
     */
    @Test
    public void handle1() throws MessageCenterException {
        // 模拟事件报告器的错误方法不执行任何操作
        doNothing().when(daipEventReporter).error(anyString(), anyString());

        // 模拟国际化服务的获取标签方法返回"test"
        Mockito.when(daipI18nService.getLabel(anyString(), (String[]) any())).thenReturn("test");

        // 模拟事件报告器的错误方法不执行任何操作
        doNothing().when(daipEventReporter).error(anyString(), anyString());

        // 创建一个包含失败和成功补丁列表的仓库更新结果列表
        List<RepositoryUpdateResult> repositoryUpdateResults = new ArrayList<>();
        RepositoryUpdateResult repositoryUpdateResult = new RepositoryUpdateResult();
        repositoryUpdateResult.setFailedPatchList(new ArrayList<>());
        repositoryUpdateResult.setSuccessPatchList(new ArrayList<>());
        repositoryUpdateResults.add(repositoryUpdateResult);

        // 创建一个请求消息体对象
        RequestMessageBody requestMessageBody = new RequestMessageBody();
        requestMessageBody.setType(MULICAST.getType());
        requestMessageBody.setBody(repositoryUpdateResults);

        // 模拟更新仓库补丁服务的处理仓库补丁结果方法不执行任何操作
        doNothing().when(updateRepositoryPatchService).handleRepositoryPatchResult(repositoryUpdateResults);

        // 模拟更新仓库补丁服务的释放锁方法不执行任何操作
        doNothing().when(updateRepositoryPatchService).releaseLock();

        // 调用处理消息的方法，并断言返回的结果为"success"
        String result = updateRepositoryResultConsumer.handle(JSONObject.toJSONString(requestMessageBody));
        assertEquals("success", result);
    }
}

/* Ended by AICoder, pid:a13e6p94864819014c400a1c612ef02f0d15a2a6 */