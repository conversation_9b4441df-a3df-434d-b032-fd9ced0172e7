
package com.zte.daip.manager.patcher.application.service;
/* Started by AICoder, pid:ca94bbb0e0l98c014cc60a0ca012f286a9744dfe */
import static junit.framework.TestCase.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.*;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.domain.update.PatchUpdateService;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateCacheService;
import org.springframework.util.CollectionUtils;

/**
 * PatchUpdateAppService测试类。
 */
@ExtendWith(MockitoExtension.class)
class PatchUpdateAppServiceTest {

    @Mock
    private PatchUpdateService mockPatchUpdateService;
    @Mock
    private PatchUpdateCacheService mockPatchUpdateCacheService;
    @Mock
    private DaipEventReporter mockDaipEventReporter;

    @InjectMocks
    private PatchUpdateAppService patchUpdateAppServiceUnderTest;

    /**
     * 测试更新补丁的方法。
     */
    @Test
    void testUpdatePatch() {
        // Setup
        final UpdateRequest updateRequest =
                new UpdateRequest("clusterId", Arrays.asList(new ServiceInstanceInfo("serviceName", "version", "serviceId",
                        "serviceInstanceId", Arrays.asList("value"))));
        when(mockPatchUpdateCacheService.isUpdatePatchPermit("clusterId")).thenReturn(false);

        // Run the test
        patchUpdateAppServiceUnderTest.updatePatch(updateRequest);

        // Verify the results
        verify(mockDaipEventReporter).info(anyString(), anyString());
        verify(mockPatchUpdateService, times(0)).update(updateRequest);
    }

    /**
     * 测试查询回滚点的方法。
     */
    @Test
    void testQueryRollbackPoints() {
        boolean result = true;
        try {
            patchUpdateAppServiceUnderTest.queryRollbackPoints("1", Lists.newArrayList());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试查询需要更新的补丁的方法。
     */
    @Test
    void queryNeedUpdatePatchs() {
        // Setup
        when(mockPatchUpdateService.queryNeedUpdatePatchs("clusterId", new ArrayList<>())).thenReturn(new HashMap<>());

        // Run the test
        Map<String, List<String>> stringListMap = patchUpdateAppServiceUnderTest.queryNeedUpdatePatchs("clusterId", new ArrayList<>());

        // Verify the results
        assertTrue(CollectionUtils.isEmpty(stringListMap));
    }
}

/* Ended by AICoder, pid:ca94bbb0e0l98c014cc60a0ca012f286a9744dfe */