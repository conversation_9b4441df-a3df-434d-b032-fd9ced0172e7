package com.zte.daip.manager.patcher.application.service;

import com.zte.daip.manager.common.deployer.bean.role.RoleBean;
import com.zte.daip.manager.common.deployer.bean.role.RolesToDeploy;
import com.zte.daip.manager.common.deployer.bean.service.ServiceDeployBean;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: UpdatePatchConsumerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/6/4</p>
 * <p>完成日期：2021/6/4</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchAutoUpdateAppServiceTest {

    @Mock
    private PatchInfoService patchInfoService;

    @Mock
    private PatchUpdateAppService patchUpdateAppService;

    @InjectMocks
    private PatchAutoUpdateAppService patchAutoUpdateAppService;

    @Before
    public void setUp() throws Exception {
        Mockito.when(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(any(), any()))
            .thenReturn(getServicePatchPo());
        Mockito.when(patchInfoService.findByServiceAndBaseVersion(any(), any())).thenReturn(getProjectPatchPo());

    }

    @Test
    public void 安装扩容流程触发打补丁流程成功111() throws Exception {
        ServiceDeployBean serviceDeployBean = generateRequestMsg();
        Mockito.doAnswer(invocation -> {
            return 1;
        }).when(patchUpdateAppService).updatePatch(any());
        patchAutoUpdateAppService.update(serviceDeployBean);
    }

    private List<PatchDetailPo> getServicePatchPo() {
        PatchDetailPo patchBean = new PatchDetailPo();
        patchBean.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP01");
        patchBean.setBaseVersion("V20.19.40.R4.B2");
        patchBean.setService("hdfs");
        List<PatchDetailPo> patchDetailPos = Lists.newArrayList();
        patchDetailPos.add(patchBean);
        return patchDetailPos;
    }

    private List<PatchDetailPo> getProjectPatchPo() {
        PatchDetailPo patchBean = new PatchDetailPo();
        patchBean.setPatchName("DAP-zdh-V20.19.40.R4.B2-SP01");
        patchBean.setBaseVersion("V20.19.40.R4.B2");
        patchBean.setService("zdh");
        return Lists.newArrayList(patchBean);
    }

    private ServiceDeployBean generateRequestMsg() {
        ServiceDeployBean serviceDeployBean =
            new ServiceDeployBean("100001", "dap.manager.hdfs", "hdfs", "hdfs", "hdfs");
        List<RolesToDeploy> selectedRoles = Lists.newArrayList();
        RolesToDeploy rolesToDeploy = new RolesToDeploy();
        rolesToDeploy.setHostName("hosts01");
        rolesToDeploy.setNodeId("********");
        rolesToDeploy.setNodeId("01");
        RoleBean roleBean1 = new RoleBean("dap.manager.hdfs.NameNode", "NameNode");
        RoleBean roleBean2 = new RoleBean("dap.manager.hdfs.JournalNode", "JournalNode");
        List<RoleBean> roleBeans = Lists.newArrayList();
        roleBeans.add(roleBean1);
        roleBeans.add(roleBean2);
        rolesToDeploy.setRoles(roleBeans);
        selectedRoles.add(rolesToDeploy);
        serviceDeployBean.setSelectedRoles(selectedRoles);
        return serviceDeployBean;
    }

}