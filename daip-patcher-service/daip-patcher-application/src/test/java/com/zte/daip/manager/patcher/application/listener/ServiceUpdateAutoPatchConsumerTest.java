package com.zte.daip.manager.patcher.application.listener;
/* Started by AICoder, pid:vf824d6eefq4bc914d9a083711b06d2bc96523dc */
import com.alibaba.fastjson.JSONObject;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.exception.MessageCenterException;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.host.HostInfo;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceComponentType;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.upgrade.UpdateServiceParams;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.application.listener.ServiceUpdateAutoPatchConsumer;
import com.zte.daip.manager.patcher.application.service.PatchUpdateAppService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ServiceUpdateAutoPatchConsumerTest {
    private static final String VERSION = "v2";
    private static final String CLUSTER_ID = "1001";
    @InjectMocks
    private ServiceUpdateAutoPatchConsumer serviceUpdateAutoPatchConsumer;
    @Mock
    private PatchUpdateAppService patchUpdateAppService;
    @Mock
    private PatchInfoService patchInfoService;
    @Mock
    private ServiceResourceControllerApi serviceResourceControllerApi;
    @Mock
    private ProductModelInfoControllerApi productModelServiceApi;

    @Test
    public void testHandleZdhUpdate() throws MessageCenterException {
        RequestMessageBody requestMessageBody = new RequestMessageBody<>();
        requestMessageBody.setBody(mockUpdateServiceParams());
        when(serviceResourceControllerApi.queryByClusterId(CLUSTER_ID)).thenReturn(mockServiceRoleInfos());
        when(productModelServiceApi.queryByClusterId(CLUSTER_ID)).thenReturn(mockServiceModels());
        when(patchInfoService.findByServiceAndBaseVersion(anyString(), anyString())).thenReturn(mockPatchDetailPos());
        when(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(anyString(), anyString()))
            .thenReturn(mockPatchDetailPos());

        serviceUpdateAutoPatchConsumer.handle(JSONObject.toJSONString(requestMessageBody));
        verify(patchUpdateAppService, times(1)).updatePatch(any(UpdateRequest.class));
    }

    private List<PatchDetailPo> mockPatchDetailPos() {

        PatchDetailPo patchDetailPo = new PatchDetailPo();
        patchDetailPo.setPatchName("patch1");
        return Arrays.asList(patchDetailPo);
    }

    private List<ServiceModel> mockServiceModels() {
        List<ServiceModel> serviceModels = new ArrayList<>();
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceId("dap.manager.hdfs");
        serviceModel.setServiceName("hdfs");
        serviceModel.setComponentType(ServiceComponentType.DAP_COMMON_BIGDATA.getComponentType());
        serviceModels.add(serviceModel);

        serviceModel = new ServiceModel();
        serviceModel.setServiceId("dap.manager.zookeeper");
        serviceModel.setServiceName("zookeeper");
        serviceModel.setComponentType(ServiceComponentType.DAP_COMMON_BIGDATA.getComponentType());
        serviceModels.add(serviceModel);
        return serviceModels;
    }

    private List<ServiceRoleInfo> mockServiceRoleInfos() {
        List<ServiceRoleInfo> serviceRoleInfos = new ArrayList<>();
        ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setServiceInstanceId("hdfs");
        serviceRoleInfo.setIpAddress("***********");
        serviceRoleInfo.setClusterId(CLUSTER_ID);
        serviceRoleInfo.setServiceId("dap.manager.hdfs");
        serviceRoleInfo.setRoleId("dap.manager.hdfs.NameNode");

        serviceRoleInfos.add(serviceRoleInfo);

        serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setServiceInstanceId("hdfs");
        serviceRoleInfo.setIpAddress("***********");
        serviceRoleInfo.setClusterId(CLUSTER_ID);
        serviceRoleInfo.setServiceId("dap.manager.hdfs");
        serviceRoleInfo.setRoleId("dap.manager.hdfs.DataNode");

        serviceRoleInfos.add(serviceRoleInfo);

        serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setServiceInstanceId("zookeeper");
        serviceRoleInfo.setIpAddress("***********");
        serviceRoleInfo.setClusterId(CLUSTER_ID);
        serviceRoleInfo.setServiceId("dap.manager.zookeeper");
        serviceRoleInfo.setRoleId("dap.manager.zookeeper.HQuorumPeer");

        serviceRoleInfos.add(serviceRoleInfo);
        return serviceRoleInfos;
    }

    private UpdateServiceParams mockUpdateServiceParams() {
        UpdateServiceParams updateServiceParams = new UpdateServiceParams();
        updateServiceParams.setClusterId(CLUSTER_ID);
        updateServiceParams.setProjectName("zdh");
        updateServiceParams.setServiceInstanceId(ServiceComponentType.DAP_COMMON_BIGDATA.getComponentType());
        updateServiceParams.setServiceId(ServiceComponentType.DAP_COMMON_BIGDATA.getComponentType());
        updateServiceParams.setTargetVersion(VERSION);
        updateServiceParams.setHosts(mockHosts());
        return updateServiceParams;
    }

    private List<HostInfo> mockHosts() {
        List<HostInfo> hosts = new ArrayList<>();
        HostInfo hostInfo = new HostInfo();
        hostInfo.setIpAddress("***********");
        hostInfo.setStatus("true");
        hostInfo.setHostName("zdh1");
        hostInfo.setClusterId(CLUSTER_ID);
        hosts.add(hostInfo);
        hostInfo = new HostInfo();
        hostInfo.setIpAddress("***********");
        hostInfo.setStatus("true");
        hostInfo.setHostName("zdh2");
        hostInfo.setClusterId(CLUSTER_ID);
        hosts.add(hostInfo);
        return hosts;
    }
}
/* Ended by AICoder, pid:nf824m6eefk4bc914d9a083711b06d2bc96523dc */