package com.zte.daip.manager.patcher.application.event.listener;

import com.zte.daip.manager.patcher.application.listener.UpdateRepositoryPatchListener;
import com.zte.daip.manager.patcher.application.service.UpdateRepositoryPatchService;
import com.zte.daip.manager.patcher.domain.upload.event.UploadPatchEvent;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

import static org.mockito.Mockito.doNothing;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchUploadFileDeleteEventListenerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/4/10</p>
 * <p>完成日期：2023/4/10</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class UpdateRepositoryPatchListenerTest {

    @Mock
    private UpdateRepositoryPatchService updateRepositoryPatchService;
    @InjectMocks
    private UpdateRepositoryPatchListener updateRepositoryPatchListener;

    @Test
    public void onApplicationEvent() {
        doNothing().when(updateRepositoryPatchService).updateRepositoryPatch();
        boolean result = true;
        try {
            updateRepositoryPatchListener.onApplicationEvent(new UploadPatchEvent("", new ArrayList<>()));
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }
}