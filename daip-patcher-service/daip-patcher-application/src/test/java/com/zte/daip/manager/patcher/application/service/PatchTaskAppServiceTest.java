package com.zte.daip.manager.patcher.application.service;
/* Started by AICoder, pid:jfcc8zf5f2j36bf14f180a99936cb618f232459b */
import com.zte.daip.manager.common.deployer.api.ClusterInfoControllerApi;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.common.task.common.enums.UpgradeModel;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import com.zte.daip.manager.response.CommonResponse;
import com.zte.daip.manager.task.api.ability.api.TaskManagerPlatformApi;
import com.zte.daip.manager.task.api.ability.dto.TaskModelVersionSet;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static junit.framework.Assert.assertFalse;
import static junit.framework.TestCase.assertTrue;

/**
 * PatchTaskAppService测试类。
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchTaskAppServiceTest {
    @Mock
    private PatchTaskOperateService patchTaskOperateService;

    @Mock
    private TaskManagerPlatformApi taskManagerPlatformApi;

    @Mock
    private SpectControllerApi spectControllerApi;

    @Mock
    private ClusterInfoControllerApi clusterInfoControllerApi;

    @InjectMocks
    private PatchTaskAppService patchTaskAppService;

    private PatchTaskDto patchTaskDto = new PatchTaskDto();

    private long taskId = 1L;

    /**
     * 初始化方法。
     */
    @Before
    public void init() {
        patchTaskDto.setTaskName("1");
    }

    /**
     * 测试创建补丁任务的方法。
     */
    @Test
    public void createPatchTask() {
        boolean result = true;

        try {
            patchTaskAppService.createPatchTask(patchTaskDto);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试修改补丁任务的方法。
     */
    @Test
    public void modifyPatchTask() {
        boolean result = true;

        try {
            patchTaskAppService.modifyPatchTask(patchTaskDto);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);

        patchTaskDto.setAllowModify(false);
        Assert.assertThrows(DaipBaseException.class, () -> patchTaskAppService.modifyPatchTask(patchTaskDto));
    }

    /**
     * 测试删除补丁任务的方法。
     */
    @Test
    public void removePatchTask() {
        boolean result = true;

        try {
            patchTaskAppService.removePatchTask(Lists.newArrayList(taskId));
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试触发补丁任务的方法。
     */
    @Test
    public void triggerPatchTask() {
        boolean result = true;

        try {
            patchTaskAppService.triggerPatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试重试补丁任务的方法。
     */
    @Test
    public void retryPatchTask() {
        boolean result = true;

        try {
            patchTaskAppService.retryPatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试暂停补丁任务的方法。
     */
    @Test
    public void pausePatchTask() {
        boolean result = true;

        try {
            patchTaskAppService.pausePatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试恢复补丁任务的方法。
     */
    @Test
    public void resumePatchTask() {
        boolean result = true;

        try {
            patchTaskAppService.resumePatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试查询所有补丁任务的方法。
     */
    @Test
    public void queryAllPatchTasks() {
        boolean result = true;

        try {
            patchTaskAppService.queryAllPatchTasks();
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试根据任务ID查询补丁任务的方法。
     */
    @Test
    public void queryPatchTaskByTaskId() {
        boolean result = true;

        try {
            patchTaskAppService.queryPatchTaskByTaskId(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试回滚补丁任务的方法。
     */
    @Test
    public void rollbackPatchTask() {
        boolean result = true;

        try {
            patchTaskAppService.rollbackPatchTask(patchTaskDto);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    /**
     * 测试查询服务实例升级的方法。
     *
     * @throws DaipBaseException 如果出现DAIP基础异常
     */
    @Test
    public void queryServiceInstanceUpgrade() throws DaipBaseException {
        VersionQueryRequestInfo versionQueryRequestInfo = buildVersionQueryRequestInfo();
        versionQueryRequestInfo.setAccessType(UpgradeModel.OFFLINE_UPDATE.getModel());
        CommonResponse<List<TaskModelVersionSet>> response = new CommonResponse<>();
        response.setStatus(0);
        TaskModelVersionSet taskModelVersionSet = new TaskModelVersionSet();
        taskModelVersionSet.setServiceId("dap.manager.zookeeper");
        taskModelVersionSet.setVersions(Collections.singleton("V1"));
        TaskModelVersionSet taskModelVersionSet1 = new TaskModelVersionSet();
        taskModelVersionSet1.setServiceId("dap.manager.hdfs");
        taskModelVersionSet1.setVersions(Collections.singleton("V1"));
        List<TaskModelVersionSet> taskModelVersionSets = new ArrayList<>();
        taskModelVersionSets.add(taskModelVersionSet);
        taskModelVersionSets.add(taskModelVersionSet1);
        response.setData(taskModelVersionSets);
        Mockito.when(taskManagerPlatformApi.modelSet(UpgradeModel.OFFLINE_UPDATE.getModel())).thenReturn(response);

        List<ProductSpect> productSpectList = new ArrayList<>();
        ProductSpect productSpect =new ProductSpect();
        productSpect.setServiceName("dap.manager.zookeeper");
        productSpect.setVersionNo("V1");
        ProductSpect productSpect1 =new ProductSpect();
        productSpect1.setServiceName("dap.manager.hdfs");
        productSpect1.setVersionNo("V1");
        productSpectList.add(productSpect);
        productSpectList.add(productSpect1);
        Mockito.when(spectControllerApi.queryAll()).thenReturn(productSpectList);

        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceId("dap.manager.zookeeper");
        Mockito.when(clusterInfoControllerApi.queryModelByServiceName(-1, "dap.manager.zookeeper")).thenReturn(serviceModel);
        serviceModel.setServiceId("dap.manager.hdfs");
        Mockito.when(clusterInfoControllerApi.queryModelByServiceName(-1, "dap.manager.hdfs")).thenReturn(serviceModel);

        List<ServiceInstanceUpgrade> map = patchTaskAppService.queryServiceInstanceUpgrade(versionQueryRequestInfo);
        Assert.assertEquals(1, map.size());
    }

    /**
     * 测试查询需要重新启动的服务的方法。
     *
     * @throws DaipBaseException 如果出现DAIP基础异常
     */
    @Test
    public void queryNeedRestartService() throws DaipBaseException {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setClusterId("test");
        patchTaskDto.setContext(Collections.singletonList(new ServiceInstancePatchInfo()));
        patchTaskDto.setRelationServices(Collections.singletonList(new ServiceInstance()));
        Mockito.when(patchTaskOperateService.queryNeedRestartService(patchTaskDto)).thenReturn(new ArrayList<>());
        Assert.assertTrue(CollectionUtils.isEmpty(patchTaskAppService.queryNeedRestartService(patchTaskDto)));
    }

    /**
     * 测试查询需要重新启动的服务失败的情况。
     *
     * @throws DaipBaseException 如果出现DAIP基础异常
     */
    @Test(expected = DaipBaseException.class)
    public void queryNeedRestartServiceFailed() throws DaipBaseException {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setClusterId("test");
        patchTaskDto.setContext(new ArrayList<>());
        Mockito.when(patchTaskOperateService.queryNeedRestartService(patchTaskDto)).thenReturn(new ArrayList<>());
        Assert.assertTrue(CollectionUtils.isEmpty(patchTaskAppService.queryNeedRestartService(patchTaskDto)));
    }

    /**
     * 构建版本查询请求信息。
     *
     * @return 版本查询请求信息对象
     */
    private VersionQueryRequestInfo buildVersionQueryRequestInfo() {
        VersionQueryRequestInfo versionQueryRequestInfo = new VersionQueryRequestInfo();
        List<ServiceVersionInfo> serviceVersionInfoList = com.google.common.collect.Lists.newArrayList();
        ServiceVersionInfo serviceVersionInfo = new ServiceVersionInfo();
        serviceVersionInfo.setServiceId("dap.manager.zookeeper");
        serviceVersionInfo.setServiceName("dap.manager.zookeeper");
        serviceVersionInfo.setComponentType("dap.manager.common.bigdata");
        ServiceVersionInfo serviceVersionInfo1 = new ServiceVersionInfo();
        serviceVersionInfo1.setServiceId("dap.manager.hdfs");
        serviceVersionInfo1.setServiceName("dap.manager.hdfs");
        serviceVersionInfo1.setComponentType("dap.manager.common.bigdata");
        serviceVersionInfoList.add(serviceVersionInfo);
        serviceVersionInfoList.add(serviceVersionInfo1);
        versionQueryRequestInfo.setServiceVersionInfoList(serviceVersionInfoList);
        return versionQueryRequestInfo;
    }
    /* Started by AICoder, pid:220f5r925c8fda214b4e0bc90034259c2237a93c */
    @Test
    public void patchTaskParamValid1() {
        boolean result = true;
        PatchTaskDto patchTaskDto1 = new PatchTaskDto(); // 创建补丁任务DTO对象
        try {
            result = patchTaskAppService.patchTaskParamValid(patchTaskDto1); // 尝试验证补丁任务参数
        } catch (Exception e) {
            result = false;
        }
        assertFalse(result); // 断言结果为假
    }

    @Test
    public void patchTaskParamValid2() {
        boolean result = true;
        PatchTaskDto patchTaskDto1 = new PatchTaskDto(); // 创建补丁任务DTO对象
        patchTaskDto1.setClusterId("1"); // 设置集群ID
        patchTaskDto1.setTaskName("%^&"); // 设置任务名称
        try {
            result = patchTaskAppService.patchTaskParamValid(patchTaskDto1); // 尝试验证补丁任务参数
        } catch (Exception e) {
            result = false;
        }
        assertFalse(result); // 断言结果为假
    }

    @Test
    public void patchTaskParamValid3() {
        boolean result = true;
        PatchTaskDto patchTaskDto1 = new PatchTaskDto(); // 创建补丁任务DTO对象
        patchTaskDto1.setClusterId("1"); // 设置集群ID
        patchTaskDto1.setTaskName("test中文testtesttesttesttest"); // 设置任务名称
        try {
            result = patchTaskAppService.patchTaskParamValid(patchTaskDto1); // 尝试验证补丁任务参数
        } catch (Exception e) {
            result = false;
        }
        assertFalse(result); // 断言结果为假
    }

    @Test
    public void patchTaskParamValid4() {
        boolean result = true;
        PatchTaskDto patchTaskDto1 = new PatchTaskDto(); // 创建补丁任务DTO对象
        patchTaskDto1.setClusterId("1"); // 设置集群ID
        patchTaskDto1.setTaskName("test"); // 设置任务名称
        try {
            result = patchTaskAppService.patchTaskParamValid(patchTaskDto1); // 尝试验证补丁任务参数
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result); // 断言结果为真
    }

    @Test
    public void patchNameNotRepeated1() {
        boolean result = true;
        PatchTaskDto patchTaskDto1 = new PatchTaskDto(); // 创建补丁任务DTO对象
        try {
            result = patchTaskAppService.patchNameNotRepeated(patchTaskDto1); // 尝试验证补丁任务名称是否重复
        } catch (Exception e) {
            result = false;
        }
        assertFalse(result); // 断言结果为假
    }

    @Test
    public void patchNameNotRepeated2() throws DaipBaseException {
        PatchTaskDto patchTaskDto2 = new PatchTaskDto(); // 创建补丁任务DTO对象
        patchTaskDto2.setTaskName("test1"); // 设置任务名称
        Mockito.when(patchTaskOperateService.queryAll()).thenReturn(Collections.singletonList(patchTaskDto2)); // 模拟补丁任务操作服务的响应
        boolean result = true;
        PatchTaskDto patchTaskDto1 = new PatchTaskDto(); // 创建补丁任务DTO对象
        patchTaskDto1.setTaskName("test"); // 设置任务名称
        try {
            result = patchTaskAppService.patchNameNotRepeated(patchTaskDto1); // 尝试验证补丁任务名称是否重复
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result); // 断言结果为真
    }

    @Test
    public void patchNameNotRepeated3() throws DaipBaseException {
        PatchTaskDto patchTaskDto2 = new PatchTaskDto(); // 创建补丁任务DTO对象
        patchTaskDto2.setTaskName("test"); // 设置任务名称
        Mockito.when(patchTaskOperateService.queryAll()).thenReturn(Collections.singletonList(patchTaskDto2)); // 模拟补丁任务操作服务的响应
        boolean result = true;
        PatchTaskDto patchTaskDto1 = new PatchTaskDto(); // 创建补丁任务DTO对象
        patchTaskDto1.setTaskName("test"); // 设置任务名称
        try {
            result = patchTaskAppService.patchNameNotRepeated(patchTaskDto1); // 尝试验证补丁任务名称是否重复
        } catch (Exception e) {
            result = false;
        }
        assertFalse(result); // 断言结果为假
    }
    /* Ended by AICoder, pid:220f5r925c8fda214b4e0bc90034259c2237a93c */

}

/* Ended by AICoder, pid:jfcc8zf5f2j36bf14f180a99936cb618f232459b */