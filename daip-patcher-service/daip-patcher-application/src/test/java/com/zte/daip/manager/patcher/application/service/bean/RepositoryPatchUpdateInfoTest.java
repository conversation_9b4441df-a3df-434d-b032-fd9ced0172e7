package com.zte.daip.manager.patcher.application.service.bean;

import com.zte.daip.manager.patcher.application.bean.RepositoryPatchUpdateInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;

@RunWith(SpringRunner.class)
public class RepositoryPatchUpdateInfoTest {

    @Test
    public void test(){
        RepositoryPatchUpdateInfo repositoryPatchUpdateInfo = new RepositoryPatchUpdateInfo();
        repositoryPatchUpdateInfo.setSourceUrl("");
        repositoryPatchUpdateInfo.setVersion("");
        repositoryPatchUpdateInfo.setServiceName("");
        repositoryPatchUpdateInfo.setPatchRepository("");
        repositoryPatchUpdateInfo.setPatchNameList(new ArrayList<>());
        assertEquals(repositoryPatchUpdateInfo.getPatchNameList().size(), new ArrayList<>().size());
        assertEquals(repositoryPatchUpdateInfo.getPatchRepository(),"");
        assertEquals(repositoryPatchUpdateInfo.getServiceName(),"");
        assertEquals(repositoryPatchUpdateInfo.getSourceUrl(),"");
        assertEquals(repositoryPatchUpdateInfo.getVersion(),"");
    }

    @Test
    public void test1(){
        RepositoryPatchUpdateInfo repositoryPatchUpdateInfo = new RepositoryPatchUpdateInfo("","","","",new ArrayList<>());
        assertEquals(repositoryPatchUpdateInfo.getPatchNameList().size(), new ArrayList<>().size());
        assertEquals(repositoryPatchUpdateInfo.getPatchRepository(),"");
        assertEquals(repositoryPatchUpdateInfo.getServiceName(),"");
        assertEquals(repositoryPatchUpdateInfo.getSourceUrl(),"");
        assertEquals(repositoryPatchUpdateInfo.getVersion(),"");
    }

    @Test
    public void equals() {
        RepositoryPatchUpdateInfo repositoryPatchUpdateInfo = new RepositoryPatchUpdateInfo();

        boolean ret = repositoryPatchUpdateInfo.equals(repositoryPatchUpdateInfo);
        Assert.assertTrue(ret);

        ret = repositoryPatchUpdateInfo.equals(null);
        Assert.assertFalse(ret);

        RepositoryPatchUpdateInfo repositoryPatchUpdateInfo2 = new RepositoryPatchUpdateInfo();

        ret = repositoryPatchUpdateInfo.equals(repositoryPatchUpdateInfo2);
        Assert.assertTrue(ret);
    }

    @Test
    public void testToString() {
        RepositoryPatchUpdateInfo repositoryPatchUpdateInfo = new RepositoryPatchUpdateInfo();
        repositoryPatchUpdateInfo.setServiceName("test");

        Assert.assertNotNull(repositoryPatchUpdateInfo.toString());
    }

}