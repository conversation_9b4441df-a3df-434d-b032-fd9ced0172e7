/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UploadSchemaPatchServcieTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/6
 * </p>
 * <p>
 * 完成日期：2021/4/6
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * <p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * <p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.service;

import java.util.List;

import com.zte.daip.manager.patcher.domain.utils.LogEvent;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.domain.schema.UpdateSchemaPatchNew;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class UpdateSchemaPatchServiceTest {
    @Mock
    private SchemaPatchApi schemaPatchApi;

    @Mock
    private UpdateSchemaPatchNew updateSchemaPatchNew;

    @Mock
    private LogEvent logEvent;

    @InjectMocks
    private UpdateSchemaPatchService updateSchemaPatchService;

    @Test
    public void 升级scheme补丁() throws Exception {
        List<PatchOperateResult> patchOperateResults = updateSchemaPatchService.updateSchemaPatch(1L);
        for (PatchOperateResult result : patchOperateResults) {
            Assert.assertTrue(result.isStatus());
        }
    }

    @Test
    public void 回退scheme补丁() throws Exception {
        List<PatchOperateResult> patchOperateResults = updateSchemaPatchService.rollbackSchemaPatch(1L);
        for (PatchOperateResult result : patchOperateResults) {
            Assert.assertTrue(result.isStatus());
        }
    }

    @Before
    public void init() {
        List<PatchTaskInfo> patchTaskInfos = Lists.newArrayList();
        PatchTaskInfo patchTaskInfo = new PatchTaskInfo();
        patchTaskInfo.setServiceName("deployer");
        patchTaskInfo.setVersion("V20.19.40.R4.B2");
        patchTaskInfo.setUpdatePatchNames(Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
        patchTaskInfos.add(patchTaskInfo);
        Mockito.when(schemaPatchApi.queryByUpdateTaskId(ArgumentMatchers.anyLong())).thenReturn(patchTaskInfos);
        Mockito.when(updateSchemaPatchNew.rollbackPatch(ArgumentMatchers.any())).thenReturn(new AsyncResult<>(new PatchOperateResult(true, "")));
        Mockito.when(updateSchemaPatchNew.updatePatch(ArgumentMatchers.any())).thenReturn(new AsyncResult<>(new PatchOperateResult(true, "")));
    }

}