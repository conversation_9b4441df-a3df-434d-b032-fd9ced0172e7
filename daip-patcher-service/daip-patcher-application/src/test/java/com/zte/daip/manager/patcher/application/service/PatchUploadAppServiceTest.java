package com.zte.daip.manager.patcher.application.service;

import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.dto.UploadResponseResult;
import com.zte.daip.manager.patcher.application.assenbler.UploadResultAssembler;
import com.zte.daip.manager.patcher.domain.upload.service.PatchUploadService;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;

@RunWith(SpringRunner.class)
public class PatchUploadAppServiceTest {

    @Mock
    private PatchEnvApi patchEnvApi;
    @Mock
    private PatchUploadService patchUploadService;

    @Mock
    private UploadResultAssembler uploadResultAssembler;

    @Mock
    private DaipEventReporter daipEventReporter;

    @InjectMocks
    private PatchUploadAppService patchUploadAppService;

    @Test
    public void test() throws InterruptedException, NoSuchFieldException, IllegalAccessException {
        Mockito.when(patchUploadService.uploadPatches(new ArrayList<>())).thenReturn("test");
        Mockito.when(patchUploadService.uploadPatchesProcess("test")).thenReturn(new ArrayList<>());
        UploadResponseResult uploadResponseTempResult = new UploadResponseResult();
        uploadResponseTempResult.setFinish(false);
        Mockito.when(uploadResultAssembler.assemblerUploadPatchProcess(new ArrayList<>()))
            .thenReturn(uploadResponseTempResult);
        ReflectionTestUtils.setField(patchUploadAppService, "TIME_OUT", 3000L);
        patchUploadAppService.uploadPatches(new ArrayList<>());
        Assert.assertTrue(true);

    }

}