package com.zte.daip.manager.patcher.application.bean;

import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

public class ServiceDeploymentInfoTest {
    /* Started by AICoder, pid:8c715q5fcaac12314a91099260c2102c29832883 */
    @Test
    public void equals() {
        ServiceDeploymentInfo serviceDeploymentInfo = new ServiceDeploymentInfo();

        boolean ret = serviceDeploymentInfo.equals(null);
        Assert.assertFalse(ret);

        ServiceDeploymentInfo serviceDeploymentInfo1 = new ServiceDeploymentInfo();

        ret = serviceDeploymentInfo.equals(serviceDeploymentInfo1);
        Assert.assertTrue(ret);
    }

    @Test
    public void testToString() {
        ServiceDeploymentInfo serviceDeploymentInfo = new ServiceDeploymentInfo();
        serviceDeploymentInfo.setServiceName("zk");
        serviceDeploymentInfo.setServiceId("zk");
        serviceDeploymentInfo.setServiceInstanceId("zk_1");
        serviceDeploymentInfo.setVersion("09");
        serviceDeploymentInfo.setClusterId("001");
        Assert.assertNotNull(serviceDeploymentInfo);
    }

    /* Ended by AICoder, pid:8c715q5fcaac12314a91099260c2102c29832883 */
}