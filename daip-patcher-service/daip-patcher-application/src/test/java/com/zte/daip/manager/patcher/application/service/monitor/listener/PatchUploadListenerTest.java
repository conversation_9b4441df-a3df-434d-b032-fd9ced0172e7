package com.zte.daip.manager.patcher.application.service.monitor.listener;

import java.io.File;
import java.io.IOException;
import java.util.List;

import com.zte.daip.manager.common.cache.lock.LockUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.patcher.application.service.monitor.filter.ZipFileFilter;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.*;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchUploadListenerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/4/6</p>
 * <p>完成日期：2021/4/6</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchUploadListenerTest {

    @Mock
    private LockUtils lockUtils;

    private PatchUploadListener patchUploadListener = new PatchUploadListener() {
        @Override
        protected boolean isFinishTransmit() {
            return true;
        }

        @Override
        protected void upload(List<String> fileNames) {
            //
        }

        @Override
        protected String queryRepositoryHomeEnv() {
            return getRepositoryHomeEnv();
        }

    };

    @Test
    public void onStop() {
        boolean result = true;
        try {
            File[] patch = FilePathCleaner.newFile(getRepositoryHomeEnv()).listFiles();
            patchUploadListener.onFileCreate(patch[0]);
            patchUploadListener.onStop(new FileAlterationObserver(String.valueOf(new ZipFileFilter())));
        } catch (Exception e) {
            result = false;
        }

    }

    private String getRepositoryHomeEnv() {
        String applicationPath =
            Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();
        return new File(applicationPath).getParent() + File.separator + "patch";
    }

    @Test
    public void copyPatchFile() throws IOException {
        String patchPath = this.getClass().getResource("/copytest").getPath();
        File patch = new File(patchPath, "DAP-HDFS-V20.19.40.R4.B2-SP028-20200901.zip");
        String desDir = this.getClass().getResource("/copytest/tmp/test").getPath();
        File uploadDir = new File(desDir).getParentFile();
        List<String> fileNames = Lists.newArrayList();
        File patchAfter = new File(uploadDir.getPath(), "DAP-HDFS-V20.19.40.R4.B2-SP028-20200901.zip");
        patchUploadListener.copyPatchFile(patch, uploadDir, fileNames);
        //FileUtils.copyFileToDirectory(patchAfter, new File(patchPath));
        Assert.assertEquals(1, fileNames.size());
    }

}