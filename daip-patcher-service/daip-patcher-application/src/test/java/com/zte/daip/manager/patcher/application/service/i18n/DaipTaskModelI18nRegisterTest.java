package com.zte.daip.manager.patcher.application.service.i18n;

import com.zte.daip.manager.common.deployer.i18n.I18nControllerApi;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.io.File;
import java.io.IOException;
import java.net.URL;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30
 */
@RunWith(MockitoJUnitRunner.class)
public class DaipTaskModelI18nRegisterTest {

    @InjectMocks
    private DaipTaskModelI18nRegister daipTaskModelI18nRegister;

    @Mock
    private I18nControllerApi i18nControllerApi;

    @Mock
    private Logger log;

    private static final String VALID_PATH = "valid/path";
    private static final String INVALID_PATH = "invalid/path";
    private static final String FILE_CONTENT = "file content";


    @Test
    public void should_UpdateI18n_When_FileExists() throws IOException {

        daipTaskModelI18nRegister.updateTaskServiceI18n("daip-patcher-task-i18n.xml");

    }

    @Test
    public void should_LogError_When_FileNotFound() throws IOException {
        daipTaskModelI18nRegister.updateTaskServiceI18n(INVALID_PATH);

    }

    @Test(expected = IOException.class)
    public void should_ThrowIOException_When_FileReadFails() throws IOException {
        URL url = new URL("daip-patcher-task-i18n.xml");
        when(daipTaskModelI18nRegister.getClass().getClassLoader().getResource(VALID_PATH)).thenReturn(url);
        when(FileUtils.readFileToString(any(File.class), eq("UTF-8"))).thenThrow(new IOException("File read error"));

        daipTaskModelI18nRegister.updateTaskServiceI18n(VALID_PATH);
    }
}