package com.zte.daip.manager.patcher.application.bean;

import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

public class PatchFileInfoBeanTest {
    @Test
    public void equals() {
        PatchFileInfoBean patchFileInfoBean = new PatchFileInfoBean();

        boolean ret = patchFileInfoBean.equals(patchFileInfoBean);
        Assert.assertTrue(ret);

        ret = patchFileInfoBean.equals(null);
        Assert.assertFalse(ret);
        PatchFileInfoBean patchFileInfoBean1 = new PatchFileInfoBean();

        ret = patchFileInfoBean.equals(patchFileInfoBean1);
        Assert.assertTrue(ret);
    }

    @Test
    public void testToString() {
        PatchFileInfoBean patchFileInfoBean = new PatchFileInfoBean();
        patchFileInfoBean.setFirstFileLength(1);
        patchFileInfoBean.setFirstFileLength(1);
        patchFileInfoBean.setSameCount(1);
        patchFileInfoBean.clear();
        Assert.assertNotNull(patchFileInfoBean.toString());
    }
}