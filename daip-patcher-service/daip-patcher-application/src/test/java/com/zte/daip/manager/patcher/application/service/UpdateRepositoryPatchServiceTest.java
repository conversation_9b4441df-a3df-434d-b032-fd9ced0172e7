package com.zte.daip.manager.patcher.application.service;
/* Started by AICoder, pid:pb095c31dd0316e1453f08bb0322f001aa7670d0 */
import com.zte.daip.manager.common.deployer.bean.loadmodel.Product;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.event.reporter.api.dto.EventLog;
import com.zte.daip.manager.patcher.application.publish.UpdateRepositoryPatchPublish;
import com.zte.daip.manager.patcher.application.bean.RepositoryUpdateResult;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.schema.utils.SchemaPatchUtils;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.redisson.RedissonLock;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;

/**
 * 测试类，用于测试更新仓库补丁服务的单元测试。
 */
@RunWith(SpringRunner.class)
public class UpdateRepositoryPatchServiceTest {

    /**
     * 被测试的服务对象。
     */
    @InjectMocks
    private UpdateRepositoryPatchService updateRepositoryPatchService;

    /**
     * Redisson客户端的模拟对象。
     */
    @Mock
    private RedissonClient redissonClient;

    /**
     * 更新仓库补丁锁的键。
     */
    private static final String UPDATE_PATCH_KEY = "UPDATE_REPOSITORY_PATCH_LOCK_KEY";

    /**
     * 补丁历史服务的模拟对象。
     */
    @Mock
    private PatchHistoryService patchHistoryService;

    /**
     * 补丁信息服务的模拟对象。
     */
    @Mock
    private PatchInfoService patchInfoService;

    /**
     * 规格控制器API的模拟对象。
     */
    @Mock
    private SpectControllerApi spectControllerApi;

    /**
     * 产品控制器API的模拟对象。
     */
    @Mock
    private ProductControllerApi productControllerApi;

    /**
     * 更新仓库补丁发布的模拟对象。
     */
    @Mock
    private UpdateRepositoryPatchPublish updateRepositoryPatchPublish;

    /**
     * 补丁环境API的模拟对象。
     */
    @Mock
    private PatchEnvApi patchEnvApi;

    /**
     * 事件报告器的模拟对象。
     */
    @Mock
    private DaipEventReporter daipEventReporter;

    /**
     * Schema补丁工具的模拟对象。
     */
    @Mock
    private SchemaPatchUtils schemaPatchUtils;

    /**
     * 更新Schema补丁服务的模拟对象。
     */
    @Mock
    private UpdateSchemaPatchService updateSchemaPatchService;

    /**
     * 在每个测试方法执行前进行设置。
     *
     * @throws Exception 如果发生异常，则抛出异常。
     */
    @Before
    public void setUp() throws Exception {
        // 设置微服务名称、端口和URL
        ReflectionTestUtils.setField(updateRepositoryPatchService, "microserviceName", "daip-patcher-svr");
        ReflectionTestUtils.setField(updateRepositoryPatchService, "microserviceport", "56210");
        ReflectionTestUtils.setField(updateRepositoryPatchService, "microserviceurl", "/api/daip-patcher-svr/v1");

        // 模拟查询所有补丁信息的方法
        Mockito.when(patchInfoService.queryAllPatchInfos()).thenReturn(generateDetail());

        // 模拟检查产品加载成功的方法
        Mockito.when(schemaPatchUtils.isProductLoadSuccess(anyString(), anyString())).thenReturn(true);

        // 模拟查询补丁历史名称的方法
        Mockito.when(patchHistoryService.queryPatchHistoryName()).thenReturn(new ArrayList<>());

        // 创建一个产品规格对象，并设置产品文件名
        ProductSpect productSpect = new ProductSpect();
        productSpect.setProductFileName("zookeeper");

        // 模拟根据服务名和版本号查询规格的方法
        Mockito.when(spectControllerApi.queryByServiceNameAndVersionNo("zookeeper", "V20.23.40.07-SNAPSHOT"))
                .thenReturn(productSpect);

        // 创建一个产品对象，并设置检查结果
        Product product = new Product();
        product.setCheckResult("true");

        // 模拟根据文件名查询产品的方
        Mockito.when(productControllerApi.queryByFileName("zookeeper")).thenReturn(product);

        // 模拟根据服务和基础版本查询补丁信息的方法
        Mockito.when(patchInfoService.findByServiceAndBaseVersion("zookeeper", "V20.23.40.07-SNAPSHOT"))
                .thenReturn(new ArrayList<>());

        // 模拟获取仓库环境的方法
        Mockito.when(patchEnvApi.getRepositoryHomeEnv()).thenReturn("/data1/version");

        // 模拟发布更新仓库消息的方法
        doNothing().when(updateRepositoryPatchPublish).publishUpdateRepositoryMsg(any());

        // 模拟批量保存补丁历史的方法
        doNothing().when(patchHistoryService).saveBatch(any());

        // 设置升级超时时间
        ReflectionTestUtils.setField(updateRepositoryPatchService, "upgradeTimeout", 10);

        // 创建Redisson锁的模拟对象
        RedissonLock redissonLock = Mockito.mock(RedissonLock.class);

        // 模拟获取锁的方法
        Mockito.when(redissonClient.getLock(UPDATE_PATCH_KEY)).thenReturn(redissonLock);

        // 模拟获取主题的方法
        Mockito.when(redissonClient.getTopic("lockReleaseTopic")).thenReturn(mock(RTopic.class));

        // 模拟判断锁是否被锁定的方法
        Mockito.when(redissonLock.isLocked()).thenReturn(false);

        // 模拟加锁的方法
        Mockito.doNothing().when(redissonLock).lock(10 * 60 * 1000L, TimeUnit.MILLISECONDS);

        // 模拟解锁的方法
        Mockito.doNothing().when(redissonLock).unlock();
    }

    /**
     * 在每个测试方法执行后进行清理。
     *
     * @throws Exception 如果发生异常，则抛出异常。
     */
    @After
    public void tearDown() throws Exception {}

    /**
     * 测试更新仓库补丁的方法。
     */
    @Test
    public void updateRepositoryPatch() {
        // 调用更新仓库补丁的方法
        updateRepositoryPatchService.updateRepositoryPatch();

        // 断言结果为空字符串
        assertEquals("", "");
    }

    /**
     * 测试更新仓库补丁的方法，当存在补丁历史名称时。
     */
    @Test
    public void updateRepositoryPatch1() {
        // 模拟查询补丁历史名称的方法
        Mockito.when(patchHistoryService.queryPatchHistoryName())
                .thenReturn(Collections.singletonList("DAP-ZOOKEEPER-V20.23.40.07-SNAPSHOT-RepositoryVersion-SP001-20230731"));

        // 调用更新仓库补丁的方法
        updateRepositoryPatchService.updateRepositoryPatch();

        // 断言结果为空字符串
        assertEquals("", "");
    }

    /**
     * 测试处理仓库补丁结果的方法。
     */
    @Test
    public void handleRepositoryPatchResult() {
        // 创建一个仓库更新结果列表
        List<RepositoryUpdateResult> repositoryUpdateResults = new ArrayList<>();

        // 创建一个仓库更新结果对象
        RepositoryUpdateResult repositoryUpdateResult = new RepositoryUpdateResult();

        // 设置错误消息
        repositoryUpdateResult.setErrorMessage("error");

        // 设置服务名
        repositoryUpdateResult.setServiceName("zookeeper");

        // 设置版本
        repositoryUpdateResult.setVersion("V20.23.40.07-SNAPSHOT");

        // 创建一个成功的补丁列表
        List<String> successPatchList = new ArrayList<>();
        successPatchList.add("patch1");

        // 创建一个失败的补丁列表
        List<String> failedPatchList = new ArrayList<>();
        failedPatchList.add("patch2");

        // 设置成功的补丁列表
        repositoryUpdateResult.setSuccessPatchList(successPatchList);

        // 设置失败的补丁列表
        repositoryUpdateResult.setFailedPatchList(failedPatchList);

        // 将仓库更新结果对象添加到列表中
        repositoryUpdateResults.add(repositoryUpdateResult);

        // 创建一个事件日志对象
        EventLog eventLog = EventLog.buildDefault("", "", "");

        // 模拟构建事件的方法
        Mockito.when(daipEventReporter.buildEvent("handle_update_repository_version_result", "error"))
                .thenReturn(eventLog);

        // 模拟记录错误的方法
        doNothing().when(daipEventReporter).error(anyString(), anyString());

        // 调用处理仓库补丁结果的方法
        updateRepositoryPatchService.handleRepositoryPatchResult(repositoryUpdateResults);

        // 验证批量保存补丁历史的方法被调用了一次
        Mockito.verify(patchHistoryService, Mockito.times(1)).saveBatch(Mockito.any());
    }

    /**
     * 生成补丁详细信息列表的方法。
     *
     * @return 补丁详细信息列表
     */
    private List<PatchDetailPo> generateDetail() {
        // 创建一个补丁详细信息列表
        List<PatchDetailPo> patchDetails = new ArrayList<>();

        // 创建一个补丁详细信息对象
        PatchDetailPo patchDetailPo = new PatchDetailPo();

        // 设置补丁名称
        patchDetailPo.setPatchName("DAP-ZOOKEEPER-V20.23.40.07-SNAPSHOT-RepositoryVersion-SP001-20230731");

        // 设置基础版本
        patchDetailPo.setBaseVersion("V20.23.40.07-SNAPSHOT");

        // 设置依赖补丁
        patchDetailPo.setDependPatch("");

        // 设置服务
        patchDetailPo.setService("zookeeper");

        // 将补丁详细信息对象添加到列表中
        patchDetails.add(patchDetailPo);

        // 返回补丁详细信息列表
        return patchDetails;
    }
}

/* Ended by AICoder, pid:pb095c31dd0316e1453f08bb0322f001aa7670d0 */