package com.zte.daip.manager.patcher.application.assenbler;

import com.zte.daip.manager.patcher.api.dto.UploadResponseResult;
import com.zte.daip.manager.patcher.domain.upload.entity.PatchUploadResult;
import com.zte.daip.manager.patcher.domain.upload.utils.OnePatchProcess;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.*;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: UploadResultAssemblerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/5/25</p>
 * <p>完成日期：2021/5/25</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class UploadResultAssemblerTest {

    @InjectMocks
    private UploadResultAssembler uploadResultAssembler;

    @Test
    public void 补丁上传流程全部完成返回true() throws Exception {
        List<PatchUploadResult> patchUploadResults = Lists.newArrayList();
        PatchUploadResult patchUploadResult = new PatchUploadResult(true, "upload success",
            "DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215", false, OnePatchProcess.FINISH);
        patchUploadResults.add(patchUploadResult);
        UploadResponseResult uploadResponseResult =
            uploadResultAssembler.assemblerUploadPatchProcess(patchUploadResults);

        assertThat(uploadResponseResult.isFinish(), is(true));
    }

    @Test
    public void 补丁上传流程超时返回false() throws Exception {
        List<PatchUploadResult> patchUploadResults = Lists.newArrayList();
        PatchUploadResult patchUploadResult = new PatchUploadResult(true, "upload success",
            "DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215", false, OnePatchProcess.FULL_PATCH_CHECK);
        patchUploadResults.add(patchUploadResult);
        UploadResponseResult uploadResponseResult =
            uploadResultAssembler.assemblerUploadPatchProcess(patchUploadResults);

        assertThat(uploadResponseResult.isFinish(), is(false));
    }

}