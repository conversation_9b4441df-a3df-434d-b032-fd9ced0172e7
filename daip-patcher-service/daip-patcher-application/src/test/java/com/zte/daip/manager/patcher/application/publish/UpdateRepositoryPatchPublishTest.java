package com.zte.daip.manager.patcher.application.publish;
/* Started by AICoder, pid:10cb32b1fei0b8e14f1c0a2100dfcb942795cda2 */
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

import java.util.ArrayList;
import java.util.List;

import com.zte.daip.manager.common.utils.i18n.DaipI18nService;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.communication.producer.EventPublisher;
import com.zte.daip.manager.patcher.application.bean.RepositoryPatchUpdateInfo;

/**
 * 测试类，用于测试更新仓库补丁发布功能。
 */
@RunWith(SpringRunner.class)
public class UpdateRepositoryPatchPublishTest {

    /**
     * 事件发布者模拟对象。
     */
    @Mock
    private EventPublisher eventPublisher;

    /**
     * 更新仓库补丁发布对象。
     */
    @InjectMocks
    private UpdateRepositoryPatchPublish updateRepositoryPatchPublish;

    /**
     * 事件报告器模拟对象。
     */
    @Mock
    private DaipEventReporter daipEventReporter;

    /**
     * 国际化服务模拟对象。
     */
    @Mock
    private DaipI18nService daipI18nService;

    /**
     * 在每个测试方法执行前进行设置。
     *
     * @throws Exception 如果发生异常，则抛出异常。
     */
    @Before
    public void setUp() throws Exception {
        // 可以在这里添加一些初始化代码，如果需要的话
    }

    /**
     * 在每个测试方法执行后进行清理。
     *
     * @throws Exception 如果发生异常，则抛出异常。
     */
    @After
    public void tearDown() throws Exception {
        // 可以在这里添加一些清理代码，如果需要的话
    }

    /**
     * 测试发布更新仓库消息的方法。
     */
    @Test
    public void publishUpdateRepositoryMsg() {
        // 模拟daipEventReporter的error方法不执行任何操作
        doNothing().when(daipEventReporter).error(anyString(), anyString());

        // 模拟daipI18nService的getLabel方法返回"test"
        Mockito.when(daipI18nService.getLabel(anyString(), (String[]) any())).thenReturn("test");

        // 模拟eventPublisher的sendByGroup方法不执行任何操作
        doNothing().when(eventPublisher).sendByGroup(any(), any());

        // 创建一个空的补丁更新信息列表
        List<RepositoryPatchUpdateInfo> patchUpdateInfos = new ArrayList<>();

        // 调用publishUpdateRepositoryMsg方法
        updateRepositoryPatchPublish.publishUpdateRepositoryMsg(patchUpdateInfos);

        // 验证eventPublisher的sendByGroup方法被调用了一次
        Mockito.verify(eventPublisher, Mockito.times(1)).sendByGroup(Mockito.any(), Mockito.any());
    }
}

/* Ended by AICoder, pid:10cb32b1fei0b8e14f1c0a2100dfcb942795cda2 */