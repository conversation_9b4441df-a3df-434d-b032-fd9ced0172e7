package com.zte.daip.manager.patcher.application.bean;

import org.junit.Assert;
import org.junit.Test;

import static org.junit.Assert.*;

public class PatchUpdateInfoTest {
    /* Started by AICoder, pid:yf0d4u0c18p849414f4f094b902d253f9a627b2f */
    @Test
    public void equals() {
        PatchUpdateInfo patchUpdateInfo = new PatchUpdateInfo();
        patchUpdateInfo.setServiceName("zk");
        patchUpdateInfo.setProjectName("zk");
        patchUpdateInfo.setVersionNumber("zk_1");

        boolean ret = patchUpdateInfo.equals(patchUpdateInfo);
        Assert.assertTrue(ret);

        ret = patchUpdateInfo.equals(null);
        Assert.assertFalse(ret);

        PatchUpdateInfo patchUpdateInfo1 = new PatchUpdateInfo();
        patchUpdateInfo1.setServiceName("zk");
        patchUpdateInfo1.setProjectName("zk");
        patchUpdateInfo1.setVersionNumber("zk_1");

        ret = patchUpdateInfo.equals(patchUpdateInfo1);
        Assert.assertTrue(ret);
    }

    @Test
    public void testToString() {
        PatchUpdateInfo patchUpdateInfo = new PatchUpdateInfo();
        patchUpdateInfo.setServiceName("zk");
        patchUpdateInfo.setProjectName("zk");
        patchUpdateInfo.setVersionNumber("zk_1");
        Assert.assertNotNull(patchUpdateInfo.toString());
    }

    /* Ended by AICoder, pid:yf0d4u0c18p849414f4f094b902d253f9a627b2f */
}