package com.zte.daip.manager.patcher.application.service.bean;

import com.zte.daip.manager.patcher.application.bean.RepositoryUpdateResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;

@RunWith(SpringRunner.class)
public class RepositoryUpdateResultTest {
    @Test
    public void test() {
        RepositoryUpdateResult repositoryUpdateResult = new RepositoryUpdateResult();
        repositoryUpdateResult.setErrorMessage("");
        repositoryUpdateResult.setVersion("");
        repositoryUpdateResult.setServiceName("");
        repositoryUpdateResult.setFailedPatchList(new ArrayList<>());
        repositoryUpdateResult.setSuccessPatchList(new ArrayList<>());
        assertEquals(repositoryUpdateResult.getFailedPatchList().size(), new ArrayList<>().size());
        assertEquals(repositoryUpdateResult.getSuccessPatchList().size(), new ArrayList<>().size());
        assertEquals(repositoryUpdateResult.getServiceName(), "");
        assertEquals(repositoryUpdateResult.getErrorMessage(), "");
        assertEquals(repositoryUpdateResult.getVersion(), "");
    }

    @Test
    public void test1() {
        RepositoryUpdateResult repositoryUpdateResult =
            new RepositoryUpdateResult("", "", new ArrayList<>(), new ArrayList<>(), "");
        assertEquals(repositoryUpdateResult.getFailedPatchList().size(), new ArrayList<>().size());
        assertEquals(repositoryUpdateResult.getSuccessPatchList().size(), new ArrayList<>().size());
        assertEquals(repositoryUpdateResult.getServiceName(), "");
        assertEquals(repositoryUpdateResult.getErrorMessage(), "");
        assertEquals(repositoryUpdateResult.getVersion(), "");
    }

    @Test
    public void equals() {
        RepositoryUpdateResult repositoryUpdateResult = new RepositoryUpdateResult();

        boolean ret = repositoryUpdateResult.equals(repositoryUpdateResult);
        Assert.assertTrue(ret);

        ret = repositoryUpdateResult.equals(null);
        Assert.assertFalse(ret);

        RepositoryUpdateResult repositoryUpdateResult1 = new RepositoryUpdateResult();

        ret = repositoryUpdateResult.equals(repositoryUpdateResult1);
        Assert.assertTrue(ret);
    }

    @Test
    public void testToString() {
        RepositoryUpdateResult repositoryUpdateResult = new RepositoryUpdateResult();
        repositoryUpdateResult.setServiceName("test");

        Assert.assertNotNull(repositoryUpdateResult.toString());
    }
}