/* Started by AICoder, pid:i1488s9f954a77714dc30ad431b504331bd1bfa6 */
package com.zte.daip.manager.patcher.application.listener;

import static com.zte.daip.communication.bean.MessageType.MULICAST;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

import java.util.List;
import java.util.Set;

import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;
import com.zte.daip.manager.patcher.application.bean.ServiceDeploymentInfo;
import com.zte.daip.manager.patcher.application.listener.ServiceDeploymentConsumer;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.dispatch.DistributeProgressService;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import com.alibaba.fastjson.JSONObject;
import com.zte.daip.communication.bean.RequestMessageBody;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 测试ServiceDeploymentConsumer类的单元测试。
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class ServiceDeploymentConsumerTest {

    @Mock
    private PatchInfoService patchInfoService; // 模拟补丁信息服务

    @Mock
    private DistributeProgressService distributeProgressService; // 模拟分发进度服务

    @Mock
    private FileDistributeControllerApi fileDistributeControllerApi; // 模拟文件分发控制器API

    @Mock
    private PatchEnvApi patchEnvApi; // 模拟补丁环境API

    @InjectMocks
    private ServiceDeploymentConsumer serviceDeploymentConsumer; // 被测试的服务部署消费者

    /**
     * 在每个测试方法执行前进行设置。
     *
     * @throws Exception 如果设置过程中出现异常，则抛出异常。
     */
    @Before
    public void setUp() throws Exception {
        Mockito.when(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(any(), any()))
                .thenReturn(getServicePatchPo()); // 设置模拟的补丁信息查找结果（不包括方案和存储库）
        Mockito.when(patchInfoService.findByServiceAndBaseVersion(any(), any())).thenReturn(getProjectPatchPo()); // 设置模拟的补丁信息查找结果（包括方案和存储库）
        Mockito.when(fileDistributeControllerApi.distributeFiles(any())).thenReturn("aaa"); // 设置模拟的文件分发结果
        Mockito.when(patchEnvApi.getRepositoryHomeEnv()).thenReturn("/data1/version"); // 设置模拟的存储库主目录环境
        Mockito.when(patchEnvApi.getPatchUrl()).thenReturn("https://127.0.0.1:8988"); // 设置模拟的补丁URL
        doNothing().when(distributeProgressService).checkAndUpdateDistributeProgress(anyString()); // 设置模拟的检查和更新分发进度操作
    }

    /**
     * 测试安装扩容流程触发补丁分发流程是否成功。
     *
     * @throws Exception 如果测试过程中出现异常，则抛出异常。
     */
    @Test
    public void 安装扩容流程触发补丁分发流程成功() throws Exception {
        boolean result = true;
        try {
            serviceDeploymentConsumer.handle(generateRequestMsg()); // 执行处理请求消息的操作
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result); // 断言结果为真，即操作成功
    }

    /**
     * 获取服务补丁PO列表。
     *
     * @return 服务补丁PO列表。
     */
    private List<PatchDetailPo> getServicePatchPo() {
        PatchDetailPo patchBean = new PatchDetailPo();
        patchBean.setPatchName("DAP-HDFS-V20.19.40.R4.B2-SP01");
        patchBean.setBaseVersion("V20.19.40.R4.B2");
        patchBean.setService("hdfs");
        List<PatchDetailPo> patchDetailPos = org.assertj.core.util.Lists.newArrayList();
        patchDetailPos.add(patchBean);
        return patchDetailPos;
    }

    /**
     * 获取项目补丁PO列表。
     *
     * @return 项目补丁PO列表。
     */
    private List<PatchDetailPo> getProjectPatchPo() {
        PatchDetailPo patchBean = new PatchDetailPo();
        patchBean.setPatchName("DAP-zdh-V20.19.40.R4.B2-SP01");
        patchBean.setBaseVersion("V20.19.40.R4.B2");
        patchBean.setService("zdh");
        return org.assertj.core.util.Lists.newArrayList(patchBean);
    }

    /**
     * 生成请求消息。
     *
     * @return 请求消息字符串。
     */
    private String generateRequestMsg() {
        RequestMessageBody requestMessageBody = new RequestMessageBody();
        requestMessageBody.setType(MULICAST.getType());
        Set<String> targetHostsName = org.assertj.core.util.Sets.newHashSet();
        Set<String> targetHostIps = org.assertj.core.util.Sets.newHashSet();
        targetHostIps.add("********");
        targetHostsName.add("host01");
        ServiceDeploymentInfo serviceDeploymentInfo = new ServiceDeploymentInfo("100001", "V20.19.40.R4.B2", "hdfs",
                "hdfs", "dap.manager.hdfs", "zdh", targetHostsName, targetHostIps);
        requestMessageBody.setBody(serviceDeploymentInfo);
        return JSONObject.toJSONString(requestMessageBody);
    }
}

/* Ended by AICoder, pid:i1488s9f954a77714dc30ad431b504331bd1bfa6 */