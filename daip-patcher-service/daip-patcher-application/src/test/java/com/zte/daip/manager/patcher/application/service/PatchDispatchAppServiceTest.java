/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDispatchAppServiceTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/31
 * </p>
 * <p>
 * 完成日期：2021/3/31
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.application.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.application.publish.UploadPatchEventPublish;
import com.zte.daip.manager.patcher.domain.query.service.PatchDetailService;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchDispatchAppServiceTest {
    @Mock
    private PatchDetailService patchDetailService;
    @Mock
    private UploadPatchEventPublish uploadPatchEventPublish;

    @InjectMocks
    private PatchDispatchAppService patchDispatchAppService;

    @Before
    public void setUp() {
        Mockito.when(patchDetailService.queryNeedDispatchPatchListMap()).thenReturn(mockNeedDispatchPatchListMap());
        doNothing().when(uploadPatchEventPublish).publishEvents(anyList());
    }

    @Test
    public void 补丁分发发送本地消息() {

        patchDispatchAppService.publishDispatchEvent();
        Mockito.verify(uploadPatchEventPublish, Mockito.times(1)).publishEvents(anyList());
    }

    private List<PatchBean> mockNeedDispatchPatchListMap() {
        PatchBean patchBean1 = new PatchBean();
        patchBean1.setPatchName("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01");
        patchBean1.setSrcVersion("V20.19.40.R4.B2");
        PatchBean patchBean2 = new PatchBean();
        patchBean2.setPatchName("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP02");
        patchBean2.setSrcVersion("V20.19.40.R4.B2");
        return Lists.newArrayList(patchBean1, patchBean2);

    }
}