package com.zte.daip.manager.patcher.application.event.listener;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.patch.entity.PatchBean;
import com.zte.daip.manager.patcher.domain.upload.event.UploadPatchEvent;
import com.zte.daip.manager.patcher.application.listener.PatchDistributeEventListener;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchDistributeEventListenerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/11</p>
 * <p>完成日期：2023/3/11</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchDistributeEventListenerTest {
    @InjectMocks
    private PatchDistributeEventListener patchDistributeEventListener;

    @Test
    public void check_distribute_listener() {
        boolean result = true;

        try {
            UploadPatchEvent uploadPatchEvent = getUploadPatchEvent();
            patchDistributeEventListener.onApplicationEvent(uploadPatchEvent);
        } catch (Exception e) {
            result = false;
        }
        Assert.assertTrue(result);
    }

    private UploadPatchEvent getUploadPatchEvent() {
        PatchBean patchBean = new PatchBean();
        patchBean.setPatchName("DAP-ZDH-V20.19.40.R4.B2-SP001");
        patchBean.setService("zdh");
        patchBean.setSrcVersion("V20.19.40.R4.B2");
        List<PatchBean> needPublishPatch = Lists.newArrayList(patchBean);
        return new UploadPatchEvent("", needPublishPatch);
    }
}