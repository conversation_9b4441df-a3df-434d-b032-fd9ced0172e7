package com.zte.daip.manager.patcher.application.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.infrastructure.PatchStartAndStopApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateServiceDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchStartAndStopAppServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/4/25</p>
 * <p>完成日期：2023/4/25</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchStartAndStopAppServiceTest {

    @Mock
    private PatchStartAndStopApi patchStartAndStopApi;

    @InjectMocks
    private PatchStartAndStopAppService patchStartAndStopAppService;

    @Before
    public void init() throws Exception {
        Mockito.when(patchStartAndStopApi.queryCanOperateInstances(anyString(), anyBoolean()))
            .thenReturn(Lists.newArrayList("zk_01", "zk_02"));
        Mockito.when(patchStartAndStopApi.queryInstancesByServiceName(anyString(), anyString()))
            .thenReturn(Lists.newArrayList("zk_01", "zk_02"));
        Mockito.when(patchStartAndStopApi.queryDependInstances(anyString(), anyString()))
            .thenReturn(Lists.newArrayList("kafka"));
        Mockito.when(patchStartAndStopApi.queryCanOperateInstances(anyString(), anyBoolean()))
            .thenReturn(Lists.newArrayList("zk_01", "zk_02", "kafka"));
        doNothing().when(patchStartAndStopApi).startServiceByCluster(anyString(), anyList());
        doNothing().when(patchStartAndStopApi).stopServiceByCluster(anyString(), anyList());
        Mockito.when(patchStartAndStopApi.queryOperateProgress(anyString())).thenReturn("kafka");
    }

    @Test
    public void queryCanOperateServices() throws Exception {

        List<String> services = patchStartAndStopAppService.queryCanOperateServices("0001", true,
            Lists.newArrayList(new PatchOperateServiceDto("zookeeper", "")));
        assertEquals(3, services.size());
    }

    @Test
    public void startCluster() {
        boolean result = true;

        try {
            patchStartAndStopAppService.startCluster("001", Lists.newArrayList());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void stopCluster() {
        boolean result = true;

        try {
            patchStartAndStopAppService.stopCluster("001", Lists.newArrayList());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryOperatorProcess() throws Exception {
        String operatorProcess = patchStartAndStopAppService.queryOperatorProcess("100001");
        assertNotNull(operatorProcess);
    }
}