<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>daip-patcher</artifactId>
        <groupId>com.zte.daip.manager.patcher</groupId>
        <version>deletePatch</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zte.daip.manager.patcher</groupId>
    <artifactId>daip-patcher-service</artifactId>
    <version>deletePatch</version>
    <packaging>pom</packaging>

    <modules>
        <module>daip-patcher-inner-api</module>
        <module>daip-patcher-impl</module>
        <module>daip-patcher-interfaces</module>
        <module>daip-patcher-domain</module>
        <module>daip-patcher-application</module>
        <module>daip-patcher-infrastructure-api</module>
    </modules>

    <dependencies>

        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-common-utils</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
    </dependencies>


</project>