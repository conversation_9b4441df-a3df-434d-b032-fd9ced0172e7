package com.zte.daip.manager.patcher.impl.inner.client;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.impl.inner.client.rebalance.PatchTaskEndPoint;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import okhttp3.Request;
import okio.Timeout;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchTaskEndPointImplTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/10/12</p>
 * <p>完成日期：2023/10/12</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PatchTaskEndPointImpl.class})
public class PatchTaskEndPointImplTest {
    @Autowired
    private PatchTaskEndPointImpl patchTaskEndPointImpl;

    @MockBean
    private PatchTaskEndPoint patchTaskEndPoint;

    private PatchTaskDto patchTaskDto = new PatchTaskDto();

    private long taskId = 1L;

    @Before
    public void init() {
        patchTaskDto.setTaskName("1");
    }

    @Test
    public void createPatchTask() {
        when(patchTaskEndPoint.createPatchTask(patchTaskDto)).thenReturn(new Call<Long>() {
            @Override
            public Response<Long> execute() {
                return Response.success(1L);
            }

            @Override
            public void enqueue(Callback<Long> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Long> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.createPatchTask(patchTaskDto);
    }

    @Test
    public void modifyPatchTask() {
        when(patchTaskEndPoint.modifyPatchTask(patchTaskDto)).thenReturn(new Call<Boolean>() {
            @Override
            public Response<Boolean> execute() {
                return Response.success(false);
            }

            @Override
            public void enqueue(Callback<Boolean> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Boolean> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.modifyPatchTask(patchTaskDto);
    }

    @Test
    public void removePatchTask() {
        when(patchTaskEndPoint.removePatchTask(Lists.newArrayList(taskId))).thenReturn(new Call<Boolean>() {
            @Override
            public Response<Boolean> execute() {
                return Response.success(false);
            }

            @Override
            public void enqueue(Callback<Boolean> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Boolean> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.removePatchTask(Lists.newArrayList(taskId));
    }

    @Test
    public void queryPatchTasks() {
        when(patchTaskEndPoint.queryPatchTasks()).thenReturn(new Call<List<PatchTaskDto>>() {
            @Override
            public Response<List<PatchTaskDto>> execute() {
                return Response.success(Lists.newArrayList(new PatchTaskDto()));
            }

            @Override
            public void enqueue(Callback<List<PatchTaskDto>> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<List<PatchTaskDto>> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.queryPatchTasks();
    }

    @Test
    public void queryPatchTaskByTaskId() {
        when(patchTaskEndPoint.queryPatchTaskByTaskId(taskId)).thenReturn(new Call<PatchTaskDto>() {
            @Override
            public Response<PatchTaskDto> execute() {
                return Response.success(new PatchTaskDto());
            }

            @Override
            public void enqueue(Callback<PatchTaskDto> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<PatchTaskDto> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.queryPatchTaskByTaskId(taskId);
    }

    @Test
    public void triggerPatchTask() {
        when(patchTaskEndPoint.triggerPatchTask(taskId)).thenReturn(new Call<Boolean>() {
            @Override
            public Response<Boolean> execute() {
                return Response.success(false);
            }

            @Override
            public void enqueue(Callback<Boolean> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Boolean> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.triggerPatchTask(taskId);
    }

    @Test
    public void retryPatchTask() {
        when(patchTaskEndPoint.retryPatchTask(taskId)).thenReturn(new Call<Boolean>() {
            @Override
            public Response<Boolean> execute() {
                return Response.success(false);
            }

            @Override
            public void enqueue(Callback<Boolean> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Boolean> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.retryPatchTask(taskId);
    }

    @Test
    public void pausePatchTask() {
        when(patchTaskEndPoint.pausePatchTask(taskId)).thenReturn(new Call<Boolean>() {
            @Override
            public Response<Boolean> execute() {
                return Response.success(false);
            }

            @Override
            public void enqueue(Callback<Boolean> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Boolean> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.pausePatchTask(taskId);
    }

    @Test
    public void resumePatchTask() {
        when(patchTaskEndPoint.resumePatchTask(taskId)).thenReturn(new Call<Boolean>() {
            @Override
            public Response<Boolean> execute() {
                return Response.success(false);
            }

            @Override
            public void enqueue(Callback<Boolean> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Boolean> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.resumePatchTask(taskId);
    }

    @Test
    public void queryServiceInstanceUpgrade() {
        VersionQueryRequestInfo versionQueryRequestInfo = new VersionQueryRequestInfo();
        when(patchTaskEndPoint.queryServiceInstanceUpgrade(versionQueryRequestInfo))
            .thenReturn(new Call<List<ServiceInstanceUpgrade>>() {
                @Override
                public Response<List<ServiceInstanceUpgrade>> execute() {
                    return Response.success(Lists.newArrayList());
                }

                @Override
                public void enqueue(Callback<List<ServiceInstanceUpgrade>> callback) {}

                @Override
                public boolean isExecuted() {
                    return false;
                }

                @Override
                public void cancel() {}

                @Override
                public boolean isCanceled() {
                    return false;
                }

                @Override
                public Call<List<ServiceInstanceUpgrade>> clone() {
                    return null;
                }

                @Override
                public Request request() {
                    return null;
                }

                public Timeout timeout() {
                    return null;
                }
            });
        patchTaskEndPointImpl.queryServiceInstanceUpgrade(versionQueryRequestInfo);
    }

    @Test
    public void rollbackPatchTask() {
        when(patchTaskEndPoint.rollbackPatchTask(patchTaskDto)).thenReturn(new Call<Boolean>() {
            @Override
            public Response<Boolean> execute() {
                return Response.success(false);
            }

            @Override
            public void enqueue(Callback<Boolean> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Boolean> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.rollbackPatchTask(patchTaskDto);
    }
    /* Started by AICoder, pid:s83a1962c9h56d0144920a2f709f96485051eccc */
    @Test
    public void queryNeedRestartService() throws DaipBaseException {
        // 使用Mockito框架的when方法模拟patchTaskEndPoint对象的queryNeedRestartService方法的行为
        when(patchTaskEndPoint.queryNeedRestartService(patchTaskDto)).thenReturn(new Call<List<ServiceInstance>>() {
            // 重写Call接口的execute方法，返回一个包含空列表的Response对象
            @Override
            public Response<List<ServiceInstance>> execute() {
                return Response.success(new ArrayList<>());
            }

            // 以下方法都是Call接口中的抽象方法，这里不做具体实现，因为它们在测试中不会被调用
            @Override
            public void enqueue(Callback<List<ServiceInstance>> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<List<ServiceInstance>> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });

        // 调用patchTaskEndPointImpl的queryNeedRestartService方法，传入patchTaskDto对象
        patchTaskEndPointImpl.queryNeedRestartService(patchTaskDto);
    }

    /* Ended by AICoder, pid:s83a1962c9h56d0144920a2f709f96485051eccc */

    @Test
    public void queryPatchTaskDetailByTaskIdTest() {
        when(patchTaskEndPoint.queryPatchTaskDetailByTaskId(taskId)).thenReturn(new Call<PatchTaskDto>() {
            @Override
            public Response<PatchTaskDto> execute() {
                return Response.success(new PatchTaskDto());
            }

            @Override
            public void enqueue(Callback<PatchTaskDto> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<PatchTaskDto> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchTaskEndPointImpl.queryPatchDetailTasks(taskId);
    }

    @Test
    public void copyPatchTaskTest() {
        when(patchTaskEndPoint.copyPatchTask(1)).thenReturn(new Call<Boolean>() {
            @Override
            public Response<Boolean> execute() {
                return Response.success(true);
            }

            @Override
            public void enqueue(Callback<Boolean> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Boolean> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        boolean result = patchTaskEndPointImpl.copyPatchTask(1);
        assertTrue(result);
    }

    @Test
    public void checkTaskCanRollbackTest() {
        when(patchTaskEndPoint.checkTaskCanRollback(1)).thenReturn(new Call<PatchOperateResult>() {
            @Override
            public Response<PatchOperateResult> execute() {
                 return Response.success(PatchOperateResult.success(""));
            }

            @Override
            public void enqueue(Callback<PatchOperateResult> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<PatchOperateResult> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        PatchOperateResult patchOperateResult = patchTaskEndPointImpl.checkTaskCanRollback(1);
        assertTrue(patchOperateResult.isStatus());
    }

    @Test
    public void checkTaskCanDuplicateTest() {
        when(patchTaskEndPoint.checkTaskCanDuplicate(1)).thenReturn(new Call<PatchOperateResult>() {
            @Override
            public Response<PatchOperateResult> execute() {
                return Response.success(PatchOperateResult.success(""));
            }

            @Override
            public void enqueue(Callback<PatchOperateResult> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<PatchOperateResult> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        PatchOperateResult patchOperateResult = patchTaskEndPointImpl.checkTaskCanDuplicate(1);
        assertTrue(patchOperateResult.isStatus());
    }
}