package com.zte.daip.manager.patcher.impl.inner.client;

import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Map;

import com.google.common.collect.Maps;
import okio.Timeout;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.impl.inner.client.rebalance.PatchUpdateInnerEndPoint;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;

import okhttp3.Request;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchUpdateInnerEndPointImplTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/11/20</p>
 * <p>完成日期：2023/11/20</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PatchUpdateInnerEndPointImpl.class})
public class PatchUpdateInnerEndPointImplTest {
    @Autowired
    private PatchUpdateInnerEndPointImpl patchUpdateInnerEndPointImpl;

    @MockBean
    private PatchUpdateInnerEndPoint patchUpdateInnerEndPoint;

    private String clusterId = "1";

    private String zkService = "zookeeper";

    @Test
    public void queryRollbackPoints() {
        when(patchUpdateInnerEndPoint.queryRollbackPoints(clusterId, Lists.newArrayList(zkService)))
            .thenReturn(new Call<List<ServiceInstancePatchInfo>>() {
                @Override
                public Response<List<ServiceInstancePatchInfo>> execute() {
                    return Response.success(Lists.newArrayList());
                }

                @Override
                public void enqueue(Callback<List<ServiceInstancePatchInfo>> callback) {}

                @Override
                public boolean isExecuted() {
                    return false;
                }

                @Override
                public void cancel() {}

                @Override
                public boolean isCanceled() {
                    return false;
                }

                @Override
                public Call<List<ServiceInstancePatchInfo>> clone() {
                    return null;
                }

                @Override
                public Request request() {
                    return null;
                }

                public Timeout timeout() {
                    return null;
                }
            });
        patchUpdateInnerEndPointImpl.queryRollbackPoints(clusterId, Lists.newArrayList(zkService));
    }
    /* Started by AICoder, pid:vd9e000e2el2bef1474508509004634e18f2d983 */
    @Test
    public void queryNeedUpdatePatchs() throws DaipBaseException {
        // 使用Mockito框架的when方法模拟patchUpdateInnerEndPoint对象的queryNeedUpdatePatchs方法的行为
        when(patchUpdateInnerEndPoint.queryNeedUpdatePatchs(clusterId, Lists.newArrayList(zkService)))
                .thenReturn(new Call<Map<String, List<String>>>() {
                    // 重写Call接口的execute方法，返回一个包含空映射的Response对象
                    @Override
                    public Response<Map<String, List<String>>> execute() {
                        return Response.success(Maps.newHashMap());
                    }

                    // 以下方法都是Call接口中的抽象方法，这里不做具体实现，因为它们在测试中不会被调用
                    @Override
                    public void enqueue(Callback<Map<String, List<String>>> callback) {}

                    @Override
                    public boolean isExecuted() {
                        return false;
                    }

                    @Override
                    public void cancel() {}

                    @Override
                    public boolean isCanceled() {
                        return false;
                    }

                    @Override
                    public Call<Map<String, List<String>>> clone() {
                        return null;
                    }

                    @Override
                    public Request request() {
                        return null;
                    }

                    public Timeout timeout() {
                        return null;
                    }
                });

        // 调用patchUpdateInnerEndPointImpl的queryNeedUpdatePatchs方法，传入集群ID和服务列表
        patchUpdateInnerEndPointImpl.queryNeedUpdatePatchs(clusterId, Lists.newArrayList(zkService));
    }

    /* Ended by AICoder, pid:vd9e000e2el2bef1474508509004634e18f2d983 */
}