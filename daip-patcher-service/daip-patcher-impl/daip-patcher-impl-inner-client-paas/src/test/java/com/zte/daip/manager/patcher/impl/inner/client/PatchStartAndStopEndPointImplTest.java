package com.zte.daip.manager.patcher.impl.inner.client;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.impl.inner.client.rebalance.PatchStartAndStopEndPoint;
import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateServiceDto;
import okhttp3.Request;
import okio.Timeout;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchStartAndStopEndPointImplTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/4/21</p>
 * <p>完成日期：2023/4/21</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PatchStartAndStopEndPointImpl.class})
public class PatchStartAndStopEndPointImplTest {

    @Autowired
    private PatchStartAndStopEndPointImpl patchStartAndStopEndPointImpl;

    @MockBean
    private PatchStartAndStopEndPoint patchStartAndStopEndPoint;

    @Test
    public void queryCanOperateServices() {

        when(patchStartAndStopEndPoint.queryCanOperateServices(anyString(), anyBoolean(), anyList()))
            .thenReturn(new Call<List<String>>() {
                @Override
                public Response<List<String>> execute() {
                    return Response.success(Lists.newArrayList("zookeeper"));
                }

                @Override
                public void enqueue(Callback<List<String>> callback) {}

                @Override
                public boolean isExecuted() {
                    return false;
                }

                @Override
                public void cancel() {}

                @Override
                public boolean isCanceled() {
                    return false;
                }

                @Override
                public Call<List<String>> clone() {
                    return null;
                }

                @Override
                public Request request() {
                    return null;
                }

                public Timeout timeout() {
                    return null;
                }
            });
        List<String> result = patchStartAndStopEndPointImpl.queryCanOperateServices("1000", true, Lists.newArrayList());
        Assert.assertEquals("zookeeper", result.get(0));

    }

    @Test
    public void startCluster() {
        when(patchStartAndStopEndPoint.startCluster(anyString(), anyList())).thenReturn(new Call<Void>() {
            @Override
            public Response<Void> execute() {
                return null;
            }

            @Override
            public void enqueue(Callback<Void> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Void> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchStartAndStopEndPointImpl.startCluster("1000", Lists.newArrayList());

    }

    @Test
    public void stopCluster() {
        when(patchStartAndStopEndPoint.stopCluster(anyString(), anyList())).thenReturn(new Call<Void>() {
            @Override
            public Response<Void> execute() {
                return null;
            }

            @Override
            public void enqueue(Callback<Void> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<Void> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        patchStartAndStopEndPointImpl.stopCluster("1000", Lists.newArrayList());
    }

    @Test
    public void queryOperatorProcess() {
        when(patchStartAndStopEndPoint.queryOperatorProcess(anyString())).thenReturn(new Call<String>() {
            @Override
            public Response<String> execute() {
                return Response.success("success");
            }

            @Override
            public void enqueue(Callback<String> callback) {}

            @Override
            public boolean isExecuted() {
                return false;
            }

            @Override
            public void cancel() {}

            @Override
            public boolean isCanceled() {
                return false;
            }

            @Override
            public Call<String> clone() {
                return null;
            }

            @Override
            public Request request() {
                return null;
            }

            public Timeout timeout() {
                return null;
            }
        });
        String result = patchStartAndStopEndPointImpl.queryOperatorProcess("1000");
        Assert.assertEquals("success", result);
    }
}