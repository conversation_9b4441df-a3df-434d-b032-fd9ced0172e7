package com.zte.daip.manager.patcher.impl.inner.client;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.impl.inner.client.rebalance.PatchUpdateInnerEndPoint;
import com.zte.daip.manager.patcher.inner.api.PatchUpdateInnerControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
import retrofit2.Response;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchUpdateInnerEndPointImpl implements PatchUpdateInnerControllerApi {

    @Autowired
    private PatchUpdateInnerEndPoint patchUpdateInnerEndPoint;

    @Override
    public List<ServiceInstancePatchInfo> queryRollbackPoints(String clusterId, List<String> instanceIds) {
        try {
            log.debug("query rollback points by cluster id: {} and instanceIds : {}", clusterId, instanceIds);
            Response<List<ServiceInstancePatchInfo>> response =
                patchUpdateInnerEndPoint.queryRollbackPoints(clusterId, instanceIds).execute();
            return response.body();
        } catch (IOException e) {
            log.error("query rollback points by cluster id", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public Map<String, List<String>> queryNeedUpdatePatchs(String clusterId, List<String> instanceIds) throws DaipBaseException {
        try {
            log.debug("query need update patchs by cluster id: {} and instanceIds : {}", clusterId, instanceIds);
            Response<Map<String, List<String>>> response =
                    patchUpdateInnerEndPoint.queryNeedUpdatePatchs(clusterId, instanceIds).execute();
            return response.body();
        } catch (IOException e) {
            log.error("query need update patchs by cluster id", e);
        }
        return Maps.newHashMap();
    }
}