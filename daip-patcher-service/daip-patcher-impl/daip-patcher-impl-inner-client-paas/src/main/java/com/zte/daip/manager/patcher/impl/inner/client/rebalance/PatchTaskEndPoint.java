package com.zte.daip.manager.patcher.impl.inner.client.rebalance;

import com.zte.daip.manager.common.client.paas.converter.constant.CommonClientPaasConstant;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.BodyConverter;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@ServiceHttpEndPoint(serviceName = "daip-patcher-svr", serviceVersion = "v1")
@BodyConverter(builderClassName = CommonClientPaasConstant.BUILDER_CLASS)
public interface PatchTaskEndPoint {
    @POST(value = "patch/task/create")
    Call<Long> createPatchTask(@Body PatchTaskDto patchTaskDto);

    @POST(value = "patch/task/modify")
    Call<Boolean> modifyPatchTask(@Body PatchTaskDto patchTaskDto);

    @POST(value = "patch/task/remove")
    Call<Boolean> removePatchTask(@Query("taskIds") List<Long> taskIds);

    @GET(value = "patch/tasks")
    Call<List<PatchTaskDto>> queryPatchTasks();

    @GET(value = "patch/task/detail/{taskId}")
    Call<PatchTaskDto> queryPatchTaskDetailByTaskId(@Path(value = "taskId") long taskId);

    @GET(value = "patch/task/{taskId}")
    Call<PatchTaskDto> queryPatchTaskByTaskId(@Path(value = "taskId") long taskId);

    @POST(value = "patch/task/trigger")
    Call<Boolean> triggerPatchTask(@Query("taskId") long taskId);

    @POST(value = "patch/task/retry")
    Call<Boolean> retryPatchTask(@Query("taskId") long taskId);

    @POST(value = "patch/task/pause")
    Call<Boolean> pausePatchTask(@Query("taskId") long taskId);

    @POST(value = "patch/task/copy")
    Call<Boolean> copyPatchTask(@Query("taskId") long taskId);

    @POST(value = "patch/task/checkRollback")
    Call<PatchOperateResult> checkTaskCanRollback(@Query("taskId") long taskId);

    @POST(value = "patch/task/checkDuplicate")
    Call<PatchOperateResult> checkTaskCanDuplicate(@Query("taskId") long taskId);

    @POST(value = "patch/task/resume")
    Call<Boolean> resumePatchTask(@Query("taskId") long taskId);

    @POST(value = "patch/serviceInstance/query")
    Call<List<ServiceInstanceUpgrade>> queryServiceInstanceUpgrade(@Body VersionQueryRequestInfo versionQueryRequestInfo);

    @POST(value = "patch/task/rollback")
    Call<Boolean> rollbackPatchTask(@Body PatchTaskDto patchTaskDto);

    @POST(value = "patch/restartService/query")
    Call<List<ServiceInstance>> queryNeedRestartService(@Body PatchTaskDto patchTaskDto);
}
