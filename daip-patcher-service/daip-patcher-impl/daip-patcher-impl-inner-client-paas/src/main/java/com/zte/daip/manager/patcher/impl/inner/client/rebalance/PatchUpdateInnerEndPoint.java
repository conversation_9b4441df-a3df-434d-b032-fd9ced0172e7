package com.zte.daip.manager.patcher.impl.inner.client.rebalance;

import java.util.List;
import java.util.Map;

import com.zte.daip.manager.common.client.paas.converter.constant.CommonClientPaasConstant;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.BodyConverter;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;

import retrofit2.Call;
import retrofit2.http.*;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@ServiceHttpEndPoint(serviceName = "daip-patcher-svr", serviceVersion = "v1")
@BodyConverter(builderClassName = CommonClientPaasConstant.BUILDER_CLASS)
public interface PatchUpdateInnerEndPoint {
    @GET(value = "patch/update/rollbackpoints")
    Call<List<ServiceInstancePatchInfo>> queryRollbackPoints(@Query("clusterId") String clusterId,
        @Query("instanceIds") List<String> instanceIds);

    @GET(value = "patch/update/needupdatepatchs")
    Call<Map<String, List<String>>> queryNeedUpdatePatchs(@Query("clusterId") String clusterId,
                                                             @Query("instanceIds") List<String> instanceIds);
}
