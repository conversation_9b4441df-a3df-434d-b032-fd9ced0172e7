/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchStartAndStopEndPoint.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/21
 * </p>
 * <p>
 * 完成日期：2023/4/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.inner.client.rebalance;

import com.zte.daip.manager.common.client.paas.converter.constant.CommonClientPaasConstant;
import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateServiceDto;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.BodyConverter;
import com.zte.ums.zenap.httpclient.retrofit.annotaion.ServiceHttpEndPoint;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@ServiceHttpEndPoint(serviceName = "daip-patcher-svr", serviceVersion = "v1")
@BodyConverter(builderClassName = CommonClientPaasConstant.BUILDER_CLASS)
public interface PatchStartAndStopEndPoint {

    @POST(value = "patches/cluster/services")
    Call<List<String>> queryCanOperateServices(@Query("clusterId") String clusterId, @Query("isStart") boolean isStart,
        @Body List<PatchOperateServiceDto> needOperateServiceDtos);

    @POST(value = "patches/cluster/start")
    Call<Void> startCluster(@Query("clusterId") String clusterId, @Body List<String> needOperateInstanceIds);

    @POST(value = "patches/cluster/stop")
    Call<Void> stopCluster(@Query("clusterId") String clusterId, @Body List<String> needOperateInstanceIds);

    @POST(value = "patches/cluster/progress")
    Call<String> queryOperatorProcess(@Query("clusterId") String clusterId);

}
