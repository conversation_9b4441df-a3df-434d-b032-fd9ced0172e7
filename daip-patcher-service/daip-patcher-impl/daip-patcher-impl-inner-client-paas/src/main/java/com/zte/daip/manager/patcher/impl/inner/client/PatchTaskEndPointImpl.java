package com.zte.daip.manager.patcher.impl.inner.client;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.impl.inner.client.rebalance.PatchTaskEndPoint;
import com.zte.daip.manager.patcher.inner.api.PatchTaskControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchTaskEndPointImpl implements PatchTaskControllerApi {

    @Autowired
    private PatchTaskEndPoint patchTaskEndPoint;

    @Override
    public long createPatchTask(PatchTaskDto patchTaskDto) {
        try {
            log.debug("create patch task.");
            Response<Long> response = patchTaskEndPoint.createPatchTask(patchTaskDto).execute();
            return response.body();
        } catch (IOException e) {
            log.error("create patch task exception", e);
            return -1;
        }
    }

    @Override
    public boolean modifyPatchTask(PatchTaskDto patchTaskDto) {
        try {
            log.debug("modify patch task.");
            Response<Boolean> response = patchTaskEndPoint.modifyPatchTask(patchTaskDto).execute();
            return response.body();
        } catch (IOException e) {
            log.error("modify patch task exception", e);
            return false;
        }
    }

    @Override
    public boolean removePatchTask(List<Long> taskIds) {
        try {
            log.debug("remove patch task.");
            Response<Boolean> response = patchTaskEndPoint.removePatchTask(taskIds).execute();
            return response.body();
        } catch (IOException e) {
            log.error("remove patch task exception", e);
            return false;
        }
    }

    @Override
    public List<PatchTaskDto> queryPatchTasks() {
        try {
            log.debug("start query patch tasks.");
            Response<List<PatchTaskDto>> response = patchTaskEndPoint.queryPatchTasks().execute();
            return response.body();
        } catch (IOException e) {
            log.error("query patch tasks exception", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public PatchTaskDto queryPatchDetailTasks(long taskId) {
        try {
            log.debug("start query patch task by id.");
            Response<PatchTaskDto> response = patchTaskEndPoint.queryPatchTaskDetailByTaskId(taskId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("query patch task by id exception", e);
        }
        return null;
    }

    @Override
    public PatchTaskDto queryPatchTaskByTaskId(long taskId) {
        try {
            log.debug("start query patch task by id.");
            Response<PatchTaskDto> response = patchTaskEndPoint.queryPatchTaskByTaskId(taskId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("query patch task by id exception", e);
        }
        return null;
    }

    @Override
    public boolean triggerPatchTask(long taskId) {
        try {
            log.debug("trigger patch task.");
            Response<Boolean> response = patchTaskEndPoint.triggerPatchTask(taskId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("trigger patch task exception", e);
            return false;
        }
    }

    @Override
    public boolean retryPatchTask(long taskId) {
        try {
            log.debug("retry patch task.");
            Response<Boolean> response = patchTaskEndPoint.retryPatchTask(taskId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("retry patch task exception", e);
            return false;
        }
    }

    @Override
    public boolean pausePatchTask(long taskId) {
        try {
            log.debug("pause patch task.");
            Response<Boolean> response = patchTaskEndPoint.pausePatchTask(taskId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("pause patch task exception", e);
            return false;
        }
    }

    @Override
    public boolean copyPatchTask(long taskId) {
        try {
            log.debug("copy patch task.");
            Response<Boolean> response = patchTaskEndPoint.copyPatchTask(taskId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("copy patch task exception", e);
            return false;
        }
    }

    @Override
    public PatchOperateResult checkTaskCanRollback(long taskId) {
        try {
            log.debug("check rollback patch task.");
            Response<PatchOperateResult> response = patchTaskEndPoint.checkTaskCanRollback(taskId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("check rollback patch task exception", e);
            return PatchOperateResult.fail("");
        }
    }

    @Override
    public PatchOperateResult checkTaskCanDuplicate(long taskId) {
        try {
            log.debug("check duplicate patch task.");
            Response<PatchOperateResult> response = patchTaskEndPoint.checkTaskCanDuplicate(taskId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("check duplicate patch task exception", e);
            return PatchOperateResult.fail("");
        }
    }

    @Override
    public boolean resumePatchTask(long taskId) {
        try {
            log.debug("resume patch task.");
            Response<Boolean> response = patchTaskEndPoint.resumePatchTask(taskId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("resume patch task exception", e);
            return false;
        }
    }

    @Override
    public List<ServiceInstanceUpgrade> queryServiceInstanceUpgrade(VersionQueryRequestInfo versionQueryRequestInfo) {
        try {
            log.debug("start query patch upgrade service instance.");
            Response<List<ServiceInstanceUpgrade>> response =
                patchTaskEndPoint.queryServiceInstanceUpgrade(versionQueryRequestInfo).execute();
            return response.body();
        } catch (IOException e) {
            log.error("query patch upgrade service instance exception", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public boolean rollbackPatchTask(PatchTaskDto patchTaskDto) {
        try {
            log.debug("rollback patch task.");
            Response<Boolean> response = patchTaskEndPoint.rollbackPatchTask(patchTaskDto).execute();
            return response.body();
        } catch (IOException e) {
            log.error("rollback patch task exception", e);
            return false;
        }
    }

    @Override
    public List<ServiceInstance> queryNeedRestartService(PatchTaskDto patchTaskDto) throws DaipBaseException {
        try {
            log.debug("start query need restart service instance.");
            Response<List<ServiceInstance>> response =
                    patchTaskEndPoint.queryNeedRestartService(patchTaskDto).execute();
            return response.body();
        } catch (IOException e) {
            log.error("query need restart service instance exception", e);
            throw new DaipBaseException("query need restart service exception");
        }
    }
}