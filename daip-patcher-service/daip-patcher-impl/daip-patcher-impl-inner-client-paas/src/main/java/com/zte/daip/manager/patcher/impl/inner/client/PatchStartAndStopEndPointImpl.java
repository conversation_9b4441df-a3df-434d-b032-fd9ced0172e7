/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchStartAndStopEndPointImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/21
 * </p>
 * <p>
 * 完成日期：2023/4/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.inner.client;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.impl.inner.client.rebalance.PatchStartAndStopEndPoint;
import com.zte.daip.manager.patcher.inner.api.PatchStartAndStopControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateServiceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;
import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatchStartAndStopEndPointImpl implements PatchStartAndStopControllerApi {

    @Autowired
    private PatchStartAndStopEndPoint patchStartAndStopEndPoint;

    @Override
    public List<String> queryCanOperateServices(String clusterId, boolean isStart,
        List<PatchOperateServiceDto> needOperateServiceDtos) {
        try {
            log.debug("start query can operate services.");
            Response<List<String>> response =
                patchStartAndStopEndPoint.queryCanOperateServices(clusterId, isStart, needOperateServiceDtos).execute();
            return response.body();
        } catch (IOException e) {
            log.error("query can operate services exception", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public void startCluster(String clusterId, List<String> needOperateInstanceIds) {
        try {
            log.debug("start cluster service.");
            patchStartAndStopEndPoint.startCluster(clusterId, needOperateInstanceIds).execute();
        } catch (IOException e) {
            log.error("start cluster service exception", e);
        }
    }

    @Override
    public void stopCluster(String clusterId, List<String> needOperateInstanceIds) {
        try {
            log.debug("stop cluster service.");
            patchStartAndStopEndPoint.stopCluster(clusterId, needOperateInstanceIds).execute();
        } catch (IOException e) {
            log.error("stop cluster service exception", e);
        }
    }

    @Override
    public String queryOperatorProcess(String clusterId) {
        try {
            log.debug("query operator process.");
            Response<String> response = patchStartAndStopEndPoint.queryOperatorProcess(clusterId).execute();
            return response.body();
        } catch (IOException e) {
            log.error("query operator process exception", e);
        }
        return "";
    }
}