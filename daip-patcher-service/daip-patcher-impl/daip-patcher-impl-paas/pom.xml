<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>daip-patcher-impl</artifactId>
        <groupId>com.zte.daip.manager.patcher</groupId>
        <version>deletePatch</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>daip-patcher-impl-paas</artifactId>

    <properties>
        <app.mainclazz>com.zte.daip.manager.patcher.impl.paas.PatcherApplication</app.mainclazz>

        <maven.build.timestamp.format>yyMMddHHmm</maven.build.timestamp.format>
        <paas.service.version>${product.version}.n${maven.build.timestamp}</paas.service.version>
        <ms.release_version.name>${product.id}_PATCHER_${product.version}</ms.release_version.name>
        <ms.product_version.name>${service.name}-${paas.service.version}</ms.product_version.name>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.4.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.core5</groupId>
            <artifactId>httpcore5</artifactId>
            <version>5.3.3</version>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-web-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-redis-redisson-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-kafka-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-db-jdbc-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-cache-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-security</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-interfaces</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-infrastructure-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>guru.nidi</groupId>
            <artifactId>jdepend</artifactId>
            <version>2.9.2</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-utils-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-filemanagement-client-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-filemanagement-download-provider</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-filemanagement-common-util</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-configcenter-client-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-inspection-client-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-deployer-client-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-alarm-client-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-task-worker</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-sensitive-log</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>DAIP.MIX.apk</groupId>
            <artifactId>daip-deployer-x86</artifactId>
            <version>${product.version}</version>
            <type>tar</type>
        </dependency>
        <dependency>
            <groupId>DAIP.MIX.apk</groupId>
            <artifactId>daip-deployer-arm</artifactId>
            <version>${product.version}</version>
            <type>tar</type>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-dls-register</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>paas</id>
            <properties>
                <app.mainclass>${app.mainclazz}</app.mainclass>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.gmaven</groupId>
                        <artifactId>groovy-maven-plugin</artifactId>
                        <version>2.1.1</version>
                        <executions>
                            <execution>
                                <phase>initialize</phase>
                                <goals>
                                    <goal>execute</goal>
                                </goals>
                                <configuration>
                                    <source>
                                        def chartVersion = project.properties["paas.service.version"].substring(1).replace(".", "-").replaceFirst("-", ".").replaceFirst("-", ".")
                                        project.getProperties().put("chart.version", chartVersion)
                                    </source>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>chart_tar</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <finalName>${service.name}-${chart.version}</finalName>
                                    <appendAssemblyId>false</appendAssemblyId>
                                    <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                                    <descriptors>
                                        <descriptor>src/assembly/assembly_chart_tar.xml</descriptor>
                                    </descriptors>
                                    <tarLongFileMode>posix</tarLongFileMode>
                                    <overrideUid>${non.root.uid}</overrideUid>
                                    <overrideGid>${non.root.uid}</overrideGid>
                                </configuration>
                            </execution>
                            <execution>
                                <id>inner_tar</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <finalName>${svr.microservice.name}</finalName>
                                    <appendAssemblyId>false</appendAssemblyId>
                                    <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                                    <descriptors>
                                        <descriptor>src/assembly/assembly_inner_tar.xml</descriptor>
                                    </descriptors>
                                    <overrideUid>${non.root.uid}</overrideUid>
                                    <overrideGid>${non.root.uid}</overrideGid>
                                </configuration>
                            </execution>
                            <execution>
                                <id>combine</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <finalName>${svr.microservice.name}</finalName>
                                    <appendAssemblyId>false</appendAssemblyId>
                                    <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                                    <descriptors>
                                        <descriptor>src/assembly/assembly_combine.xml</descriptor>
                                    </descriptors>
                                </configuration>
                            </execution>
                            <execution>
                                <id>outer_tar</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <finalName>${ms.product_version.name}</finalName>
                                    <appendAssemblyId>false</appendAssemblyId>
                                    <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                                    <descriptors>
                                        <descriptor>src/assembly/assembly_outer_tar.xml</descriptor>
                                    </descriptors>
                                </configuration>
                            </execution>
                            <execution>
                                <id>inner_lib_tar</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <finalName>jdepend</finalName>
                                    <appendAssemblyId>false</appendAssemblyId>
                                    <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                                    <descriptors>
                                        <descriptor>src/assembly/assembly_inner_lib_tar.xml
                                        </descriptor>
                                    </descriptors>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>cpaas</id>
            <properties>
                <kafka.bootstrap.servers.url>${kafka_brokers}:${kafka_port}
                </kafka.bootstrap.servers.url>

                <another.msb.network.plane.register></another.msb.network.plane.register>
                <k8s.networks></k8s.networks>
                <svr.microservice.name.msbname>daip-patcher-svr</svr.microservice.name.msbname>
                <paas.tenants>zenap</paas.tenants>
                <default.msb.network.plane.type>net_api</default.msb.network.plane.type>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>release_tar_gz</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <finalName>${ms.release_version.name}</finalName>
                                    <appendAssemblyId>false</appendAssemblyId>
                                    <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                                    <outputDirectory>${project.build.directory}/cpaas/
                                    </outputDirectory>
                                    <descriptors>
                                        <descriptor>src/assembly/cpaas/assembly_release_tar.xml
                                        </descriptor>
                                    </descriptors>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>tcf</id>
            <properties>
                <kafka.bootstrap.servers.url>'[daip-kafka-default]:19092'</kafka.bootstrap.servers.url>
                <another.msb.network.plane.register>{"serviceName": "daip-patcher-svr-vm","version":
                    "v1","url": "/api/daip-patcher-svr/v1","protocol": "REST","path":
                    "/api/daip-patcher-svr/v1","port": "56210","visualRange":
                    "1","network_plane_type": "zdh-net"},
                </another.msb.network.plane.register>
                <k8s.networks>,"k8s.v1.cni.cncf.io/networks":
                    "[{\"name\":\"net-api\",\"namespace\":\"default\",\"interface\":\"eth1\"},{\"name\":\"lan\",\"interface\":\"eth3\"}]"
                </k8s.networks>
                <net.api.eth>eth2</net.api.eth>
                <svr.microservice.name.msbname>daip-patcher-svr-vm</svr.microservice.name.msbname>
                <paas.tenants>${NAMESPACE}</paas.tenants>
                <default.msb.network.plane.type>default</default.msb.network.plane.type>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>release_tar_gz</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <configuration>
                                    <finalName>${ms.release_version.name}</finalName>
                                    <appendAssemblyId>false</appendAssemblyId>
                                    <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                                    <outputDirectory>${project.build.directory}/tcf/
                                    </outputDirectory>
                                    <descriptors>
                                        <descriptor>src/assembly/tcf/assembly_release_tar.xml
                                        </descriptor>
                                    </descriptors>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**</exclude>
                </excludes>
            </resource>
        </resources>
    </build>
</project>