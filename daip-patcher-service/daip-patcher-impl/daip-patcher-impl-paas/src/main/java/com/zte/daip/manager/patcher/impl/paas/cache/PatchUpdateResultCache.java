package com.zte.daip.manager.patcher.impl.paas.cache;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.cache.QueueCacheReader;
import com.zte.daip.manager.common.cache.QueueCacheWriter;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateResultCacheApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zte.daip.manager.patcher.infrastructure.constants.PatchUpdateConstants.*;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class PatchUpdateResultCache implements PatchUpdateResultCacheApi {
    @Autowired
    private QueueCacheReader<String, String> successUpdateQueueCacheReader;

    @Autowired
    private QueueCacheWriter<String, String> successUpdateQueueCacheWriter;

    @Autowired
    private QueueCacheReader<String, String> failedUpdateQueueCacheReader;

    @Autowired
    private QueueCacheWriter<String, String> failedUpdateQueueCacheWriter;

    public void updateSuccessPatchResult(String clusterId, String patchResultJson) {
        String keyName = getSuccessKeyName(clusterId);
        successUpdateQueueCacheWriter.offer(keyName, patchResultJson);
    }

    public void updateFailedPatchResult(String clusterId, String patchUpdateResultJson) {
        String keyName = getFailedKeyName(clusterId);
        failedUpdateQueueCacheWriter.offer(keyName, patchUpdateResultJson);
    }

    public List<String> pollSuccessPatchUpdateResult(String clusterId) {
        List<String> patchResults = Lists.newArrayList();
        String keyName = getSuccessKeyName(clusterId);
        while (successUpdateQueueCacheReader.size(keyName) > 0) {
            patchResults.add(successUpdateQueueCacheReader.poll(keyName));
        }
        return patchResults;
    }

    public int querySuccessPatchUpdateResultSize(String clusterId) {
        String keyName = getSuccessKeyName(clusterId);
        return successUpdateQueueCacheReader.size(keyName);
    }

    public int queryFailedPatchUpdateResultSize(String clusterId) {
        String keyName = getFailedKeyName(clusterId);
        return failedUpdateQueueCacheReader.size(keyName);
    }

    public List<String> pollFailedPatchUpdateResult(String clusterId) {
        List<String> patchUpdateResults = Lists.newArrayList();
        String keyName = getFailedKeyName(clusterId);
        while (failedUpdateQueueCacheReader.size(keyName) > 0) {
            patchUpdateResults.add(failedUpdateQueueCacheReader.poll(keyName));
        }
        return patchUpdateResults;
    }

    public void clearCache(String clusterId) {
        successUpdateQueueCacheWriter.delete(getSuccessKeyName(clusterId));
        failedUpdateQueueCacheWriter.delete(getFailedKeyName(clusterId));
    }

    private String getSuccessKeyName(String clusterId) {
        return String.format(PATCH_UPDATE_SUCCESS_RESULT_CACHE_KEYNAME, clusterId);
    }

    private String getFailedKeyName(String clusterId) {
        return String.format(PATCH_UPDATE_FAILED_RESULT_CACHE_KEYNAME, clusterId);
    }
}