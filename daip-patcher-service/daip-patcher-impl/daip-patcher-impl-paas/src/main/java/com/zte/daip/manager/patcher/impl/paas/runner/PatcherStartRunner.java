/* Started by AICoder, pid:85083g0054s9e66146b909ede0b3e85d30a6d009 */
package com.zte.daip.manager.patcher.impl.paas.runner;

import com.zte.daip.manager.patcher.domain.taskmodel.service.TaskModelLoadService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PatcherStartRunner implements ApplicationRunner, DisposableBean {

    private static final String PATCH_START = "PATCH_START_SIGNAL";
    private static final String PATCH_SERVICE_NUMBER = "PATCH_SERVICE_NUMBER";

    private final TaskModelLoadService taskModelLoadService;
    private final RedissonClient redissonClient;

    @Autowired
    public PatcherStartRunner(TaskModelLoadService taskModelLoadService, RedissonClient redissonClient) {
        this.taskModelLoadService = taskModelLoadService;
        this.redissonClient = redissonClient;
    }

    @Async
    @Override
    @Order(Integer.MAX_VALUE)
    public void run(ApplicationArguments args) {
        RBucket<Object> bucket = redissonClient.getBucket(PATCH_START);
        if (bucket.trySet(PATCH_START)) {
            log.info("start to load patcher taskModel...");
            taskModelLoadService.loadTaskModel();
        }
        RAtomicLong atomicLong = redissonClient.getAtomicLong(PATCH_SERVICE_NUMBER);
        atomicLong.incrementAndGet();
    }

    @Override
    @Async
    @Order(1)
    public void destroy() {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(PATCH_SERVICE_NUMBER);
        if (atomicLong.decrementAndGet() == 0L) {
            atomicLong.delete();
            RBucket<String> bucket = redissonClient.getBucket(PATCH_START);
            bucket.delete();
        }
    }
}

/* Ended by AICoder, pid:85083g0054s9e66146b909ede0b3e85d30a6d009 */