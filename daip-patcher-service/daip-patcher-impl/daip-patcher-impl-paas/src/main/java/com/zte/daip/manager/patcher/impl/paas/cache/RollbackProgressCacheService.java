/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: RollbackProgressCacheService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/22
 * </p>
 * <p>
 * 完成日期：2023/3/22
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.cache;

import com.zte.daip.manager.patcher.domain.rollback.entity.HostRollbackProgress;
import com.zte.daip.manager.patcher.domain.rollback.progress.RollbackProgressApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class RollbackProgressCacheService implements RollbackProgressApi {

    @Autowired
    private PatchRollbackProgressReader patchRollbackProgressReader;

    @Autowired
    private PatchRollbackProgressWriter patchRollbackProgressWriter;

    @Autowired
    private PatchRollbackServiceReader patchRollbackServiceReader;

    @Autowired
    private PatchRollbackServiceWriter patchRollbackServiceWriter;

    @Override
    public void initAndUpdateRollbackProgress(String clusterId, String service, String ipAddress,
        HostRollbackProgress hostRollbackProgress) {
        log.info("initAndUpdateRollbackProgress:{}", hostRollbackProgress);
        patchRollbackProgressWriter.put(generateProgressKey(clusterId, service), ipAddress, hostRollbackProgress);
    }

    @Override
    public void updateTimeoutRollbackProgress(String key, String ipAddress, HostRollbackProgress hostRollbackProgress) {
        patchRollbackProgressWriter.put(key, ipAddress, hostRollbackProgress);
    }

    @Override
    public void deleteRollbackProgress(String clusterId, String service) {
        patchRollbackProgressWriter.delete(generateProgressKey(clusterId, service));
    }

    @Override
    public void initRollbackServices(String clusterId, List<String> services) {
        log.info("initRollbackServices:{}", services);
        patchRollbackServiceWriter.delete(generateServiceKey(clusterId));
        patchRollbackServiceWriter.put(generateServiceKey(clusterId), services);
    }

    @Override
    public List<String> queryRollbackServices(String clusterId) {
        return patchRollbackServiceReader.get(generateServiceKey(clusterId));
    }

    @Override
    public List<HostRollbackProgress> queryProgress(String clusterId, String service) {
        return patchRollbackProgressReader.getAllValue(generateProgressKey(clusterId, service));
    }

    @Override
    public List<HostRollbackProgress> queryProgress(String key) {
        return patchRollbackProgressReader.getAllValue(key);
    }

    private String generateProgressKey(String clusterId, String service) {
        return "ROLLBACK_PROGRESS:" + clusterId + service;
    }

    private String generateServiceKey(String clusterId) {
        return "ROLLBACK_SERVICE:" + clusterId;
    }
}