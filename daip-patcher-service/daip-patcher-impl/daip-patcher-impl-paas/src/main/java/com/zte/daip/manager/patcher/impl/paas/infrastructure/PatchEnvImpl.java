/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchRnvPaasImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/10
 * </p>
 * <p>
 * 完成日期：2021/3/10
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import java.io.File;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.paas.url.AccessUrl;
import com.zte.daip.manager.patcher.infrastructure.PatchEnvApi;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class PatchEnvImpl implements PatchEnvApi {

    private static final String REPOSITORY_HOME = "/data1/version";

    @Autowired
    private AccessUrl accessUrl;

    @Value("${daip.patcher.msbname:daip-patcher-svr}")
    private String microserviceName;

    @Value("${dexcloud.base.microservice.name:daip-patcher-svr}")
    private String serviceName;

    @Value("${server.servlet.port:56210}")
    private String port;

    @Value("${server.servlet.context-path:/api/daip-patcher-svr/v1}")
    private String baseUrl;

    @Value("${dexcloud.base.microservice.version:v1}")
    private String version;

    @Override
    public String getPatchUploadHomeEnv() {
        return File.separator + "home" + File.separator + "version" + File.separator + "patch";
    }

    @Override
    public String getRepositoryHomeEnv() {
        String repositoryHome = System.getenv("daip-patcher-env");
        return StringUtils.isNotEmpty(repositoryHome) ? repositoryHome : REPOSITORY_HOME;
    }

    @Override
    public String getPatchUrl() throws DaipBaseException {
        try {
            return String.format("http://%s.%s:%s%s", serviceName, System.getenv("OPENPALETTE_NAMESPACE"), port, baseUrl);
        } catch (Exception e) {
            throw new DaipBaseException("get patcher url exception:", e);
        }

    }

}