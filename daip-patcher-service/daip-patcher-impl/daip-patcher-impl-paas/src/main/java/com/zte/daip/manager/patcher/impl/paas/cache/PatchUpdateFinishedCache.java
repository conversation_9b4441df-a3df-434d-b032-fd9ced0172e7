package com.zte.daip.manager.patcher.impl.paas.cache;

import com.zte.daip.manager.patcher.infrastructure.PatchUpdateFinishedCacheApi;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.zte.daip.manager.patcher.infrastructure.constants.PatchUpdateConstants.*;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class PatchUpdateFinishedCache implements PatchUpdateFinishedCacheApi {
    @Autowired
    private PatchCacheReader patchCacheReader;

    @Autowired
    private PatchCacheWriter patchCacheWriter;

    public void updateCache(String patchKey, String finishedValue) {
        patchCacheWriter.putTtlKey(getKeyName(patchKey), PATCH_UPDATE_FINISHED_FIELD_NAME, finishedValue,
            DEFAULT_CACHE_EXPIRE_TIME);
    }

    public String queryCacheResult(String patchKey) {
        String patchFinishedCache = patchCacheReader.getTtlKey(getKeyName(patchKey), PATCH_UPDATE_FINISHED_FIELD_NAME);
        return StringUtils.isNotEmpty(patchFinishedCache) ? patchFinishedCache : null;
    }

    public void clearCache(String patchKey) {
        patchCacheWriter.removeByPrefix(getKeyName(patchKey));
    }

    private String getKeyName(String patchKey) {
        return String.format(PATCH_UPDATE_FINISHED_CACHE_KEYNAME, patchKey);
    }
}