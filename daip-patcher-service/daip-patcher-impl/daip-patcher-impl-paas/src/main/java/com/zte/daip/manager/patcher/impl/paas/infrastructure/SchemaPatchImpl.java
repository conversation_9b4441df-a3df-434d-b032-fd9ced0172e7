package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import static com.zte.daip.communication.bean.MessageType.BROADCAST;

import java.io.File;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.daip.communication.bean.RequestMessageBody;
import com.zte.daip.communication.producer.EventPublisher;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchRegisterRequest;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchUpdateRequest;
import com.zte.daip.manager.patcher.api.schema.UpdateSchemaPatchFile;
import com.zte.daip.manager.patcher.application.bean.PatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.impl.paas.utils.RestClient;
import com.zte.daip.manager.patcher.infrastructure.po.*;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchTaskInfoRepository;
import com.zte.daip.manager.patcher.infrastructure.repository.SchemaRegisterRepoistory;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchCategoryEnum;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Slf4j
@Service
public class SchemaPatchImpl implements SchemaPatchApi {

    @Autowired
    private RestClient restClient;

    @Autowired
    private SchemaRegisterRepoistory schemaRegisterRepoistory;

    @Autowired
    private PatchTaskInfoRepository patchTaskInfoRepository;

    @Autowired
    private EventPublisher eventPublisher;

    private static final String REPOSITORY_HOME = File.separator + "data1" + File.separator + "version";
    private static final String PATCH_INSTALL_HOME =
        File.separator + "home" + File.separator + "Feagle" + File.separator + "daip-patcher-svr";

    public String queryPatchPath(String serviceName, String patchName) {
        return getRepositoryHome() + File.separator + "patch" + File.separator + serviceName + File.separator
            + patchName + File.separator + patchName + ".zip";
    }

    @Override
    public String queryPatchHome(String serviceName, String version) {
        return getInstallPath() + File.separator + "works" + File.separator + "schema" + File.separator + serviceName
            + "_" + version;
    }

    public String getRepositoryHome() {
        String repositoryHome = System.getenv("daip-patcher-env");
        return StringUtils.isNotEmpty(repositoryHome) ? repositoryHome : REPOSITORY_HOME;
    }

    public String getInstallPath() {
        String installHome = System.getenv("INSTALL_PATH");
        return StringUtils.isNotEmpty(installHome) ? installHome : PATCH_INSTALL_HOME;
    }

    public String queryTargetPath(String serviceName, String version) {
        return getInstallPath() + File.separator + "works" + File.separator + "schema" + File.separator + serviceName
            + "_" + version + File.separator;
    }

    @Override
    public List<SchemaPatchRegisterRequest> querySchemaRegisterInfos() {
        List<PatchRegisterPo> allPatchRegisterPos = schemaRegisterRepoistory.findAll();
        List<SchemaPatchRegisterRequest> schemaPatchRegisterRequests = Lists.newArrayList();
        for (PatchRegisterPo patchRegisterPo : allPatchRegisterPos) {
            PatchRegisterKey registerPoId = patchRegisterPo.getId();
            List<UpdateSchemaPatchFile> registerSchemaPatchFiles =
                JSON.parseArray(patchRegisterPo.getSchemaFiles(), UpdateSchemaPatchFile.class);
            SchemaPatchRegisterRequest schemaPatchRegisterRequest =
                new SchemaPatchRegisterRequest(registerPoId.getUrl(), registerPoId.getServiceName(),
                    patchRegisterPo.getPort(), registerSchemaPatchFiles);
            schemaPatchRegisterRequests.add(schemaPatchRegisterRequest);
        }
        return schemaPatchRegisterRequests;
    }

    @Override
    public void saveSchemaRegisterInfos(SchemaPatchRegisterRequest schemaPatchRegisterRequest) {
        PatchRegisterPo patchRegisterPo = getPatchRegisterPo(schemaPatchRegisterRequest);
        schemaRegisterRepoistory.saveAndFlush(patchRegisterPo);
    }

    private PatchRegisterPo getPatchRegisterPo(SchemaPatchRegisterRequest schemaPatchRegisterRequest) {
        PatchRegisterPo patchRegisterPo = new PatchRegisterPo();
        PatchRegisterKey registerPoId =
            new PatchRegisterKey(schemaPatchRegisterRequest.getServiceName(), schemaPatchRegisterRequest.getUrl());
        patchRegisterPo.setId(registerPoId);
        patchRegisterPo.setPort(schemaPatchRegisterRequest.getPort());
        patchRegisterPo.setSchemaFiles(JSON.toJSONString(schemaPatchRegisterRequest.getRegisterSchemaPatchFiles()));
        return patchRegisterPo;
    }

    @Override
    public PatchOperateResult notifyToThirdService(SchemaPatchRegisterRequest schemaPatchRegisterRequest,
        SchemaPatchUpdateRequest schemaPatchUpdateRequest) {
        String url = String.format("http://%s.%s:%s/%s", schemaPatchRegisterRequest.getServiceName(),
            System.getenv("OPENPALETTE_NAMESPACE"), schemaPatchRegisterRequest.getPort(),
            schemaPatchRegisterRequest.getUrl());
        return restClient.post(url, new HashMap<>(), schemaPatchUpdateRequest, PatchOperateResult.class);
    }

    @Override
    public void notifyThirdServiceRegister() {
        RequestMessageBody<List<PatchUpdateInfo>> requestMessageBody = new RequestMessageBody<>();
        requestMessageBody.setType(BROADCAST.getType());
        eventPublisher.send("registerSchemaPatch", requestMessageBody);
    }

    @Override
    public void saveUpdateSchemaPatchInfos(long updateTaskId, List<PatchTaskInfo> patchTaskInfos) {
        PatchTaskInfoPo patchTaskInfoPo = getPatchTaskInfoPo(updateTaskId, patchTaskInfos);
        patchTaskInfoRepository.saveAndFlush(patchTaskInfoPo);
    }

    private PatchTaskInfoPo getPatchTaskInfoPo(long updateTaskId, List<PatchTaskInfo> patchTaskInfos) {
        PatchTaskInfoPo patchTaskInfoPo = new PatchTaskInfoPo();
        PatchTaskInfoKey patchTaskInfoKey = new PatchTaskInfoKey();
        patchTaskInfoKey.setUpdateTaskId(updateTaskId);
        patchTaskInfoKey.setRollbackTaskId(0L);
        patchTaskInfoPo.setId(patchTaskInfoKey);
        patchTaskInfoPo.setPatchInfos(JSON.toJSONString(patchTaskInfos));
        return patchTaskInfoPo;
    }

    @Override
    public void saveRollbackSchemaPatchInfos(long updateTaskId, long rollbackTaskId) {
        patchTaskInfoRepository.updateByUpdateTaskId(updateTaskId, rollbackTaskId);
    }

    @Override
    public List<PatchTaskInfo> queryByUpdateTaskId(long update_task_id) {
        PatchTaskInfoPo patchTaskInfoPo = patchTaskInfoRepository.findByUpdateTaskId(update_task_id);
        if (patchTaskInfoPo == null) {
            return Lists.newArrayList();
        }
        String patchInfos = patchTaskInfoPo.getPatchInfos();
        return JSON.parseArray(patchInfos, PatchTaskInfo.class);
    }

    @Override
    public List<PatchTaskInfo> queryByRollbackTaskId(long rollback_task_id) {
        PatchTaskInfoPo patchTaskInfoPo = patchTaskInfoRepository.findByRollbackTaskId(rollback_task_id);
        if (patchTaskInfoPo == null) {
            return Lists.newArrayList();
        }
        String patchInfos = patchTaskInfoPo.getPatchInfos();
        return JSON.parseArray(patchInfos, PatchTaskInfo.class);
    }

    @Override
    public void removeSchemaPatchTaskInfos(List<PatchTaskPo> patchTaskPos) {
        for (PatchTaskPo patchTaskPo : patchTaskPos) {
            if (patchTaskPo.getTaskType() == PatchTaskTypeEnum.ROLLBACK.getTaskType()
                && StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.SCHEMA.getPatchCategory())) {
                removeRollbackPatchTaskInfo(patchTaskPo);
            } else if (patchTaskPo.getTaskType() == PatchTaskTypeEnum.UPDATE.getTaskType()
                && StringUtils.equals(patchTaskPo.getPatchCategory(), PatchCategoryEnum.SCHEMA.getPatchCategory())) {
                removeUpdatePatchTaskInfo(patchTaskPo);
            }
        }
    }

    @Override
    public void removeAllSchemaPatchTaskInfos() {
        patchTaskInfoRepository.deleteAll();
    }

    private void removeRollbackPatchTaskInfo(PatchTaskPo patchTaskPo) {
        if (patchTaskPo.getRelationTaskId() != 0L) {
            PatchTaskInfoPo updateTask = patchTaskInfoRepository.findByUpdateTaskId(patchTaskPo.getRelationTaskId());
            if (updateTask != null) {
                patchTaskInfoRepository.modifyRollbackTaskIdToZero(Lists.newArrayList(patchTaskPo.getTaskId()));
                return;
            }
        }
        patchTaskInfoRepository.deleteRollbackTaskId(Lists.newArrayList(patchTaskPo.getTaskId()));
    }

    private void removeUpdatePatchTaskInfo(PatchTaskPo patchTaskPo) {
        if (patchTaskPo.getRelationTaskId() != 0L) {
            PatchTaskInfoPo rollbackTask =
                patchTaskInfoRepository.findByRollbackTaskId(patchTaskPo.getRelationTaskId());
            if (rollbackTask != null) {
                patchTaskInfoRepository.modifyUpdateTaskIdToZero(Lists.newArrayList(patchTaskPo.getTaskId()));
                return;
            }
        }
        patchTaskInfoRepository.deleteUpdateTaskId(Lists.newArrayList(patchTaskPo.getTaskId()));
    }

    @Override
    public List<String> queryPatchInfosByService(String serviceName, String version) {
        List<String> allPatchTaskNames = Lists.newArrayList();
        List<PatchTaskInfoPo> patchTaskInfoPos = patchTaskInfoRepository.findAll();
        if (CollectionUtils.isNotEmpty(patchTaskInfoPos)) {
            for (PatchTaskInfoPo patchTaskInfoPo : patchTaskInfoPos) {
                List<PatchTaskInfo> patchTaskInfos =
                    JSON.parseArray(patchTaskInfoPo.getPatchInfos(), PatchTaskInfo.class);
                patchTaskInfos.stream()
                    .filter(patchTaskInfo -> StringUtils.equals(patchTaskInfo.getServiceName(), serviceName)
                        && StringUtils.equals(patchTaskInfo.getVersion(), version))
                    .forEach(patchTaskInfo -> allPatchTaskNames.addAll(patchTaskInfo.getUpdatePatchNames()));
            }
        }

        return allPatchTaskNames;
    }
}