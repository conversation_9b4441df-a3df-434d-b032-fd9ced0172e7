/**
 * <p>
 * <owner>00139549</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: RestClient.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/8/9
 * </p>
 * <p>
 * 完成日期：2023/8/9
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.utils;

import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.rest.RestTemplateProvider;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class RestClient {

    @Value("${daip.rest.connectTimeOut:5000}")
    private int connectTimeOut;

    @Autowired
    private RestTemplate restTemplate;

    public <T> T post(String url, Map<String, Object> params, Object requestData, Class<T> responseType) {
        url = url + getParamStr(params);
        ResponseEntity<T> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
            new HttpEntity<>(JSON.toJSON(requestData), getRequestHeaders()), responseType);
        return responseEntity.getBody();
    }

    public <T> T post(String url, Object requestData, Class<T> responseType, int readTimeOut) throws DaipBaseException {
        RestTemplate template = RestTemplateProvider.newRestTemplate(connectTimeOut, readTimeOut);

        ResponseEntity<T> responseEntity = template.exchange(url, HttpMethod.POST,
            new HttpEntity<>(JSON.toJSON(requestData), getRequestHeaders()), responseType);

        return responseEntity.getBody();
    }

    public <T> T postForParameterizedTypeReference(String url, Map<String, Object> params, Object requestData,
        ParameterizedTypeReference<T> responseType) {
        url = url + getParamStr(params);
        ResponseEntity<T> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
            new HttpEntity<>(JSON.toJSON(requestData), getRequestHeaders()), responseType);
        return responseEntity.getBody();
    }

    private HttpHeaders getRequestHeaders() {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        return requestHeaders;
    }

    private String getParamStr(Map<String, Object> params) {
        return MapUtils.isEmpty(params) ? "" : "?"
            + params.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining("&"));
    }
}