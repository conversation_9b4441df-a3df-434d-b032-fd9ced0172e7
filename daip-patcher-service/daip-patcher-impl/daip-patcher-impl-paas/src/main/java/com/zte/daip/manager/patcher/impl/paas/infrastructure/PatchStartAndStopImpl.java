/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchStartAndStopImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/21
 * </p>
 * <p>
 * 完成日期：2023/4/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.configcenter.api.ServiceConfigControllerApi;
import com.zte.daip.manager.common.configcenter.bean.DeploymentServiceInstance;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.api.StartAndStopClusterControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.operation.ClusterOperationInfoBean;
import com.zte.daip.manager.common.deployer.bean.service.ServiceInstanceBean;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.infrastructure.PatchStartAndStopApi;
import com.zte.daip.manager.patcher.infrastructure.ServiceModelInfoCacheApi;
import com.zte.daip.manager.patcher.infrastructure.bean.ServiceModelBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class PatchStartAndStopImpl implements PatchStartAndStopApi {

    @Autowired
    private ServiceConfigControllerApi serviceConfigControllerApi;
    @Autowired
    private ServiceModelInfoCacheApi serviceModelInfoCacheApi;
    @Autowired
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;
    @Autowired
    private StartAndStopClusterControllerApi startAndStopClusterControllerApi;

    @Override
    public void stopServiceByCluster(String clusterId, List<String> needOperateInstanceIds) throws DaipBaseException {
        try {
            startAndStopClusterControllerApi.stopCluster(clusterId, needOperateInstanceIds);
        } catch (Exception e) {
            throw new DaipBaseException("stop Service By Cluster exception:", e);
        }
    }

    @Override
    public void startServiceByCluster(String clusterId, List<String> needOperateInstanceIds) throws DaipBaseException {
        try {
            startAndStopClusterControllerApi.startCluster(clusterId, needOperateInstanceIds);
        } catch (Exception e) {
            throw new DaipBaseException("start Service By Cluster exception:", e);
        }
    }

    @Override
    public String queryOperateProgress(String clusterId) throws DaipBaseException {
        try {
            ClusterOperationInfoBean clusterOperationInfoBean =
                startAndStopClusterControllerApi.queryClusterOperatorProcess(clusterId);
            return JSON.toJSONString(clusterOperationInfoBean);
        } catch (Exception e) {
            throw new DaipBaseException("queryClusterOperatorProcess exception:", e);
        }
    }

    @Override
    public List<String> queryInstancesByServiceName(String clusterId, String serviceName) throws DaipBaseException {

        try {
            ServiceModelBean serviceModelBean =
                serviceModelInfoCacheApi.queryByClusterIdAndServiceName(clusterId, serviceName);
            List<String> serviceInstanceIds = Lists.newArrayList();
            if (serviceModelBean != null) {
                List<String> instances = deploymentInstanceServiceControllerApi
                    .queryByClusterIdAndServiceId(clusterId, serviceModelBean.getServiceId()).stream()
                    .map(deploymentServiceInstance -> deploymentServiceInstance.getServiceInstanceId())
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(instances)) {
                    serviceInstanceIds.addAll(instances);
                }
            }
            return serviceInstanceIds;
        } catch (Exception e) {
            throw new DaipBaseException("queryInstancesByServiceName exception:", e);
        }

    }

    @Override
    public List<String> queryDependInstances(String clusterId, String instanceId) {
        List<DeploymentServiceInstance> deploymentServiceInstances =
            serviceConfigControllerApi.queryBeDependence(clusterId, instanceId);
        return deploymentServiceInstances.stream().map(DeploymentServiceInstance::getServiceInstanceId)
            .collect(Collectors.toList());
    }

    @Override
    public List<String> queryCanOperateInstances(String clusterId, boolean isStart) throws DaipBaseException {
        try {
            List<ServiceInstanceBean> serviceInstanceBeans =
                startAndStopClusterControllerApi.queryInstancesCanOperate(clusterId, isStart,false);
            return serviceInstanceBeans.stream().map(ServiceInstanceBean::getServiceInstanceId)
                .collect(Collectors.toList());
        } catch (Exception e) {
            throw new DaipBaseException("queryCanOperateInstances exception:", e);
        }

    }
}