package com.zte.daip.manager.patcher.impl.paas.cache;

import com.zte.daip.manager.patcher.infrastructure.PatchUpdateProgressCacheApi;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.zte.daip.manager.patcher.infrastructure.constants.PatchUpdateConstants.*;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class PatchUpdateProgressCache implements PatchUpdateProgressCacheApi {
    @Autowired
    private PatchCacheReader patchCacheReader;

    @Autowired
    private PatchCacheWriter patchCacheWriter;

    public String queryCacheResult(String clusterId) {
        String patchUpdateCache = patchCacheReader.getTtlKey(getKeyName(clusterId), PATCH_UPDATE_PROGRESS_FIELD_NAME);
        return StringUtils.isNotEmpty(patchUpdateCache) ? patchUpdateCache : null;
    }

    public void updateCache(String clusterId, String patchUpdateCacheJson) {
        patchCacheWriter.putTtlKey(getKeyName(clusterId), PATCH_UPDATE_PROGRESS_FIELD_NAME, patchUpdateCacheJson,
            DEFAULT_CACHE_EXPIRE_TIME);
    }

    public void clearCache(String clusterId) {
        patchCacheWriter.removeByPrefix(getKeyName(clusterId));
    }

    private String getKeyName(String clusterId) {
        return String.format(PATCH_UPDATE_PROGRESS_CACHE_KEYNAME, clusterId);
    }
}