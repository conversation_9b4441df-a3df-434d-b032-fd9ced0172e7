/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UnzipFileImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/17
 * </p>
 * <p>
 * 完成日期：2021/3/17
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.common.utils.shell.ShellExecutorEx;
import com.zte.daip.manager.common.utils.shell.ShellResult;
import com.zte.daip.manager.common.utils.util.ZipUtil;
import com.zte.daip.manager.patcher.infrastructure.UnzipFileApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.util.zip.ZipFile;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class UnzipFileImpl implements UnzipFileApi {

    @Override
    public void unzipFile(File zipFile, String targetDir) {
        try {
            ZipUtil.unzip(zipFile.getAbsolutePath(), targetDir);
        } catch (IOException | DaipBaseException e) {
            log.error("unzip " + zipFile.getName() + " fail,result:", e);
        }
    }

    @Override
    public void unzipFile(File zipFile, String destFileStr, String destDir) throws IOException {
        try (ZipFile zf = new ZipFile(zipFile)) {
            File tempDir = FilePathCleaner.newFile(destDir);
            if (tempDir != null) {
                log.info("mkdir:{} result:{}", tempDir.getName(), tempDir.mkdirs());
            }
            File destFile = FilePathCleaner.newFile(destDir, zipFile.getName());
            FileUtils.copyFile(zipFile, destFile);
            InputStream in = zf.getInputStream(zf.getEntry(destFileStr));
            File patchConfig = FilePathCleaner.newFile(destDir + "/" + destFileStr);
            inputstreamToFile(in, patchConfig);
        } catch (IOException e) {
            log.error("unzip file {} failed.", zipFile.getName());
        }
    }

    private void inputstreamToFile(InputStream ins, File file) throws IOException {
        try (OutputStream outputStream = Files.newOutputStream(file.toPath())) {
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            log.error("inputstreamToFile exception,", e);
        } finally {
            ins.close();
        }
    }
}