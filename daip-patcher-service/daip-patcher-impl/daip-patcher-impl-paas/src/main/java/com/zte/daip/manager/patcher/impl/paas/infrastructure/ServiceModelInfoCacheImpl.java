/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: ServiceModelInfoCacheImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/21
 * </p>
 * <p>
 * 完成日期：2023/4/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.infrastructure.ServiceModelInfoCacheApi;
import com.zte.daip.manager.patcher.infrastructure.bean.ServiceModelBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class ServiceModelInfoCacheImpl implements ServiceModelInfoCacheApi {

    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    private LoadingCache<String, List<ServiceModel>> serviceModelCache = CacheBuilder.newBuilder().maximumSize(10000)
        .refreshAfterWrite(30, TimeUnit.SECONDS).build(new CacheLoader<String, List<ServiceModel>>() {
            @Override
            public List<ServiceModel> load(String clusterId) {
                List<ServiceModel> serviceModels = productModelInfoControllerApi.queryByClusterId(clusterId);
                if (!CollectionUtils.isEmpty(serviceModels)) {
                    return serviceModels;
                }
                return Lists.newArrayList();
            }
        });

    @Override
    public ServiceModelBean queryByClusterIdAndServiceName(String clusterId, String serviceName)
        throws DaipBaseException {
        try {
            List<ServiceModel> serviceModels = serviceModelCache.get(clusterId);
            if (!CollectionUtils.isEmpty(serviceModels)) {
                List<ServiceModel> filteredServiceModels = serviceModels.stream()
                    .filter(serviceModel -> StringUtils.equalsIgnoreCase(serviceName, serviceModel.getServiceName()))
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(filteredServiceModels)) {
                    ServiceModel serviceModel = filteredServiceModels.get(0);
                    return ServiceModelBean.builder().serviceId(serviceModel.getServiceId())
                        .serviceName(serviceModel.getServiceName()).version(serviceModel.getVersion()).build();
                }
            }
            return null;
        } catch (Exception e) {
            throw new DaipBaseException("queryByClusterIdAndServiceName exception:", e);
        }

    }
}