/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: GenerateSeedImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/20
 * </p>
 * <p>
 * 完成日期：2021/3/20
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.common.utils.network.DaipDeploymentMode;
import com.zte.daip.manager.common.utils.paas.url.AccessUrl;
import com.zte.daip.manager.miniagent.seed.bean.Seed;
import com.zte.daip.manager.miniagent.seed.bean.SeedPriority;
import com.zte.daip.manager.miniagent.seed.bean.SeedVersion;
import com.zte.daip.manager.miniagent.seed.repository.VersionPathGenerator;
import com.zte.daip.manager.miniagent.seed.utils.ConfigureValue;
import com.zte.daip.manager.patcher.infrastructure.GenerateSeedApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class GenerateSeedImpl implements GenerateSeedApi {

    @Autowired
    private ConfigureValue configureValue;

    @Autowired
    private VersionPathGenerator versionPathGenerator;

    @Autowired
    private AccessUrl accessUrl;

    @Value("${daip.patcher.msbname:daip-patcher-svr}")
    private String microserviceName;

    @Value("${dexcloud.base.microservice.name:daip-patcher-svr}")
    private String patcherMicroserviceName;

    @Value("${server.servlet.port:56210}")
    private String microserviceport;

    @Value("${server.servlet.context-path:/api/daip-patcher-svr/v1}")
    private String microserviceurl;

    @Value("${dexcloud.base.microservice.version:v1}")
    private String microserviceVersion;

    @Override
    public Seed generateSeed(String projectName, String serviceName, String version, SeedPriority priority,
        SeedVersion seedVersion) throws DaipBaseException {

        try {
            log.info("Start generate seed :{}:{}:{}", projectName, serviceName, version);
            String hostName = (DaipDeploymentMode.isMultiLayerNet())
                ? String.format("http://%s.%s:%s%s", microserviceName, System.getenv("OPENPALETTE_NAMESPACE"),
                    microserviceport, microserviceurl)
                : accessUrl.queryUrl(patcherMicroserviceName, microserviceVersion, microserviceport, microserviceurl);
            String clusterId = configureValue.getClusterId();
            Seed seed = new Seed(projectName, serviceName, version, hostName, clusterId);
            seed.setPriority(priority.getPriority());
            seed.setSeedVersion(seedVersion);
            String localRepositoryPath = versionPathGenerator.generate(projectName, serviceName, version);

            analysisSeedFiles(localRepositoryPath, seed);
            log.info("Generate seed succes.");
            return seed;
        } catch (Exception e) {
            throw new DaipBaseException("generateSeed error : ");
        }

    }

    private void analysisSeedFiles(String localRepositoryPath, Seed seed) throws DaipBaseException {
        File file = FilePathCleaner.newFile(localRepositoryPath);
        if (file.isDirectory()) {
            log.info("Start analysis seed files.");
            File[] listFiles = file.listFiles();
            if (listFiles == null || listFiles.length == 0) {
                throw new DaipBaseException("localRepositoryPath error listFile is null: " + localRepositoryPath);
            }
            for (File listFile : listFiles) {
                String filePath = listFile.getAbsolutePath();
                analysisSeedFiles(filePath, seed);
            }
        } else if (file.isFile()) {
            seed.addFile(file.getAbsolutePath());
        } else {
            log.error("localRepositoryPath error : " + localRepositoryPath);
        }
    }
}