/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchUpdateInfoCache.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/5/25
 * </p>
 * <p>
 * 完成日期：2021/5/25
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.cache;

import static com.zte.daip.manager.patcher.infrastructure.constants.PatchUpdateConstants.DEFAULT_CACHE_EXPIRE_TIME;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.zte.daip.manager.patcher.infrastructure.PatchUpdateInfoCacheApi;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@Service
public class PatchUpdateInfoCacheService implements PatchUpdateInfoCacheApi {

    @Autowired
    private PatchCacheReader patchCacheReader;
    @Autowired
    private PatchCacheWriter patchCacheWriter;

    private LoadingCache<String, Map<String, String>> cache = CacheBuilder.newBuilder().maximumSize(500)
        .expireAfterWrite(15, TimeUnit.SECONDS).build(new CacheLoader<String, Map<String, String>>() {
            @Override
            public Map<String, String> load(String patchRequestKey) {
                return patchCacheReader.getTtlKeyAll(patchRequestKey);
            }
        });

    @Override
    public void putPatchInfo(String patchRequestKey, Map<String, String> requestContent) {
        patchCacheWriter.putTtlKey(patchRequestKey, requestContent, DEFAULT_CACHE_EXPIRE_TIME);
        validLocalCache(patchRequestKey);
    }

    @Override
    public String queryPatchInfo(String patchRequestKey, String hostIp) {
        String patchInfo = "";
        try {
            Map<String, String> content = cache.get(patchRequestKey);
            if (!CollectionUtils.isEmpty(content)) {
                patchInfo = content.get(hostIp);
            }
        } catch (Exception e) {
            log.error("query patch info exception:" + patchRequestKey);
        }
        return !StringUtils.isEmpty(patchInfo) ? patchInfo : patchCacheReader.getTtlKey(patchRequestKey, hostIp);
    }

    @Override
    public void validLocalCache(String patchRequestKey)
    {
        try
        {
            cache.invalidate(patchRequestKey);
        } catch (Exception e)
        {
            log.error("validLocalCache exception:", e);
        }
    }
}