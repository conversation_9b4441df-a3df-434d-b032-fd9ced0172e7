/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: SchemaPatchAuthorization.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/6/1
 * </p>
 * <p>
 * 完成日期：2021/6/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.cache;

import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchFlagApi;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Service
public class SchemaPatchFlagCache implements SchemaPatchFlagApi {

    @Autowired
    private PatchCacheReader patchCacheReader;
    @Autowired
    private PatchCacheWriter patchCacheWriter;

    private static final String UPDATE_PATCH = "updateSchemaPatch";

    private static final String FINISH_FLAG = "true";

    @Override
    public boolean isFinished() {
        String result = patchCacheReader.getTtlKey(UPDATE_PATCH, UPDATE_PATCH);
        return !StringUtils.equals(result, FINISH_FLAG);
    }

    public void authorize() {
        patchCacheWriter.putTtlKey(UPDATE_PATCH, UPDATE_PATCH, FINISH_FLAG, 5 * 60L);
    }

    public void release() {
        patchCacheWriter.removeByPrefix(UPDATE_PATCH);
    }
}