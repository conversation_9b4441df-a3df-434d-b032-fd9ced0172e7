package com.zte.daip.manager.patcher.impl.paas;

import com.zte.oes.dexcloud.commons.annotation.DexCloudApplication;
import com.zte.oes.dexcloud.commons.component.retrofit.annotation.EnableRetrofitRPC;
import com.zte.oes.dexcloud.redis.redisson.annotation.EnableRedisson;
import org.dexcloud.springboot.kafka.config.annotation.EnableKafka;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@DexCloudApplication
@EnableKafka
@EnableRetrofitRPC
@EnableRedisson
@EnableAutoConfiguration
@ServletComponentScan(basePackages = "com.zte.daip.*")
@ComponentScan(basePackages = "com.zte.daip.*")
@EntityScan(basePackages = "com.zte.daip.*")
@EnableAsync(proxyTargetClass = true)
@EnableScheduling
@EnableTransactionManagement
@EnableRetry
@EnableJpaRepositories("com.zte.daip.*")
public class PatcherApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(PatcherApplication.class, args);
    }

}