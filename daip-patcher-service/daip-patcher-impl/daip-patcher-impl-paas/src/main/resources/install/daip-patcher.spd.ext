releaseName: @service.name@
version: @paas.service.version@
description: @service.name@ services.
parameters:
  values:
    #云原生应用 镜像规范
    serviceImage:
      repository: '${imageRepository}'
      tenant: '${imageTenant}'
    #云原生应用 挂载网络
    annotationsPatcher:
      k8s.v1.cni.cncf.io/networks: ${daip_app_network_annotation}
    #云原生应用优雅退出时间
    terminationGracePeriodSeconds: ${termination_grace_period_seconds}
    # 微服务自己的部署参数
    envs:
      commons:
        jetty_threadpool_maxthreads: ${jetty_threadpool_maxthreads}
        REGISTER_NET_NAME: ${REGISTER_NET_NAME}
        DAIP_DEPLOYMENT_MODE: ${DAIP_DEPLOYMENT_MODE}
        daip_patcher_daip_patcher_svr_jvm_xmx: ${daip_patcher_daip_patcher_svr_jvm_xmx}
        FORCE_INSTALL: ${FORCE_INSTALL}
        FORCE_DELETE: ${FORCE_DELETE}
        FEAGLE_INSTALL_HOME: ${FEAGLE_INSTALL_HOME}
        ZDH_HOME: ${ZDH_HOME}
        PERMITROOTSTART: ${PERMITROOTSTART}
      deployer:
        - name: product
          value: daip
    #云原生应用使用 使用第三方公共服务 方式一
    kafkaConfig:
      OPENPALETTE_KAFKA_ADDRESS: get_property:[$(${KAFKA_INS_NAME}),${KAFKA_ADDRESS},${KAFKA_TYPE}]
      OPENPALETTE_KAFKA_PORT: get_property:[$(${KAFKA_INS_NAME}),${KAFKA_PORT},${KAFKA_TYPE}]
    pgConfig:
      OPENPALETTE_PG_ADDRESS: get_property:[$(${PG_INS_NAME}),${PG_ADDRESS},${PG_TYPE}]
      OPENPALETTE_PG_PORT: get_property:[$(${PG_INS_NAME}),${PG_PORT},${PG_TYPE}]
      OPENPALETTE_PG_DBNAME: get_property:[$(${PG_INS_NAME}),${PG_DBNAME},${PG_TYPE}]
      OPENPALETTE_PG_USERNAME: get_property:[$(${PG_INS_NAME}),${PG_USERNAME},${PG_TYPE}]
      OPENPALETTE_PG_PASSWORD: get_property:[$(${PG_INS_NAME}),${PG_PASSWORD},${PG_TYPE}]
    redisConfig:
      OPENPALETTE_REDIS_ADDRESS: get_property:[$(${REDIS_INS_NAME}),${REDIS_ADDRESS},${REDIS_TYPE}]
      OPENPALETTE_REDIS_PORT: get_property:[$(${REDIS_INS_NAME}),${REDIS_PORT},${REDIS_TYPE}]
      OPENPALETTE_REDIS_PASSWORD: get_property:[$(${REDIS_INS_NAME}),${REDIS_PASSWORD},${REDIS_TYPE}]
      OPENPALETTE_REDIS_SENTINEL_ADDRESS: get_property:[$(${REDIS_INS_NAME}),${REDIS_SENTINEL_ADDRESS},${REDIS_TYPE}]
      OPENPALETTE_REDIS_SENTINEL_PORT: get_property:[$(${REDIS_INS_NAME}),${REDIS_SENTINEL_PORT},${REDIS_TYPE}]
      OPENPALETTE_REDIS_SENTINEL_MASTERNAME: get_property:[$(${REDIS_INS_NAME}),${REDIS_SENTINEL_MASTERNAME},${REDIS_TYPE}]
    volumes:
      daipPatcherSvr:
        - name: password-pg
          readOnly: true
          volumeType: secret
          volumeKey: secretName
          mountPath: /etc/secrets/oes/pg
          volumeValue: get_property:[$(${PG_INS_NAME}),${PG_SECRET_NAME},${PG_TYPE}]
        - name: password-redis
          readOnly: true
          volumeType: secret
          volumeKey: secretName
          volumeValue: get_property:[$(${REDIS_INS_NAME}),${REDIS_SECRET_NAME},${REDIS_TYPE}]
          mountPath: /etc/secrets/oes/redis
        - name: daip-patcher
          readOnly: false
          volumeType: persistentVolumeClaim
          volumeKey: claimName
          volumeValue: daip-patcher
          mountPath: /data1/version
        - name: daip-patch-upload
          readOnly: false
          volumeType: hostPath
          volumeKey: path
          volumeValue: ${daip_patch_upload_dir}
          mountPath: /home/<USER>/patch
        - name: daip-certs
          readOnly: true
          volumeType: secret
          volumeKey: secretName
          volumeValue: daip-certs
          mountPath: /home/<USER>/daip-certs
      daipPatcherInit:
        - name: password-pg
          readOnly: true
          volumeType: secret
          volumeKey: secretName
          mountPath: /etc/secrets/oes/pg
          volumeValue: get_property:[$(${PG_INS_NAME}),${PG_SECRET_NAME},${PG_TYPE}]
        - name: password-redis
          readOnly: true
          volumeType: secret
          volumeKey: secretName
          volumeValue: get_property:[$(${REDIS_INS_NAME}),${REDIS_SECRET_NAME},${REDIS_TYPE}]
          mountPath: /etc/secrets/oes/redis
        - name: daip-patcher
          readOnly: false
          volumeType: persistentVolumeClaim
          volumeKey: claimName
          volumeValue: daip-patcher
          mountPath: /data1/version
        - name: daip-patch-upload
          readOnly: false
          volumeType: hostPath
          volumeKey: path
          volumeValue: /paasdata/op-tenant/ranoss/daip/version/patch
          mountPath: /home/<USER>/patch
      daipPatcherHandler:
        - name: daip-patcher
          readOnly: false
          volumeType: persistentVolumeClaim
          volumeKey: claimName
          volumeValue: daip-patcher
          mountPath: /data1/version
      daipPatcherHandlerInit:
        - name: daip-patcher
          readOnly: false
          volumeType: persistentVolumeClaim
          volumeKey: claimName
          volumeValue: daip-patcher
          mountPath: /data1/version

  #云原生应用 容器资源定义
    resources:
      daipPatcherSvr:
        requests:
          cpu: ${daip-patcher_daip-patcher-svr_cpu_request}
          memory: ${daip-patcher_daip-patcher-svr_mem_request}
        limits:
          cpu: ${daip-patcher_daip-patcher-svr_cpu_limit}
          memory: ${daip-patcher_daip-patcher-svr_mem_limit}
      daipPatcherHandler:
        requests:
          cpu: ${daip-patcher_daip-patcher-handler_cpu_request}
          memory: ${daip-patcher_daip-patcher-handler_mem_request}
        limits:
          cpu: ${daip-patcher_daip-patcher-handler_cpu_limit}
          memory: ${daip-patcher_daip-patcher-handler_mem_limit}
      daipPatcherIui:
        requests:
          cpu: ${daip-patcher_daip-patcher-iui_cpu_request}
          memory: ${daip-patcher_daip-patcher-iui_mem_request}
        limits:
          cpu: ${daip-patcher_daip-patcher-iui_cpu_limit}
          memory: ${daip-patcher_daip-patcher-iui_mem_limit}
    # 云原生应用 弹缩最大最小副本数
    replicas:
      daipPatcherSvr:
        init: "${daip-patcher_daip-patcher-svr_replica_init}"
        min: "${daip-patcher_daip-patcher-svr_replica_min}"
        max: "${daip-patcher_daip-patcher-svr_replica_max}"
      daipPatcherHandler:
        init: "${daip-patcher_daip-patcher-handler_replica_init}"
        min: "${daip-patcher_daip-patcher-handler_replica_min}"
        max: "${daip-patcher_daip-patcher-handler_replica_max}"
  #云原生应用使用 CSM 公共服务 -- 使用replaces能力，向部署配置文件resources暴露变量以便应用可按需替换
  replaces:
    public:
      # 微服务自己的部署参数
      - name: daip_app_network_annotation
        value: '[{"name":"net-api","namespace":"default", "interface":"eth1", "cni-args":{"dpdk": false}},{"name":"zdh-net","namespace":"default", "interface":"eth2", "cni-args":{"dpdk": false}}]'
        type: default
      - name: termination_grace_period_seconds
        value: 0
        type: default
      - name: jetty_threadpool_maxthreads
        type: default
        value: '100'
      - name: REGISTER_NET_NAME
        type: default
        value: @net.api.eth@
      - name: DAIP_DEPLOYMENT_MODE
        type: default
        value: zdh-net
      - name: msb_publishIp
        type: default
        value: ''
      # OKI在TCF场景下，自动处理 imageRepository和imageTenant
      - name: imageRepository
        type: default
        value: swr:2512
      - name: imageTenant
        type: default
        value: zenap
      # 微服务自己的资源参数
      - name: daip-patcher_daip-patcher-svr_cpu_request
        value: 1
        type: default
      - name: daip-patcher_daip-patcher-svr_mem_request
        value: 1Gi
        type: default
      - name: daip-patcher_daip-patcher-svr_cpu_limit
        value: 4
        type: default
      - name: daip-patcher_daip-patcher-svr_mem_limit
        value: 4Gi
        type: default
      - name: daip-patcher_daip-patcher-svr_replica_init
        type: default
        value: 1
      - name: daip-patcher_daip-patcher-svr_replica_min
        type: default
        value: 1
      - name: daip-patcher_daip-patcher-svr_replica_max
        type: default
        value: 3
      - name: daip-patcher_daip-patcher-iui_cpu_request
        value: 0.5
        type: default
      - name: daip-patcher_daip-patcher-iui_mem_request
        value: 500Mi
        type: default
      - name: daip-patcher_daip-patcher-iui_cpu_limit
        value: 1
        type: default
      - name: daip-patcher_daip-patcher-iui_mem_limit
        value: 1Gi
        type: default
      - name: daip-patcher_daip-patcher-handler_cpu_request
        value: 1
        type: default
      - name: daip-patcher_daip-patcher-handler_mem_request
        value: 1Gi
        type: default
      - name: daip-patcher_daip-patcher-handler_cpu_limit
        value: 2
        type: default
      - name: daip-patcher_daip-patcher-handler_mem_limit
        value: 4Gi
        type: default
      - name: daip-patcher_daip-patcher-handler_replica_init
        type: default
        value: 1
      - name: daip-patcher_daip-patcher-handler_replica_min
        type: default
        value: 1
      - name: daip-patcher_daip-patcher-handler_replica_max
        type: default
        value: 3
      - name: daip_patcher_daip_patcher_svr_jvm_xmx
        type: default
        value: 4G
      - name: daip_patch_upload_dir
        type: default
        value: /paasdata/op-tenant/ranoss/daip/version/patch
      - name: FORCE_INSTALL
        type: default
        value: "false"
      - name: FORCE_DELETE
        type: default
        value: "false"
      - name: FEAGLE_INSTALL_HOME
        type: default
        value: /opt/Feagle/
      - name: ZDH_HOME
        type: default
        value: /opt/ZDH/
      - name: PERMITROOTSTART
        type: default
        value: "true"
      # 依赖的外部服务，对应BP蓝图的common_service_list
      # 应用使用到的KAFKA配置
      - name: KAFKA_INS_NAME
        value: KafkaResourceInstance,daip-kafka
        type: default
      - name: KAFKA_TYPE
        value: ts_secret
        type: default
      - name: KAFKA_ADDRESS
        value: OPENPALETTE_KAFKA_ADDRESS
        type: default
      - name: KAFKA_PORT
        value: OPENPALETTE_KAFKA_PORT
        type: default
      # PG类型：cms、tsm
      - name: PG_TYPE
        type: default
        value: ts_secret
      # 应用使用到的PG配置
      - name: PG_INS_NAME
        value: Pgtask,daip-pgsql
        type: default
      - name: PG_SECRET_NAME
        value: openpalette_secret_name
        type: default
      - name: PG_ADDRESS
        value: OPENPALETTE_PG_ADDRESS
        type: default
      - name: PG_PORT
        value: OPENPALETTE_PG_PORT
        type: default
      - name: PG_DBNAME
        value: OPENPALETTE_PG_DBNAME
        type: default
      - name: PG_USERNAME
        value: OPENPALETTE_PG_USERNAME
        type: default
      - name: PG_PASSWORD
        value: OPENPALETTE_PG_PASSWORD
        type: default
        # 应用使用到的Redis配置
      # Redis类型：cms、tsm
      - name: REDIS_TYPE
        value: ts_secret
        type: default
      # 应用使用到的Redis配置
      - name: REDIS_INS_NAME
        value: RedisFailover,daip-redis
        type: default
      - name: REDIS_SECRET_NAME
        value: openpalette_secret_name
        type: default
      - name: REDIS_ADDRESS
        value: OPENPALETTE_REDIS_ADDRESS
        type: default
      - name: REDIS_PORT
        value: OPENPALETTE_REDIS_PORT
        type: default
      - name: REDIS_PASSWORD
        value: OPENPALETTE_REDIS_PASSWORD
        type: default
      - name: REDIS_SENTINEL_ADDRESS
        value: OPENPALETTE_REDIS_SENTINEL_ADDRESS
        type: default
      - name: REDIS_SENTINEL_PORT
        value: OPENPALETTE_REDIS_SENTINEL_PORT
        type: default
      - name: REDIS_SENTINEL_MASTERNAME
        value: OPENPALETTE_REDIS_SENTINEL_MASTERNAME
        type: default
