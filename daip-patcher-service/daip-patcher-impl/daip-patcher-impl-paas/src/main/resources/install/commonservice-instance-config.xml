<?xml version="1.0" encoding="UTF-8"?>
<service>

    <common-service display_name="Kafka" orch_name="commsrv_kafka_bp">
        <instance name="daip_kafka"/>
    </common-service>
    <common-service display_name="daip_patcher_redis" orch_name="commsrv_inner_redis_single_bp">
        <instance name="daip_patcher_redis"/>
    </common-service>
    <common-service display_name="PostgreSQL" orch_name="commsrv_pg_bp">
        <instance name="daip_pgsql"/>
    </common-service>
</service>
