{"product_name": "@service.name@", "version": "@paas.service.version@", "description": "@service.name@ services.", "service_list": [{"service_name": "@service.name@", "version": "@paas.service.version@", "binding_port_list": [], "common_service_list": [{"common_service_name": "daip_kafka", "resource_name": "daip_kafka"}, {"common_service_name": "daip_patcher_redis", "resource_name": "daip_patcher_redis"}, {"common_service_name": "daip_pgsql", "resource_name": "daip_pgsql"}], "volume_list": [{"name": "daip-patcher", "key": "claim<PERSON>ame", "type": "persistentVolumeClaim", "value": "daip-patcher"}, {"name": "daip-patch-upload", "key": "path", "type": "hostPath", "value": "/paasdata/op-tenant/{TENENT}/daip/version/patch"}], "env_list": [{"name": "forceInstall", "value": "false"}, {"name": "forceDelete", "value": "false"}, {"name": "feagle.install.home", "value": "/opt/Feagle/"}, {"name": "ZDH_HOME", "value": "/opt/ZDH/"}, {"name": "jetty_threadpool_maxthreads", "value": "100"}], "other_list": [{"name": "daip-patcher_daip-patcher-svr_cpu_request", "value": "2"}, {"name": "daip-patcher_daip-patcher-svr_cpu_limit", "value": "4"}, {"name": "daip-patcher_daip-patcher-svr_mem_request", "value": "2G"}, {"name": "daip-patcher_daip-patcher-svr_mem_limit", "value": "4G"}, {"name": "daip-patcher_daip-patcher-svr_replica_init", "value": "1"}, {"name": "daip-patcher_daip-patcher-svr_replica_min", "value": "1"}, {"name": "daip-patcher_daip-patcher-svr_replica_max", "value": "5"}]}]}