#!/bin/bash
#上传服务到paas软件仓库
set -e
DIRNAME=`dirname $0`
WORK_HOME=`cd $DIRNAME/; pwd`
BASE_IMAGE_VERSION=<EMAIL>@
GLOBAL_IMAGE_VERSION=<EMAIL>@
ZARTCLI=/root/zartcli/zartcli
IUI_COMMON_IMAGE=@iui.common.microservice.name@
IAG_VERSION=@zenap.iag@
DEXCLOUD_IMAGE_NAME=@dexcloud.image.name@
if [ -z $TENANTS ];then
  TENANTS=zenap
fi
if [ -z $DEPLOYTYPE ];then
  DEPLOYTYPE=service
fi

VERSION_NO=@paas.service.version@
SERVICE_NAME=@service.name@

CHART_VERSION=@chart.version@
CHART_NAME=@service.name@

BP_BASE_PATH=$WORK_HOME/blueprint/"$SERVICE_NAME"_service
BP_PATH=${BP_BASE_PATH}/zdh-net

log_file=${SERVICE_NAME}_install.log
log_path=""

# ============================================================================
# log log_text
# ============================================================================
function log(){
    if [ -z "$log_path" ]; then
        log_path=/var/log/$SERVICE_NAME
    fi

    if [ ! -d $log_path ]; then
        mkdir -p $log_path
        chmod 750 $log_path
    else
        log_path_mode=$(stat -c '%a' $log_path)
        if [ "$log_path_mode" != "750" ]; then
            chmod 750 $log_path
          fi
    fi

    local target=$log_path/$log_file
    if [ ! -f $target ]; then
        touch $target
        chmod 640 $target
    fi

    log_text="`date +%Y-%m-%d\ %T` [$SERVICE_NAME] $1"

    echo "$log_text" >> $target
}

function clearLog(){
    if [ -z "$log_path" ]; then
        log_path=/var/log/$SERVICE_NAME
    fi

    if [ ! -d $log_path ]; then
        mkdir -p $log_path
        chmod 750 $log_path
    fi

    local target=$log_path/$log_file
    if [ -f $target ]; then
        rm -rf $target
    fi
}

checkImageState() {
 if [ $# -ne 2 ]
 then
  echo usage:checkImageState IMAGE_NAME IMAGE_NO
  exit 1
 fi

 local IMAGE_NAME=$1
 local IMAGE_NO=$2
 local image_state=""
 for i in {1..200}
 do
    image_state=$($ZARTCLI -o=query -i=$TENANTS -m=image -n=$IMAGE_NAME -v=$IMAGE_NO|grep '"status":'|awk '{print $2}'|sed 's/,$//g'|sed 's/^"//g'|sed 's/"$//g')
    log "status of image $IMAGE_NAME: $image_state"
    if [ "$image_state" = "available" ] || [ "$image_state" = "unavailable" ];then
      break
    else
      sleep 3
      continue
    fi
  done
 echo $image_state
}

buildImage() {
 if [ $# -ne 2 ]
 then
  log usage:buildImage IMAGE_NAME IMAGE_VERSION
  exit 1
 fi

 local IMAGE_NAME=$1
 local IMAGE_VERSION=$2
 local IMAGE_STAT=""
 local dockerFilePath=$WORK_HOME/"$IMAGE_NAME"
 if [ ! -d ${dockerFilePath} ]
 then
  log "The directory for Dockerfile doesnot exist, path:${dockerFilePath}, please check."
  exit 2
 fi
  if [ "$IMAGE_NAME" != "$SERVICE_NAME-iui" ]
  then
    editDockerfile $dockerFilePath $IMAGE_NAME $IMAGE_VERSION
  else
    editIUIDockerfile $dockerFilePath $IMAGE_NAME $IMAGE_VERSION
  fi

 for i in {1..2}
 do
    image_state=$($ZARTCLI -o=query -i=$TENANTS -m=image -n=$IMAGE_NAME -v=$IMAGE_VERSION|grep '"status":'|awk '{print $2}'|sed 's/,$//g'|sed 's/^"//g'|sed 's/"$//g')
    if [ "$image_state" = "available" ];then
        log "status of image $IMAGE_NAME: $image_state,no need to build"
        break
    fi
    sed -ri 's#^FROM .*/daip-common-iui#FROM '$TENANTS'/daip-common-iui#' $dockerFilePath/Dockerfile
    $ZARTCLI -o delete -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_VERSION || true >/dev/null
    $ZARTCLI -o build -i $TENANTS -m image -n $IMAGE_NAME -v $IMAGE_VERSION -b yes -p $dockerFilePath
    IMAGE_STAT=$(checkImageState $IMAGE_NAME $IMAGE_VERSION)
    if [ "$IMAGE_STAT" = "available" ];then
       log "status of image $IMAGE_NAME: $IMAGE_STAT, build success."
       break
    elif [ "$IMAGE_STAT" = "unavailable" ];then
       if [ $i -eq 1 ];then log "status of image $IMAGE_NAME: $IMAGE_STAT, retry...";continue;fi
       if [ $i -eq 2 ];then log "status of image $IMAGE_NAME: $IMAGE_STAT, build failed.";exit 1;fi
    else
       log "status of image $IMAGE_NAME: $IMAGE_STAT, unknown error."
       exit 1
    fi
 done
}

uploadChartBP() {
  local chartBPPath=$WORK_HOME/blueprint/"$SERVICE_NAME"_chart
    if [ ! -d ${chartBPPath} ]
    then
        log "chart blueprint dir doesnot exist,path:${chartBPPath},please check."
      exit 2
    fi

  $ZARTCLI -o delete -i $TENANTS -m chart -n $CHART_NAME -v $CHART_VERSION || true >/dev/null
  $ZARTCLI -o upload -i $TENANTS -m chart -n $CHART_NAME -v $CHART_VERSION -b yes -p $chartBPPath
}

uploadServiceBP() {
	local serviceBPPath=$BP_PATH
    if [ ! -d ${serviceBPPath} ]
    then
        echo "Service blueprint dir doesnot exist,path:${serviceBPPath},please check."
	    exit 2
    fi
	$ZARTCLI -o delete -i $TENANTS -m bp -t service -n $SERVICE_NAME -v $VERSION_NO || true >/dev/null
  $ZARTCLI -o upload -i $TENANTS -m bp -t service -n $SERVICE_NAME -v $VERSION_NO -b yes -p $serviceBPPath
}

editIUIDockerfile(){
    local dockerFilePath=$1
    local IMAGE_VERSION=$3
    #获取Dockerfile中的默认版本
    DEFAULT_BASE_IMAGE_VERSION=$(sed -n '1p' $dockerFilePath/Dockerfile |awk -F ':' '{print $2}')
    #查询swr侧的最新版本
    BASE_IMAGE_VERSION_IN_SWR=$(curl "swr:6000/swr/v1/tenants/${TENANTS}/image-query?name=daip-common-iui&_order=desc&tenant=${TENANTS}&_limit=1&_sort=version"|grep "version"|awk '{print $2}'|sed 's/,//g'|sed 's/"//g');
    if test -z "$BASE_IMAGE_VERSION_IN_SWR"
    then
       log "cannot find iui image version in swr!"
       BASE_IMAGE_VERSION_IN_REGISTRY=$(curl -s http://${REGISTRY_URL}/v2/daip/daip-common-iui/tags/list | grep '"tags"' | sed 's/^.*"tags":\[\(.*\)\].*$/\1/' | awk -F ',' '{max_version="";for(i=1;i<=NF;i++){sub(/^["]+/, "", $i);sub(/["]/, "", $i);if($i>max_version)max_version=$i;}printf(max_version)}')
       if test -z "$BASE_IMAGE_VERSION_IN_REGISTRY"
       then
           log "cannot find iui image version in registry!"
       else
           IAG_VERSION=$BASE_IMAGE_VERSION_IN_REGISTRY
           sed -i "1c FROM zenap/daip-common-iui:${BASE_IMAGE_VERSION_IN_REGISTRY}"  $dockerFilePath/Dockerfile
       fi
    else
      IAG_VERSION=$BASE_IMAGE_VERSION_IN_SWR
      sed -i "1c FROM zenap/daip-common-iui:${BASE_IMAGE_VERSION_IN_SWR}"  $dockerFilePath/Dockerfile
    fi
}

editDockerfile(){
  local dockerFilePath=$1
  local IMAGE_NAME=$2
  local IMAGE_VERSION=$3
  #获取Dockerfile中的默认版本
  DEFAULT_BASE_IMAGE_VERSION=$(sed -n '1p' $dockerFilePath/Dockerfile |awk -F ':' '{print $2}')
  verifyImageVersion $dockerFilePath $DEFAULT_BASE_IMAGE_VERSION
}
verifyImageVersion(){
    local dockerFilePath=$1
    local DEFAULT_BASE_IMAGE_VERSION=$2

    count_in_swr=$(curl "swr:6000/swr/v1/tenants/zenap/image-query?name=${DEXCLOUD_IMAGE_NAME}&tenant=zenap&version=${DEFAULT_BASE_IMAGE_VERSION}"|grep "version"|awk '{print $2}'|sed 's/,//g'|sed 's/"//g'|grep $DEFAULT_BASE_IMAGE_VERSION|wc -l)
    #swr中没有默认的镜像版本，查询最新
    if [ $count_in_swr -eq 0 ]
    then
       log "can not find ${DEXCLOUD_IMAGE_NAME} ${DEFAULT_BASE_IMAGE_VERSION} in swr,will use the latest version in swr"
       #查询swr侧的最新版本
       BASE_IMAGE_VERSION_IN_SWR=$(curl "swr:6000/swr/v1/tenants/zenap/image-query?name=${DEXCLOUD_IMAGE_NAME}&_order=desc&tenant=zenap&_limit=1&_sort=version"|grep "version"|awk '{print $2}'|sed 's/,//g'|sed 's/"//g');
       if test -z "$BASE_IMAGE_VERSION_IN_SWR"
       then
         log "cannot find image version in swr!"
       else
         BASE_IMAGE_VERSION=$BASE_IMAGE_VERSION_IN_SWR
         #替换Dockerfile中的版本到swr中的最新版本
         sed -i "1c FROM zenap/${DEXCLOUD_IMAGE_NAME}:${BASE_IMAGE_VERSION_IN_SWR}"  $dockerFilePath/Dockerfile
       fi
    fi
}

findBpPath() {
  set +e
  networks=`kubectl get network -n default`
  if [ $? -eq 0 ]; then
    echo $networks | grep "zdh-net" 1>/dev/null 2>/dev/null
    if [ $? -ne 0 ]; then
      BP_PATH=${BP_BASE_PATH}/single-net
    fi
  fi
  set -e
}

#
clearLog

log "================================"
log "@WORK_HOME@ $WORK_HOME"
log "@VERSION_NO@ $VERSION_NO"
log "@TENANTS@ $TENANTS"
log "================================"

echo "=========Begin Upload $SERVICE_NAME========="
cd $WORK_HOME

#
# echo "Build Base Image"
# for dir in $(ls)
# do
#  if [ -d $dir ] && [ $dir == "$TENANTS"-"$SERVICE_NAME"-dependency ]; then
#   ms=${dir#"$TENANTS"-}
#   buildImage $ms $VERSION_NO
#  fi
# done

#
echo "Build Component Image"
cd $WORK_HOME
for ms in $(ls)
do
	if [ -d $ms ] && [ $ms != blueprint ] && [ $ms != "$SERVICE_NAME"-dependency ];then
		buildImage $ms $VERSION_NO
	fi
done

# 上传服务蓝图
if [ "$DEPLOYTYPE" = "native" ]; then
  log "===============Begin Upload Native $chartBPPath================"
  uploadChartBP
else
  log "===============Begin Upload Service $serviceBPPath================"
  cd $WORK_HOME
  findBpPath
  echo "bp path is $BP_PATH"
  uploadServiceBP
fi

exit 0
