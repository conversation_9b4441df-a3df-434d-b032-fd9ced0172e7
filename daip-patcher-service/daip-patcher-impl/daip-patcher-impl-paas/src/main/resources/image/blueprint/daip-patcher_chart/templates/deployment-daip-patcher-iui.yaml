apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    appm: {{ .Release.Name }}
  name: &msName {{ .Release.Name }}-iui
spec:
  replicas: 1
  selector:
    matchLabels:
      name: {{ .Release.Name }}-iui
  template:
    metadata:
      annotations: {{- toYaml .Values.annotationsPatcher | nindent 8 }}
      labels:
        name: {{ .Release.Name }}-iui
    spec:
      {{- if .Values.labelselector }}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
            {{- toYaml .Values.labelselector | nindent 16 }}
      {{- end}}
      containers:
        - name: &serverContainerName {{ .Release.Name }}-iui
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- if .Values.serviceImage.tenant }}
          image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.tenant }}/@iui.microservice.name@:{{ .Values.serviceImage.version.daipPatcherIui }}
          {{- else }}
          image: {{ .Values.serviceImage.repository }}/@iui.microservice.name@:{{ .Values.serviceImage.version.daipPatcherIui }}
          {{- end }}
          imagePullPolicy: {{ quote .Values.serviceImage.pullPolicy }}
          tty: false
          stdin: false
          env:
            - name: TZ
              value: {{ .Values.global.TZ | quote }}
            - name: OPENPALETTE_MSB_IP
              value: "{{ .Values.global.OPENPALETTE_MSB_IP }}"
            - name: DAIP_DEPLOYMENT_MODE
              value: "{{ .Values.envs.commons.DAIP_DEPLOYMENT_MODE }}"
            - name: permit_root_start
              value: "{{ .Values.envs.commons.PERMITROOTSTART }}"
            - name: register_net_name
              value: "{{ .Values.envs.commons.REGISTER_NET_NAME }}"
            - name: OPENPALETTE_MSB_PORT
              value: "{{ .Values.global.OPENPALETTE_MSB_PORT }}"
            - name: OPENPALETTE_MSB_ROUTER_HTTPS_PORT
              value: "{{ .Values.global.OPENPALETTE_MSB_ROUTER_HTTPS_PORT }}"
            - name: OPENPALETTE_MSB_ROUTER_IP
              value: "{{ .Values.global.OPENPALETTE_MSB_ROUTER_IP }}"
            - name: OPENPALETTE_MSB_ROUTER_PORT
              value: "{{ .Values.global.OPENPALETTE_MSB_ROUTER_PORT }}"
            - name: OPENPALETTE_NAMESPACE
              value: "{{ .Values.global.OPENPALETTE_NAMESPACE }}"
            - name: openpalette_ms_bpname
              value: *msName
            - name: openpalette_container_name
              value: *serverContainerName
            {{- range .Values.envs.deployer }}
            {{- include "oki.containerEnvs" . | nindent 12 }}
            {{- end }}
          ports:
            - containerPort: 13002
              protocol: TCP
          resources:
            requests:
              memory: {{ .Values.resources.daipPatcherIui.requests.memory }}
              cpu: {{ .Values.resources.daipPatcherIui.requests.cpu }}
            limits:
              memory: {{ .Values.resources.daipPatcherIui.limits.memory }}
              cpu: {{ .Values.resources.daipPatcherIui.limits.cpu }}
          readinessProbe:
            failureThreshold: 30
            tcpSocket:
              port: 13002
            initialDelaySeconds: 15
            periodSeconds: 15
            successThreshold: 1
            timeoutSeconds: 15
          livenessProbe:
            failureThreshold: 30
            tcpSocket:
              port: 13002
            initialDelaySeconds: 15
            periodSeconds: 15
            successThreshold: 1
            timeoutSeconds: 15
      enableServiceLinks: false
      initContainers: []
      restartPolicy: {{ .Values.restartPolicy }}
      terminationGracePeriodSeconds: 30
      hostNetwork: false
      securityContext:
        sysctls: []
  strategy:
    type: {{ .Values.strategy.type }}
    rollingUpdate:
      maxSurge: {{ .Values.strategy.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.strategy.rollingUpdate.maxUnavailable }}
