{{- if and .Values.idn.pbcEnabled -}}
apiVersion: {{ template "oki.pbc.apiVersion" . }}
kind: Msb<PERSON><PERSON>
{{- else }}
apiVersion: {{ template "oki.msb.apiVersion" . }}
kind: Ms<PERSON><PERSON><PERSON>e
{{- end }}
metadata:
  name: {{ .Release.Name }}-handler
  namespace: {{ .Values.serviceImage.tenant }}
  labels:
    # 管理pbc组件名
    componentName: {{ .Release.Name }}-pbc
    # 关联pbc组件api服务名
    pbc.apiServiceName: @handler.microservice.name@
spec:
  httpRules:
    - match:
        path: /api/@handler.microservice.name@/v1
        protocol: REST
        rewriteTarget: /api/@handler.microservice.name@/v1
      backend:
        service:
          name: {{ .Release.Name }}-handler
          portName: http
      advancedConfig:
        lbPolicy: round-robin
