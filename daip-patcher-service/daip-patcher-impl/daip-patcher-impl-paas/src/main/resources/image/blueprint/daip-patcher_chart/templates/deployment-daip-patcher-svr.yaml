apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    appm: {{ .Release.Name }}
  name: &msName {{ .Release.Name }}-svr
spec:
  replicas: {{ .Values.replicas.daipPatcherSvr.init }}
  selector:
    matchLabels:
      name: {{ .Release.Name }}-svr
  template:
    metadata:
      annotations: {{- toYaml .Values.annotationsPatcher | nindent 8 }}
      labels:
        name: {{ .Release.Name }}-svr
        daip-metric: {{ .Release.Name }}-svr
        daip-service: {{ .Release.Name }}-svr
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: "daip-service"
                      operator: In
                      values:
                        - {{ .Release.Name }}-svr
                topologyKey: "kubernetes.io/hostname"
        {{- if .Values.labelselector }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
            {{- toYaml .Values.labelselector | nindent 16 }}
        {{- end}}
      containers:
        - name: &serverContainerName {{ .Release.Name }}-svr
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- if .Values.serviceImage.tenant }}
          image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.tenant }}/@svr.microservice.name@:{{ .Values.serviceImage.version.daipPatcherSvr }}
          {{- else }}
          image: {{ .Values.serviceImage.repository }}/@svr.microservice.name@:{{ .Values.serviceImage.version.daipPatcherSvr }}
          {{- end }}
          imagePullPolicy: {{ quote .Values.serviceImage.pullPolicy }}
          env:
            - name: TZ
              value: {{ .Values.global.TZ | quote }}
            - name: DAIP_DEPLOYMENT_MODE
              value: {{ .Values.envs.commons.DAIP_DEPLOYMENT_MODE | quote }}
            - name: jetty_threadpool_maxthreads
              value: {{ .Values.envs.commons.jetty_threadpool_maxthreads | quote }}
            - name: daip_patcher_daip_patcher_svr_jvm_xmx
              value: {{ .Values.envs.commons.daip_patcher_daip_patcher_svr_jvm_xmx | quote }}
            - name: permit_root_start
              value: {{ .Values.envs.commons.PERMITROOTSTART | quote }}
            - name: OPENPALETTE_KAFKA_ADDRESS
              value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_ADDRESS | quote }}
            - name: OPENPALETTE_KAFKA_PORT
              value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_PORT | quote }}
            - name: OPENPALETTE_REDIS_ADDRESS
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_ADDRESS | quote }}
            - name: OPENPALETTE_REDIS_PORT
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PORT | quote }}
            - name: OPENPALETTE_REDIS_SENTINEL_ADDRESS
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_ADDRESS | quote }}
            - name: OPENPALETTE_REDIS_SENTINEL_PORT
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_PORT | quote }}
            - name: OPENPALETTE_REDIS_SENTINEL_MASTERNAME
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_MASTERNAME | quote }}
            - name: OPENPALETTE_REDIS_PASSWORD
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PASSWORD | quote }}
            - name: OPENPALETTE_PG_ADDRESS
              value: {{ .Values.pgConfig.OPENPALETTE_PG_ADDRESS | quote }}
            - name: OPENPALETTE_PG_PORT
              value: {{ .Values.pgConfig.OPENPALETTE_PG_PORT | quote }}
            - name: OPENPALETTE_PG_DBNAME
              value: {{ .Values.pgConfig.OPENPALETTE_PG_DBNAME | quote }}
            - name: OPENPALETTE_PG_USERNAME
              value: {{ .Values.pgConfig.OPENPALETTE_PG_USERNAME | quote }}
            - name: OPENPALETTE_PG_PASSWORD
              value: {{ .Values.pgConfig.OPENPALETTE_PG_PASSWORD | quote }}
            - name: register_net_name
              value: {{ .Values.envs.commons.REGISTER_NET_NAME | quote }}
            - name: OPENPALETTE_MSB_IP
              value: {{ .Values.global.OPENPALETTE_MSB_IP | quote }}
            - name: OPENPALETTE_MSB_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_PORT | quote }}
            - name: OPENPALETTE_MSB_ROUTER_HTTPS_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_HTTPS_PORT | quote }}
            - name: OPENPALETTE_MSB_ROUTER_IP
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_IP | quote }}
            - name: OPENPALETTE_MSB_ROUTER_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_PORT | quote }}
            - name: OPENPALETTE_NAMESPACE
              value: {{ .Values.global.OPENPALETTE_NAMESPACE | quote }}
            - name: openpalette_ms_bpname
              value: *msName
            - name: openpalette_container_name
              value: *serverContainerName
            {{- range .Values.envs.deployer }}
            {{- include "oki.containerEnvs" . | nindent 12 }}
            {{- end }}
          ports:
            - containerPort: 56210
              protocol: TCP
              name: svr-port
          volumeMounts:
            {{- range .Values.volumes.daipPatcherSvr }}
            - name: {{ .name}}
              mountPath: {{ .mountPath}}
              readOnly: {{ .readOnly}}
            {{- end }}
            - mountPath: /cloud-disk-notification-volume/
              name: cloud-disk-notification-volume
          resources:
            requests:
              memory: {{ .Values.resources.daipPatcherSvr.requests.memory }}
              cpu: {{ .Values.resources.daipPatcherSvr.requests.cpu }}
            limits:
              memory: {{ .Values.resources.daipPatcherSvr.limits.memory }}
              cpu: {{ .Values.resources.daipPatcherSvr.limits.cpu }}
          readinessProbe:
            failureThreshold: 30
            tcpSocket:
              port: 56210
            initialDelaySeconds: 15
            periodSeconds: 15
            successThreshold: 1
            timeoutSeconds: 15
          livenessProbe:
            failureThreshold: 30
            tcpSocket:
              port: 56210
            initialDelaySeconds: 15
            periodSeconds: 15
            successThreshold: 1
            timeoutSeconds: 15
      enableServiceLinks: false
      restartPolicy: {{ .Values.restartPolicy }}
      initContainers:
        - name: {{ .Release.Name }}-svr-init
          securityContext:
            privileged: false
            allowPrivilegeEscalation: false
            seccompProfile:
              type: "RuntimeDefault"
            seLinuxOptions:
              user: "system_u"
              role: "system_r"
              type: "container_t"
              level: "s0"
          {{- if .Values.serviceImage.tenant }}
          image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.tenant }}/{{ .Values.serviceImage.initImageName }}:{{ .Values.serviceImage.version.daipPatcherInit }}
          {{- else }}
          image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.initImageName }}:{{ .Values.serviceImage.version.daipPatcherInit }}
          {{- end }}
          imagePullPolicy: {{ quote .Values.serviceImage.pullPolicy }}
          env:
            - name: TZ
              value: {{ .Values.global.TZ | quote }}
            - name: DAIP_DEPLOYMENT_MODE
              value: {{ .Values.envs.commons.DAIP_DEPLOYMENT_MODE | quote }}
            - name: forceDelete
              value: {{ .Values.envs.commons.FORCE_DELETE | quote }}
            - name: forceInstall
              value: {{ .Values.envs.commons.FORCE_INSTALL | quote }}
            - name: feagle.install.home
              value: {{ .Values.envs.commons.FEAGLE_INSTALL_HOME | quote }}
            - name: ZDH_HOME
              value: {{ .Values.envs.commons.ZDH_HOME | quote }}
            - name: permit_root_start
              value: {{ .Values.envs.commons.PERMITROOTSTART | quote }}
            - name: OPENPALETTE_PG_ADDRESS
              value: {{ .Values.pgConfig.OPENPALETTE_PG_ADDRESS | quote }}
            - name: OPENPALETTE_PG_PORT
              value: {{ .Values.pgConfig.OPENPALETTE_PG_PORT | quote }}
            - name: OPENPALETTE_PG_DBNAME
              value: {{ .Values.pgConfig.OPENPALETTE_PG_DBNAME | quote }}
            - name: OPENPALETTE_PG_USERNAME
              value: {{ .Values.pgConfig.OPENPALETTE_PG_USERNAME | quote }}
            - name: OPENPALETTE_PG_PASSWORD
              value: {{ .Values.pgConfig.OPENPALETTE_PG_PASSWORD | quote }}
            - name: OPENPALETTE_REDIS_ADDRESS
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_ADDRESS | quote }}
            - name: OPENPALETTE_REDIS_PORT
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PORT | quote }}
            - name: OPENPALETTE_REDIS_SENTINEL_ADDRESS
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_ADDRESS | quote }}
            - name: OPENPALETTE_REDIS_SENTINEL_PORT
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_PORT | quote }}
            - name: OPENPALETTE_REDIS_SENTINEL_MASTERNAME
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_SENTINEL_MASTERNAME | quote }}
            - name: OPENPALETTE_REDIS_PASSWORD
              value: {{ .Values.redisConfig.OPENPALETTE_REDIS_PASSWORD | quote }}
            - name: OPENPALETTE_KAFKA_ADDRESS
              value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_ADDRESS | quote }}
            - name: OPENPALETTE_KAFKA_PORT
              value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_PORT | quote }}
            - name: OPENPALETTE_MSB_IP
              value: {{ .Values.global.OPENPALETTE_MSB_IP | quote }}
            - name: OPENPALETTE_MSB_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_PORT | quote }}
            - name: OPENPALETTE_MSB_ROUTER_HTTPS_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_HTTPS_PORT | quote }}
            - name: OPENPALETTE_MSB_ROUTER_IP
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_IP | quote }}
            - name: OPENPALETTE_MSB_ROUTER_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_PORT | quote }}
            - name: OPENPALETTE_NAMESPACE
              value: {{ .Values.global.OPENPALETTE_NAMESPACE | quote }}
          volumeMounts:
            {{- range .Values.volumes.daipPatcherInit }}
            - name: {{ .name }}
              mountPath: {{ .mountPath }}
              readOnly: {{ .readOnly }}
            {{- end }}
            - mountPath: /cloud-disk-notification-volume/
              name: cloud-disk-notification-volume
          resources:
            requests:
              memory: {{ .Values.resources.daipPatcherInit.requests.memory }}
              cpu: {{ .Values.resources.daipPatcherInit.requests.cpu }}
            limits:
              memory: {{ .Values.resources.daipPatcherInit.limits.memory }}
              cpu: {{ .Values.resources.daipPatcherInit.limits.cpu }}
      volumes:
        {{- range .Values.volumes.daipPatcherSvr }}
        - name: {{ .name}}
          {{ .volumeType }}:
          {{ .volumeKey | indent 2 }}: {{ .volumeValue }}
          {{- if or (eq .volumeType "configMap") (eq .volumeType "secret") }}
          defaultMode: {{ .defaultMode }}
          {{- end }}
        {{- end }}
        - name: cloud-disk-notification-volume
          emptyDir: {}
      terminationGracePeriodSeconds: 30
      hostNetwork: false
      securityContext:
        sysctls: []
      hostIPC: false
  strategy:
    type: {{ .Values.strategy.type }}
    rollingUpdate:
      maxSurge: {{ .Values.strategy.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.strategy.rollingUpdate.maxUnavailable }}
