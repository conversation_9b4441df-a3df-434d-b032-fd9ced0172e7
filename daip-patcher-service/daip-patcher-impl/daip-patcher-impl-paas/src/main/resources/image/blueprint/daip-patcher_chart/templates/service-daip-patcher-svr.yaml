apiVersion: v1
kind: Service
metadata:
  annotations:
    com.zte.zenap.msb/service: '{"protocol":"TCP","visualRange":"1"}'
  labels:
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/name: {{ .Release.Name }}
  name: {{ .Release.Name }}-svr
  namespace: {{ .Values.global.OPENPALETTE_NAMESPACE }}
spec:
  type: ClusterIP
  ports:
    - name: "daip-patcher-svr-http-port"
      port: 56210
      targetPort: 56210
      protocol: TCP
  selector:
    daip-service: {{ .Release.Name }}-svr
