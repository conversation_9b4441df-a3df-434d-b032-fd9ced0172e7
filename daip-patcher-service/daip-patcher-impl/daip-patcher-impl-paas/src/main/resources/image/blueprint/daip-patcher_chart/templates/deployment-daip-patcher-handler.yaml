apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    appm: {{ .Release.Name }}
  name: &msName {{ .Release.Name }}-handler
spec:
  replicas: {{ .Values.replicas.daipPatcherHandler.init }}
  selector:
    matchLabels:
      name: {{ .Release.Name }}-handler
  template:
    metadata:
      annotations: {{- toYaml .Values.annotationsPatcher | nindent 8 }}
      labels:
        name: {{ .Release.Name }}-handler
        daip-metric: {{ .Release.Name }}-handler
        daip-service: {{ .Release.Name }}-handler
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: "daip-service"
                      operator: In
                      values:
                        - {{ .Release.Name }}-handler
                topologyKey: "kubernetes.io/hostname"
        {{- if .Values.labelselector }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
            {{- toYaml .Values.labelselector | nindent 16 }}
        {{- end}}
      containers:
        - name: &serverContainerName {{ .Release.Name }}-handler
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- if .Values.serviceImage.tenant }}
          image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.tenant }}/@handler.microservice.name@:{{ .Values.serviceImage.version.daipPatcherHandler }}
          {{- else }}
          image: {{ .Values.serviceImage.repository }}/@handler.microservice.name@:{{ .Values.serviceImage.version.daipPatcherHandler }}
          {{- end }}
          imagePullPolicy: {{ quote .Values.serviceImage.pullPolicy }}
          env:
            - name: TZ
              value: {{ .Values.global.TZ | quote }}
            - name: DAIP_DEPLOYMENT_MODE
              value: {{ .Values.envs.commons.DAIP_DEPLOYMENT_MODE | quote }}
            - name: jetty_threadpool_maxthreads
              value: {{ .Values.envs.commons.jetty_threadpool_maxthreads | quote }}
            - name: OPENPALETTE_KAFKA_ADDRESS
              value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_ADDRESS | quote }}
            - name: OPENPALETTE_KAFKA_PORT
              value: {{ .Values.kafkaConfig.OPENPALETTE_KAFKA_PORT | quote }}
            - name: register_net_name
              value: {{ .Values.envs.commons.REGISTER_NET_NAME | quote }}
            - name: OPENPALETTE_MSB_IP
              value: {{ .Values.global.OPENPALETTE_MSB_IP | quote }}
            - name: OPENPALETTE_MSB_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_PORT | quote }}
            - name: OPENPALETTE_MSB_ROUTER_HTTPS_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_HTTPS_PORT | quote }}
            - name: OPENPALETTE_MSB_ROUTER_IP
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_IP | quote }}
            - name: OPENPALETTE_MSB_ROUTER_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_PORT | quote }}
            - name: OPENPALETTE_NAMESPACE
              value: {{ .Values.global.OPENPALETTE_NAMESPACE | quote }}
            - name: openpalette_ms_bpname
              value: *msName
            - name: openpalette_container_name
              value: *serverContainerName
            {{- range .Values.envs.deployer }}
            {{- include "oki.containerEnvs" . | nindent 12 }}
            {{- end }}
          ports:
            - containerPort: 13001
              protocol: TCP
          volumeMounts:
            {{- range .Values.volumes.daipPatcherHandler }}
            - name: {{ .name}}
              mountPath: {{ .mountPath}}
              readOnly: {{ .readOnly}}
            {{- end }}
            - mountPath: /cloud-disk-notification-volume/
              name: cloud-disk-notification-volume
          resources:
            requests:
              memory: {{ .Values.resources.daipPatcherHandler.requests.memory }}
              cpu: {{ .Values.resources.daipPatcherHandler.requests.cpu }}
            limits:
              memory: {{ .Values.resources.daipPatcherHandler.limits.memory }}
              cpu: {{ .Values.resources.daipPatcherHandler.limits.cpu }}
          readinessProbe:
            failureThreshold: 30
            tcpSocket:
              port: 13001
            initialDelaySeconds: 15
            periodSeconds: 15
            successThreshold: 1
            timeoutSeconds: 15
          livenessProbe:
            failureThreshold: 30
            tcpSocket:
              port: 13001
            initialDelaySeconds: 15
            periodSeconds: 15
            successThreshold: 1
            timeoutSeconds: 15
      enableServiceLinks: false
      restartPolicy: {{ .Values.restartPolicy }}
      initContainers:
        - name: {{ .Release.Name }}-handler-init
          securityContext:
            privileged: false
            allowPrivilegeEscalation: false
            seccompProfile:
              type: "RuntimeDefault"
            seLinuxOptions:
              user: "system_u"
              role: "system_r"
              type: "container_t"
              level: "s0"
          {{- if .Values.serviceImage.tenant }}
          image: {{ .Values.serviceImage.repository }}/{{ .Values.serviceImage.tenant }}/@handler.microservice.name@:{{ .Values.serviceImage.version.daipPatcherHandler }}
          {{- else }}
          image: {{ .Values.serviceImage.repository }}/@handler.microservice.name@:{{ .Values.serviceImage.version.daipPatcherHandler }}
          {{- end }}
          imagePullPolicy: {{ quote .Values.serviceImage.pullPolicy }}
          env:
            - name: TZ
              value: {{ .Values.global.TZ | quote }}
            - name: DAIP_DEPLOYMENT_MODE
              value: {{ .Values.envs.commons.DAIP_DEPLOYMENT_MODE | quote }}
            - name: register_net_name
              value: {{ .Values.envs.commons.REGISTER_NET_NAME | quote }}
            - name: OPENPALETTE_MSB_IP
              value: {{ .Values.global.OPENPALETTE_MSB_IP | quote }}
            - name: OPENPALETTE_MSB_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_PORT | quote }}
            - name: OPENPALETTE_MSB_ROUTER_HTTPS_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_HTTPS_PORT | quote }}
            - name: OPENPALETTE_MSB_ROUTER_IP
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_IP | quote }}
            - name: OPENPALETTE_MSB_ROUTER_PORT
              value: {{ .Values.global.OPENPALETTE_MSB_ROUTER_PORT | quote }}
            - name: OPENPALETTE_NAMESPACE
              value: {{ .Values.global.OPENPALETTE_NAMESPACE | quote }}
          command:
            - /home/<USER>/daip-patcher-handler/bin/modify_pvc_permission.sh
          ports:
            - containerPort: 13003
              protocol: TCP
          volumeMounts:
            {{- range .Values.volumes.daipPatcherHandlerInit }}
            - name: {{ .name }}
              mountPath: {{ .mountPath }}
              readOnly: {{ .readOnly }}
            {{- end }}
            - mountPath: /cloud-disk-notification-volume/
              name: cloud-disk-notification-volume
          resources:
            requests:
              memory: {{ .Values.resources.daipPatcherHandlerInit.requests.memory }}
              cpu: {{ .Values.resources.daipPatcherHandlerInit.requests.cpu }}
            limits:
              memory: {{ .Values.resources.daipPatcherHandlerInit.limits.memory }}
              cpu: {{ .Values.resources.daipPatcherHandlerInit.limits.cpu }}
      volumes:
        {{- range .Values.volumes.daipPatcherHandler }}
        - name: {{ .name}}
          {{ .volumeType }}:
          {{ .volumeKey | indent 2 }}: {{ .volumeValue }}
          {{- if or (eq .volumeType "configMap") (eq .volumeType "secret") }}
          defaultMode: {{ .defaultMode }}
          {{- end }}
        {{- end }}
        - name: cloud-disk-notification-volume
          emptyDir: {}
      terminationGracePeriodSeconds: 30
      hostNetwork: false
      securityContext:
        sysctls: []
      hostIPC: false
  strategy:
    type: {{ .Values.strategy.type }}
    rollingUpdate:
      maxSurge: {{ .Values.strategy.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.strategy.rollingUpdate.maxUnavailable }}
