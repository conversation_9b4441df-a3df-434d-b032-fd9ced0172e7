apiVersion: {{ template "oki.msb.apiVersion" . }}
kind: MsbServiceSlice
metadata:
  name: {{ .Release.Name }}-iui
  namespace: {{ .Values.serviceImage.tenant }}
  labels:
    msb.zte.com.cn/service-name: @iui.microservice.name@
    msb.zte.com.cn/service-version: v1
    msb.zte.com.cn/release: {{ .Values.release }}
spec:
  selector:
    # 用于选择实际提供服务的实例，需要和 Deployment 配置中的spec.template.metadata.labels匹配 (key和value均一致)
    name: {{ .Release.Name }}-iui
  ports:
      - name: ui
        protocol: HTTP
        targetPort: 13002
  serviceInfo:
    url: /iui/@iui.microservice.name@
