{
  "kind": "Template",
  "apiVersion": "v1",
  "namespace": "${NAMESPACE}",
  "metadata": {
    "name": "daip-patcher",
    "labels": {
      "name": "daip-patcher"
    }
  },
  "objects": [
    {
      "kind": "Deployment",
      "apiVersion": "apps/v1",
      "metadata": {
        "name": "daip-patcher-svr",
        "namespace": "${NAMESPACE}"
      },
      "spec": {
        "selector": {
          "matchLabels": {
            "name": "daip-patcher-svr"
          }
        },
        "template": {
          "metadata": {
            "labels": {
              "name": "daip-patcher-svr",
              "daip-service": "@service.name@-svr",
              "namespace": "${NAMESPACE}"
            },
            "annotations": {
              "cnrm.knitter.io/enhance_config": "{\"containers\":[{\"name\":\"daip-patcher-svr\",\"oomKillDisable\":false},{\"name\":\"daip-patcher-init\",\"oomKillDisable\":false}]}",
              "zte.com.cn/numa_request": "{\"numaAffinityPolicy\":\"none\"}",
              "volume.hostpaths": "[{\"name\":\"daip-patch-upload\",\"quota\":\"20Gi\"}]"
              @k8s.networks@
            }
          },
          "spec": {
            "affinity": {
              "podAntiAffinity": {
                "preferredDuringSchedulingIgnoredDuringExecution": [
                  {
                    "weight": 100,
                    "podAffinityTerm": {
                      "labelSelector": {
                        "matchExpressions": [
                          {
                            "key": "daip-service",
                            "operator": "In",
                            "values": [
                              "@service.name@-svr"
                            ]
                          },
                          {
                            "key": "namespace",
                            "operator": "In",
                            "values": [
                              "${NAMESPACE}"
                            ]
                          }
                        ]
                      },
                      "topologyKey": "kubernetes.io/hostname"
                    }
                  }
                ]
              }
            },
            "containers": [
              {
                "name": "daip-patcher-svr",
                "image": "/@paas.tenants@/daip-patcher-svr:@paas.service.version@",
                "imagePullPolicy": "Always",
                "tty": false,
                "securityContext": {
                  "privileged": false,
                  "allowPrivilegeEscalation": false,
                  "runAsNonRoot": true,
                  "runAsUser": @non.root.uid@,
                  "seccompProfile": {
                    "type": "RuntimeDefault"
                  },
                  "seLinuxOptions":{
                    "user": "system_u",
                    "role": "system_r",
                    "type": "container_t",
                    "level": "s0"
                  }
                },
                "stdin": false,
                "command": [],
                "env": [
                  {
                    "name": "DAIP_DEPLOYMENT_MODE",
                    "value": "zdh-net"
                  },
                  {
                    "name": "TZ",
                    "value": "${TZ}"
                  },
                  {
                    "name": "jetty_threadpool_maxthreads",
                    "value": "100"
                  },
                  {
                    "name": "daip_patcher_daip_patcher_svr_jvm_xmx",
                    "value": "${daip-patcher_daip-patcher-svr_mem_limit}"
                  },
                  {
                    "name": "permit_root_start",
                    "value": "true"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_ADDRESS",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_PORT",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_ZOOKEEPER_PORT",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_ZOOKEEPER_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_ADDRESS",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_PORT",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_PASSWORD",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_PASSWORD]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_SENTINEL_ADDRESS",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_SENTINEL_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_SENTINEL_PORT",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_SENTINEL_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_SENTINEL_MASTERNAME",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_SENTINEL_MASTERNAME]"
                  },
                  {
                    "name": "OPENPALETTE_PG_ADDRESS",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_PG_PORT",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_PG_DBNAME",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_DBNAME]"
                  },
                  {
                    "name": "OPENPALETTE_PG_USERNAME",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_USERNAME]"
                  },
                  {
                    "name": "OPENPALETTE_PG_PASSWORD",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_PASSWORD]"
                  },
                  {
                    "name": "register_net_name",
                    "value": "eth0"
                  }
                ],
                "ports": [
                  {
                    "protocol": "TCP",
                    "containerPort": 56210
                  }
                ],
                "volumeMounts": [
                  {
                    "mountPath": "/data1/version",
                    "name": "daip-patcher",
                    "readOnly": false
                  },
                  {
                    "mountPath": "/home/<USER>/patch",
                    "name": "daip-patch-upload",
                    "readOnly": false
                  },
                  {
                    "mountPath": "/etc/secrets/oes/pg",
                    "name": "password-pg",
                    "readOnly": false
                  },
                  {
                    "mountPath": "/etc/secrets/oes/redis",
                    "name": "password-redis",
                    "readOnly": false
                  },
                  {
                    "name": "daip-certs",
                    "mountPath": "/home/<USER>/daip-certs",
                    "readOnly": true
                  }
                ],
                "resources": {
                  "requests": {
                    "cpu": "${daip-patcher_daip-patcher-svr_cpu_request}",
                    "memory": "${daip-patcher_daip-patcher-svr_mem_request}"
                  },
                  "limits": {
                    "cpu": "${daip-patcher_daip-patcher-svr_cpu_limit}",
                    "memory": "${daip-patcher_daip-patcher-svr_mem_limit}"
                  }
                },
                "readinessProbe": {
                  "failureThreshold": 30,
                  "tcpSocket": {
                    "port": 56210
                  },
                  "initialDelaySeconds": 15,
                  "periodSeconds": 15,
                  "successThreshold": 1,
                  "timeoutSeconds": 15
                },
                "livenessProbe": {
                  "failureThreshold": 30,
                  "tcpSocket": {
                    "port": 56210
                  },
                  "initialDelaySeconds": 15,
                  "periodSeconds": 15,
                  "successThreshold": 1,
                  "timeoutSeconds": 15
                }
              }
            ],
            "initContainers": [
              {
                "name": "daip-patcher-init",
                "image": "/@paas.tenants@/daip-patcher-init:@paas.service.version@",
                "imagePullPolicy": "Always",
                "tty": false,
                "securityContext": {
                  "privileged": false,
                  "allowPrivilegeEscalation": false,
                  "seccompProfile": {
                    "type": "RuntimeDefault"
                  },
                  "seLinuxOptions":{
                    "user": "system_u",
                    "role": "system_r",
                    "type": "container_t",
                    "level": "s0"
                  }
                },
                "stdin": false,
                "command": [],
                "env": [
                  {
                    "name": "DAIP_DEPLOYMENT_MODE",
                    "value": "zdh-net"
                  },
                  {
                    "name": "TZ",
                    "value": "${TZ}"
                  },
                  {
                    "name": "forceInstall",
                    "value": "${forceInstall}"
                  },
                  {
                    "name": "forceDelete",
                    "value": "${forceDelete}"
                  },
                  {
                    "name": "feagle.install.home",
                    "value": "${feagle.install.home}"
                  },
                  {
                    "name": "ZDH_HOME",
                    "value": "${ZDH_HOME}"
                  },
                  {
                    "name": "permit_root_start",
                    "value": "true"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_ADDRESS",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_PORT",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_ZOOKEEPER_PORT",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_ZOOKEEPER_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_ADDRESS",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_PORT",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_PASSWORD",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_PASSWORD]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_SENTINEL_ADDRESS",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_SENTINEL_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_SENTINEL_PORT",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_SENTINEL_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_REDIS_SENTINEL_MASTERNAME",
                    "value": "get_property:[${daip_patcher_redis},OPENPALETTE_REDIS_SENTINEL_MASTERNAME]"
                  },
                  {
                    "name": "OPENPALETTE_PG_ADDRESS",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_PG_PORT",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_PORT]"
                  },
                  {
                    "name": "OPENPALETTE_PG_DBNAME",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_DBNAME]"
                  },
                  {
                    "name": "OPENPALETTE_PG_USERNAME",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_USERNAME]"
                  },
                  {
                    "name": "OPENPALETTE_PG_PASSWORD",
                    "value": "get_property:[${daip_pgsql},OPENPALETTE_PG_PASSWORD]"
                  }
                ],
                "ports": [],
                "volumeMounts": [
                  {
                    "mountPath": "/data1/version",
                    "name": "daip-patcher",
                    "readOnly": false
                  },
                  {
                    "mountPath": "/home/<USER>/patch",
                    "name": "daip-patch-upload",
                    "readOnly": false
                  },
                  {
                    "name": "password-pg",
                    "mountPath": "/etc/secrets/oes/pg",
                    "readOnly": false
                  },
                  {
                    "name": "password-redis",
                    "mountPath": "/etc/secrets/oes/redis",
                    "readOnly": false
                  }
                ],
                "resources": {
                  "requests": {
                    "cpu": "0.1",
                    "memory": "0.1Gi"
                  },
                  "limits": {
                    "cpu": "1",
                    "memory": "1Gi"
                  }
                }
              }
            ],
            "restartPolicy": "Always",
            "volumes": [
              {
                "name": "daip-patcher",
                "${daip-patcher_type}": {
                  "${daip-patcher_key}": "${daip-patcher_value}"
                }
              },
              {
                "name": "daip-patch-upload",
                "${daip-patch-upload_type}": {
                  "${daip-patch-upload_key}": "${daip-patch-upload_value}"
                }
              },
              {
                "name": "password-pg",
                "${password-pg_type}": {
                  "${password-pg_key}": "${password-pg_value}"
                }
              },
              {
                "name": "password-redis",
                "${password-redis_type}": {
                  "${password-redis_key}": "${password-redis_value}"
                }
              },
              {
                "name": "daip-certs",
                "secret": {
                  "secretName": "daip-certs"
                }
              }
            ],
            "terminationGracePeriodSeconds": 30,
            "hostNetwork": false,
            "securityContext": {
              "sysctls": []
            },
            "hostIPC": false
          }
        },
        "replicas": "${daip-patcher_daip-patcher-svr_replica_init}",
        "strategy": {
          "type": "RollingUpdate",
          "rollingUpdate": {
            "maxSurge": "25%",
            "maxUnavailable": "25%"
          }
        }
      }
    },
    {
      "kind": "Deployment",
      "apiVersion": "apps/v1",
      "metadata": {
        "name": "daip-patcher-iui",
        "namespace": "${NAMESPACE}"
      },
      "spec": {
        "selector": {
          "matchLabels": {
            "name": "daip-patcher-iui"
          }
        },
        "template": {
          "metadata": {
            "labels": {
              "name": "daip-patcher-iui"
            },
            "annotations": {
              "cnrm.knitter.io/enhance_config": "{\"containers\":[{\"name\":\"daip-patcher-iui\",\"oomKillDisable\":false}]}",
              "zte.com.cn/numa_request": "{\"numaAffinityPolicy\":\"none\"}"
            }
          },
          "spec": {
            "containers": [
              {
                "name": "daip-patcher-iui",
                "image": "/@paas.tenants@/daip-patcher-iui:@paas.service.version@",
                "imagePullPolicy": "Always",
                "tty": false,
                "securityContext": {
                  "privileged": false,
                  "allowPrivilegeEscalation": false,
                  "runAsNonRoot": true,
                  "runAsUser": @non.root.uid@,
                  "seccompProfile": {
                    "type": "RuntimeDefault"
                  },
                  "seLinuxOptions":{
                    "user": "system_u",
                    "role": "system_r",
                    "type": "container_t",
                    "level": "s0"
                  }
                },
                "stdin": false,
                "command": [],
                "env": [
                  {
                    "name": "DAIP_DEPLOYMENT_MODE",
                    "value": "zdh-net"
                  },
                  {
                    "name": "TZ",
                    "value": "${TZ}"
                  },
                  {
                    "name": "permit_root_start",
                    "value": "true"
                  },
                  {
                    "name": "register_net_name",
                    "value": "eth0"
                  }
                ],
                "ports": [
                  {
                    "protocol": "TCP",
                    "containerPort": 13002
                  }
                ],
                "volumeMounts": [],
                "resources": {
                  "requests": {
                    "cpu": "0.1",
                    "memory": "0.1Gi"
                  },
                  "limits": {
                    "cpu": "1",
                    "memory": "1Gi"
                  }
                },
                "readinessProbe": {
                  "failureThreshold": 30,
                  "tcpSocket": {
                    "port": 13002
                  },
                  "initialDelaySeconds": 15,
                  "periodSeconds": 15,
                  "successThreshold": 1,
                  "timeoutSeconds": 15
                },
                "livenessProbe": {
                  "failureThreshold": 30,
                  "tcpSocket": {
                    "port": 13002
                  },
                  "initialDelaySeconds": 15,
                  "periodSeconds": 15,
                  "successThreshold": 1,
                  "timeoutSeconds": 15
                }
              }
            ],
            "initContainers": [],
            "restartPolicy": "Always",
            "volumes": [],
            "terminationGracePeriodSeconds": 30,
            "hostNetwork": false,
            "securityContext": {
              "sysctls": []
            },
            "hostIPC": false
          }
        },
        "replicas": "1",
        "strategy": {
          "type": "RollingUpdate",
          "rollingUpdate": {
            "maxSurge": "25%",
            "maxUnavailable": "25%"
          }
        }
      }
    },
    {
      "kind": "Deployment",
      "apiVersion": "apps/v1",
      "metadata": {
        "name": "daip-patcher-handler",
        "namespace": "${NAMESPACE}"
      },
      "spec": {
        "selector": {
          "matchLabels": {
            "name": "daip-patcher-handler"
          }
        },
        "template": {
          "metadata": {
            "labels": {
              "name": "daip-patcher-handler"
            },
            "annotations": {
              "cnrm.knitter.io/enhance_config": "{\"containers\":[{\"name\":\"daip-patcher-handler\",\"oomKillDisable\":false}]}",
              "zte.com.cn/numa_request": "{\"numaAffinityPolicy\":\"none\"}"
              @k8s.networks@
            }
          },
          "spec": {
            "containers": [
              {
                "name": "daip-patcher-handler",
                "image": "/@paas.tenants@/daip-patcher-handler:@paas.service.version@",
                "imagePullPolicy": "Always",
                "tty": false,
                "securityContext": {
                  "privileged": false,
                  "allowPrivilegeEscalation": false,
                  "runAsNonRoot": true,
                  "runAsUser": @non.root.uid@,
                  "seccompProfile": {
                    "type": "RuntimeDefault"
                  },
                  "seLinuxOptions":{
                    "user": "system_u",
                    "role": "system_r",
                    "type": "container_t",
                    "level": "s0"
                  }
                },
                "stdin": false,
                "command": [],
                "env": [
                  {
                    "name": "DAIP_DEPLOYMENT_MODE",
                    "value": "zdh-net"
                  },
                  {
                    "name": "TZ",
                    "value": "${TZ}"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_ADDRESS",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_ADDRESS]"
                  },
                  {
                    "name": "OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS",
                    "value": "get_property:[${daip_kafka},OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS]"
                  },
                  {
                    "name": "jetty_threadpool_maxthreads",
                    "value": "100"
                  },
                  {
                    "name": "register_net_name",
                    "value": "eth0"
                  }
                ],
                "ports": [
                  {
                    "protocol": "TCP",
                    "containerPort": 13001
                  }
                ],
                "volumeMounts": [
                  {
                    "name": "daip-patcher",
                    "mountPath": "/data1/version",
                    "readOnly": false
                  }
                ],
                "resources": {
                  "requests": {
                    "cpu": "0.5",
                    "memory": "2Gi"
                  },
                  "limits": {
                    "cpu": "2",
                    "memory": "4Gi"
                  }
                },
                "readinessProbe": {
                  "failureThreshold": 30,
                  "tcpSocket": {
                    "port": 13001
                  },
                  "initialDelaySeconds": 15,
                  "periodSeconds": 15,
                  "successThreshold": 1,
                  "timeoutSeconds": 15
                },
                "livenessProbe": {
                  "failureThreshold": 30,
                  "tcpSocket": {
                    "port": 13001
                  },
                  "initialDelaySeconds": 15,
                  "periodSeconds": 15,
                  "successThreshold": 1,
                  "timeoutSeconds": 15
                }
              }
            ],
            "initContainers": [{
              "name": "daip-patcher-handler-init",
              "image": "/@paas.tenants@/daip-patcher-handler:@paas.service.version@",
              "imagePullPolicy": "Always",
              "tty": false,
              "securityContext": {
                "privileged": false,
                "allowPrivilegeEscalation": false,
                "seccompProfile": {
                  "type": "RuntimeDefault"
                },
                "seLinuxOptions":{
                  "user": "system_u",
                  "role": "system_r",
                  "type": "container_t",
                  "level": "s0"
                }
              },
              "stdin": false,
              "command": ["/home/<USER>/daip-patcher-handler/bin/modify_pvc_permission.sh"],
              "env": [
                {
                  "name": "DAIP_DEPLOYMENT_MODE",
                  "value": "zdh-net"
                },
                {
                  "name": "TZ",
                  "value": "${TZ}"
                },
                {
                  "name": "register_net_name",
                  "value": "eth0"
                }
              ],
              "ports": [
                {
                  "protocol": "TCP",
                  "containerPort": 13003
                }
              ],
              "volumeMounts": [
                {
                  "name": "daip-patcher",
                  "mountPath": "/data1/version",
                  "readOnly": false
                }
              ],
              "resources": {
                "requests": {
                  "cpu": "0.1",
                  "memory": "0.1Gi"
                },
                "limits": {
                  "cpu": "1",
                  "memory": "1Gi"
                }
              }
            }],
            "restartPolicy": "Always",
            "volumes": [
              {
                "name": "daip-patcher",
                "${daip-patcher_type}": {
                  "${daip-patcher_key}": "${daip-patcher_value}"
                }
              }
            ],
            "terminationGracePeriodSeconds": 30,
            "hostNetwork": false,
            "securityContext": {
              "sysctls": []
            },
            "hostIPC": false
          }
        },
        "replicas": "${daip-patcher_daip-patcher-svr_replica_init}",
        "strategy": {
          "type": "RollingUpdate",
          "rollingUpdate": {
            "maxSurge": "25%",
            "maxUnavailable": "25%"
          }
        }
      }
    }
  ],
  "vnpm_param": {
    "vnpm_object": [
      {
        "name": "daip-patcher-svr",
        "route_list": [
          {
            "serviceName": "${daip-patcher-svr}",
            "protocol": "REST",
            "port": "56210",
            "visualRange": "0",
            "network_plane_type": "@default.msb.network.plane.type@",
            "version": "v1",
            "nic_name": "eth0",
            "url": "/api/daip-patcher-svr/v1",
            "path": "/api/daip-patcher-svr/v1",
            "lb_policy": "round-robin",
            "function": "std",
            "enable_client_verify": false,
            "enable_tls": false,
            "enable_http2": false,
            "enable_ssl": false
          }
        ],
        "common_service": [
          {
            "logicName": "${daip_kafka}"
          },
          {
            "logicName": "${daip_patcher_redis}"
          },
          {
            "logicName": "${daip_pgsql}"
          }
        ],
        "networks": {
          "ports": [
            {
              "attach_to_network": "net_api",
              "attributes": {
                "nic_name": "eth0",
                "function": "std",
                "nic_type": "normal",
                "accelerate": "false",
                "combinable": "true"
              }
            },
            {
              "attach_to_network": "lan",
              "attributes": {
                "nic_name": "eth1",
                "function": "std",
                "nic_type": "normal",
                "accelerate": "false",
                "combinable": "true"
              }
            }
          ],
          "version": "v2"
        },
        "isUseServiceDiscovery": true,
        "cluster_info": {
          "cluster_type": "kubernetes",
          "labelselector": []
        },
        "microservice_labels": {}
      },
      {
        "name": "daip-patcher-iui",
        "route_list": [
          {
            "serviceName": "${daip-patcher-iui}",
            "protocol": "UI",
            "port": "13002",
            "visualRange": "0",
            "network_plane_type": "@default.msb.network.plane.type@",
            "version": "v1",
            "nic_name": "eth0",
            "url": "/iui/daip-patcher-iui",
            "lb_policy": "round-robin",
            "function": "std",
            "enable_client_verify": false,
            "enable_tls": false,
            "enable_http2": false,
            "enable_ssl": false
          }
        ],
        "common_service": [],
        "networks": {
          "ports": [
            {
              "attach_to_network": "net_api",
              "attributes": {
                "nic_name": "eth0",
                "function": "std",
                "nic_type": "normal",
                "accelerate": "false",
                "combinable": "true",
                "layer_type": "layer3"
              }
            },
            {
              "attach_to_network": "lan",
              "attributes": {
                "nic_name": "eth1",
                "function": "std",
                "nic_type": "normal",
                "accelerate": "false",
                "combinable": "true",
                "layer_type": "layer3"
              }
            }
          ],
          "version": "v2"
        },
        "isUseServiceDiscovery": false,
        "cluster_info": {
          "cluster_type": "kubernetes",
          "labelselector": []
        },
        "microservice_labels": {}
      },
      {
        "name": "daip-patcher-handler",
        "route_list": [
          {
            "serviceName": "${daip-patcher-handler}",
            "protocol": "REST",
            "port": "13001",
            "visualRange": "0",
            "network_plane_type": "@default.msb.network.plane.type@",
            "version": "v1",
            "function": "std",
            "lb_policy": "round-robin",
            "url": "/api/daip-patcher-handler/v1",
            "path": "/api/daip-patcher-handler/v1",
            "enable_client_verify": false,
            "enable_ssl": false,
            "enable_http2": false
          }
        ],
        "common_service": [
          {
            "logicName": "${daip_kafka}"
          }
        ],
        "networks": {
          "ports": [
            {
              "attach_to_network": "net_api",
              "attributes": {
                "nic_name": "eth0",
                "function": "std",
                "nic_type": "normal",
                "accelerate": "false",
                "combinable": "true"
              }
            },
            {
              "attach_to_network": "lan",
              "attributes": {
                "nic_name": "eth1",
                "function": "std",
                "nic_type": "normal",
                "accelerate": "false",
                "combinable": "true"
              }
            }
          ],
          "version": "v2"
        },
        "isUseServiceDiscovery": true,
        "cluster_info": {
          "cluster_type": "kubernetes",
          "labelselector": []
        },
        "microservice_labels": {}
      }
    ],
    "service_labels": {
      "app-version": "@paas.service.version@",
      "app-name": "daip-patcher"
    }
  },
  "eps_param": {
    "auto_policy": {
      "daip-patcher-svr": {
        "daip-patcher-svr": [{
          "param_name": "cpu_usage_rate",
          "defaultValue": [10, 90],
          "isDefault": true,
          "value": ""
        }, {
          "param_name": "memory_usage_rate",
          "defaultValue": [20, 80],
          "isDefault": true,
          "value": ""
        }],
        "daip-patcher-init": []
      },
      "daip-patcher-iui": {
        "daip-patcher-iui": []
      },
      "daip-patcher-handler": {
        "daip-patcher-handler": []
      }
    },
    "scale_alg": {
      "daip-patcher-svr": {
        "scale_decision_period": 30,
        "scale_in_forbidden_window": 300,
        "scale_start_delay": 300,
        "kpi_sample_num": 2,
        "scale_out_forbidden_window": 180,
        "step_params": {
          "step_mode": "auto"
        }
      },
      "daip-patcher-iui": {
        "scale_decision_period": 30,
        "scale_in_forbidden_window": 300,
        "scale_start_delay": 300,
        "kpi_sample_num": 2,
        "scale_out_forbidden_window": 180,
        "step_params": {
          "step_mode": "auto"
        }
      },
      "daip-patcher-handler": {
        "scale_in_forbidden_window": 300,
        "scale_out_forbidden_window": 180,
        "scale_decision_period": 30,
        "kpi_sample_num": 2,
        "scale_start_delay": 300,
        "step_params": {
          "step_mode": "auto"
        }
      }
    },
    "replicasPara_list": [
      {
        "ms_name": "daip-patcher-svr",
        "replicasMin": "${daip-patcher_daip-patcher-svr_replica_min}",
        "replicasMax": "${daip-patcher_daip-patcher-svr_replica_max}"
      },
      {
        "ms_name": "daip-patcher-iui",
        "replicasMin": "1",
        "replicasMax": "1"
      },
      {
        "ms_name": "daip-patcher-handler",
        "replicasMin": "${daip-patcher_daip-patcher-svr_replica_min}",
        "replicasMax": "${daip-patcher_daip-patcher-svr_replica_max}"
      }
    ],
    "pod_migration": {
      "daip-patcher-svr": {
        "enable": false,
        "migration_mode": "deleted_first"
      },
      "daip-patcher-iui": {
        "enable": false,
        "migration_mode": "deleted_first"
      },
      "daip-patcher-handler": {
        "enable": false,
        "migration_mode": "deleted_first"
      }
    }
  },
  "parameters": [
    {
      "name": "daip_kafka",
      "displayName": "daip_kafka",
      "description": "daip_kafka",
      "value": "",
      "section": "commonService",
      "subSection": "Kafka",
      "type": "string"
    },
    {
      "name": "daip_patcher_redis",
      "displayName": "daip_patcher_redis",
      "description": "daip_patcher_redis",
      "value": "",
      "section": "commonService",
      "subSection": "redis",
      "type": "string"
    },
    {
      "name": "daip_pgsql",
      "displayName": "daip_pgsql",
      "description": "daip_pgsql",
      "value": "",
      "section": "commonService",
      "subSection": "PostgreSQL",
      "type": "string"
    },
    {
      "name": "forceInstall",
      "displayName": "forceInstall",
      "value": "false",
      "section": "env",
      "type": "string",
      "description": ""
    },
    {
      "name": "forceDelete",
      "displayName": "forceDelete",
      "value": "false",
      "section": "env",
      "type": "string",
      "description": ""
    },
    {
      "name": "TZ",
      "displayName": "TZ",
      "value": "Asia/Shanghai",
      "section": "env",
      "type": "string",
      "description": ""
    },
    {
      "name": "jetty_threadpool_maxthreads",
      "displayName": "jetty_threadpool_maxthreads",
      "value": "100",
      "section": "env",
      "type": "string",
      "description": ""
    },
    {
      "name": "feagle.install.home",
      "displayName": "feagle.install.home",
      "value": "/opt/Feagle/",
      "section": "env",
      "type": "string",
      "description": ""
    },
    {
      "name": "ZDH_HOME",
      "displayName": "ZDH_HOME",
      "value": "/opt/ZDH/",
      "section": "env",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher_daip-patcher-svr_replica_init",
      "displayName": "daip-patcher_daip-patcher-svr_replica_init",
      "value": "1",
      "section": "other",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher_daip-patcher-svr_replica_min",
      "displayName": "daip-patcher_daip-patcher-svr_replica_min",
      "value": "1",
      "section": "other",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher_daip-patcher-svr_replica_max",
      "displayName": "daip-patcher_daip-patcher-svr_replica_max",
      "value": "5",
      "section": "other",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher_daip-patcher-svr_cpu_request",
      "displayName": "daip-patcher_daip-patcher-svr_cpu_request",
      "value": "2",
      "section": "other",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher_daip-patcher-svr_cpu_limit",
      "displayName": "daip-patcher_daip-patcher-svr_cpu_limit",
      "value": "8",
      "section": "other",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher_daip-patcher-svr_mem_request",
      "displayName": "daip-patcher_daip-patcher-svr_mem_request",
      "value": "2G",
      "section": "other",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher_daip-patcher-svr_mem_limit",
      "displayName": "daip-patcher_daip-patcher-svr_mem_limit",
      "value": "8G",
      "section": "other",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher_type",
      "displayName": "daip-patcher",
      "description": "",
      "value": "persistentVolumeClaim",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "daip-patcher_key",
      "displayName": "daip-patcher",
      "description": "",
      "value": "claimName",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "daip-patcher_value",
      "displayName": "daip-patcher",
      "description": "",
      "value": "/data1/version",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "daip-patch-upload_type",
      "displayName": "daip-patch-upload",
      "description": "",
      "value": "hostPath",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "daip-patch-upload_key",
      "displayName": "daip-patch-upload",
      "description": "",
      "value": "path",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "daip-patch-upload_value",
      "displayName": "daip-patch-upload",
      "description": "",
      "value": "/paasdata/op-tenant/ranoss/daip/version/patch",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "password-pg_type",
      "displayName": "password-pg",
      "description": "",
      "value": "secret",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "password-pg_key",
      "displayName": "password-pg",
      "description": "",
      "value": "secretName",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "password-pg_value",
      "displayName": "password-pg",
      "description": "",
      "value": "get_property:[${daip_pgsql},openpalette_secret_name]",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "password-redis_type",
      "displayName": "password-redis",
      "description": "",
      "value": "secret",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "password-redis_key",
      "displayName": "password-redis",
      "description": "",
      "value": "secretName",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "password-redis_value",
      "displayName": "password-redis",
      "description": "",
      "value": "get_property:[${daip_patcher_redis},openpalette_secret_name]",
      "section": "volumes",
      "type": "string"
    },
    {
      "name": "appName",
      "displayName": "appName",
      "value": "daip-patcher",
      "section": "route",
      "type": "string",
      "description": ""
    },
    {
      "name": "appVersion",
      "displayName": "appVersion",
      "value": "@paas.service.version@",
      "section": "route",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher-svr",
      "displayName": "daip-patcher-svr",
      "value": "daip-patcher-svr",
      "section": "route",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher-iui",
      "displayName": "daip-patcher-iui",
      "value": "daip-patcher-iui",
      "section": "route",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher-handler",
      "displayName": "daip-patcher-handler",
      "value": "daip-patcher-handler",
      "section": "route",
      "type": "string",
      "description": ""
    },
    {
      "name": "daip-patcher-svr-vm",
      "displayName": "daip-patcher-svr-vm",
      "value": "daip-patcher-svr-vm",
      "section": "route",
      "type": "string",
      "description": ""
    },
    {
      "name": "NAMESPACE",
      "displayName": "",
      "description": "",
      "value": "zenap",
      "section": "None",
      "type": "string"
    }
  ],
  "view_params": [
    {
      "name": "net_api",
      "position": {
        "width": 6,
        "top": 100,
        "height": 600,
        "left": 720
      },
      "children": []
    },
    {
      "name": "lan",
      "position": {
        "width": 6,
        "top": 100,
        "height": 600,
        "left": 750
      },
      "children": []
    },
    {
      "name": "daip_kafka",
      "position": {
        "width": 180,
        "top": 100,
        "height": 50,
        "left": 80
      },
      "children": []
    },
    {
      "name": "daip_patcher_redis",
      "position": {
        "width": 180,
        "top": 180,
        "height": 50,
        "left": 80
      },
      "children": []
    },
    {
      "name": "daip_pgsql",
      "position": {
        "width": 180,
        "top": 260,
        "height": 50,
        "left": 80
      },
      "children": []
    },
    {
      "name": "daip-patcher",
      "position": {
        "width": 180,
        "top": 100,
        "height": 50,
        "left": 350
      },
      "children": [
        {
          "name": "daip-patcher-svr",
          "position": {
            "width": 180,
            "top": 6,
            "height": 50,
            "left": 39
          },
          "children": [
            {
              "name": "daip-patcher-svr",
              "position": {
                "width": 180,
                "top": 0,
                "height": 50,
                "left": 0
              },
              "children": [
                {
                  "name": "daip-patcher-svr",
                  "position": {
                    "width": 16,
                    "top": 0,
                    "height": 16,
                    "left": 0
                  },
                  "children": []
                }
              ]
            },
            {
              "name": "daip-patcher-init",
              "position": {
                "width": 180,
                "top": 60,
                "height": 50,
                "left": 41
              },
              "children": []
            },
            {
              "name": "eth0",
              "position": {
                "width": 16,
                "top": 0,
                "height": 16,
                "left": 0
              },
              "children": []
            },
            {
              "name": "eth1",
              "position": {
                "width": 16,
                "top": 0,
                "height": 16,
                "left": 0
              },
              "children": []
            }
          ]
        },
        {
          "name": "daip-patcher-iui",
          "position": {
            "width": 180,
            "top": 157,
            "height": 50,
            "left": 57
          },
          "children": [
            {
              "name": "daip-patcher-iui",
              "position": {
                "width": 180,
                "top": 26,
                "height": 50,
                "left": 33
              },
              "children": [
                {
                  "name": "daip-patcher-iui",
                  "position": {
                    "width": 16,
                    "top": 26,
                    "height": 16,
                    "left": 13
                  },
                  "children": []
                }
              ]
            },
            {
              "name": "eth0",
              "position": {
                "width": 16,
                "height": 16
              },
              "children": []
            },
            {
              "name": "eth1",
              "position": {
                "width": 16,
                "height": 16
              },
              "children": []
            }
          ]
        },
        {
          "name": "daip-patcher-handler",
          "position": {
            "width": 180,
            "height": 50,
            "left": 59,
            "top": 387
          },
          "children": [
            {
              "name": "eth0",
              "position": {
                "width": 16,
                "height": 16
              },
              "children": []
            },
            {
              "name": "eth1",
              "position": {
                "width": 16,
                "height": 16
              },
              "children": []
            },
            {
              "name": "daip-patcher-handler",
              "position": {
                "width": 180,
                "height": 50,
                "left": 64,
                "top": 20
              },
              "children": [
                {
                  "name": "daip-patcher-svr",
                  "position": {
                    "width": 16,
                    "height": 16,
                    "left": 17,
                    "top": 30
                  },
                  "children": []
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}