fullname:
  override: ""
  prefix: ""
  suffix: ""

idn:
  pbcEnabled: false

serviceImage:
  repository: swr:2512
  tenant: ranoss
  version:
    daipPatcherSvr: @paas.service.version@
    daipPatcherInit: @paas.service.version@
    daipPatcherIui: @paas.service.version@
    daipPatcherHandler: @paas.service.version@
  pullPolicy: Always
  initImageName: @init.microservice.name@
release: @paas.service.version@

replicas:
  daipPatcherSvr:
    init: 1
    min: 1
    max: 3
  daipPatcherHandler:
    init: 1
    min: 1
    max: 3

resources:
  daipPatcherSvr:
    limits:
      cpu: 4
      memory: 4Gi
    requests:
      cpu: 1
      memory: 1Gi
  daipPatcherInit:
    limits:
      cpu: 1
      memory: 1Gi
    requests:
      cpu: 0.1
      memory: 0.1Gi
  daipPatcherIui:
    limits:
      cpu: 1
      memory: 1Gi
    requests:
      cpu: 0.5
      memory: 500Mi
  daipPatcherHandler:
    limits:
      cpu: 2
      memory: 4Gi
    requests:
      cpu: 1
      memory: 1Gi
  daipPatcherHandlerInit:
    limits:
      cpu: 1
      memory: 1Gi
    requests:
      cpu: 0.1
      memory: 500Mi

annotationsPatcher:
  k8s.v1.cni.cncf.io/networks: '[]'

securityContext:
  privileged: false
  runAsNonRoot: true
  runAsUser: @non.root.uid@
  runAsGroup: @non.root.gid@
  allowPrivilegeEscalation: false
  seccompProfile:
    type: "RuntimeDefault"
  seLinuxOptions:
    user: "system_u"
    role: "system_r"
    type: "container_t"
    level: "s0"

envs:
  commons:
    jetty_threadpool_maxthreads: 100
    DAIP_DEPLOYMENT_MODE: zdh-net
    REGISTER_NET_NAME: eth2
    daip_patcher_daip_patcher_svr_jvm_xmx: 8G
    FORCE_INSTALL: false
    FORCE_DELETE: false
    FEAGLE_INSTALL_HOME: /opt/Feagle/
    ZDH_HOME: /opt/ZDH/
    PERMITROOTSTART: true
  deployer:
    - name: product
      value: daip

kafkaConfig:
  OPENPALETTE_KAFKA_ADDRESS: ""
  OPENPALETTE_KAFKA_PORT: 9092
  scene: tcf

pgConfig:
  OPENPALETTE_PG_ADDRESS: ""
  OPENPALETTE_PG_PORT: ""
  OPENPALETTE_PG_DBNAME: ""
  OPENPALETTE_PG_USERNAME: ""
  OPENPALETTE_PG_PASSWORD: ""

redisConfig:
  OPENPALETTE_REDIS_ADDRESS: ""
  OPENPALETTE_REDIS_PORT: ""
  OPENPALETTE_REDIS_PASSWORD: ""
  OPENPALETTE_REDIS_SENTINEL_ADDRESS: ""
  OPENPALETTE_REDIS_SENTINEL_PORT: ""
  OPENPALETTE_REDIS_SENTINEL_MASTERNAME: ""

global:
  TZ: Asia/Shanghai
  OPENPALETTE_NAMESPACE: zenap
  OPENPALETTE_MSB_IP: ""
  OPENPALETTE_MSB_PORT: 10081
  OPENPALETTE_MSB_ROUTER_IP: ""
  OPENPALETTE_MSB_ROUTER_PORT: 80
  OPENPALETTE_MSB_ROUTER_HTTPS_PORT: 443
  tsmApiGroupPrefix: ""
  usePlatformMsb: true
  okiDomain: ""

apiVersionMigrateTab:
  autoscaling/HorizontalPodAutoscaler: "v2beta2"

volumes:
  daipPatcherSvr:
    - name: password-pg
      readOnly: false
      volumeType: secret
      volumeKey: secretName
      mountPath: /etc/secrets/oes/pg
      volumeValue: ""
    - name: password-redis
      readOnly: false
      volumeType: secret
      volumeKey: secretName
      volumeValue: ""
      mountPath: /etc/secrets/oes/redis
    - name: daip-patcher
      readOnly: false
      volumeType: persistentVolumeClaim
      volumeKey: ""
      volumeValue: ""
      mountPath: /data1/version
    - name: daip-patch-upload
      readOnly: false
      volumeType: hostPath
      volumeKey: ""
      volumeValue: ""
      mountPath: /home/<USER>/patch
    - name: daip-certs
      readOnly: true
      volumeType: secret
      volumeKey: ""
      volumeValue: ""
      mountPath: /home/<USER>/daip-certs
  daipPatcherInit:
    - name: password-pg
      readOnly: false
      volumeType: secret
      volumeKey: secretName
      mountPath: /etc/secrets/oes/pg
      volumeValue: ""
    - name: password-redis
      readOnly: false
      volumeType: secret
      volumeKey: secretName
      volumeValue: ""
      mountPath: /etc/secrets/oes/redis
    - name: daip-patcher
      readOnly: false
      volumeType: persistentVolumeClaim
      volumeKey: ""
      volumeValue: ""
      mountPath: /data1/version
    - name: daip-patch-upload
      readOnly: false
      volumeType: hostPath
      volumeKey: ""
      volumeValue: ""
      mountPath: /home/<USER>/patch
  daipPatcherHandler:
    - name: daip-patcher
      readOnly: false
      volumeType: persistentVolumeClaim
      volumeKey: ""
      volumeValue: ""
      mountPath: /data1/version
  daipPatcherHandlerInit:
    - name: daip-patcher
      readOnly: false
      volumeType: persistentVolumeClaim
      volumeKey: ""
      volumeValue: ""
      mountPath: /data1/version

restartPolicy: Always
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%
labelselector: []
