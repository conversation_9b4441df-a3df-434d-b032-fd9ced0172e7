{{- if and .Values.idn.pbcEnabled -}}
apiVersion: {{ template "oki.pbc.apiVersion" . }}
kind: Msb<PERSON><PERSON>
{{- else }}
apiVersion: {{ template "oki.msb.apiVersion" . }}
kind: Ms<PERSON><PERSON><PERSON>e
{{- end }}
metadata:
  name: {{ .Release.Name }}-iui
  namespace: {{ .Values.serviceImage.tenant }}
  labels:
    # 管理pbc组件名
    componentName: {{ .Release.Name }}-pbc
    # 关联pbc组件api服务名
    pbc.apiServiceName: @iui.microservice.name@
spec:
  httpRules:
    - match:
        path: /iui/@iui.microservice.name@
        protocol: UI
        rewriteTarget: /iui/@iui.microservice.name@
      backend:
        service:
          name: {{ .Release.Name }}-iui
          portName: ui
      advancedConfig:
        lbPolicy: round-robin
