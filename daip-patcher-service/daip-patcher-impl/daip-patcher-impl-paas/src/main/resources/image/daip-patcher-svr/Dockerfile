FROM zenap/@dexcloud.image.name@:<EMAIL>@

MAINTAINER "10231312"

WORKDIR /home/<USER>

ENV HTTP_PORT=56210
ENV INSTALL_PATH=/home/<USER>/@svr.microservice.name@/

ADD @svr.microservice.name@*.tar.gz /home/<USER>
ADD daip-deployer-arm-*.tar /
ADD daip-deployer-x86-*.tar /
ADD cmd.sh /

RUN sh /cmd.sh /apk && rm -rf /usr/bin/nc

EXPOSE ${HTTP_PORT}

RUN mkdir -p /opt/dap_security && chown @non.root.uid@:@non.root.uid@ /home/<USER>/opt/dap_security

CMD /home/<USER>/@svr.microservice.name@/bin/run.sh
