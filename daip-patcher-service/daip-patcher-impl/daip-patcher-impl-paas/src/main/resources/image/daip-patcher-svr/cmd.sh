#!/bin/sh

packagename="zip bash coreutils openssl"

function installApk(){
    cd ${apkpath}

    apk add --allow-untrusted ${packagename}

    afterinstall
}

function afterinstall(){
    #
    echo "alias ll='ls -l --color=auto'" >> /etc/profile
    alias ll='ls -l --color=auto'
    ln -sf /bin/bash /bin/sh
    source /etc/profile;
}

function cmd_aarch64(){
    chmod 750 /home/<USER>/@svr.microservice.name@/bin/*.sh && \
    rpm -q tar && rm -rf /apk/tar-*.rpm && \
    rpm -q unzip && rm -rf /apk/unzip-*.rpm && \
    rpm -i /apk/*.rpm
}

function cmd_x86_64(){
    chmod 750 /home/<USER>/@svr.microservice.name@/bin/*.sh && dos2unix /home/<USER>/@svr.microservice.name@/bin/*.sh && \
    echo "file://apk/" > /etc/apk/repositories && \
    installApk
}

apkpath=$1

sys_version=$(cat /proc/version | awk '{print toupper($0)}')
if [[ "${sys_version}" =~ AARCH64 ]]; then
    cmd_aarch64
else
    apkpath=$1/x86_64
    cmd_x86_64
fi


