#!/bin/sh

function cmd_aarch64(){
    chmod 750 /home/<USER>/@init.microservice.name@/bin/*.sh && \
    rpm -q tar && rm -rf /apk/tar-*.rpm && \
    rpm -q unzip && rm -rf /apk/unzip-*.rpm
}

function cmd_x86_64(){
    chmod 750 /home/<USER>/@init.microservice.name@/bin/*.sh && \
    dos2unix /home/<USER>/@init.microservice.name@/bin/*.sh
}

sys_version=$(cat /proc/version | awk '{print toupper($0)}')
if [[ "${sys_version}" =~ AARCH64 ]]; then
    cmd_aarch64
else
    cmd_x86_64
fi


