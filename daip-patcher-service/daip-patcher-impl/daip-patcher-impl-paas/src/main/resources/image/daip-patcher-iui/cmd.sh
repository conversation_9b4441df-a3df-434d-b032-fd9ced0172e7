#!/bin/sh

function cmd_aarch64(){
    mv /usr/share/nginx/webroot/iui/@iui.common.microservice.name@ /usr/share/nginx/webroot/iui/@iui.microservice.name@/
}

function cmd_x86_64(){
    mv /usr/share/nginx/webroot/iui/@iui.common.microservice.name@ /usr/share/nginx/webroot/iui/@iui.microservice.name@/
}

sys_version=$(cat /proc/version | awk '{print toupper($0)}')
if [[ "${sys_version}" =~ AARCH64 ]]; then
    cmd_aarch64
else
    cmd_x86_64
fi


