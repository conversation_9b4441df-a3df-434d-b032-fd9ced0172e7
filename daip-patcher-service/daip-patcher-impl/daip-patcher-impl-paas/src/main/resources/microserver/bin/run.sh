#!/bin/sh

DIRNAME=`dirname $0`
RUNHOME=`cd $DIRNAME/; pwd`
echo @RUNHOME@ $RUNHOME

if [ -f "/home/<USER>/initGlobalEnv.sh" ]; then
  sed -i "s/jaeger:14250/daip-jaeger-collector:14250/g" "/home/<USER>/initGlobalEnv.sh"
  . "/home/<USER>/initGlobalEnv.sh"
else
echo "can not found /home/<USER>/initGlobalEnv.sh"
fi

if [ -f "$RUNHOME/setenv.sh" ]; then
. "$RUNHOME/setenv.sh"
else
echo "can not found $RUNHOME/setenv.sh"
fi

DIRNAME=`dirname $0`
RUNHOME=`cd $DIRNAME/; pwd`
echo @RUNHOME@ $RUNHOME

get_propval()
{
  if [ -f "$1" ]; then
    grep -w "$2" "$1" | cut -d"=" -f2- |sed 's/^[[:space:]]*//g' |sed 's/*[[:space:]]$//g'
  else
    echo ""
  fi
}

set_configs()
{
   ##
   profileFile=$RUNHOME/../conf/bootstrap-prod.yml

    ##
   hostName=$(hostname -f);
   sed -i '/^dapmanager.host.name: .*$/d' "${profileFile}";
   sed -i '$a dapmanager.host.name: '"${hostName}" "${profileFile}";
}

source /etc/profile;
set_configs;

echo ================== ENV_INFO  =============================================
echo @RUNHOME@  $RUNHOME
echo @JAVA_BASE@  $JAVA_BASE
echo @Main_Class@  $Main_Class
echo @APP_INFO@  $APP_INFO
echo @Main_JAR@  $Main_JAR
echo @Main_Conf@ $Main_Conf
echo ==========================================================================

echo start $APP_INFO ...

export Boot_Process_Duration=600
JAVA="$JAVA_HOME/bin/java"

#openjdk和openj9在1.8.0_302及之后的版本里均支持UseContainerSupport、InitialRAMPercentage、MaxRAMPercentage参数
#InitialRAMPercentage类似Xms，MaxRAMPercentage类似Xmx
JAVA_OPTS="$JAVA_OPTS -XX:+UseContainerSupport -XX:InitialRAMPercentage=10.0 -XX:MaxRAMPercentage=90.0 $JAVA_GLOBAL_OPTS $JVM_GC_OPTS"
JAVA_OPTS="$JAVA_OPTS -DMS_APP_NAME=JMS_DEMO_MICROSERVICENAME"
port=8888
#JAVA_OPTS="$JAVA_OPTS -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:$port"
CLASS_PATH="$LIB_DIRS:$RUNHOME/:$RUNHOME/$Main_JAR"

echo ================== RUN_INFO  =============================================
echo @JAVA_HOME@ $JAVA_HOME
echo @JAVA@ $JAVA
echo @JAVA_OPTS@ $JAVA_OPTS
echo @CLASS_PATH@ $CLASS_PATH
echo @LIB_DIRS@ $LIB_DIRS
echo ==========================================================================
if [ -d "/cloud-disk-notification-volume" ]; then
  echo "{\"podtype\": \"3\", \"podname\": \"$HOSTNAME\", \"namespace\": \"$OPENPALETTE_NAMESPACE\"}" > /cloud-disk-notification-volume/podtype.json
fi
echo @JAVA@ $JAVA
echo @JAVA_CMD@
"$JAVA" $JAVA_OPTS -classpath "$CLASS_PATH" $Main_Class


