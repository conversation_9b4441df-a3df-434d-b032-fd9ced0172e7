{"modelName": "components_offline_update_schema_patch_task_model", "isDefault": true, "modelNameI18n": "components_offline_update_schema_patch_task_model", "serviceId": "dap.manager.components", "descI18n": "components_offline_update_schema_patch", "resourceRequirement": "service", "version": "default", "steps": [{"order": 1, "definitionRefs": "update_schema_patch", "stage": "update_schema_patch", "roleRefs": ["Upgrade_Service"]}], "roleOrderDefinitions": [{"roleDefinitionName": "Upgrade_Service", "order": 0, "roleId": null, "roleName": null, "roleType": null, "parallelType": "<PERSON><PERSON><PERSON>"}], "definitions": [{"stepName": "update_schema_patch", "stepNameI18n": "update_schema_patch", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": 30, "taskExecutor": {"service": "daip-patcher-svr", "name": "schemaPatcherUpdateProcessor", "param": null}, "extraAttributes": {"triggerSteps": "components_offline_rollback_schema_patch_task_model.rollback_schema_patch", "supportRollBack": "true"}}]}