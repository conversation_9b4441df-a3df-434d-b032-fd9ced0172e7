{"modelName": "components_offline_rollback_patch_task_model", "isDefault": true, "modelNameI18n": "components_offline_rollback_patch_task_model", "serviceId": "dap.manager.components", "descI18n": "components_offline_rollback_patch", "resourceRequirement": "service", "version": "default", "steps": [{"order": 1, "definitionRefs": "patch_stop_service", "stage": "patch_stop_service", "roleRefs": ["Rollback_Service"]}, {"order": 2, "definitionRefs": "rollback_patch", "stage": "rollback_patch", "roleRefs": ["Rollback_Service"]}, {"order": 3, "definitionRefs": "patch_start_service", "stage": "patch_start_service", "roleRefs": ["Rollback_Service"]}], "roleOrderDefinitions": [{"roleDefinitionName": "Rollback_Service", "order": 0, "roleId": null, "roleName": null, "roleType": null, "parallelType": "<PERSON><PERSON><PERSON>"}], "definitions": [{"stepName": "patch_stop_service", "stepNameI18n": "patch_stop_service", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": -1, "taskExecutor": {"service": "daip-patcher-svr", "name": "stopClusterProcessor", "param": null}, "extraAttributes": null}, {"stepName": "rollback_patch", "stepNameI18n": "rollback_patch", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": 30, "taskExecutor": {"service": "daip-patcher-svr", "name": "patcherRollbackProcessor", "param": null}, "extraAttributes": null}, {"stepName": "patch_start_service", "stepNameI18n": "patch_start_service", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": -1, "taskExecutor": {"service": "daip-patcher-svr", "name": "startClusterProcessor", "param": null}, "extraAttributes": null}]}