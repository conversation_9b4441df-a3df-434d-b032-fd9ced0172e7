{"modelName": "components_offline_update_patch_task_model", "isDefault": true, "modelNameI18n": "components_offline_update_patch_task_model", "serviceId": "dap.manager.components", "descI18n": "components_offline_update_patch", "resourceRequirement": "service", "version": "default", "steps": [{"order": 1, "definitionRefs": "patch_stop_service", "stage": "patch_stop_service", "roleRefs": ["Upgrade_Service"]}, {"order": 2, "definitionRefs": "update_patch", "stage": "update_patch", "roleRefs": ["Upgrade_Service"]}, {"order": 3, "definitionRefs": "patch_start_service", "stage": "patch_start_service", "roleRefs": ["Upgrade_Service"]}], "roleOrderDefinitions": [{"roleDefinitionName": "Upgrade_Service", "order": 0, "roleId": null, "roleName": null, "roleType": null, "parallelType": "<PERSON><PERSON><PERSON>"}], "definitions": [{"stepName": "patch_stop_service", "stepNameI18n": "patch_stop_service", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": -1, "taskExecutor": {"service": "daip-patcher-svr", "name": "stopClusterProcessor", "param": null}, "extraAttributes": {"triggerSteps": "components_offline_rollback_patch_task_model.patch_start_service", "supportRollBack": "true"}}, {"stepName": "update_patch", "stepNameI18n": "update_patch", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": 30, "taskExecutor": {"service": "daip-patcher-svr", "name": "patcherUpdateProcessor", "param": null}, "extraAttributes": {"triggerSteps": "components_offline_rollback_patch_task_model.rollback_patch", "supportRollBack": "true"}}, {"stepName": "patch_start_service", "stepNameI18n": "patch_start_service", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": -1, "taskExecutor": {"service": "daip-patcher-svr", "name": "startClusterProcessor", "param": null}, "extraAttributes": {"triggerSteps": "components_offline_rollback_patch_task_model.patch_stop_service", "supportRollBack": "true"}}]}