{"modelName": "pre_operation_offline_update_patch_task_model", "isDefault": true, "modelNameI18n": "pre_operation_offline_update_patch_task_model", "serviceId": "dap.manager.components", "descI18n": "pre_operation_offline_update_patch", "resourceRequirement": "service", "version": "default", "steps": [{"order": 1, "definitionRefs": "patch_distribute_patch", "stage": "patch_distribute_patch", "roleRefs": ["Prepare_Service"]}], "roleOrderDefinitions": [{"roleDefinitionName": "Prepare_Service", "order": 0, "roleId": null, "roleName": null, "roleType": null, "parallelType": null}], "definitions": [{"stepName": "patch_distribute_patch", "stepNameI18n": "patch_distribute_patch", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 3, "timeout": 30, "taskExecutor": {"service": "daip-patcher-svr", "name": "patcherDistributeProcessor", "param": null}, "extraAttributes": {"triggerSteps": "pre_operation_offline_rollback_patch_task_model.patch_distribute_patch", "supportRollBack": "true"}}]}