{"modelName": "components_offline_rollback_schema_patch_task_model", "isDefault": true, "modelNameI18n": "components_offline_rollback_schema_patch_task_model", "serviceId": "dap.manager.components", "descI18n": "components_offline_rollback_schema_patch", "resourceRequirement": "service", "version": "default", "steps": [{"order": 1, "definitionRefs": "rollback_schema_patch", "stage": "rollback_schema_patch", "roleRefs": ["Rollback_Service"]}], "roleOrderDefinitions": [{"roleDefinitionName": "Rollback_Service", "order": 0, "roleId": null, "roleName": null, "roleType": null, "parallelType": "<PERSON><PERSON><PERSON>"}], "definitions": [{"stepName": "rollback_schema_patch", "stepNameI18n": "rollback_schema_patch", "operatorType": "Auto", "skipWhenFailed": false, "stepCategory": "Service", "retryCntWhenFailed": 1, "timeout": 30, "taskExecutor": {"service": "daip-patcher-svr", "name": "schemaPatcherRollbackProcessor", "param": null}, "extraAttributes": null}]}