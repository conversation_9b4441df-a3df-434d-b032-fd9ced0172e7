upload_patch_zip=上传补丁包
generate_temp_file=生成补丁文件到临时目录
upload_patch_notification=补丁上传文件
handler_patch_files=处理补丁文件
handler_patch_set_files=处理补丁集文件
handler_patch_normal_files=处理普通补丁文件
domain_handler_patch_files=开始处理补丁文件
domain_handler_patch_set_files=开始处理补丁集文件
domain_handler_patch_normal_files=开始处理普通补丁文件
domain_validate_patch_files=开始验证补丁文件
validate_patch_files=验证补丁文件
parsing_patch_files=解析补丁xml文件为补丁实体
convert_xml_to_bean= 补丁xml转换实体
unzip_file=解压文件
copy_file=拷贝文件

delete_patch=补丁删除文件
domain_delete_patch_db_record=开始删除补丁数据记录
domain_delete_patch_repository=开始从仓库删除补丁
domain_delete_patch_home_version=开始从homeVersion删除补丁
domain_delete_patch_in_agent=开始从agent侧删除补丁
delete_patch_in_agent=从agent侧删除补丁

dispatch_patch=分发补丁
update_dispatch_result=更新补丁分发结果
publish_dispatch_event=发布补丁分发事件
domain_patch_dispatcher_event=开始发布补丁分发事件
execute_dispatch_patches=执行补丁分发操作
send_kafka_msg=发送kafka消息

upgrade_patch=升级补丁
domain_upgrade_patch=开始升级补丁
domain_download_unzip_schema_patch=下载解压schema补丁
domain_execute_update_schema_patch=开始执行升级schema补丁
domain_execute_rollback_schema_patch=开始执行回退schema补丁
update_schema_patch=升级schema补丁
rollback_schema_patch=回退schema补丁
execute_update_patch=开始执行升级补丁
execute_patchtool_shell=执行patchtool脚本
update_schema_patch_config=升级schema补丁配置文件
update_schema_patch_i18n=升级schema补丁i18n文件
update_schema_patch_inspection=升级schema补丁inspection文件
update_schema_patch_alarm=升级schema补丁alarm文件
update_schema_patch_step=升级schema补丁step文件
update_schema_patch_model=升级schema补丁model文件
update_schema_patch_spect=升级schema补丁spect文件
update_schema_patch_dashboard=升级schema补丁dashboard文件
update_schema_patch_task_model=升级schema补丁任务模型文件
prepare_before_upgrade=升级补丁前准备
organize_patch_update_requests=组织升级补丁参数
publish_upgrade_patch_msg=发布升级补丁消息
rollback_patch=补丁回退
update_patch_copy=拷贝

query_update_patch_permit=查询补丁升级许可
query_update_patch_progress=查询补丁升级进度

container_path_command_list_empty=容器补丁xml命令列表为空
update_container_path_failed=升级容器补丁失败
patch_common_operation=补丁通用操作
daip-patcher-svr=补丁管理

com_zte_daip_manager_patcher_executor_distribute_patch=执行器分发补丁
com_zte_daip_manager_patcher_publish_distribute_patch_event=发布分发补丁事件
com_zte_daip_manager_patcher_auto_distribute_patch=分发补丁
com_zte_daip_manager_patcher_auto_distribute_patch_no_need= 无补丁需要分发
com_zte_daip_manager_patcher_auto_distribute_patch_failed=分发补丁失败
com_zte_daip_manager_patcher_executor_distribute_patch_failed=执行器分发补丁失败
com_zte_daip_manager_patcher_executor_distribute_patch_success=执行器分发补丁成功

verify_patch_can_update=校验补丁是否可以升级
begin_update_repository_version_patch=开始升级RepositoryVersion补丁
publish_update_repository_kafka_message=发布升级RepositoryVersion补丁消息，等待处理
handle_update_repository_version_result=处理升级RepositoryVersion补丁结果
waiting_for_upgrade_patch_list=待升级RepositoryVersion补丁列表：{0}
repository_version_patch_upgrade_result=RepositoryVersion补丁升级结果：成功补丁列表：{0}，失败补丁列表：{1}
com_zte_daip_manager_patcher_create_patch_task=创建补丁任务
com_zte_daip_manager_patcher_remove_patch_task=删除补丁任务
com_zte_daip_manager_patcher_modify_patch_task=修改补丁任务
com_zte_daip_manager_patcher_trigger_patch_task=触发补丁任务
com_zte_daip_manager_patcher_retry_patch_task=重试补丁任务
com_zte_daip_manager_patcher_pause_patch_task=暂停补丁任务
com_zte_daip_manager_patcher_resume_patch_task=继续补丁任务
com_zte_daip_manager_patcher_rollback_patch_task=回退补丁任务
com_zte_daip_manager_patcher_query_service_instance=查询补丁升级服务实例
patch_zip=补丁
patch_info=补丁信息:{0}
patch_task_duplicate_fail=补丁任务已存在:{0}
rollback_task_sequence_fail=请按顺序回退，先回退任务:{0}
copy_task_sequence_fail=请按顺序拷贝，先拷贝任务:{0}
patch_task_no_data_fail=补丁信息为空，请检查补丁是否上传
duplicate_patch_relation_task_fail=请先回退补丁任务，再重新升级
duplicate_patch_task_finish=补丁已全部升级完成，无需升级
rollback_patch_task_finish=补丁已全部回退完成，无需回退
