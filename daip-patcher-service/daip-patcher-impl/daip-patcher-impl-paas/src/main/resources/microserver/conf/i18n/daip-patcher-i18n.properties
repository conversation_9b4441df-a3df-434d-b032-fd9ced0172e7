upload_patch_zip=Upload patch package
generate_patch_file_2_temp_dir=Generate patch files to temp dir
upload_patch_notification=Patch Upload File
handler_patch_files=Handler patch files
handler_patch_set_files=Handler patch set files
handler_patch_normal_files=Handler normal patch files
domain_handler_patch_files=Handler patch files operation
domain_handler_patch_set_files=Handler patch set files operation
domain_handler_patch_normal_files==Handler normal patch files operation
domain_validate_patch_files=Validate patch files operation
validate_patch_files=Validate patch files
parsing_patch_files=Parsing patch files
convert_xml_to_bean=Convert xml to bean
unzip_file=Unzip file
copy_file=Copy file
delete_patch=Patch Delete File
domain_delete_patch_db_record=Delete patch db record operation
domain_delete_patch_repository=Delete patch repository operation
domain_delete_patch_home_version=Delete patch home version operation
domain_delete_patch_in_agent=Delete patch in agent operation
delete_patch_in_agent=Delete patch in agent
dispatch_patch=Dispatch patch
update_dispatch_result=Update dispatch Result
publish_dispatch_event=Publish dispatch event
domain_patch_dispatcher_event=Publish dispatch event operation
execute_dispatch_patches=Execute dispatch patches
send_kafka_msg=Send kafka message
upgrade_patch=Upgrade patch
domain_upgrade_patch=Upgrade patch operation
domain_upgrade_schema_patch=Upgrade schema patch operation
domain_download_unzip_schema_patch=Download and unzip schema patch
domain_execute_update_schema_patch=Execute update schema patch
domain_execute_rollback_schema_patch=Execute rollback schema patch
update_schema_patch=Update schema patch
rollback_schema_patch=Rollback schema patch
execute_update_patch=Execute update patch
execute_patchtool_shell=Execute patchtool shell
execute_update_schema_patch=Update schema patch
update_schema_patch_config=Update schema patch config
update_schema_patch_i18n=Update schema patch i18n file
update_schema_patch_inspection=Update schema patch inspection file
update_schema_patch_alarm=Update schema patch alarm file
update_schema_patch_step=Update schema patch step file
update_schema_patch_model=Update schema patch model file
update_schema_patch_spect=Update schema patch spect file
update_schema_patch_dashboard=Update schema patch dashboard file
update_schema_patch_task_model=Update schema patch task model file
prepare_before_upgrade=Prepare before upgrade
organize_patch_update_requests=Organize patch update requests
publish_upgrade_patch_msg=Publish upgrade patch message
rollback_patch=Rollback patch
update_patch_copy=Copy
query_update_patch_permit=Query update patch permit
query_update_patch_progress=Query update patch progress
daip-patcher-svr=patcher
com_zte_daip_manager_patcher_executor_distribute_patch=Executor distribute patch
com_zte_daip_manager_patcher_publish_distribute_patch_event=Publish distribute patch event
com_zte_daip_manager_patcher_auto_distribute_patch=Distribute patch
com_zte_daip_manager_patcher_auto_distribute_patch_no_need= No patch need distribute
com_zte_daip_manager_patcher_auto_distribute_patch_failed=Distribute patch failed
com_zte_daip_manager_patcher_executor_distribute_patch_failed=Executor distribute patch failed
com_zte_daip_manager_patcher_executor_distribute_patch_success=Executor distribute patch success
verify_patch_can_update=verify patch can update
begin_update_repository_version_patch=Upgrade the RepositoryVersion patch
publish_update_repository_kafka_message=Release the RepositoryVersion patch upgrade message and wait for processing
handle_update_repository_version_result=Processing the RepositoryVersion Patch Upgrade Result
waiting_for_upgrade_patch_list=List of RepositoryVersion patches to be upgraded: {0}
repository_version_patch_upgrade_result=RepositoryVersion patch upgrade result: Success patch list: {0}, failure patch list: {1}
com_zte_daip_manager_patcher_create_patch_task=Create patch task
com_zte_daip_manager_patcher_remove_patch_task=Remove patch task
com_zte_daip_manager_patcher_modify_patch_task=Modify patch task
com_zte_daip_manager_patcher_trigger_patch_task=Trigger patch task
com_zte_daip_manager_patcher_retry_patch_task=Retry patch task
com_zte_daip_manager_patcher_pause_patch_task=Pause patch task
com_zte_daip_manager_patcher_resume_patch_task=Resume patch task
com_zte_daip_manager_patcher_rollback_patch_task=Rollback patch task
com_zte_daip_manager_patcher_query_service_instance=Query patch upgrade service instance
patch_zip=Patch
patch_info=Patch info:{0}
patch_task_duplicate_fail=Patch task exists:{0}
rollback_task_sequence_fail=Please rollback in order, rollback the task first: {0}
copy_task_sequence_fail=Please copy in order, copy the task first: {0}
patch_task_no_data_fail=The patch information is empty, please check whether the patch has been uploaded.
duplicate_patch_relation_task_fail=Please roll back the patch task first and then upgrade again
duplicate_patch_task_finish=All patches have been upgraded and there is no need to upgrade.
rollback_patch_task_finish=All patches have been rollbacked and there is no need to rollback.