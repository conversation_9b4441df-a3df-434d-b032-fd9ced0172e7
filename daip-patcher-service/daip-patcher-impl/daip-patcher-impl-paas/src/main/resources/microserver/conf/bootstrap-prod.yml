spring:
  main:
    allow-circular-references: true
  messages:
    basename: i18n/daip-patcher-i18n
    encoding: UTF-8
    fallback-to-system-locale: true
  application:
    name: @svr.microservice.name@
  jersey:
    application-path: /api/@svr.microservice.name@/v1
  cloud:
    zookeeper:
      enabled: false
  datasource:
    url: jdbc:postgresql://${rdb_ip}:${rdb_port}/${rdb_dbname}
    username: ${rdb_user}
    password: ${rdb_password}
    driver-class-name: ${rdb_driver}
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      maximumPoolSize: 10
      minimumIdle: 2
      idleTimeout: 600000
      connectionTimeout: 30000
      maxLifetime: 1800000
  jpa:
    generate-ddl: false
    show-sql: false
    properties:
      hibernate:
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      ddl-auto: none
    database: postgresql
  kafka:
    bootstrap-servers: @kafka.bootstrap.servers.url@
    producer:
      retries: 0
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: @svr.microservice.name@
      auto-offset-reset: latest
      enable-auto-commit: true
      auto-commit-interval: 100
      session.timeout.ms: 15000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      group-topics: dapmanager_TASK_WORKER_ASYNC_REQUEST
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 10240MB
server:
  servlet:
    session:
      cookie:
        http-only: true
        secure: true
logging:
  config: classpath:logback.xml
  path: ./logs
dexcloud:
  base:
    microservice:
      name: @svr.microservice.name@
      version: v1
      organization: zdh
  discovery:
    msb:
      enabled: true
      server:
        address: ${msb_svrIp}
        port: ${msb_svrPort}
        namespace: ${msb_nameSpace}
  clusterJob:
    enabled: false
  es:
    enabled: false

  redis:
    redisson:
      host: ${redis_host}                       #【单机、集群模式】redis服务器的地址。在PaaS环境中，redis_host会自动替换为环境变量OPENPALETTE_REDIS_ADDRESS的值
      port: ${redis_port}                       #【单机、集群模式】redis服务器的端口。在PaaS环境中，redis_port会自动替换为环境变量OPENPALETTE_REDIS_PORT的值
      password: ${redis_password}               #【所有模式】redis服务器的密码。在PaaS环境中，redis_password会自动替换为环境变量OPENPALETTE_REDIS_PASSWORD的值，并自动解密
      sentinelHost: ${redis_sentinel_host:}     #【HA模式】redis sentinel的地址。在PaaS环境中，redis_sentinel_host会自动替换为环境变量OPENPALETTE_REDIS_SENTINEL_ADDRESS的值
      sentinelPort: ${redis_sentinel_port:}     #【HA模式】redis sentinel的端口。在PaaS环境中，redis_sentinel_port会自动替换为环境变量OPENPALETTE_REDIS_SENTINEL_PORT的值
      masterName: ${redis_sentinel_mastername:} #【HA模式】redis master节点的名称。在PaaS环境中，redis_sentinel_mastername会自动替换为环境变量OPENPALETTE_REDIS_SENTINEL_MASTERNAME的值
      poolSize: 32                              #【所有模式】【默认值64】主节点的连接池最大容量。请按需配置
      poolMinIdleSize: 4                        #【所有模式】【默认值32】每个主节点的最小保持连接数（长连接）。请按需配置
      slavePoolSize: 2                          #【集群、HA模式】【默认值64】每个从节点里用于普通操作（非发布和订阅）连接的连接池最大容量。请按需配置
      slavePoolMinIdleSize: 1                   #【集群、HA模式】【默认值32】每个从服务节点里用于普通操作（非发布和订阅）的最小保持连接数（长连接）。请按需配置
      dnsMonitoringInterval: 5000               #【所有模式】【默认值5000】检查节点DNS变化的时间间隔（毫秒）
      readMode: MASTER                          #【集群、HA模式】【默认值SLAVE】（注意必须大写）设置读取操作选择节点的模式。 可用值为： SLAVE - 只在从服务节点里读取。 MASTER - 只在主服务节点里读取。 MASTER_SLAVE - 在主从服务节点里都可以读取。
      scanInterval: 1000                        #【集群模式】【默认值1000】对Redis集群节点状态扫描的时间间隔（毫秒）
      timeout: 3000                             #【所有模式】【默认值3000】等待节点回复命令的时间（毫秒）。该时间从命令发送成功时开始计时
      connectTimeout: 10000                     #【所有模式】【默认值10000】同任何节点建立连接时的等待超时（毫秒）
      idleConnectionTimeout: 10000              #【所有模式】【默认值10000】如果当前连接池里的连接数量超过了最小空闲连接数，而同时有连接空闲时间超过了该数值，那么这些连接将会自动被关闭，并从连接池里去掉（毫秒）
      retryAttempts: 3                          #【所有模式】【默认值3】如果尝试达到 retryAttempts（命令失败重试次数） 仍然不能将命令发送至某个指定的节点时，将抛出错误。如果尝试在此限制之内发送成功，则开始启用 timeout（命令等待超时） 计时。
      retryInterval: 1500                       #【所有模式】【默认值1500】在一条命令发送失败以后，等待重试发送的时间间隔（毫秒）

kafkaclientconf:
  bootstrapServers: ${kafka_brokers}:${kafka_port}
  kafkaServiceName: kafka
  kafkaServiceVersion: v1
  consumerConf:
    properties:
      group.id: @svr.microservice.name@
      value.deserializer: org.apache.kafka.common.serialization.StringDeserializer
  producerConf:
    properties:
      key.serializer: org.apache.kafka.common.serialization.StringSerializer
      value.serializer: org.apache.kafka.common.serialization.StringSerializer
      compression.type: gzip
      retries: 1
contentlengthlimitconf:
  defaultlimit: -1
  limits:
    api: /api/daip-patcher-svr/v1/patches/uploading
    limit: -1

commonpgservice:
  user: daip_metadata_U_name

daip:
  msb:
    server:
      ip: ${msb_svrIp}
      port: ${msb_svrPort}
  patcher:
    msbname: @svr.microservice.name.msbname@
  service:
    name: @svr.microservice.name.msbname@


