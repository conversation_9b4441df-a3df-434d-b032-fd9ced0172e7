debug: false

server:
  servlet:
    context-path: /api/@svr.microservice.name@/v1
  port: 56210
  jetty:
    threads:
      max: ${jetty_threadpool_maxthreads}
    connection-idle-timeout: 300000
dexcloud:
  base:
    microservice:
      name: @svr.microservice.name@
      version: v1
      organization: zdh
      metaInfo:
        scope: test
  web:
    swagger:
      beanConfig:
        title: 'DAIP patcher Service API Documentation'
        version: '1.0'
  serviceinfo:
    serviceName: @svr.microservice.name@
  clusterJob:
    enabled: false
  es:
    enabled: false


spring:
  main:
    allow-circular-references: true
  cloud:
    zookeeper:
      enabled: false
  jersey:
    application-path: /api/@svr.microservice.name@/v1
  jpa:
    generate-ddl: false
    show-sql: false
    properties:
      hibernate:
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      ddl-auto: none
    database: postgresql
  freemarker:
    template-loader-path: file:/home/<USER>/@svr.microservice.name@/templates/
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration

logging:
  config: classpath:logback.xml
  path: ./logs

management:
  endpoints:
    web:
      expose: '*'
      base-path: /actuator

LOCALE: zh_CN

daip:
  version:
    main: @paas.service.version@
  redis:
    delay:
      queue:
        name: daip-patcher-rollback
        enable: true