<assembly>
    <id>assembly_release_tar</id>
    <formats>
        <!-- zip,tar,tar.gz,tar.bz2,jar,dir,war -->
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>
                ${project.build.directory}/
            </directory>
            <includes>
                <include>${ms.product_version.name}*.tar.gz</include>
            </includes>
            <outputDirectory>./${ms.release_version.name}/business-images/${service.name}</outputDirectory>
            <fileMode>0750</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/src/main/resources/install/</directory>
            <includes>
                <include>*.xml</include>
                <include>*.spd</include>
                <include>*.yaml</include>
                <include>*.spd.ext</include>
                <include>*.properties</include>
            </includes>
            <filtered>true</filtered>
            <outputDirectory>./${ms.release_version.name}/business-images/${service.name}/</outputDirectory>
            <fileMode>0750</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/src/main/resources/install/</directory>
            <excludes>
                <exclude>*</exclude>
            </excludes>
            <filtered>true</filtered>
            <outputDirectory>./${ms.release_version.name}/</outputDirectory>
            <fileMode>0750</fileMode>
        </fileSet>
    </fileSets>

</assembly>