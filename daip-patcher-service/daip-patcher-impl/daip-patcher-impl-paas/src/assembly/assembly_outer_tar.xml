<assembly>
    <id>assembly_outer_tar</id>
    <formats>
        <!-- zip,tar,tar.gz,tar.bz2,jar,dir,war -->
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>
                ${project.build.directory}/${svr.microservice.name}/
            </directory>
            <includes>
                <include>**</include>
            </includes>
            <outputDirectory>./${service.name}/</outputDirectory>
            <fileMode>0750</fileMode>
        </fileSet>
    </fileSets>

</assembly>