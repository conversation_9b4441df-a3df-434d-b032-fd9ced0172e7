<assembly>
    <id>assembly_combine_dir</id>
    <formats>
        <!-- zip,tar,tar.gz,tar.bz2,jar,dir,war -->
        <format>dir</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/image/
            </directory>
            <includes>
                <include>**</include>
            </includes>
            <excludes>
                <exclude>./daip-patcher-svr/apk/**</exclude>
                <exclude>blueprint/daip-patcher_chart/**</exclude>
            </excludes>
            <outputDirectory>./</outputDirectory>
            <filtered>true</filtered>
            <fileMode>0750</fileMode>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/image/${svr.microservice.name}/
            </directory>
            <includes>
                <include>install_apk.sh</include>
            </includes>
            <outputDirectory>./${init.microservice.name}/</outputDirectory>
            <filtered>true</filtered>
            <fileMode>0755</fileMode>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/target/
            </directory>
            <includes>
                <include>${svr.microservice.name}*.tar.gz</include>
            </includes>
            <outputDirectory>./${svr.microservice.name}/</outputDirectory>
            <fileMode>0750</fileMode>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/../../../daip-patcher-handler/daip-patcher-handler-impl/daip-patcher-handler-impl-paas/target/
            </directory>
            <includes>
                <include>*.tar.gz</include>
            </includes>
            <outputDirectory>./${handler.microservice.name}/</outputDirectory>
            <fileMode>0750</fileMode>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/../../../daip-patcher-init/target/
            </directory>
            <includes>
                <include>*.tar.gz</include>
            </includes>
            <outputDirectory>./${init.microservice.name}/</outputDirectory>
            <fileMode>0750</fileMode>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/../../../${iui.microservice.name}/target/
            </directory>
            <includes>
                <include>*.tar.gz</include>
            </includes>
            <outputDirectory>./${iui.microservice.name}/</outputDirectory>
            <fileMode>0750</fileMode>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/target/
            </directory>
            <includes>
                <include>daip-patcher*.tgz</include>
            </includes>
            <outputDirectory>./blueprint/daip-patcher_chart/</outputDirectory>
            <fileMode>0640</fileMode>
        </fileSet>
    </fileSets>
    <dependencySets>
        <dependencySet>
            <outputDirectory>./${svr.microservice.name}</outputDirectory>
            <includes>
                <include>DAIP.MIX.apk:daip-deployer-x86:*</include>
            </includes>
        </dependencySet>
        <dependencySet>
            <outputDirectory>./${svr.microservice.name}</outputDirectory>
            <includes>
                <include>DAIP.MIX.apk:daip-deployer-arm:*</include>
            </includes>
        </dependencySet>
    </dependencySets>
</assembly>