<assembly>
    <id>${svr.microservice.name}</id>
    <formats>
        <!-- zip,tar,tar.gz,tar.bz2,jar,dir,war -->
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/microserver/
            </directory>
            <excludes>
                <exclude>conf/**</exclude>
                <exclude>taskmodel/**</exclude>
            </excludes>
            <outputDirectory>./${svr.microservice.name}/</outputDirectory>
            <filtered>true</filtered>
            <directoryMode>0750</directoryMode>
            <fileMode>0750</fileMode>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/microserver/
            </directory>
            <includes>
                <include>conf/**</include>
                <include>taskmodel/**</include>
            </includes>
            <outputDirectory>./${svr.microservice.name}/</outputDirectory>
            <filtered>true</filtered>
            <directoryMode>0750</directoryMode>
            <fileMode>0640</fileMode>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/
            </directory>
            <includes>
                <include></include>
            </includes>
            <outputDirectory>./${svr.microservice.name}/lib/</outputDirectory>
            <directoryMode>0750</directoryMode>
        </fileSet>
    </fileSets>

    <dependencySets>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>./${svr.microservice.name}/lib/</outputDirectory>
            <fileMode>0640</fileMode>
            <includes>
                <include>org.antlr:antlr4-runtime</include>
                <include>org.apache.xmlgraphics:batik-css</include>
                <include>org.owasp.esapi:esapi</include>
                <include>commons-fileupload:commons-fileupload</include>
                <include>xerces:xercesImpl</include>
                <include>com.zte.daip*:*</include>
                <include>org.apache.commons:*</include>
                <include>com.alibaba:*</include>
                <include>com.alibaba.fastjson2:fastjson2-extension</include>
                <include>com.alibaba.fastjson2:fastjson2</include>
                <include>javax.validation:*</include>
                <include>io.github.openfeign:*</include>
                <include>log4j:log4j</include>
                <include>com.baomidou:*</include>
                <include>org.freemarker:freemarker</include>
                <include>org.springframework.data:*</include>
                <include>org.apache.commons:commons-pool2</include>
                <include>redis.clients:jedis</include>
                <include>org.springframework:spring-context-support</include>
                <include>org.springframework:spring-orm</include>
                <include>com.zte.zdh:zdh-commons</include>
                <include>org.hibernate*:*</include>
                <include>org.owasp.esapi:esapi</include>
                <include>commons-logging:commons-logging</include>
                <include>commons-beanutils:commons-beanutils-core</include>
                <include>commons-fileupload:commons-fileupload</include>
                <include>xom:xom</include>
                <include>xalan:xalan</include>
                <include>org.beanshell:bsh-core</include>
                <include>org.owasp.antisamy:antisamy</include>
                <include>org.apache.xmlgraphics:batik-css</include>
                <include>com.zte.ums.zenap.sm.sso.agent:sm-sso-agent</include>
                <include>org.apache.xmlgraphics:batik-ext</include>
                <include>org.apache.xmlgraphics:batik-util</include>
                <include>xml-apis:xml-apis-ext</include>
                <include>net.sourceforge.nekohtml:nekohtml</include>
                <include>commons-httpclient:commons-httpclient</include>
                <include>commons-codec:commons-codec</include>
                <include>xerces:xercesImpl</include>
                <include>xml-apis:xml-apis</include>
                <include>javax.transaction:javax.transaction-api</include>
                <include>org.dom4j:dom4j</include>
                <include>antlr:antlr</include>
                <include>org.jvnet.hudson:ganymed-ssh2</include>
                <include>commons-dbutils:commons-dbutils</include>
                <include>commons-configuration:commons-configuration</include>
                <include>org.springframework:spring-messaging</include>
                <include>org.apache.commons:commons-lang3</include>
                <include>org.springframework.session:spring-session-core</include>
                <include>com.auth0:java-jwt</include>
                <include>io.jsonwebtoken:*</include>
                <include>org.springframework.security:spring-security-rsa</include>
                <include>aopalliance:aopalliance</include>
                <include>io.micrometer:*</include>
<!--                <include>org.springframework.cloud:spring-cloud-sleuth-*</include>-->
                <include>io.zipkin.*:*</include>
                <include>org.aspectj:aspectjrt</include>
                <include>com.lmax:disruptor</include>
                <include>jakarta.persistence:jakarta.persistence-api</include>
                <include>jakarta.transaction:jakarta.transaction-api</include>
                <include>org.springframework.security:spring-security-rsa</include>
                <include>io.opentelemetry:*</include>
                <include>io.github.hakky54:*</include>
                <include>dev.failsafe:failsafe</include>
                <include>com.zte.ums.zenap.sm.sso.agent:sm-sso-agent</include>
            </includes>
            <excludes>
                <exclude>com.zte.daip.manager.patcher:daip-patcher-impl-paas</exclude>
                <exclude>com.zte.daip.manager.patcher:daip-patcher-impl-inner-client-paas</exclude>
            </excludes>
        </dependencySet>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>./${svr.microservice.name}/</outputDirectory>
            <fileMode>0640</fileMode>
            <includes>
                <include>com.zte.daip.manager.patcher:daip-patcher-impl-paas</include>
            </includes>
        </dependencySet>
    </dependencySets>
</assembly>