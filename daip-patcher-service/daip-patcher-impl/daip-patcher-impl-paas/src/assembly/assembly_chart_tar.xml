<assembly>
    <id>${service.name}</id>
    <formats>
        <!-- zip,tar,tar.gz,tar.bz2,jar,dir,war -->
        <format>tgz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/image/blueprint/daip-patcher_chart
            </directory>
            <includes>
                <include>
                    **/*
                </include>
            </includes>
            <outputDirectory>./${service.name}/</outputDirectory>
            <filtered>true</filtered>
            <directoryMode>0750</directoryMode>
            <fileMode>0640</fileMode>
        </fileSet>
    </fileSets>

</assembly>
