package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.configcenter.api.ServiceConfigControllerApi;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.api.StartAndStopClusterControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.operation.ClusterOperationInfoBean;
import com.zte.daip.manager.common.deployer.bean.exception.DaipDeployerException;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.deployer.bean.service.ServiceInstanceBean;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.infrastructure.ServiceModelInfoCacheApi;
import com.zte.daip.manager.patcher.infrastructure.bean.ServiceModelBean;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import jakarta.validation.constraints.AssertTrue;
import java.util.List;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchStartAndStopImplTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/4/25</p>
 * <p>完成日期：2023/4/25</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchStartAndStopImplTest {

    @Mock
    private ServiceConfigControllerApi serviceConfigControllerApi;
    @Mock
    private ServiceModelInfoCacheApi serviceModelInfoCacheApi;
    @Mock
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;
    @Mock
    private StartAndStopClusterControllerApi startAndStopClusterControllerApi;

    @InjectMocks
    private PatchStartAndStopImpl patchStartAndStopImpl;

    @Before
    public void init() throws Exception {
        Mockito.when(startAndStopClusterControllerApi.stopCluster(anyString(), anyList())).thenReturn("");
        Mockito.when(startAndStopClusterControllerApi.startCluster(anyString(), anyList())).thenReturn("");
        Mockito.when(startAndStopClusterControllerApi.queryClusterOperatorProcess(anyString()))
            .thenReturn(new ClusterOperationInfoBean());
        ServiceModelBean serviceModelBean = new ServiceModelBean();
        serviceModelBean.setServiceId("aaa");
        Mockito.when(serviceModelInfoCacheApi.queryByClusterIdAndServiceName(anyString(), anyString()))
            .thenReturn(serviceModelBean);
        DeploymentServiceInstance deploymentServiceInstance = new DeploymentServiceInstance();
        deploymentServiceInstance.setServiceInstanceId("zk_01");
        Mockito.when(deploymentInstanceServiceControllerApi.queryByClusterIdAndServiceId(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(deploymentServiceInstance));
        com.zte.daip.manager.common.configcenter.bean.DeploymentServiceInstance deploymentServiceInstance1 =
            new com.zte.daip.manager.common.configcenter.bean.DeploymentServiceInstance();
        deploymentServiceInstance1.setServiceInstanceId("zk_01");
        Mockito.when(serviceConfigControllerApi.queryBeDependence(anyString(), anyString()))
            .thenReturn(Lists.newArrayList(deploymentServiceInstance1));
        ServiceInstanceBean serviceInstanceBean = new ServiceInstanceBean();
        serviceInstanceBean.setServiceInstanceId("zk_01");
        Mockito.when(startAndStopClusterControllerApi.queryInstancesCanOperate(anyString(), anyBoolean(), anyBoolean()))
            .thenReturn(Lists.newArrayList(serviceInstanceBean));
    }

    @Test
    public void stopServiceByCluster() {

        boolean result = true;

        try {
            patchStartAndStopImpl.stopServiceByCluster("001", Lists.newArrayList());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);

    }

    @Test(expected = DaipBaseException.class)
    public void stopServiceByCluster_exception() throws DaipDeployerException, DaipBaseException {
        Mockito.when(startAndStopClusterControllerApi.stopCluster(anyString(), anyList()))
            .thenThrow(new DaipDeployerException(""));
        patchStartAndStopImpl.stopServiceByCluster("001", Lists.newArrayList());
    }

    @Test(expected = DaipBaseException.class)
    public void startServiceByCluster_exception() throws DaipDeployerException, DaipBaseException {
        Mockito.when(startAndStopClusterControllerApi.startCluster(anyString(), anyList()))
            .thenThrow(new DaipDeployerException(""));
        patchStartAndStopImpl.startServiceByCluster("001", Lists.newArrayList());
    }

    @Test(expected = DaipBaseException.class)
    public void queryOperateProgress_exception() throws DaipBaseException, DaipDeployerException {
        Mockito.when(startAndStopClusterControllerApi.queryClusterOperatorProcess(anyString()))
            .thenThrow(new DaipDeployerException(""));
        patchStartAndStopImpl.queryOperateProgress("001");
    }

    @Test(expected = DaipBaseException.class)
    public void queryInstancesByServiceName_exception() throws DaipBaseException {
        Mockito.when(serviceModelInfoCacheApi.queryByClusterIdAndServiceName(anyString(), anyString()))
            .thenThrow(new DaipBaseException(""));
        patchStartAndStopImpl.queryInstancesByServiceName("001", "zk");
    }

    @Test(expected = DaipBaseException.class)
    public void queryCanOperateInstances_exception() throws DaipBaseException, DaipDeployerException {
        Mockito.when(startAndStopClusterControllerApi.queryInstancesCanOperate(anyString(), anyBoolean(), anyBoolean()))
            .thenThrow(new DaipDeployerException(""));
        patchStartAndStopImpl.queryCanOperateInstances("001", true);
    }

    @Test
    public void startServiceByCluster() {
        boolean result = true;

        try {
            patchStartAndStopImpl.startServiceByCluster("001", Lists.newArrayList());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryOperateProgress() throws Exception {
        String operateProgress = patchStartAndStopImpl.queryOperateProgress("001");
        assertNotNull(operateProgress);
    }

    @Test
    public void queryInstancesByServiceName() throws Exception {
        List<String> list = patchStartAndStopImpl.queryInstancesByServiceName("001", "zk");
        assertEquals("zk_01", list.get(0));
    }

    @Test
    public void queryDependInstances() {
        List<String> list = patchStartAndStopImpl.queryDependInstances("001", "zk");
        assertEquals("zk_01", list.get(0));
    }

    @Test
    public void queryCanOperateInstances() throws Exception {
        List<String> list = patchStartAndStopImpl.queryCanOperateInstances("001", true);
        assertEquals("zk_01", list.get(0));
    }
}