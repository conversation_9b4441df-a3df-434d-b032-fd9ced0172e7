package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import org.junit.Test;

import java.io.File;

import static org.junit.Assert.*;

public class UnzipFileImplTest {
    private UnzipFileImpl unzipFile = new UnzipFileImpl();

    private String patchPath;

    @Test
    public void unzipFile() throws Exception {
        patchPath = this.getClass().getResource("/patch/DAP-HDFS-V20.19.40.R4.B2-SP028-20200902.zip").getPath();
        File patch = new File(patchPath);
        String desDir = this.getClass().getResource("/patch/").getPath() + "DAP-HDFS-V20.19.40.R4.B2-SP028-20200902";
        unzipFile.unzipFile(patch, "patch-update-config.xml", desDir);
        File patchxml = new File(desDir, "patch-update-config.xml");
        assertEquals(true, patchxml.exists());
    }

    @Test
    public void unzipFiles() throws Exception {
        patchPath = this.getClass().getResource("/patch/DAP-HDFS-V20.19.40.R4.B2-SP028-20200902.zip").getPath();
        File patch = new File(patchPath);
        String desDir = this.getClass().getResource("/patch/").getPath() + "DAP-HDFS-V20.19.40.R4.B2-SP028-20200902";
        unzipFile.unzipFile(patch, desDir);
        File patchxml = new File(desDir, "patch-update-config.xml");
        assertEquals(true, patchxml.exists());
    }
}