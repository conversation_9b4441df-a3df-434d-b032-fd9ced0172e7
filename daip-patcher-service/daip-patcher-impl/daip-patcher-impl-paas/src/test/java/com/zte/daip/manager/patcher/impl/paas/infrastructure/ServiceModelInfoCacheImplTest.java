package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.patcher.infrastructure.bean.ServiceModelBean;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static junit.framework.TestCase.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: ServiceModelInfoCacheImplTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/4/25</p>
 * <p>完成日期：2023/4/25</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class ServiceModelInfoCacheImplTest {
    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;
    @InjectMocks
    private ServiceModelInfoCacheImpl serviceModelInfoCacheImpl;

    @Before
    public void setUp() throws Exception {

        List<ServiceModel> serviceModels = Lists.newArrayList();
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setServiceName("zookeeper");
        serviceModel.setServiceId("zookeeper");
        serviceModel.setVersion("942");
        serviceModels.add(serviceModel);
        when(productModelInfoControllerApi.queryByClusterId(anyString())).thenReturn(serviceModels);
    }

    @Test
    public void queryByClusterIdAndServiceName() throws Exception {
        ServiceModelBean serviceModelBean =
            serviceModelInfoCacheImpl.queryByClusterIdAndServiceName("100000", "zookeeper");
        assertEquals(serviceModelBean.getServiceId(), "zookeeper");
    }
}