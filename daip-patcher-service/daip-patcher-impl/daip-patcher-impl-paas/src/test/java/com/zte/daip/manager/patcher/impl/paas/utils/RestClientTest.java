package com.zte.daip.manager.patcher.impl.paas.utils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

import java.util.List;

import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;

@RunWith(MockitoJUnitRunner.class)
public class RestClientTest {

    @InjectMocks
    private RestClient restClient;

    @Mock
    private RestTemplate restTemplate;

    @Test
    public void postForParameterizedTypeReference() {
        List<String> result = Lists.newArrayList("success");
        ResponseEntity<List<String>> success = ResponseEntity.ok(result);
        Mockito.when(restTemplate.exchange(anyString(), any(), any(), Mockito.any(ParameterizedTypeReference.class)))
            .thenReturn(success);
        List<String> resultBody = restClient.postForParameterizedTypeReference("test", null, null,
            new ParameterizedTypeReference<List<String>>() {});
        Assert.assertEquals(1, resultBody.size());
    }
}