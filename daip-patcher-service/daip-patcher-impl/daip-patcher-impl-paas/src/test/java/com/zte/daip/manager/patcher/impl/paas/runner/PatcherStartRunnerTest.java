package com.zte.daip.manager.patcher.impl.paas.runner;/* Started by AICoder, pid:3b2da0f8ac654f9141a50afab05383320799a088 */
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.zte.daip.manager.patcher.domain.taskmodel.service.TaskModelLoadService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;

public class PatcherStartRunnerTest {
    private PatcherStartRunner patcherStartRunner;

    @Mock private TaskModelLoadService taskModelLoadService;
    @Mock private RedissonClient redissonClient;
    @Mock private ApplicationArguments args;
    @Mock private RBucket<Object> bucket;
    @Mock private RAtomicLong atomicLong;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(bucket.trySet(anyString())).thenReturn(true);
        when(redissonClient.getAtomicLong(anyString())).thenReturn(atomicLong);
        patcherStartRunner = new PatcherStartRunner(taskModelLoadService, redissonClient);
    }

    @Test
    public void testRun() throws Exception {

        patcherStartRunner.run(args);
    }

    @Test
    public void testDestroy() throws Exception {

        patcherStartRunner.destroy();
    }
}


/* Ended by AICoder, pid:3b2da0f8ac654f9141a50afab05383320799a088 */