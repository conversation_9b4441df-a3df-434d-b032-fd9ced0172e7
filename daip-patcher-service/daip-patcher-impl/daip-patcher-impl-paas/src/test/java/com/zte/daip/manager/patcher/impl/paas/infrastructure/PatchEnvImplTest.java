package com.zte.daip.manager.patcher.impl.paas.infrastructure;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.paas.url.AccessUrl;
import com.zte.daip.manager.miniagent.seed.bean.SeedPriority;
import com.zte.daip.manager.miniagent.seed.bean.SeedVersion;
import com.zte.daip.manager.miniagent.seed.repository.VersionPathGenerator;
import com.zte.daip.manager.miniagent.seed.utils.ConfigureValue;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchEnvImplTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/15</p>
 * <p>完成日期：2023/3/15</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PatchEnvImpl.class})
@Slf4j
public class PatchEnvImplTest {

    @MockBean
    private AccessUrl accessUrl;
    @MockBean
    private ConfigureValue configureValue;
    @MockBean
    private VersionPathGenerator versionPathGenerator;

    @Autowired
    private PatchEnvImpl patchEnvImpl;

    @Test
    public void getPatchUrl() {

        try {
            Mockito.when(accessUrl.queryUrl(any(), any(), any(), any())).thenReturn("https://123");
            Mockito.when(configureValue.getClusterId()).thenReturn("12345");
            Mockito.when(versionPathGenerator.generate(any(), any(), any())).thenReturn("123");
            ReflectionTestUtils.setField(patchEnvImpl, "microserviceName", "123");
            ReflectionTestUtils.setField(patchEnvImpl, "serviceName", "123");
            ReflectionTestUtils.setField(patchEnvImpl, "port", "123");
            ReflectionTestUtils.setField(patchEnvImpl, "baseUrl", "123");
            ReflectionTestUtils.setField(patchEnvImpl, "version", "123");
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

        try {
            String patchUrl = patchEnvImpl.getPatchUrl();
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

    }
}