/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: CycleTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/8
 * </p>
 * <p>
 * 完成日期：2021/3/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.paas.jdepend;

import com.google.common.collect.Lists;
import jdepend.framework.JDepend;
import jdepend.framework.JavaPackage;
import jdepend.framework.PackageFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class JDependTest
{
    private JDepend jdepend;


    public void setUp() throws IOException
    {

        final String path = this.getClass().getResource("/").getPath();
        File file = new File(path);
        final String libPath = file.getParent() + "/jdepend/lib/";

        // Package Filters 过滤包
        PackageFilter filter = new PackageFilter();
        filter.addPackage("java.*");
        filter.addPackage("javax.*");
        filter.addPackage("org.*");
        filter.addPackage("io.*");
        filter.addPackage("ch.*");
        filter.addPackage("feign.*");
        filter.addPackage("antlr.*");
        filter.addPackage("com.auth0.*");
        filter.addPackage("com.alibaba.*");
        filter.addPackage("com.orbitz.*");
        filter.addPackage("com.google.*");
        filter.addPackage("com.zte.zdh.*");
        filter.addPackage("com.zte.daip.manager.common.utils.*");
        filter.addPackage("com.zte.daip.manager.event.*");
        filter.addPackage("zipkin2.*");
        filter.addPackage("brave.*");
        filter.addPackage("com.lmax.*");

        jdepend = new JDepend(filter);
        jdepend.addDirectory(libPath);
    }


    public void Patcher包的循环依赖度为0()
    {
        jdepend.getFilter().addPackage("com.zte.daip.manager.patcher.domain.upload.*");

        jdepend.getFilter().addPackage("com.zte.daip.manager.common.*");

        jdepend.getFilter().addPackage("com.zte.daip.manager.miniagent.*");

        jdepend.getFilter().addPackage("com.zte.daip.manager.inspection.*");

        Collection packages = jdepend.analyze();

        List bCycles = new ArrayList();

        final boolean containsCycles = containsCycles(packages, bCycles);

        printCycles(bCycles);

        assertFalse(containsCycles);
    }


    public void API包不稳定程度为0()
    {

        jdepend.getFilter().addPackage("com.zte.daip.manager.patcher.api.dto");

        jdepend.analyze();

        JavaPackage p = jdepend.getPackage("com.zte.daip.manager.patcher.api");
        // 被分析package中的抽象类和接口与所在 package 所有类数量的比例
        final float abstractness = p.abstractness();
        // 衡量一个包的不稳定程度
        final float instability = p.instability();
        // 评价包的抽象程度与稳定程度的平衡关系
        final float distance = p.distance();

        assertEquals(1.00f, abstractness, 0);
        assertEquals(0.00f, instability, 0);
        assertEquals(0.00f, distance, 0);
    }


    public void 基础层接口不稳定程度为0()
    {

        jdepend.getFilter().addPackage("com.zte.daip.manager.miniagent.seed.*");
        jdepend.analyze();

        JavaPackage p = jdepend.getPackage("com.zte.daip.manager.patcher.infrastructure");

        final float abstractness = p.abstractness();

        final float instability = p.instability();

        final float distance = p.distance();

        assertEquals(1.00f, abstractness, 0);
        assertEquals(0.00f, instability, 0);
        assertEquals(0.00f, distance, 0);

    }


    public void Interfaces包不稳定程度为1()
    {
        jdepend.analyze();

        JavaPackage p = jdepend.getPackage("com.zte.daip.manager.patcher.interfaces.controller");

        final float abstractness = p.abstractness();
        final float instability = p.instability();
        final float distance = p.distance();
        final boolean dependsUponPackages = isDependsUponPackages(p.getEfferents(), Lists
            .newArrayList("com.zte.daip.manager.patcher.api",
                "com.zte.daip.manager.patcher.interfaces", "com.zte.daip.manager.patcher.domain",
                "com.zte.daip.manager.patcher.application",
                "com.zte.daip.manager.event.reporter"));

        assertEquals(0.00f, abstractness, 0);
        assertEquals(1.00f, instability, 0);
        assertEquals(0.00f, distance, 0);

        // assertTrue(dependsUponPackages);
    }

    public void 应用层依赖Api_domain_kafka_基础层()
    {
        jdepend.analyze();

        final List<JavaPackage> packages = getPackage("com.zte.daip.manager.patcher.application.*");

        final List<String> packageNames = Lists
            .newArrayList("com.zte.daip.manager.patcher.api", "com.zte.daip.manager.patcher.domain",
                "com.zte.daip.manager.patcher.application",
                "com.zte.daip.manager.patcher.infrastructure", "com.zte.daip.communication");

        for (JavaPackage javaPackage : packages)
        {
            final boolean dependsUponPackages = isDependsUponPackages(javaPackage.getEfferents(),
                packageNames);
            if (!dependsUponPackages)
            {
                log.info(javaPackage.getName());
            }
            // assertTrue(dependsUponPackages);
        }
        assertTrue(packages.size() > 0);
    }


    public void domain层依赖Api_基础层()
    {
        jdepend.analyze();

        final List<JavaPackage> packages = getPackage("com.zte.daip.manager.patcher.domain.*");

        final List<String> packageNames = Lists
            .newArrayList("com.zte.daip.manager.patcher.api", "com.zte.daip.manager.patcher.domain",
                "com.zte.daip.manager.patcher.infrastructure");

        for (JavaPackage javaPackage : packages)
        {
            final boolean dependsUponPackages = isDependsUponPackages(javaPackage.getEfferents(),
                packageNames);
            if (!dependsUponPackages)
            {
                log.info(javaPackage.getName());
            }
            // assertTrue(dependsUponPackages);
        }
        assertTrue(packages.size() > 0);
    }

    private List<JavaPackage> getPackage(String packageName)
    {
        if (packageName.endsWith("*"))
        {
            final String finalPackageName = packageName.substring(0, packageName.length() - 1);
            return jdepend.getPackages().stream()
                .filter(javaPackage -> javaPackage.getName().startsWith(finalPackageName))
                .collect(Collectors.toList());

        }
        return Lists.newArrayList(jdepend.getPackage(packageName));
    }

    private boolean isDependsUponPackages(Collection<JavaPackage> efferents,
        List<String> packageNames)
    {
        if (CollectionUtils.isEmpty(packageNames))
        {
            return false;
        }
        for (JavaPackage javaPackage : efferents)
        {
            final String packageName = javaPackage.getName();
            final boolean dependsUponPackages = isDependsUponPackages(packageName, packageNames);
            if (!dependsUponPackages)
            {
                return false;
            }
        }
        return true;
    }

    private boolean isDependsUponPackages(String packageName, List<String> packageNames)
    {
        if (CollectionUtils.isEmpty(packageNames))
        {
            return false;
        }
        for (String name : packageNames)
        {
            final boolean contains = packageName.contains(name);
            if (contains)
            {
                return true;
            }
        }
        return false;
    }

    private void printCycles(List list)
    {
        StringBuilder stringBuilder = new StringBuilder();
        Iterator i = list.iterator();
        while (i.hasNext())
        {
            JavaPackage p = (JavaPackage) i.next();
            if (i.hasNext())
            {
                stringBuilder.append(p.getName() + "->");
            } else
            {
                stringBuilder.append(p.getName());
            }
        }
        log.info(stringBuilder.toString());
    }

    private boolean containsCycles(Collection packages, List list)
    {
        Iterator i = packages.iterator();

        JavaPackage jPackage;
        do
        {
            if (!i.hasNext())
            {
                return false;
            }

            jPackage = (JavaPackage) i.next();
        } while (!jPackage.collectAllCycles(list));

        return true;
    }

}