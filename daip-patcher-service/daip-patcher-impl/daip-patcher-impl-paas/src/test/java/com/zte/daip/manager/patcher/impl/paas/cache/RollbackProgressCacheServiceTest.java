package com.zte.daip.manager.patcher.impl.paas.cache;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.domain.rollback.entity.HostRollbackProgress;
import com.zte.daip.manager.patcher.domain.rollback.entity.PatchRollBackResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: RollbackProgressCacheServiceTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/3/23</p>
 * <p>完成日期：2023/3/23</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class RollbackProgressCacheServiceTest {

    @InjectMocks
    private RollbackProgressCacheService rollbackProgressCacheService;
    @Mock
    private PatchRollbackProgressReader patchRollbackProgressReader;
    @Mock
    private PatchRollbackProgressWriter patchRollbackProgressWriter;
    @Mock
    private PatchRollbackServiceReader patchRollbackServiceReader;
    @Mock
    private PatchRollbackServiceWriter patchRollbackServiceWriter;

    @Before
    public void setUp() {
        doNothing().when(patchRollbackProgressWriter).put(anyString(), anyString(), any());
        doNothing().when(patchRollbackProgressWriter).delete(anyString());
        Mockito.when(patchRollbackServiceReader.get(anyString())).thenReturn(Lists.newArrayList());
        Mockito.when(patchRollbackProgressReader.getAllValue(anyString())).thenReturn(Lists.newArrayList());
        doNothing().when(patchRollbackServiceWriter).put(anyString(), any());
        doNothing().when(patchRollbackServiceWriter).delete(anyString());
    }

    @Test
    public void initAndUpdateRollbackProgress() {
        boolean result = true;
        try {
            rollbackProgressCacheService.initAndUpdateRollbackProgress("1", "kafka", "********",
                new HostRollbackProgress());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void deleteRollbackProgress() {
        boolean result = true;
        try {
            rollbackProgressCacheService.deleteRollbackProgress("1", "kafka");
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void initRollbackServices() {
        boolean result = true;
        try {
            rollbackProgressCacheService.initRollbackServices("1", Lists.newArrayList("kafka"));
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);

    }

    @Test
    public void queryRollbackServices() {
        boolean result = true;
        try {
            rollbackProgressCacheService.queryRollbackServices("1");
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryProgress() {
        boolean result = true;
        try {
            rollbackProgressCacheService.queryProgress("1", "kafka");
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }
}