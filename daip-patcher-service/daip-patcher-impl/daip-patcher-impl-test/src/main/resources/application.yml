server.port: 8080
patch.dir: classpath:patch
spring.cloud.refresh.refreshable: none
# spring.datasource.x
spring.datasource.driverClassName: org.h2.Driver
spring.datasource.url: jdbc:h2:mem:test_db;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.schema: classpath:db/schema.sql
spring.datasource.data: classpath:db/data.sql
spring.datasource.initialization-mode: always

# 是否允许访问控制台
spring.h2.console.enabled: true



