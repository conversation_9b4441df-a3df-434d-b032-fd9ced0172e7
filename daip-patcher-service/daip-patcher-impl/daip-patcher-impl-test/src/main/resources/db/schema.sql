drop table IF EXISTS dapmanager_patch_detail_info ;

create TABLE dapmanager_patch_detail_info(
  id serial NOT NULL ,
  patch_name VARCHAR (100) NOT NULL DEFAULT '',
  patch_display_name_zh VARCHAR (100) DEFAULT NULL,
  patch_display_name_en VARCHAR (100) DEFAULT NULL,
  depend_patch VARCHAR (100) DEFAULT NULL,
  base_version VARCHAR (100) NOT NULL DEFAULT '',
  hot_patch INT   DEFAULT NULL,
  patch_create_date DATE DEFAULT NULL,
  patch_size INT   NOT NULL DEFAULT '0',
  service VARCHAR (50) NOT NULL DEFAULT '',
  roles VARCHAR (500) DEFAULT NULL,
  patch_upload_time timestamp NOT NULL DEFAULT '0001-01-01 00:00:00',
  description_zh VARCHAR (2000) DEFAULT NULL,
  description_en VARCHAR (2000) DEFAULT NULL,
  is_container_patch int NOT NULL DEFAULT 0,
  is_full_patch int NOT NULL DEFAULT 0,
  PRIMARY KEY (id)
) ;

DROP TABLE IF EXISTS dapmanager_patch_history;

CREATE TABLE dapmanager_patch_history (
	patchUpTime bigint NOT NULL,
	ip varchar(128) NOT NULL,
	patchName varchar(100) NOT NULL,
	serviceName varchar(100) NOT NULL,
	roleName varchar(100) NOT NULL DEFAULT '',
	serviceInstanceId varchar(100) NOT NULL DEFAULT '',
	PRIMARY KEY (ip, patchName, serviceName, roleName, serviceInstanceId)
);
