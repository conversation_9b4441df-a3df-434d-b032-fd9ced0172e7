package com.zte.daip.manager.patcher.impl.test;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@SpringBootApplication
@ComponentScan(basePackages = "com.zte.daip.*")
@EntityScan(basePackages = {"com.zte.daip.*"})
@EnableJpaRepositories(basePackages = {"com.zte.daip.*"})
public class PatcherTestApplication {

    public static void main(String[] args) {

        SpringApplication.run(PatcherTestApplication.class, args);
    }

}
