/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: GenerateSeedImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/20
 * </p>
 * <p>
 * 完成日期：2021/3/20
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.test.infrastructure;

import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.miniagent.seed.bean.Seed;
import com.zte.daip.manager.miniagent.seed.bean.SeedPriority;
import com.zte.daip.manager.miniagent.seed.bean.SeedVersion;
import com.zte.daip.manager.miniagent.seed.repository.VersionPathGenerator;
import com.zte.daip.manager.miniagent.seed.utils.ConfigureValue;
import com.zte.daip.manager.patcher.infrastructure.GenerateSeedApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class GenerateSeedImpl implements GenerateSeedApi {

    @Autowired
    private ConfigureValue configureValue;
    @Autowired
    private VersionPathGenerator versionPathGenerator;

    @Override
    public Seed generateSeed(String projectName, String serviceName, String version, SeedPriority priority,
        SeedVersion seedVersion) {
        String hostName = configureValue.getHostName();
        String clusterId = configureValue.getClusterId();

        Seed seed = new Seed(projectName, serviceName, version, hostName, clusterId);
        seed.setPriority(priority.getPriority());
        seed.setSeedVersion(seedVersion);

        String localRepositoryPath = versionPathGenerator.generate(projectName, serviceName, version);

        analysisSeedFiles(localRepositoryPath, seed);

        return seed;
    }

    private void analysisSeedFiles(String localRepositoryPath, Seed seed) {
        File file = FilePathCleaner.newFile(localRepositoryPath);
        if (file.isDirectory()) {
            File[] listFiles = file.listFiles();
            if (listFiles != null) {
                for (int i = 0; i < listFiles.length; i++) {
                    String filePath = listFiles[i].getAbsolutePath();
                    analysisSeedFiles(filePath, seed);
                }
            }
        } else if (file.isFile()) {
            seed.addFile(file.getAbsolutePath());
        } else {
            log.error("localRepositoryPath error : " + localRepositoryPath);
        }
    }

}