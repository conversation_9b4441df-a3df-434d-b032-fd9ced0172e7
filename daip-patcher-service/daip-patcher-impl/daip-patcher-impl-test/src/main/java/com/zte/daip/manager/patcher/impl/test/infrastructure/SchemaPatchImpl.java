package com.zte.daip.manager.patcher.impl.test.infrastructure;

import java.io.File;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.zte.daip.communication.producer.EventPublisher;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchRegisterRequest;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchUpdateRequest;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchApi;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.infrastructure.repository.SchemaRegisterRepoistory;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Slf4j
@Service
public class SchemaPatchImpl implements SchemaPatchApi {

    @Autowired
    private SchemaRegisterRepoistory schemaRegisterRepoistory;

    @Autowired
    private EventPublisher eventPublisher;

    private String applicationPath =
        Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();

    @Override
    public String getRepositoryHome() {
        return new File(applicationPath).getParent() + File.separator + "patch";
    }

    @Override
    public String queryPatchPath(String serviceName, String patchName) {
        return getRepositoryHome() + File.separator + patchName + ".zip";
    }

    @Override
    public String queryPatchHome(String serviceName, String version) {
        return getRepositoryHome() + File.separator + serviceName + "_" + version;
    }

    @Override
    public List<SchemaPatchRegisterRequest> querySchemaRegisterInfos() {
        return Lists.newArrayList();
    }

    @Override
    public void saveSchemaRegisterInfos(SchemaPatchRegisterRequest schemaPatchRegisterRequest) {
        log.info("save schema register infos");
    }

    @Override
    public PatchOperateResult notifyToThirdService(SchemaPatchRegisterRequest schemaPatchRegisterRequest,
        SchemaPatchUpdateRequest schemaPatchUpdateRequest) {
        return new PatchOperateResult();
    }

    @Override
    public void notifyThirdServiceRegister() {
        log.info("notify third service register");
    }

    @Override
    public void saveUpdateSchemaPatchInfos(long updateTaskId, List<PatchTaskInfo> patchTaskInfos) {
        log.info("save update schema patch infos");
    }

    @Override
    public void saveRollbackSchemaPatchInfos(long updateTaskId, long rollbackTaskId) {
        log.info("save rollback schema patch infos");
    }

    @Override
    public List<PatchTaskInfo> queryByUpdateTaskId(long taskId) {
        return Lists.newArrayList();
    }

    @Override
    public List<PatchTaskInfo> queryByRollbackTaskId(long taskId) {
        return Lists.newArrayList();
    }

    @Override
    public void removeSchemaPatchTaskInfos(List<PatchTaskPo> patchTaskPos) {
        log.info("remove schema patch infos");
    }

    @Override
    public void removeAllSchemaPatchTaskInfos() {
        log.info("remove all schema patch infos");
    }

    @Override
    public List<String> queryPatchInfosByService(String serviceName, String version) {
        return Lists.newArrayList();
    }

    @Override
    public String queryTargetPath(String serviceName, String version) {
        return getRepositoryHome() + File.separator + serviceName + "_" + version + File.separator;
    }
}