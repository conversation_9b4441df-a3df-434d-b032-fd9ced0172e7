/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: MockPatchTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/30
 * </p>
 * <p>
 * 完成日期：2021/3/30
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.test.controller;

import com.zte.daip.manager.common.configcenter.api.ServiceConfigControllerApi;
import com.zte.daip.manager.common.deployer.api.*;
import com.zte.daip.manager.common.deployer.model.controller.api.*;
import com.zte.daip.manager.patcher.domain.schema.adapter.SchemaPatchFlagApi;
import com.zte.daip.manager.patcher.infrastructure.*;
import org.springframework.boot.test.mock.mockito.MockBean;

import com.zte.daip.manager.common.configcenter.api.ConfigPatchControllerApi;
import com.zte.daip.manager.common.deployer.i18n.I18nControllerApi;
import com.zte.daip.manager.patcher.domain.query.service.PatchDetailService;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
public class MockPatch {
    @MockBean
    protected SpectControllerApi spectControllerApi;

    @MockBean
    protected ProductControllerApi productControllerApi;

    @MockBean
    protected ConfigPatchControllerApi configPatchControllerApi;

    @MockBean
    protected I18nControllerApi i18nControllerApi;

    @MockBean
    protected ProductModelInfoControllerApi productModelInfoControllerApi;

    @MockBean
    protected DeployStepControllerApi deployStepControllerApi;

    @MockBean
    protected PatchDetailService patchInfoService;

    @MockBean
    protected SchemaPatchFlagApi schemaPatchFlagApi;

    @MockBean
    private ServiceResourceControllerApi serviceResourceControllerApi;
    @MockBean
    private HostResourceControllerApi hostResourceControllerApi;

    @MockBean
    private ServiceConfigControllerApi serviceConfigControllerApi;
    @MockBean
    private VersionSettingControllerApi versionSettingControllerApi;
    @MockBean
    private ClusterVersionControllerApi clusterVersionControllerApi;
    @MockBean
    private ServiceVersionResourceControllerApi serviceVersionResourceControllerApi;

    @MockBean
    private PatchUpdateProgressCacheApi patchUpdateProgressCacheApi;
    @MockBean
    private PatchUpdateResultCacheApi patchUpdateResultCacheApi;
    @MockBean
    private PatchUpdateFinishedCacheApi patchUpdateFinishedCacheApi;
    @MockBean
    private ClusterInfoControllerApi clusterInfoControllerApi;
    @MockBean
    private PatchUpdateInfoCacheApi patchUpdateInfoCacheApi;
}