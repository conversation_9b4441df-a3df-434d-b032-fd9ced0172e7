/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: COnf.java
 * </p>
 * <p>
 * 文件描述: �?
 * </p>
 * <p>
 * 版权�?�?: 版权�?�?(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴�?�讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: �?
 * </p>
 * <p>
 * 其他说明: �?
 * </p>
 * <p>
 * 创建日期�?2021/4/8
 * </p>
 * <p>
 * 完成日期�?2021/4/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期�?�修改�?�及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期�?
 *    �? �? 号：
 *    �? �? 人：
 *    修改内容�?
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期�?�评审人及评审内�?
 * </p>
 * 
 * <pre>
 *    评审日期�?
 *    �? �? 号：
 *    �? �? 人：
 *    评审内容�?
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.test.controller;

import java.io.File;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.zte.daip.manager.miniagent.seed.utils.ConfigureValue;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Configuration
public class ConfigureValueConf {
    @Bean
    @Primary
    public ConfigureValue ConfigureValue() {

        ConfigureValue configureValueTest = new ConfigureValue() {
            @Override
            public String getLocalRepository() {
                String applicationPath =
                    Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();
                String applicationParentPath = new File(applicationPath).getParent();
                return applicationParentPath + File.separator + "data1/version";
            }
        };
        return configureValueTest;
    }
}