/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: GenerateSeedImplTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/31
 * </p>
 * <p>
 * 完成日期：2021/3/31
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.test.infrastructure;

import com.zte.daip.manager.miniagent.seed.bean.Seed;
import com.zte.daip.manager.miniagent.seed.bean.SeedPriority;
import com.zte.daip.manager.miniagent.seed.bean.SeedVersion;
import com.zte.daip.manager.miniagent.seed.repository.VersionPathGenerator;
import com.zte.daip.manager.miniagent.seed.utils.ConfigureValue;
import com.zte.daip.manager.miniagent.seed.utils.LocalFileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.util.List;

import static junit.framework.TestCase.assertEquals;
import static junit.framework.TestCase.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class GenerateSeedImplTest {
    @Mock
    private ConfigureValue configureValue;
    @Mock
    private VersionPathGenerator versionPathGenerator;
    private LocalFileUtils localFileUtils = new LocalFileUtils();
    @InjectMocks
    private GenerateSeedImpl generateSeed;
    private String localRepository;
    private String patchPath;

    @Before
    public void setUp() {
        localRepository = this.getClass().getResource("/").getPath();
        patchPath = this.getClass().getResource("/patch/DAP-HDFS-V20.19.40.R4.B2-SP028-20200902.zip").getPath();
        when(configureValue.getHostName()).thenReturn("localhost");
        when(configureValue.getClusterId()).thenReturn("100000");
        when(versionPathGenerator.generate(anyString(), anyString(), anyString()))
            .thenReturn(localRepository + "patch" + File.separator + "zookeeper" + File.separator + "20.19.40.R4.B2");
    }

    @Test
    public void 生成本地种子() throws IOException {
        String projectName = "patch";
        String serviceName = "zookeeper";
        String version = "20.19.40.R4.B2";
        SeedPriority priority = SeedPriority.LOCAL_VERSION_MACHINE_SEED;
        SeedVersion seedVersion = SeedVersion.V1;

        localFileUtils.copyFileToDirectory(patchPath, versionPathGenerator.generate(projectName, serviceName, version),
            false);

        final Seed seed = generateSeed.generateSeed(projectName, serviceName, version, priority, seedVersion);

        final List<String> files = seed.getFiles();

        assertEquals(1, files.size());

        assertTrue(files.get(0).contains("DAP-HDFS-V20.19.40.R4.B2-SP028-20200902.zip"));

    }

    @Test
    public void test_seed() throws Exception
    {

        when(configureValue.getClusterId()).thenReturn("1");

        String path = this.getClass().getResource("/").getPath();
        when(versionPathGenerator.generate("zdh", "hdfs", "9.4.2")).thenReturn(path);


        generateSeed.generateSeed("zdh","hdfs","9.4.2", SeedPriority.LOCAL_SEED, SeedVersion.V1);

        verify(versionPathGenerator, times(1)).generate("zdh", "hdfs", "9.4.2");
    }
}
