/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: SchemaPatchImplTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2024/1/16
 * </p>
 * <p>
 * 完成日期：2024/1/16
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.impl.test.infrastructure;

import static junit.framework.TestCase.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.daip.communication.producer.EventPublisher;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.schema.SchemaAction;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchRegisterRequest;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchUpdateRequest;
import com.zte.daip.manager.patcher.api.schema.UpdateSchemaPatchFile;
import com.zte.daip.manager.patcher.domain.schema.entity.PatchTaskInfo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRegisterKey;
import com.zte.daip.manager.patcher.infrastructure.po.PatchRegisterPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskInfoPo;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.infrastructure.repository.SchemaRegisterRepoistory;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchCategoryEnum;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskTypeEnum;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class SchemaPatchImplTest {

    @Mock
    private SchemaRegisterRepoistory schemaRegisterRepoistory;

    @Mock
    private EventPublisher eventPublisher;

    @InjectMocks
    private SchemaPatchImpl schemaPatchImpl;

    @Test
    public void queryPatchPathTest() {
        String str = schemaPatchImpl.queryPatchPath("zookeeper", "SP001");
        assertNotNull(str);
    }

    @Test
    public void queryPatchHomeTest() {
        String str = schemaPatchImpl.queryPatchHome("zookeeper", "SP001");
        assertNotNull(str);
    }

    @Test
    public void getRepositoryHomeTest() {
        String str = schemaPatchImpl.getRepositoryHome();
        assertNotNull(str);
    }

    @Test
    public void queryTargetPathTest() {
        String str = schemaPatchImpl.queryTargetPath("zookeeper", "SP001");
        assertNotNull(str);
    }

    @Test
    public void querySchemaRegisterInfosTest() {
        List<SchemaPatchRegisterRequest> schemaPatchRegisterRequests = schemaPatchImpl.querySchemaRegisterInfos();
        assertEquals(schemaPatchRegisterRequests.size(), 0);
    }

    @Test
    public void saveSchemaRegisterInfosTest() {
        schemaPatchImpl.saveSchemaRegisterInfos(initSchemaPatchRegisterRequest());
    }

    @Test
    public void notifyToThirdServiceTest() {
        SchemaPatchUpdateRequest schemaPatchUpdateRequest = new SchemaPatchUpdateRequest("zookeeper", "V20.19.40.R4.B2",
            "SP001", "SP005", Lists.newArrayList(), SchemaAction.UPDATE);
        PatchOperateResult patchOperateResult =
            schemaPatchImpl.notifyToThirdService(initSchemaPatchRegisterRequest(), schemaPatchUpdateRequest);
        assertEquals(patchOperateResult.isStatus(), false);
    }

    @Test
    public void notifyThirdServiceRegisterTest() {
        schemaPatchImpl.notifyThirdServiceRegister();
    }

    @Test
    public void saveSchemaPatchInfosTest() {
        schemaPatchImpl.saveUpdateSchemaPatchInfos(1L, initPatchTaskInfos());
    }

    @Test
    public void saveUpdatePatchInfosTest() {
        schemaPatchImpl.saveRollbackSchemaPatchInfos(1L, 2L);
    }

    @Test
    public void queryPatchInfosByTaskIdTest() {
        List<PatchTaskInfo> patchTaskInfos = schemaPatchImpl.queryByUpdateTaskId(1L);
        assertEquals(patchTaskInfos.size(), 0);
    }

    @Test
    public void queryByRollbackTaskIdTest() {
        List<PatchTaskInfo> patchTaskInfos = schemaPatchImpl.queryByRollbackTaskId(1L);
        assertEquals(patchTaskInfos.size(), 0);
    }

    @Test
    public void removeSchemaPatchTaskInfosTest() {
        PatchTaskInfoPo patchTaskInfoPo = new PatchTaskInfoPo();
        patchTaskInfoPo.setPatchInfos(JSON.toJSONString(initPatchTaskInfos()));
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        patchTaskPo.setPatchCategory(PatchCategoryEnum.SCHEMA.getPatchCategory());
        patchTaskPo.setTaskType(PatchTaskTypeEnum.ROLLBACK.getTaskType());
        schemaPatchImpl.removeSchemaPatchTaskInfos(Lists.newArrayList(patchTaskPo));
        patchTaskPo.setTaskType(PatchTaskTypeEnum.UPDATE.getTaskType());
        schemaPatchImpl.removeSchemaPatchTaskInfos(Lists.newArrayList(patchTaskPo));
    }

    @Test
    public void removeAllSchemaPatchTaskInfosTest() {
        schemaPatchImpl.removeAllSchemaPatchTaskInfos();
    }

    private List<PatchTaskInfo> initPatchTaskInfos() {
        List<PatchTaskInfo> patchTaskInfos = Lists.newArrayList();
        PatchTaskInfo patchTaskInfo = new PatchTaskInfo();
        patchTaskInfo.setServiceName("kafka");
        patchTaskInfo.setVersion("V20.19.40.R4.B2");
        patchTaskInfo.setUpdatePatchNames(Lists.newArrayList("DAP-deployer-V20.19.40.R4.B2-schema-SP030-20201215"));
        patchTaskInfos.add(patchTaskInfo);
        return patchTaskInfos;
    }

    private List<UpdateSchemaPatchFile> initRegisterSchemaPatchFile() {
        List<UpdateSchemaPatchFile> registerSchemaPatchFiles = Lists.newArrayList();
        UpdateSchemaPatchFile registerSchemaPatchFile = new UpdateSchemaPatchFile();
        registerSchemaPatchFile.setUpdatePatchFilePath("");
        registerSchemaPatchFile.setUpdatePatchFileName(".*-configure-default.json");
        registerSchemaPatchFiles.add(registerSchemaPatchFile);
        return registerSchemaPatchFiles;
    }

    private SchemaPatchRegisterRequest initSchemaPatchRegisterRequest() {
        SchemaPatchRegisterRequest schemaPatchRegisterRequest = new SchemaPatchRegisterRequest();
        schemaPatchRegisterRequest.setPort("56160");
        schemaPatchRegisterRequest.setServiceName("daip-deployer-svr");
        schemaPatchRegisterRequest.setUrl("/api/daip-deployer-svr/v1/schema/update");
        schemaPatchRegisterRequest.setRegisterSchemaPatchFiles(initRegisterSchemaPatchFile());
        return schemaPatchRegisterRequest;
    }

}