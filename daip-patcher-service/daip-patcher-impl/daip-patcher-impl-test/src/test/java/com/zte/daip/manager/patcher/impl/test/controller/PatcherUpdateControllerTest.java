package com.zte.daip.manager.patcher.impl.test.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateCacheService;
import com.zte.daip.manager.patcher.interfaces.controller.PatcherUpdateController;

public class PatcherUpdateControllerTest {

    @Mock
    private PatchUpdateCacheService patchUpdateCacheService;
    @InjectMocks
    private PatcherUpdateController patcherUpdateController;

    @Test(expected = Exception.class)
    public void queryNeedUpdatePatchInfo() throws UnsupportedEncodingException {
        String encode = URLEncoder.encode("1_/opt/ZDH/HOME_ZDH", "utf-8");
        patcherUpdateController.queryNeedUpdatePatchInfo(encode, "************");
    }
}