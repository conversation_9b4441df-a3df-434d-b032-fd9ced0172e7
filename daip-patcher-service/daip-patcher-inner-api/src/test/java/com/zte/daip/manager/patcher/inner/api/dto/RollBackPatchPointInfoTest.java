package com.zte.daip.manager.patcher.inner.api.dto;

import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: RollBackPatchPointInfoTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/10/12</p>
 * <p>完成日期：2023/10/12</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = RollBackPatchPointInfo.class)
public class RollBackPatchPointInfoTest
{

    @Test
    public void setRollBackPatchPoint()
    {
        RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
        rollBackPatchPointInfo.setRollBackPatchPoint("V1");
        assertThat("V1", is(rollBackPatchPointInfo.getRollBackPatchPoint()));
    }

    @Test
    public void setPatchHostInfos()
    {
        RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
        PatchHostInfoDto patchHostInfoDto = new PatchHostInfoDto();
        patchHostInfoDto.setIp("*******");
        patchHostInfoDto.setHostName("host1");
        rollBackPatchPointInfo.setPatchHostInfos(Lists.newArrayList(patchHostInfoDto));
        assertThat(1, is(rollBackPatchPointInfo.getPatchHostInfos().size()));
    }

    public void testEqualsAndHashCode()
    {
        PatchHostInfoDto patchHostInfoDto1 = new PatchHostInfoDto();
        patchHostInfoDto1.setIp("*******");
        patchHostInfoDto1.setHostName("host1");
        RollBackPatchPointInfo rollBackPatchPointInfo1 = new RollBackPatchPointInfo();
        rollBackPatchPointInfo1.setPatchHostInfos(Lists.newArrayList(patchHostInfoDto1));
        PatchHostInfoDto patchHostInfoDto2 = new PatchHostInfoDto();
        patchHostInfoDto2.setIp("*******");
        patchHostInfoDto2.setHostName("host1");
        RollBackPatchPointInfo rollBackPatchPointInfo2 = new RollBackPatchPointInfo();
        rollBackPatchPointInfo2.setPatchHostInfos(Lists.newArrayList(patchHostInfoDto2));
        Assert.assertEquals(rollBackPatchPointInfo1.hashCode(), rollBackPatchPointInfo2.hashCode());
        Assert.assertEquals(rollBackPatchPointInfo1, rollBackPatchPointInfo2);
    }
}