package com.zte.daip.manager.patcher.inner.api.dto;

import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchRollbackHostInfoTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/11/20</p>
 * <p>完成日期：2023/11/20</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = PatchRollbackHostInfo.class)
public class PatchRollbackHostInfoTest
{
    @Test
    public void setPatchName()
    {
        PatchRollbackHostInfo patchRollbackHostInfo = new PatchRollbackHostInfo();
        patchRollbackHostInfo.setPatchName("1");
        assertThat("1", is(patchRollbackHostInfo.getPatchName()));
    }

    @Test
    public void setServiceInstanceId()
    {
        PatchRollbackHostInfo patchRollbackHostInfo = new PatchRollbackHostInfo();
        patchRollbackHostInfo.setServiceInstanceId("1");
        assertThat("1", is(patchRollbackHostInfo.getServiceInstanceId()));
    }

    @Test
    public void setServiceName()
    {
        PatchRollbackHostInfo patchRollbackHostInfo = new PatchRollbackHostInfo();
        patchRollbackHostInfo.setServiceName("1");
        assertThat("1", is(patchRollbackHostInfo.getServiceName()));
    }

    @Test
    public void setRoleName()
    {
        PatchRollbackHostInfo patchRollbackHostInfo = new PatchRollbackHostInfo();
        patchRollbackHostInfo.setRoleName("1");
        assertThat("1", is(patchRollbackHostInfo.getRoleName()));
    }

    @Test
    public void setPatchHostInfoList()
    {
        List<PatchHostInfoDto> patchHostInfoDtos = Lists.newArrayList(new PatchHostInfoDto());
        PatchRollbackHostInfo patchRollbackHostInfo = new PatchRollbackHostInfo();
        patchRollbackHostInfo.setPatchHostInfoList(patchHostInfoDtos);
        assertThat(1, is(patchRollbackHostInfo.getPatchHostInfoList().size()));
    }

    @Test
    public void testEqualsAndHashCode()
    {
        String service = "hdfs";

        PatchRollbackHostInfo patchRollbackHostInfo1 = new PatchRollbackHostInfo();
        patchRollbackHostInfo1.setServiceInstanceId(service);
        patchRollbackHostInfo1.setServiceName(service);
        patchRollbackHostInfo1.setRoleName(service);
        patchRollbackHostInfo1.setPatchName(service);
        patchRollbackHostInfo1.setPatchHostInfoList(Lists.newArrayList());
        PatchRollbackHostInfo patchRollbackHostInfo2 = new PatchRollbackHostInfo();
        patchRollbackHostInfo2.setServiceInstanceId(service);
        patchRollbackHostInfo2.setServiceName(service);
        patchRollbackHostInfo2.setRoleName(service);
        patchRollbackHostInfo2.setPatchName(service);
        patchRollbackHostInfo2.setPatchHostInfoList(Lists.newArrayList());
        Assert.assertEquals(patchRollbackHostInfo1.hashCode(), patchRollbackHostInfo2.hashCode());
        Assert.assertEquals(patchRollbackHostInfo1, patchRollbackHostInfo2);
    }
}