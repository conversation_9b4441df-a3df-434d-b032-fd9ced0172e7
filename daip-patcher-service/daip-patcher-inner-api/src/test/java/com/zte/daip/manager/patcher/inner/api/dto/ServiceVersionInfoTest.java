package com.zte.daip.manager.patcher.inner.api.dto;

import org.junit.Assert;
import org.junit.Test;

public class ServiceVersionInfoTest {

    @Test
    public void test(){
        ServiceVersionInfo serviceVersionInfo = new ServiceVersionInfo();
        serviceVersionInfo.setServiceId("dap.manager.trino");
        serviceVersionInfo.setServiceName("trino");
        serviceVersionInfo.setComponentType("dap.manager.common.bigdata");
        Assert.assertEquals("dap.manager.trino", serviceVersionInfo.getServiceId());
        Assert.assertEquals("trino", serviceVersionInfo.getServiceName());
        Assert.assertEquals("dap.manager.common.bigdata", serviceVersionInfo.getComponentType());
    }

}