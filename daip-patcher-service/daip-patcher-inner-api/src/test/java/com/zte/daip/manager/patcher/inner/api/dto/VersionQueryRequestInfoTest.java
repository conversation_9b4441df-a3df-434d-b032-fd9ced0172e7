package com.zte.daip.manager.patcher.inner.api.dto;

import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import com.zte.daip.manager.common.task.common.enums.UpgradeModel;

public class VersionQueryRequestInfoTest {

    @Test
    public void test(){
        VersionQueryRequestInfo versionQueryRequestInfo = new VersionQueryRequestInfo();
        versionQueryRequestInfo.setAccessType(UpgradeModel.ROLLING_UPDATE.getModel());
        versionQueryRequestInfo.setServiceVersionInfoList(Lists.newArrayList());
        Assert.assertEquals(UpgradeModel.ROLLING_UPDATE.getModel(), versionQueryRequestInfo.getAccessType());
        Assert.assertTrue(CollectionUtils.isEmpty(versionQueryRequestInfo.getServiceVersionInfoList()));
    }

}