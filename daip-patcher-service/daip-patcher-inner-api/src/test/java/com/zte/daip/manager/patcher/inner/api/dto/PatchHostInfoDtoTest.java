package com.zte.daip.manager.patcher.inner.api.dto;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchHostInfoDtoTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/10/12</p>
 * <p>完成日期：2023/10/12</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = PatchHostInfoDto.class)
public class PatchHostInfoDtoTest
{

    @Test
    public void setIp()
    {
        PatchHostInfoDto patchHostInfoDto = new PatchHostInfoDto();
        patchHostInfoDto.setIp("*******");
        assertThat("*******", is(patchHostInfoDto.getIp()));
    }

    @Test
    public void setHostName()
    {
        PatchHostInfoDto patchHostInfoDto = new PatchHostInfoDto();
        patchHostInfoDto.setIp("host1");
        assertThat("host1", is(patchHostInfoDto.getIp()));
    }

    public void testEqualsAndHashCode()
    {
        PatchHostInfoDto patchHostInfoDto1 = new PatchHostInfoDto();
        patchHostInfoDto1.setIp("*******");
        patchHostInfoDto1.setHostName("host1");
        PatchHostInfoDto patchHostInfoDto2 = new PatchHostInfoDto();
        patchHostInfoDto2.setIp("*******");
        patchHostInfoDto2.setHostName("host1");
        Assert.assertEquals(patchHostInfoDto1.hashCode(), patchHostInfoDto2.hashCode());
        Assert.assertEquals(patchHostInfoDto1, patchHostInfoDto2);
    }
}