package com.zte.daip.manager.patcher.inner.api.dto;

import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: ServiceInstancePatchInfoTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/9/28</p>
 * <p>完成日期：2023/9/28</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceInstancePatchInfo.class)
public class ServiceInstancePatchInfoTest
{
    private ServiceInstance serviceInstance = new ServiceInstance();
    private List<RollBackPatchPointInfo> rollBackPatchPointInfoList = Lists.newArrayList();

    @Before
    public void init() {
        String serviceId = "dap.manager.hdfs";
        String service = "hdfs";
        serviceInstance.setServiceId(serviceId);
        serviceInstance.setServiceName(service);
        serviceInstance.setServiceInstanceId(service);
        serviceInstance.setServiceInstanceName(service);

        RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
        rollBackPatchPointInfo.setRollBackPatchPoint("1");
        PatchHostInfoDto patchHostInfoDto = new PatchHostInfoDto();
        patchHostInfoDto.setIp("*******");
        patchHostInfoDto.setHostName("host1");
        rollBackPatchPointInfo.setPatchHostInfos(Lists.newArrayList(patchHostInfoDto));
        rollBackPatchPointInfoList.add(rollBackPatchPointInfo);
    }

    @Test
    public void setServiceInstance()
    {
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);
        assertThat(serviceInstance, is(serviceInstancePatchInfo.getServiceInstance()));
    }

    @Test
    public void setRollBackPatchPoint()
    {
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        serviceInstancePatchInfo.setRollBackPatchPoints(rollBackPatchPointInfoList);
        assertThat(1, is(serviceInstancePatchInfo.getRollBackPatchPoints().size()));
    }


    @Test
    public void convertRollbackPatchPointMapTest()
    {
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        serviceInstancePatchInfo.setRollBackPatchPoints(rollBackPatchPointInfoList);
        assertThat(1, is(serviceInstancePatchInfo.convertRollbackPatchPointMap().size()));
    }

    @Test
    public void convertCurrentPatchPointMapTest()
    {
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        serviceInstancePatchInfo.setCurrentPatchPoints(rollBackPatchPointInfoList);
        assertThat(1, is(serviceInstancePatchInfo.convertCurrentPatchPointMap().size()));
    }

    @Test
    public void testEqualsAndHashCode()
    {
        ServiceInstancePatchInfo serviceInstancePatchInfo1 = new ServiceInstancePatchInfo();
        serviceInstancePatchInfo1.setServiceInstance(serviceInstance);
        serviceInstancePatchInfo1.setRollBackPatchPoints(rollBackPatchPointInfoList);
        ServiceInstancePatchInfo serviceInstancePatchInfo2 = new ServiceInstancePatchInfo();
        serviceInstancePatchInfo2.setServiceInstance(serviceInstance);
        serviceInstancePatchInfo2.setRollBackPatchPoints(rollBackPatchPointInfoList);
        Assert.assertEquals(serviceInstancePatchInfo1.hashCode(), serviceInstancePatchInfo2.hashCode());
        Assert.assertEquals(serviceInstancePatchInfo1, serviceInstancePatchInfo2);
    }
}