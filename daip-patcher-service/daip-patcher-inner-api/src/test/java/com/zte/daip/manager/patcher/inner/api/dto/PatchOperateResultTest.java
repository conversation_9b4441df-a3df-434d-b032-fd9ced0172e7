/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchOperateResultTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2024/5/23
 * </p>
 * <p>
 * 完成日期：2024/5/23
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.inner.api.dto;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertTrue;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PatchOperateResult.class)
public class PatchOperateResultTest {
    @Test
    public void setMessage() {
        PatchOperateResult patchOperateResult = new PatchOperateResult();
        patchOperateResult.setMessage("success");
        assertThat("success", is(patchOperateResult.getMessage()));
    }

    @Test
    public void setStatus() {
        PatchOperateResult patchOperateResult = new PatchOperateResult();
        patchOperateResult.setStatus(true);
        assertThat(true, is(patchOperateResult.isStatus()));
    }

    @Test
    public void setHostName() {
        PatchOperateResult patchOperateResult = new PatchOperateResult();
        patchOperateResult.setHostName("host1");
        assertThat("host1", is(patchOperateResult.getHostName()));
    }

    @Test
    public void success() {
        PatchOperateResult result = PatchOperateResult.success("");
        assertTrue(result.isStatus());
    }

    @Test
    public void construct() {
        PatchOperateResult result = new PatchOperateResult(true, "");
        assertTrue(result.isStatus());
    }

    @Test
    public void fail() {
        PatchOperateResult result = PatchOperateResult.fail("");
        assertTrue(!result.isStatus());
    }

    public void testEqualsAndHashCode()
    {
        PatchOperateResult patchOperateResult1 = new PatchOperateResult();
        patchOperateResult1.setMessage("");
        patchOperateResult1.setHostName("host1");
        PatchOperateResult patchOperateResult2 = new PatchOperateResult();
        patchOperateResult1.setMessage("");
        patchOperateResult1.setHostName("host1");
        Assert.assertEquals(patchOperateResult1.hashCode(), patchOperateResult2.hashCode());
        Assert.assertEquals(patchOperateResult2, patchOperateResult1);
    }

}