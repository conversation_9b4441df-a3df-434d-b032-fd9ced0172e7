package com.zte.daip.manager.patcher.inner.api.dto;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: ServiceInstancePatchInfoTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/9/28</p>
 * <p>完成日期：2023/9/28</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceInstance.class)
public class ServiceInstanceTest
{
    @Test
    public void setServiceId()
    {
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceId("1");
        assertThat("1", is(serviceInstance.getServiceId()));
    }

    @Test
    public void setServiceName()
    {
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceName("1");
        assertThat("1", is(serviceInstance.getServiceName()));
    }

    @Test
    public void setServiceInstanceId()
    {
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceInstanceId("1");
        assertThat("1", is(serviceInstance.getServiceInstanceId()));
    }

    @Test
    public void setServiceInstanceName()
    {
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceInstanceName("1");
        assertThat("1", is(serviceInstance.getServiceInstanceName()));
    }

    @Test
    public void setRoleName()
    {
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setRoleName("1");
        assertThat("1", is(serviceInstance.getRoleName()));
    }

    @Test
    public void setVersion()
    {
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setVersion("1");
        assertThat("1", is(serviceInstance.getVersion()));
    }

    @Test
    public void setRollbackServiceInstanceId()
    {
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setRollbackServiceInstanceId("1");
        assertThat("1", is(serviceInstance.getRollbackServiceInstanceId()));
    }

    @Test
    public void testEqualsAndHashCode()
    {
        String serviceId = "dap.manager.hdfs";
        String service = "hdfs";

        ServiceInstance serviceInstance1 = new ServiceInstance();
        serviceInstance1.setServiceId(serviceId);
        serviceInstance1.setServiceName(service);
        serviceInstance1.setServiceInstanceId(service);
        serviceInstance1.setServiceInstanceName(service);
        ServiceInstance serviceInstance2 = new ServiceInstance();
        serviceInstance2.setServiceId(serviceId);
        serviceInstance2.setServiceName(service);
        serviceInstance2.setServiceInstanceId(service);
        serviceInstance2.setServiceInstanceName(service);
        Assert.assertEquals(serviceInstance1.hashCode(), serviceInstance2.hashCode());
        Assert.assertEquals(serviceInstance1, serviceInstance2);
    }
}