package com.zte.daip.manager.patcher.inner.api.dto;

import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchTaskDtoTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/9/28</p>
 * <p>完成日期：2023/9/28</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = PatchTaskDto.class)
public class PatchTaskDtoTest
{
    @Test
    public void setTaskId()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setTaskId(1L);
        assertThat(1L, is(patchTaskDto.getTaskId()));
    }

    @Test
    public void setTaskName()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setTaskName("1");
        assertThat("1", is(patchTaskDto.getTaskName()));
    }

    @Test
    public void setTaskType()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setTaskType(1);
        assertThat(1, is(patchTaskDto.getTaskType()));
    }

    @Test
    public void setClusterId()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setClusterId("1");
        assertThat("1", is(patchTaskDto.getClusterId()));
    }

    @Test
    public void setClusterName()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setClusterName("1");
        assertThat("1", is(patchTaskDto.getClusterName()));
    }

    @Test
    public void setOperateType()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setOperateType(1);
        assertThat(1, is(patchTaskDto.getOperateType()));
    }

    @Test
    public void setCreateTime()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setCreateTime("1");
        assertThat("1", is(patchTaskDto.getCreateTime()));
    }

    @Test
    public void setAllowModify()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setAllowModify(false);
        assertThat(false, is(patchTaskDto.isAllowModify()));
    }

    @Test
    public void setPatchType()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setPatchCategory("schema");
        assertThat("schema", is(patchTaskDto.getPatchCategory()));
    }

    @Test
    public void setLatestExecutionTime()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setLatestExecutionTime("1");
        assertThat("1", is(patchTaskDto.getLatestExecutionTime()));
    }

    @Test
    public void setStatus()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setStatus("1");
        assertThat("1", is(patchTaskDto.getStatus()));
    }

    @Test
    public void setSuccessCnt()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setSuccessCnt(1);
        assertThat(1, is(patchTaskDto.getSuccessCnt()));
    }

    @Test
    public void setTotal()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setTotal(1);
        assertThat(1, is(patchTaskDto.getTotal()));
    }

    @Test
    public void setContext()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        List<ServiceInstancePatchInfo> context = Lists.newArrayList(new ServiceInstancePatchInfo());
        patchTaskDto.setContext(context);
        assertThat(1, is(patchTaskDto.getContext().size()));
    }

    @Test
    public void setAccessType()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setAccessType("1");
        assertThat("1", is(patchTaskDto.getAccessType()));
    }

    @Test
    public void setRelationTaskId()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setRelationTaskId(1L);
        assertThat(1L, is(patchTaskDto.getRelationTaskId()));
    }

    @Test
    public void setRelationServices()
    {
        PatchTaskDto patchTaskDto = new PatchTaskDto();
        List<ServiceInstance> serviceInstances = Lists.newArrayList(new ServiceInstance());
        patchTaskDto.setRelationServices(serviceInstances);
        assertThat(1, is(patchTaskDto.getRelationServices().size()));
    }
    /* Started by AICoder, pid:0c0caee354967601466309851071893a3532f2e7 */
    @Test
    public void setNeedRestartServices() {
        // 创建一个PatchTaskDto对象
        PatchTaskDto patchTaskDto = new PatchTaskDto();

        // 创建一个ServiceInstance列表，并添加一个ServiceInstance对象
        List<ServiceInstance> serviceInstances = Lists.newArrayList(new ServiceInstance());

        // 设置需要重启的服务列表
        patchTaskDto.setNeedRestartServices(serviceInstances);

        // 验证getNeedRestartServices方法返回的列表大小是否为1
        assertThat(1, is(patchTaskDto.getNeedRestartServices().size()));
    }

    @Test
    public void testEqualsAndHashCode() {
        // 创建两个PatchTaskDto对象，它们具有相同的needRestartServices和taskId
        PatchTaskDto patchTaskDto1 = new PatchTaskDto();
        patchTaskDto1.setNeedRestartServices(Lists.newArrayList());
        patchTaskDto1.setTaskId(1L);

        PatchTaskDto patchTaskDto2 = new PatchTaskDto();
        patchTaskDto2.setNeedRestartServices(Lists.newArrayList());
        patchTaskDto2.setTaskId(1L);

        // 验证两个对象的hashCode是否相等
        Assert.assertEquals(patchTaskDto1.hashCode(), patchTaskDto2.hashCode());

        // 验证两个对象是否相等
        Assert.assertEquals(patchTaskDto1, patchTaskDto2);
    }

    /* Ended by AICoder, pid:0c0caee354967601466309851071893a3532f2e7 */
}