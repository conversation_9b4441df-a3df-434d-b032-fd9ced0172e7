package com.zte.daip.manager.patcher.inner.api.dto;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceInstanceUpgrade.class)
public class ServiceInstanceUpgradeTest {

    @Test
    public void setIp()
    {
        ServiceInstanceUpgrade patchHostInfoDto = new ServiceInstanceUpgrade();
        patchHostInfoDto.setServiceId("test");
        patchHostInfoDto.setTargetVersions(new ArrayList<>());
        assertThat("test", is(patchHostInfoDto.getServiceId()));
        ServiceInstanceUpgrade patchHostInfoDto1 = new ServiceInstanceUpgrade("test", new ArrayList<>());
        Assert.assertEquals(0, patchHostInfoDto1.getTargetVersions().size());
    }


    public void testEqualsAndHashCode()
    {
        ServiceInstanceUpgrade patchHostInfoDto1 = new ServiceInstanceUpgrade();
        patchHostInfoDto1.setServiceId("test");
        patchHostInfoDto1.setTargetVersions(new ArrayList<>());
        ServiceInstanceUpgrade patchHostInfoDto2 = new ServiceInstanceUpgrade();
        patchHostInfoDto2.setServiceId("test");
        patchHostInfoDto2.setTargetVersions(new ArrayList<>());
        Assert.assertEquals(patchHostInfoDto1.hashCode(), patchHostInfoDto2.hashCode());
        Assert.assertEquals(patchHostInfoDto1, patchHostInfoDto2);
    }
}
