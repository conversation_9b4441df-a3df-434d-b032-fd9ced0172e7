package com.zte.daip.manager.patcher.inner.api.dto;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class RollBackPatchPointInfo {
    private String rollBackPatchPoint = "";
    private String displayRollbackPatchPoint = "";
    private List<PatchHostInfoDto> patchHostInfos = Lists.newArrayList();

    public RollBackPatchPointInfo(String rollBackPatchPoint, List<PatchHostInfoDto> patchHostInfos) {
        this.rollBackPatchPoint = rollBackPatchPoint;
        this.patchHostInfos = patchHostInfos;
    }
}