package com.zte.daip.manager.patcher.inner.api.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PatchRollbackHostInfo {
    private String serviceName;
    private String serviceInstanceId;
    private String roleName;
    private String patchName;
    private List<PatchHostInfoDto> patchHostInfoList;
}