/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchStartAndStopControllerApi.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/21
 * </p>
 * <p>
 * 完成日期：2023/4/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.inner.api;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateServiceDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface PatchStartAndStopControllerApi {
    @ApiOperation(value = "查询补丁升级（回退）过程中的可启动（停止）的服务实例id.（集群启停方式）", notes = "查询补丁升级（回退）过程中的可启动（停止）的服务实例id.（集群启停方式）")
    @PostMapping(value = "/patches/cluster/services")
    List<String> queryCanOperateServices(@RequestParam(value = "clusterId", required = true) String clusterId,
        @RequestParam(value = "isStart", required = true) boolean isStart,
        @RequestBody(required = false) List<PatchOperateServiceDto> needOperateServiceDtos) throws DaipBaseException;

    @ApiOperation(value = "补丁升级（回退）过程中的服务启动.（集群启动方式）", notes = "补丁升级（回退）过程中的服务启动.（集群启动方式）")
    @PostMapping(value = "/patches/cluster/start")
    void startCluster(@RequestParam(value = "clusterId", required = true) String clusterId,
        @RequestBody(required = false) List<String> needOperateInstanceIds) throws DaipBaseException;

    @ApiOperation(value = "补丁升级（回退）过程中的服务停止.（集群停止方式）", notes = "补丁升级（回退）过程中的服务停止.（集群停止方式")
    @PostMapping("/patches/cluster/stop")
    void stopCluster(@RequestParam(value = "clusterId", required = true) String clusterId,
        @RequestBody(required = false) List<String> needOperateInstanceIds) throws DaipBaseException;

    @ApiOperation(value = "查询补丁升级（回退）过程中的启动（停止）的进度.（集群启停方式）", notes = "查询补丁升级（回退）过程中的启动（停止）.的服务实例id")
    @PostMapping(value = "/patches/cluster/progress")
    String queryOperatorProcess(@RequestParam(value = "clusterId", required = true) String clusterId)
        throws DaipBaseException;
}
