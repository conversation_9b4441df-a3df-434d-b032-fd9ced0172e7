package com.zte.daip.manager.patcher.inner.api.dto.task;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public enum PatchTaskModifyPermisionEnum
{

    ALLOW(0),

    DENIED(1);

    private final int modifyPermision;

    PatchTaskModifyPermisionEnum(int modifyPermision) {
        this.modifyPermision = modifyPermision;
    }

    public int getModifyPermision()
    {
        return modifyPermision;
    }
}
