package com.zte.daip.manager.patcher.inner.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ServiceInstance {
    private String serviceId;
    private String serviceName;
    private String serviceInstanceId;
    private String serviceInstanceName;
    private String roleName;
    private String version;
    private String rollbackServiceInstanceId;
}