package com.zte.daip.manager.patcher.inner.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class PatchHostInfoDto {
    private String ip;
    private String hostName;

    public PatchHostInfoDto(String ip, String hostName) {
        this.ip = ip;
        this.hostName = hostName;
    }
}