/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchOperateResult.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/8/31
 * </p>
 * <p>
 * 完成日期：2023/8/31
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.inner.api.dto;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatchOperateResult {
    private boolean status;

    private String message;

    private String hostName;

    private Map<String, String> extraInfo;

    public PatchOperateResult(boolean status, String message) {
        this.status = status;
        this.message = message;
    }

    public static PatchOperateResult success(String msg) {
        PatchOperateResult patchOperateResult = new PatchOperateResult();
        patchOperateResult.setStatus(true);
        patchOperateResult.setMessage(msg);
        return patchOperateResult;
    }

    public static PatchOperateResult fail(String msg) {
        PatchOperateResult patchOperateResult = new PatchOperateResult();
        patchOperateResult.setStatus(false);
        patchOperateResult.setMessage(msg);
        return patchOperateResult;
    }

}