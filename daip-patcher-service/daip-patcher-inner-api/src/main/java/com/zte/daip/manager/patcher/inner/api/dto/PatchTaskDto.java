package com.zte.daip.manager.patcher.inner.api.dto;

import com.zte.daip.manager.patcher.inner.api.dto.task.PatchCategoryEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@NoArgsConstructor
public class PatchTaskDto
{
    private long taskId;
    private String taskName;
    private int taskType;
    private String clusterId;
    private String clusterName;
    private int operateType;
    private String createTime;
    private boolean allowModify = true;
    private String patchCategory = PatchCategoryEnum.ORDINARY.getPatchCategory();
    private long relationTaskId;
    private String accessType;
    private String latestExecutionTime;
    private String status;
    private int successCnt;
    private int total;
    private List<ServiceInstancePatchInfo> context;
    private List<ServiceInstance> relationServices;
    private List<ServiceInstance> needRestartServices;
}