package com.zte.daip.manager.patcher.inner.api.dto;

import com.zte.daip.manager.common.task.common.enums.UpgradeModel;
import com.zte.daip.manager.common.utils.validator.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class VersionQueryRequestInfo {

    private List<ServiceVersionInfo> serviceVersionInfoList;

    /**
     * OFFLINE: 离线 <br>
     * ROLLING: 滚动
     * @see UpgradeModel
     */
    @NotBlank(message = "accessType can not be null!")
    @EnumValue(value = UpgradeModel.class, method = "getModel", message = "upgrade_method_error")
    private String accessType;
}
