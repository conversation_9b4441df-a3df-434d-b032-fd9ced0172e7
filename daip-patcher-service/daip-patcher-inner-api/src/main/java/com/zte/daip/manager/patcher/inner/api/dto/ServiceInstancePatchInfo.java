package com.zte.daip.manager.patcher.inner.api.dto;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zte.daip.manager.patcher.inner.api.dto.task.PatchTaskTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class ServiceInstancePatchInfo {
    private ServiceInstance serviceInstance;
    private String patchType;
    private String targetPatchPoint;
    private List<RollBackPatchPointInfo> rollBackPatchPoints = Lists.newArrayList();
    private List<RollBackPatchPointInfo> currentPatchPoints = Lists.newArrayList();

    public Set<PatchHostInfoDto> queryRollBackPointHosts() {
        Set<PatchHostInfoDto> rollbackHostPoints = Sets.newHashSet();
        rollBackPatchPoints
            .forEach(rollBackPatchPointInfo -> rollbackHostPoints.addAll(rollBackPatchPointInfo.getPatchHostInfos()));
        return rollbackHostPoints;
    }

    public Map<String, String> convertRollbackPatchPointMap() {
        Map<String, String> rollbackPatchPointMap = Maps.newHashMap();
        for (RollBackPatchPointInfo rollBackPatchPointInfo : rollBackPatchPoints) {
            List<PatchHostInfoDto> patchHostInfos = rollBackPatchPointInfo.getPatchHostInfos();
            for (PatchHostInfoDto patchHostInfoDto : patchHostInfos) {
                if (!rollbackPatchPointMap.containsKey(patchHostInfoDto.getIp()))
                    rollbackPatchPointMap.put(patchHostInfoDto.getIp(),
                        rollBackPatchPointInfo.getDisplayRollbackPatchPoint());
            }
        }
        return rollbackPatchPointMap;
    }

    public Map<String, String> convertCurrentPatchPointMap() {
        Map<String, String> currentPatchPointMap = Maps.newHashMap();
        for (RollBackPatchPointInfo currentPatchPointInfo : currentPatchPoints) {
            List<PatchHostInfoDto> patchHostInfos = currentPatchPointInfo.getPatchHostInfos();
            for (PatchHostInfoDto patchHostInfoDto : patchHostInfos) {
                if (!currentPatchPointMap.containsKey(patchHostInfoDto.getIp()))
                    currentPatchPointMap.put(patchHostInfoDto.getIp(), currentPatchPointInfo.getRollBackPatchPoint());
            }
        }
        return currentPatchPointMap;
    }
}