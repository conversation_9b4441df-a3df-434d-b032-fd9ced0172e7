package com.zte.daip.manager.patcher.inner.api;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface PatchTaskControllerApi {
    @ApiOperation(value = "新增补丁任务", notes = "新增补丁任务")
    @PostMapping(value = "/patch/task/create")
    long createPatchTask(@RequestBody(required = true) PatchTaskDto patchTaskDto);

    @ApiOperation(value = "修改补丁任务", notes = "修改补丁任务")
    @PostMapping(value = "/patch/task/modify")
    boolean modifyPatchTask(@RequestBody(required = true) PatchTaskDto patchTaskDto);

    @ApiOperation(value = "删除补丁任务", notes = "删除补丁任务")
    @PostMapping(value = "/patch/task/remove")
    boolean removePatchTask(@RequestParam(value = "taskIds", required = true) List<Long> taskIds);

    @ApiOperation(value = "查询补丁任务", notes = "查询补丁任务")
    @GetMapping(value = "/patch/tasks")
    List<PatchTaskDto> queryPatchTasks() throws DaipBaseException;

    @ApiOperation(value = "查询补丁任务详情", notes = "查询补丁任务详情")
    @GetMapping(value = "/patch/task/detail/{taskId}")
    PatchTaskDto queryPatchDetailTasks(@PathVariable(value = "taskId") long taskId) throws DaipBaseException;

    @ApiOperation(value = "根据任务ID查询补丁任务", notes = "根据任务ID查询补丁任务")
    @GetMapping(value = "/patch/task/{taskId}")
    PatchTaskDto queryPatchTaskByTaskId(@PathVariable(value = "taskId") long taskId) throws DaipBaseException;

    @ApiOperation(value = "执行补丁任务", notes = "执行补丁任务")
    @PostMapping(value = "/patch/task/trigger")
    boolean triggerPatchTask(@RequestParam(value = "taskId", required = true) long taskId);

    @ApiOperation(value = "重试补丁任务", notes = "重试补丁任务")
    @PostMapping(value = "/patch/task/retry")
    boolean retryPatchTask(@RequestParam(value = "taskId", required = true) long taskId);

    @ApiOperation(value = "暂停补丁任务", notes = "暂停补丁任务")
    @PostMapping(value = "/patch/task/pause")
    boolean pausePatchTask(@RequestParam(value = "taskId", required = true) long taskId);

    @ApiOperation(value = "继续补丁任务", notes = "继续补丁任务")
    @PostMapping(value = "/patch/task/resume")
    boolean resumePatchTask(@RequestParam(value = "taskId", required = true) long taskId);

    @ApiOperation(value = "复制补丁任务", notes = "复制补丁任务")
    @PostMapping(value = "/patch/task/copy")
    boolean copyPatchTask(@RequestParam(value = "taskId", required = true) long taskId);

    @ApiOperation(value = "检查任务是否可回退", notes = "检查任务是否可回退")
    @PostMapping(value = "/patch/task/checkRollback")
    PatchOperateResult checkTaskCanRollback(@RequestParam(value = "taskId", required = true) long taskId) throws DaipBaseException;

    @ApiOperation(value = "检查任务是否可复制", notes = "检查任务是否可复制")
    @PostMapping(value = "/patch/task/checkDuplicate")
    PatchOperateResult checkTaskCanDuplicate(@RequestParam(value = "taskId", required = true) long taskId) throws DaipBaseException;

    @ApiOperation(value = "查询补丁升级服务实例", notes = "查询补丁升级服务实例")
    @PostMapping(value = "/patch/serviceInstance/query")
    List<ServiceInstanceUpgrade> queryServiceInstanceUpgrade(
        @RequestBody VersionQueryRequestInfo versionQueryRequestInfo) throws DaipBaseException;

    @ApiOperation(value = "回退补丁任务", notes = "回退补丁任务")
    @PostMapping(value = "/patch/task/rollback")
    boolean rollbackPatchTask(@RequestBody(required = true) PatchTaskDto patchTaskDto);

    @ApiOperation(value = "查询待重启服务", notes = "查询待重启服务")
    @PostMapping(value = "/patch/restartService/query")
    List<ServiceInstance> queryNeedRestartService(@RequestBody(required = true) PatchTaskDto patchTaskDto) throws DaipBaseException;
}
