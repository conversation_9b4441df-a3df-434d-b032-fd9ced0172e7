package com.zte.daip.manager.patcher.inner.api.dto.task;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public enum PatchTaskTypeEnum
{

    UPDATE(0, "update"),

    ROLLBACK(1, "rollback");

    private final int taskType;

    private final String taskTypeName;

    PatchTaskTypeEnum(int taskType, String taskTypeName) {
        this.taskType = taskType;
        this.taskTypeName = taskTypeName;
    }

    public int getTaskType() {
        return taskType;
    }

    public String getTaskTypeName() {
        return taskTypeName;
    }

    public static PatchTaskTypeEnum queryPatchTaskType(int taskType) {
        PatchTaskTypeEnum[] patchTaskTypes = PatchTaskTypeEnum.values();
        for (PatchTaskTypeEnum patchTaskType : patchTaskTypes) {
            if (taskType == patchTaskType.getTaskType()) {
                return patchTaskType;
            }
        }
        return null;
    }
}
