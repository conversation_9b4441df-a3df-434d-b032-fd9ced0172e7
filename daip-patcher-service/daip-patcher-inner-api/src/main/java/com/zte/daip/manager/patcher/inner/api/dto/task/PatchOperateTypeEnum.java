package com.zte.daip.manager.patcher.inner.api.dto.task;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public enum PatchOperateTypeEnum
{

    OFFLINE(0, "offline"),

    ROLLING(1, "rolling");

    private final int operateType;

    private final String operateTypeName;

    PatchOperateTypeEnum(int taskType, String operateTypeName) {
        this.operateType = taskType;
        this.operateTypeName = operateTypeName;
    }

    public int getOperateType() {
        return operateType;
    }

    public String getOperateTypeName() {
        return operateTypeName;
    }

    public static PatchOperateTypeEnum queryPatchOperateType(int operateType) {
        PatchOperateTypeEnum[] patchOperateTypes = PatchOperateTypeEnum.values();
        for (PatchOperateTypeEnum patchOperateType : patchOperateTypes) {
            if (operateType == patchOperateType.getOperateType()) {
                return patchOperateType;
            }
        }
        return null;
    }
}
