package com.zte.daip.manager.patcher.inner.api.dto.task;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public enum PatchCategoryEnum
{

    ORDINARY("ordinary"),

    SCHEMA("schema");

    private final String patchCategory;

    PatchCategoryEnum(String patchCategory) {
        this.patchCategory = patchCategory;
    }

    public String getPatchCategory() {
        return patchCategory;
    }
}
