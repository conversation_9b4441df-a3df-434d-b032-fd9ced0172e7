package com.zte.daip.manager.patcher.inner.api;

import java.util.List;
import java.util.Map;

import com.zte.daip.manager.patcher.inner.api.dto.*;
import org.springframework.web.bind.annotation.*;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;

import io.swagger.annotations.ApiOperation;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface PatchUpdateInnerControllerApi {
    @ApiOperation(value = "查询当前升级批次的补丁回退点", notes = "查询当前升级批次的补丁回退点")
    @PostMapping(value = "/patch/update/rollbackpoints")
    List<ServiceInstancePatchInfo> queryRollbackPoints(
        @RequestParam(value = "clusterId", required = true) String clusterId,
        @RequestParam(value = "instanceIds", required = true) List<String> instanceIds) throws DaipBaseException;

    @ApiOperation(value = "查询当前升级批次的待升级补丁", notes = "查询当前升级批次的待升级补丁")
    @PostMapping(value = "/patch/update/needupdatepatchs")
    Map<String, List<String>> queryNeedUpdatePatchs(
            @RequestParam(value = "clusterId", required = true) String clusterId,
            @RequestParam(value = "instanceIds", required = true) List<String> instanceIds) throws DaipBaseException;
}
