package com.zte.daip.manager.patcher.inner.api.dto;

import lombok.*;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ServiceVersionInfo {

    @NotBlank
    @EqualsAndHashCode.Include
    private String serviceId;

    @NotBlank
    private String serviceName;

    @NotBlank
    private String componentType;

}
