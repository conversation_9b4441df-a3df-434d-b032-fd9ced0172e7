load("//:defs.bzl", "run_tests")

java_library(
    name = "daip-patcher-inner-api",
    srcs = glob(
        include = ["src/main/java/**/*.java"],
        exclude = [],
    ),
    resources = glob(
        include = ["src/main/resources/**/*"],
        exclude = [],
    ),
    visibility = [
    ],
    deps = [
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/alibaba:cooma",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/com/github/luben:zstd_jni",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/errorprone:error_prone_annotations",
        "//3rdparty/jvm/com/google/guava",
        "//3rdparty/jvm/com/google/guava:failureaccess",
        "//3rdparty/jvm/com/google/guava:listenablefuture",
        "//3rdparty/jvm/com/google/j2objc:j2objc_annotations",
        "//3rdparty/jvm/com/io7m/xom",
        "//3rdparty/jvm/com/networknt:json_schema_validator",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_security",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean",
        "//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter",
        "//3rdparty/jvm/com/zte/zdh:zdh_commons",
        "//3rdparty/jvm/commons_beanutils",
        "//3rdparty/jvm/commons_codec",
        "//3rdparty/jvm/commons_collections",
        "//3rdparty/jvm/commons_configuration",
        "//3rdparty/jvm/commons_fileupload",
        "//3rdparty/jvm/commons_io",
        "//3rdparty/jvm/commons_lang",
        "//3rdparty/jvm/commons_logging",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_codec_dns",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/io/netty:netty_transport_native_unix_common",
        "//3rdparty/jvm/io/projectreactor:reactor_core",
        "//3rdparty/jvm/io/reactivex/rxjava3:rxjava",
        "//3rdparty/jvm/io/springfox:springfox_core",
        "//3rdparty/jvm/io/springfox:springfox_schema",
        "//3rdparty/jvm/io/springfox:springfox_spi",
        "//3rdparty/jvm/io/springfox:springfox_spring_web",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_common",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/io/swagger:swagger_annotations",
        "//3rdparty/jvm/io/swagger:swagger_models",
        "//3rdparty/jvm/jakarta/activation:jakarta_activation_api",
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//3rdparty/jvm/javax/cache:cache_api",
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy_agent",
        "//3rdparty/jvm/org/apache/commons:commons_collections4",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_api",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j",
        "//3rdparty/jvm/org/apache/sshd:sshd_common",
        "//3rdparty/jvm/org/apache/sshd:sshd_core",
        "//3rdparty/jvm/org/apache/sshd:sshd_scp",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_constants",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_css",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_i18n",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_util",
        "//3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons",
        "//3rdparty/jvm/org/apache_extras/beanshell:bsh",
        "//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on",
        "//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on",
        "//3rdparty/jvm/org/checkerframework:checker_qual",
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/htmlunit:neko_htmlunit",
        "//3rdparty/jvm/org/jboss/logging:jboss_logging",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river",
        "//3rdparty/jvm/org/jodd:jodd_bean",
        "//3rdparty/jvm/org/jodd:jodd_core",
        "//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2",
        "//3rdparty/jvm/org/lz4:lz4_java",
        "//3rdparty/jvm/org/mapstruct",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/objenesis",
        "//3rdparty/jvm/org/ow2/asm",
        "//3rdparty/jvm/org/owasp/antisamy",
        "//3rdparty/jvm/org/owasp/esapi",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/reactivestreams:reactive_streams",
        "//3rdparty/jvm/org/redisson",
        "//3rdparty/jvm/org/slf4j:jcl_over_slf4j",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//3rdparty/jvm/org/springframework:spring_beans",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/springframework:spring_expression",
        "//3rdparty/jvm/org/springframework:spring_jcl",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//3rdparty/jvm/org/springframework:spring_webmvc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//3rdparty/jvm/org/xerial/snappy:snappy_java",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//3rdparty/jvm/xerces:xercesImpl",
        "//3rdparty/jvm/xml_apis",
        "//3rdparty/jvm/xml_apis:xml_apis_ext",
    ],
)

java_library(
    name = "daip-patcher-inner-api-test-classes",
    srcs = glob(
        include = ["src/test/java/**/*.java"],
        exclude = [],
    ),
    resources = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
        ":daip-patcher-inner-api",
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/alibaba:cooma",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/com/github/luben:zstd_jni",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/errorprone:error_prone_annotations",
        "//3rdparty/jvm/com/google/guava",
        "//3rdparty/jvm/com/google/guava:failureaccess",
        "//3rdparty/jvm/com/google/guava:listenablefuture",
        "//3rdparty/jvm/com/google/j2objc:j2objc_annotations",
        "//3rdparty/jvm/com/io7m/xom",
        "//3rdparty/jvm/com/jayway/jsonpath:json_path",
        "//3rdparty/jvm/com/networknt:json_schema_validator",
        "//3rdparty/jvm/com/vaadin/external/google:android_json",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_security",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean",
        "//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter",
        "//3rdparty/jvm/com/zte/zdh:zdh_commons",
        "//3rdparty/jvm/commons_beanutils",
        "//3rdparty/jvm/commons_codec",
        "//3rdparty/jvm/commons_collections",
        "//3rdparty/jvm/commons_configuration",
        "//3rdparty/jvm/commons_fileupload",
        "//3rdparty/jvm/commons_io",
        "//3rdparty/jvm/commons_lang",
        "//3rdparty/jvm/commons_logging",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_codec_dns",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/io/netty:netty_transport_native_unix_common",
        "//3rdparty/jvm/io/projectreactor:reactor_core",
        "//3rdparty/jvm/io/reactivex/rxjava3:rxjava",
        "//3rdparty/jvm/io/springfox:springfox_core",
        "//3rdparty/jvm/io/springfox:springfox_schema",
        "//3rdparty/jvm/io/springfox:springfox_spi",
        "//3rdparty/jvm/io/springfox:springfox_spring_web",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_common",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/io/swagger:swagger_annotations",
        "//3rdparty/jvm/io/swagger:swagger_models",
        "//3rdparty/jvm/jakarta/activation:jakarta_activation_api",
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//3rdparty/jvm/javax/cache:cache_api",
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//3rdparty/jvm/junit",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy_agent",
        "//3rdparty/jvm/net/minidev:accessors_smart",
        "//3rdparty/jvm/net/minidev:json_smart",
        "//3rdparty/jvm/org/apache/commons:commons_collections4",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_api",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j",
        "//3rdparty/jvm/org/apache/sshd:sshd_common",
        "//3rdparty/jvm/org/apache/sshd:sshd_core",
        "//3rdparty/jvm/org/apache/sshd:sshd_scp",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_constants",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_css",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_i18n",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_util",
        "//3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons",
        "//3rdparty/jvm/org/apache_extras/beanshell:bsh",
        "//3rdparty/jvm/org/apiguardian:apiguardian_api",
        "//3rdparty/jvm/org/assertj:assertj_core",
        "//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on",
        "//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on",
        "//3rdparty/jvm/org/checkerframework:checker_qual",
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//3rdparty/jvm/org/hamcrest",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/htmlunit:neko_htmlunit",
        "//3rdparty/jvm/org/jboss/logging:jboss_logging",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river",
        "//3rdparty/jvm/org/jodd:jodd_bean",
        "//3rdparty/jvm/org/jodd:jodd_core",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_engine",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_params",
        "//3rdparty/jvm/org/junit/platform:junit_platform_commons",
        "//3rdparty/jvm/org/junit/platform:junit_platform_engine",
        "//3rdparty/jvm/org/junit/vintage:junit_vintage_engine",
        "//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2",
        "//3rdparty/jvm/org/lz4:lz4_java",
        "//3rdparty/jvm/org/mapstruct",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/mockito:mockito_junit_jupiter",
        "//3rdparty/jvm/org/objenesis",
        "//3rdparty/jvm/org/opentest4j",
        "//3rdparty/jvm/org/ow2/asm",
        "//3rdparty/jvm/org/owasp/antisamy",
        "//3rdparty/jvm/org/owasp/esapi",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/reactivestreams:reactive_streams",
        "//3rdparty/jvm/org/redisson",
        "//3rdparty/jvm/org/skyscreamer:jsonassert",
        "//3rdparty/jvm/org/slf4j:jcl_over_slf4j",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//3rdparty/jvm/org/springframework:spring_beans",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/springframework:spring_expression",
        "//3rdparty/jvm/org/springframework:spring_jcl",
        "//3rdparty/jvm/org/springframework:spring_test",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//3rdparty/jvm/org/springframework:spring_webmvc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_test",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_test",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_test_autoconfigure",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//3rdparty/jvm/org/xerial/snappy:snappy_java",
        "//3rdparty/jvm/org/xmlunit:xmlunit_core",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//3rdparty/jvm/xerces:xercesImpl",
        "//3rdparty/jvm/xml_apis",
        "//3rdparty/jvm/xml_apis:xml_apis_ext",
    ],
)

run_tests(
    name = "AllTests",
    size = "small",
    srcs = glob(
        include = [
            "src/test/java/**/Test*.java",
            "src/test/java/**/*Test.java",
        ],
        exclude = [],
    ),
    data = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
        ":daip-patcher-inner-api",
        ":daip-patcher-inner-api-test-classes",
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/alibaba:cooma",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/com/github/luben:zstd_jni",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/errorprone:error_prone_annotations",
        "//3rdparty/jvm/com/google/guava",
        "//3rdparty/jvm/com/google/guava:failureaccess",
        "//3rdparty/jvm/com/google/guava:listenablefuture",
        "//3rdparty/jvm/com/google/j2objc:j2objc_annotations",
        "//3rdparty/jvm/com/io7m/xom",
        "//3rdparty/jvm/com/jayway/jsonpath:json_path",
        "//3rdparty/jvm/com/networknt:json_schema_validator",
        "//3rdparty/jvm/com/vaadin/external/google:android_json",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_security",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean",
        "//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter",
        "//3rdparty/jvm/com/zte/zdh:zdh_commons",
        "//3rdparty/jvm/commons_beanutils",
        "//3rdparty/jvm/commons_codec",
        "//3rdparty/jvm/commons_collections",
        "//3rdparty/jvm/commons_configuration",
        "//3rdparty/jvm/commons_fileupload",
        "//3rdparty/jvm/commons_io",
        "//3rdparty/jvm/commons_lang",
        "//3rdparty/jvm/commons_logging",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_codec_dns",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/io/netty:netty_transport_native_unix_common",
        "//3rdparty/jvm/io/projectreactor:reactor_core",
        "//3rdparty/jvm/io/reactivex/rxjava3:rxjava",
        "//3rdparty/jvm/io/springfox:springfox_core",
        "//3rdparty/jvm/io/springfox:springfox_schema",
        "//3rdparty/jvm/io/springfox:springfox_spi",
        "//3rdparty/jvm/io/springfox:springfox_spring_web",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_common",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/io/swagger:swagger_annotations",
        "//3rdparty/jvm/io/swagger:swagger_models",
        "//3rdparty/jvm/jakarta/activation:jakarta_activation_api",
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//3rdparty/jvm/javax/cache:cache_api",
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//3rdparty/jvm/junit",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy_agent",
        "//3rdparty/jvm/net/minidev:accessors_smart",
        "//3rdparty/jvm/net/minidev:json_smart",
        "//3rdparty/jvm/org/apache/commons:commons_collections4",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_api",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j",
        "//3rdparty/jvm/org/apache/sshd:sshd_common",
        "//3rdparty/jvm/org/apache/sshd:sshd_core",
        "//3rdparty/jvm/org/apache/sshd:sshd_scp",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_constants",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_css",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_i18n",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_util",
        "//3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons",
        "//3rdparty/jvm/org/apache_extras/beanshell:bsh",
        "//3rdparty/jvm/org/apiguardian:apiguardian_api",
        "//3rdparty/jvm/org/assertj:assertj_core",
        "//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on",
        "//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on",
        "//3rdparty/jvm/org/checkerframework:checker_qual",
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//3rdparty/jvm/org/hamcrest",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/htmlunit:neko_htmlunit",
        "//3rdparty/jvm/org/jboss/logging:jboss_logging",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river",
        "//3rdparty/jvm/org/jodd:jodd_bean",
        "//3rdparty/jvm/org/jodd:jodd_core",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_engine",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_params",
        "//3rdparty/jvm/org/junit/platform:junit_platform_commons",
        "//3rdparty/jvm/org/junit/platform:junit_platform_engine",
        "//3rdparty/jvm/org/junit/vintage:junit_vintage_engine",
        "//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2",
        "//3rdparty/jvm/org/lz4:lz4_java",
        "//3rdparty/jvm/org/mapstruct",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/mockito:mockito_junit_jupiter",
        "//3rdparty/jvm/org/objenesis",
        "//3rdparty/jvm/org/opentest4j",
        "//3rdparty/jvm/org/ow2/asm",
        "//3rdparty/jvm/org/owasp/antisamy",
        "//3rdparty/jvm/org/owasp/esapi",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/reactivestreams:reactive_streams",
        "//3rdparty/jvm/org/redisson",
        "//3rdparty/jvm/org/skyscreamer:jsonassert",
        "//3rdparty/jvm/org/slf4j:jcl_over_slf4j",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//3rdparty/jvm/org/springframework:spring_beans",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/springframework:spring_expression",
        "//3rdparty/jvm/org/springframework:spring_jcl",
        "//3rdparty/jvm/org/springframework:spring_test",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//3rdparty/jvm/org/springframework:spring_webmvc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_test",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_test",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_test_autoconfigure",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//3rdparty/jvm/org/xerial/snappy:snappy_java",
        "//3rdparty/jvm/org/xmlunit:xmlunit_core",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//3rdparty/jvm/xerces:xercesImpl",
        "//3rdparty/jvm/xml_apis",
        "//3rdparty/jvm/xml_apis:xml_apis_ext",
    ],
)
