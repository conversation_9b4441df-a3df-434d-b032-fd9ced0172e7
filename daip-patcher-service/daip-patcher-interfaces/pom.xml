<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>daip-patcher-service</artifactId>
        <groupId>com.zte.daip.manager.patcher</groupId>
        <version>deletePatch</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>daip-patcher-interfaces</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-patcher-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-inner-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-domain</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-application</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-http-auth</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-common-utils</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>


</project>