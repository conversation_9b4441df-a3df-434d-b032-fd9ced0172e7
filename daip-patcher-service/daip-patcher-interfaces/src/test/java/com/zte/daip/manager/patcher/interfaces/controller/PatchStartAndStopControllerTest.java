package com.zte.daip.manager.patcher.interfaces.controller;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.application.service.PatchStartAndStopAppService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10259451</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchStartAndStopControllerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/4/25</p>
 * <p>完成日期：2023/4/25</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchStartAndStopControllerTest {

    @Mock
    private PatchStartAndStopAppService patchStartAndStopAppService;
    @Mock
    private SpringAppCsrfProtector springAppCsrfProtector;
    @InjectMocks
    private PatchStartAndStopController patchStartAndStopController;

    @Before
    public void init() throws Exception {
        doNothing().when(springAppCsrfProtector).validRequest();
        doNothing().when(patchStartAndStopAppService).stopCluster(anyString(), anyList());
        doNothing().when(patchStartAndStopAppService).startCluster(anyString(), anyList());
        when(patchStartAndStopAppService.queryOperatorProcess(anyString())).thenReturn("kafka");
        when(patchStartAndStopAppService.queryCanOperateServices(anyString(), anyBoolean(), anyList()))
            .thenReturn(Lists.newArrayList("kafka"));
    }

    @Test
    public void queryCanOperateServices() throws DaipBaseException {
        List<String> services = patchStartAndStopController.queryCanOperateServices("1000", true, Lists.newArrayList());
        assertNotNull(services);
    }

    @Test
    public void startCluster() {
        boolean result = true;

        try {
            patchStartAndStopController.startCluster("001", Lists.newArrayList());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void stopCluster() {
        boolean result = true;

        try {
            patchStartAndStopController.stopCluster("001", Lists.newArrayList());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryOperatorProcess() throws DaipBaseException {
        String s = patchStartAndStopController.queryOperatorProcess("10000");
        assertNotNull(s);
    }
}