/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * 文件名称: PatcherDispatchControllerTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/9/6
 * </p>
 * <p>
 * 完成日期：2023/9/6
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.interfaces.controller;

import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.application.service.PatchDispatchAppService;
import com.zte.daip.manager.patcher.domain.dispatch.PatchDistributeService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertTrue;
@RunWith(SpringRunner.class)
public class PatcherDispatchControllerTest {
    @Mock
    private PatchDispatchAppService patchDispatchAppService;

    @Mock
    private PatchDistributeService patchDistributeService;
    @InjectMocks
    private PatcherDispatchController patcherDispatchController;

    @Before
    public void setup() {
        Mockito.when(patchDistributeService.dispatchPatchesPrecheck()).thenReturn(new PatchOperateResult(true, ""));
    }

    @Test
    public void queryNeedUpdatePatchInfo() {
        PatchOperateResult patchOperateResult = patcherDispatchController.dispatchPatchesPrecheck();

        assertTrue(patchOperateResult.isStatus());
    }
}