package com.zte.daip.manager.patcher.interfaces.controller;

/* Started by AICoder, pid:68047xdc7b686191481408c3901399420a344d85 */
import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.domain.update.cache.PatchRollbackCacheQueue;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

@RestController
public class PatchOfflineRollbackControllerTest {

    @Mock
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Mock
    private PatchRollbackCacheQueue patchRollbackCacheQueue;

    @InjectMocks
    private PatchOfflineRollbackController controller;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void should_AddPatchServiceParamsToQueue_When_ValidRequestPassed() throws Exception {
        // Given
        List<PatchServiceParam> patchServiceParams = new ArrayList<>();
        patchServiceParams.add(new PatchServiceParam());

        // When
        controller.deleteRollbackServicePatchHistory(patchServiceParams);

        // Then
        verify(springAppCsrfProtector).validRequest();
        verify(patchRollbackCacheQueue).add(patchServiceParams);
    }
}

/* Ended by AICoder, pid:68047xdc7b686191481408c3901399420a344d85 */