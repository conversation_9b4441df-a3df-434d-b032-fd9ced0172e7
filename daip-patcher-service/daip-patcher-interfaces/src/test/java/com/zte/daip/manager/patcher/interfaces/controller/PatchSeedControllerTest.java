package com.zte.daip.manager.patcher.interfaces.controller;

import com.zte.daip.manager.common.deployer.bean.host.install.ResponseResult;
import com.zte.daip.manager.miniagent.seed.controller.SeedController;
import com.zte.daip.manager.patcher.api.dto.Seed;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PatchSeedControllerTest {

    @Mock
    private SeedController seedController;

    @InjectMocks
    private PatchSeedController patchSeedController;


    @Test
    public void testQueryAll() {
        // 模拟返回的 JSON 字符串
        String seedJsonStr =
                "[{\"projectName\":\"seed1\",\"version\":\"1.0\"},{\"projectName\":\"seed2\",\"version\":\"2.0\"}]";
        ResponseResult responseResult = new ResponseResult();
        responseResult.setMessage(seedJsonStr);
        when(seedController.seedQuery()).thenReturn(responseResult);

        // 调用 queryAll 方法
        List<Seed> result = patchSeedController.queryAll();

        // 验证结果
        assertEquals(2, result.size());
        assertEquals("seed1", result.get(0).getProjectName());
        assertEquals("1.0", result.get(0).getVersion());
        assertEquals("seed2", result.get(1).getProjectName());
        assertEquals("2.0", result.get(1).getVersion());

        // 验证 seedController.seedQuery 方法是否被调用
        verify(seedController, times(1)).seedQuery();
    }
}