package com.zte.daip.manager.patcher.interfaces.controller;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.update.cache.PatchUpdateReportCacheQueue;
import com.zte.daip.manager.patcher.domain.update.service.PatchQueryService;
import io.jsonwebtoken.lang.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class PatcherRollUpdateControllerTest {

    @Mock
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Mock
    private PatchQueryService patchQueryService;
    @Mock
    private PatchUpdateReportCacheQueue patchUpdateReportCacheQueue;

    @InjectMocks
    private PatcherRollUpdateController patcherRollUpdateController;

    @Before
    public void init() {
        doNothing().when(springAppCsrfProtector).validRequest();
    }

    @Test
    public void queryUpdatePatchInfo() throws DaipBaseException {
        when(patchQueryService.queryUpdatePatchInfo(any()))
            .thenReturn(Lists.newArrayList(new RollAutoPatchUpdateInfo()));
        List<RollAutoPatchUpdateInfo> rollAutoPatchUpdateInfos =
            patcherRollUpdateController.queryUpdatePatchInfo(new RollAutoPatchUpdateParam());
        Assert.notEmpty(rollAutoPatchUpdateInfos);
    }

    @Test
    public void reportUpdateResult() {

        boolean result = true;

        try {
            doNothing().when(patchUpdateReportCacheQueue).add(anyList());
            patcherRollUpdateController.reportUpdateResult(new PatchResult());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }
}