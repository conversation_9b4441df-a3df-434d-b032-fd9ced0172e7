package com.zte.daip.manager.patcher.interfaces.controller;

import static junit.framework.TestCase.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

import java.util.ArrayList;
import java.util.List;

import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.application.service.PatchTaskAppService;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;
import com.zte.daip.manager.patcher.inner.api.dto.VersionQueryRequestInfo;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchTaskControllerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/10/7</p>
 * <p>完成日期：2023/10/7</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class PatchTaskControllerTest {
    @Mock
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Mock
    private PatchTaskAppService patchTaskAppService;

    @InjectMocks
    private PatchTaskController patchTaskController;

    private PatchTaskDto patchTaskDto = new PatchTaskDto();

    private long taskId = 1L;

    @Before
    public void init() {
        doNothing().when(springAppCsrfProtector).validRequest();

        patchTaskDto.setTaskName("1");
    }
    /* Started by AICoder, pid:tbc3fd1bb704c3714b3b08dd4030794d17a9c5a2 */
    @Test
    public void createPatchTask() {
        Assert.assertEquals(-1, patchTaskController.createPatchTask(null)); // 断言当传入null参数时返回-1

        boolean result = true;
        Mockito.when(patchTaskAppService.patchTaskParamValid(any())).thenReturn(true); // 模拟补丁任务参数验证方法返回true

        try {
            patchTaskController.createPatchTask(patchTaskDto); // 尝试创建补丁任务
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result); // 断言结果为true
    }

    @Test
    public void createPatchTask1() {
        Mockito.when(patchTaskAppService.patchTaskParamValid(any())).thenReturn(false); // 模拟补丁任务参数验证方法返回false
        Assert.assertEquals(-1, patchTaskController.createPatchTask(null)); // 断言当传入null参数时返回-1
    }

    @Test
    public void modifyPatchTask() throws DaipBaseException {
        Assert.assertFalse(patchTaskController.modifyPatchTask(null)); // 断言当传入null参数时返回false

        boolean result = true;
        Mockito.when(patchTaskAppService.patchTaskParamValid(any())).thenReturn(true); // 模拟补丁任务参数验证方法返回true
        Mockito.when(patchTaskAppService.patchNameNotRepeated(any())).thenReturn(true); // 模拟补丁任务名称不重复验证方法返回true

        try {
            patchTaskController.modifyPatchTask(patchTaskDto); // 尝试修改补丁任务
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result); // 断言结果为true
    }

    @Test
    public void modifyPatchTask1() {
        Mockito.when(patchTaskAppService.patchTaskParamValid(any())).thenReturn(false); // 模拟补丁任务参数验证方法返回false
        Assert.assertFalse(patchTaskController.modifyPatchTask(patchTaskDto)); // 断言修改补丁任务失败
    }

    @Test
    public void modifyPatchTask2() throws DaipBaseException {
        Mockito.when(patchTaskAppService.patchTaskParamValid(any())).thenReturn(true); // 模拟补丁任务参数验证方法返回true
        Mockito.when(patchTaskAppService.patchNameNotRepeated(any())).thenReturn(false); // 模拟补丁任务名称不重复验证方法返回false
        Assert.assertFalse(patchTaskController.modifyPatchTask(patchTaskDto)); // 断言修改补丁任务失败
    }
    /* Ended by AICoder, pid:tbc3fd1bb704c3714b3b08dd4030794d17a9c5a2 */

    @Test
    public void removePatchTask() {
        boolean result = true;

        try {
            patchTaskController.removePatchTask(Lists.newArrayList(taskId));
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryPatchTasks() {
        boolean result = true;

        try {
            patchTaskController.queryPatchTasks();
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryPatchDetailTasksTest() {
        boolean result = true;

        try {
            patchTaskController.queryPatchDetailTasks(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryPatchTaskByTaskId() {
        boolean result = true;

        try {
            patchTaskController.queryPatchTaskByTaskId(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void triggerPatchTask() {
        boolean result = true;

        try {
            patchTaskController.triggerPatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void retryPatchTask() {
        boolean result = true;

        try {
            patchTaskController.retryPatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void pausePatchTask() {
        boolean result = true;

        try {
            patchTaskController.pausePatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void resumePatchTask() {
        boolean result = true;

        try {
            patchTaskController.resumePatchTask(taskId);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void queryServiceInstanceUpgrade() {
        boolean result = true;

        try {
            patchTaskController.queryServiceInstanceUpgrade(new VersionQueryRequestInfo());
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void rollbackPatchTask() {
        boolean result = patchTaskController.rollbackPatchTask(null);
        assertFalse(result);

        result = patchTaskController.rollbackPatchTask(patchTaskDto);
        assertTrue(result);
    }

    /* Started by AICoder, pid:u275565c454da04142d00b96805bf21fe8f74c3f */
    @Test
    public void queryNeedRestartService() throws DaipBaseException {
        // 调用patchTaskController的queryNeedRestartService方法，传入null参数
        List<ServiceInstance> result = patchTaskController.queryNeedRestartService(null);

        // 断言返回的列表大小为0，即没有需要重启的服务实例
        assertEquals(0, result.size());

        // 使用Mockito模拟patchTaskAppService的queryNeedRestartService方法返回一个空列表
        Mockito.when(patchTaskAppService.queryNeedRestartService(patchTaskDto)).thenReturn(new ArrayList<>());

        // 再次调用patchTaskController的queryNeedRestartService方法，传入非null参数
        List<ServiceInstance> result1 = patchTaskController.queryNeedRestartService(patchTaskDto);

        // 断言返回的列表大小仍然为0，即没有需要重启的服务实例
        assertEquals(0, result1.size());
    }

    /* Ended by AICoder, pid:u275565c454da04142d00b96805bf21fe8f74c3f */

    @Test
    public void checkTaskCanRollbackTest() {
        boolean result = true;
        try {
            patchTaskController.checkTaskCanRollback(1);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void checkTaskCanDuplicateTest() {
        boolean result = true;
        try {
            patchTaskController.checkTaskCanDuplicate(1);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }

    @Test
    public void copyPatchTaskTest() {
        boolean result = true;
        try {
            patchTaskController.copyPatchTask(1);
        } catch (Exception e) {
            result = false;
        }
        assertTrue(result);
    }
}