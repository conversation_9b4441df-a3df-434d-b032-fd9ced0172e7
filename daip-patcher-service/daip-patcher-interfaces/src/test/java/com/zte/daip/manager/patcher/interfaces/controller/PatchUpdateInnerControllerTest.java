package com.zte.daip.manager.patcher.interfaces.controller;
/* Started by AICoder, pid:ca9af38412jd92e14eca0b0e2001829b4484c104 */
import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.patcher.application.service.PatchUpdateAppService;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import static junit.framework.TestCase.assertTrue;
import static org.mockito.Mockito.doNothing;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchUpdateInnerControllerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/11/20</p>
 * <p>完成日期：2023/11/20</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class PatchUpdateInnerControllerTest {
    // 使用Mockito框架的@Mock注解模拟SpringAppCsrfProtector和PatchUpdateAppService对象
    @Mock
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Mock
    private PatchUpdateAppService patchUpdateAppService;

    // 使用Mockito框架的@InjectMocks注解注入被测试的PatchUpdateInnerController对象
    @InjectMocks
    private PatchUpdateInnerController patchUpdateInnerController;

    // 在每个测试方法执行前初始化模拟对象的行为
    @Before
    public void init() {
        doNothing().when(springAppCsrfProtector).validRequest();
    }

    // 测试queryRollbackPoints方法
    @Test
    public void queryRollbackPoints() {
        boolean result = true;

        try {
            // 调用patchUpdateInnerController的queryRollbackPoints方法
            patchUpdateInnerController.queryRollbackPoints("1", Lists.newArrayList());
        } catch (Exception e) {
            // 如果捕获到异常，则将result设置为false
            result = false;
        }
        // 断言result为true，即没有抛出异常
        assertTrue(result);
    }

    // 测试queryNeedUpdatePatchs方法
    @Test
    public void queryNeedUpdatePatchs() {
        boolean result = true;

        try {
            // 调用patchUpdateInnerController的queryNeedUpdatePatchs方法
            patchUpdateInnerController.queryNeedUpdatePatchs("1", Lists.newArrayList());
        } catch (Exception e) {
            // 如果捕获到异常，则将result设置为false
            result = false;
        }
        // 断言result为true，即没有抛出异常
        assertTrue(result);
    }
}

/* Ended by AICoder, pid:ca9af38412jd92e14eca0b0e2001829b4484c104 */
