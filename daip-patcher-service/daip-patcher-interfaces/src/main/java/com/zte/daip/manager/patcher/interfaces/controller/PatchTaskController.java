package com.zte.daip.manager.patcher.interfaces.controller;

import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.application.service.PatchTaskAppService;
import com.zte.daip.manager.patcher.inner.api.PatchTaskControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
public class PatchTaskController implements PatchTaskControllerApi {
    @Autowired
    private PatchTaskAppService patchTaskAppService;

    @Autowired
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Override
    public long createPatchTask(PatchTaskDto patchTaskDto) {
        springAppCsrfProtector.validRequest();
        try {
            if (!patchTaskAppService.patchTaskParamValid(patchTaskDto)) {
                return -1;
            }
            return patchTaskAppService.createPatchTask(patchTaskDto);
        } catch (Exception e) {
            log.error("create patch task exception", e);
            return -1;
        }
    }

    @Override
    public boolean modifyPatchTask(PatchTaskDto patchTaskDto) {
        springAppCsrfProtector.validRequest();
        try {
            if (!patchTaskAppService.patchTaskParamValid(patchTaskDto)
                || !patchTaskAppService.patchNameNotRepeated(patchTaskDto)) {
                return false;
            }
            patchTaskAppService.modifyPatchTask(patchTaskDto);
            return true;
        } catch (Exception e) {
            log.error("modify patch task exception", e);
            return false;
        }
    }

    @Override
    public boolean removePatchTask(List<Long> taskIds) {
        springAppCsrfProtector.validRequest();
        try {
            patchTaskAppService.removePatchTask(taskIds);
            return true;
        } catch (Exception e) {
            log.error("remove patch task exception", e);
            return false;
        }
    }

    @Override
    public List<PatchTaskDto> queryPatchTasks() throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            return patchTaskAppService.queryAllPatchTasks();
        } catch (Exception e) {
            throw new DaipBaseException("query all patch tasks exception", e);
        }
    }

    @Override
    public PatchTaskDto queryPatchDetailTasks(long taskId) throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            return patchTaskAppService.queryPatchTaskDetailByTaskId(taskId);
        } catch (Exception e) {
            throw new DaipBaseException("query patch task detail by id exception", e);
        }
    }

    @Override
    public PatchTaskDto queryPatchTaskByTaskId(long taskId) throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            return patchTaskAppService.queryPatchTaskByTaskId(taskId);
        } catch (Exception e) {
            throw new DaipBaseException("query patch task by id exception", e);
        }
    }

    @Override
    public boolean triggerPatchTask(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            patchTaskAppService.triggerPatchTask(taskId);
            return true;
        } catch (Exception e) {
            log.error("trigger patch task exception", e);
            return false;
        }
    }

    @Override
    public boolean retryPatchTask(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            patchTaskAppService.retryPatchTask(taskId);
            return true;
        } catch (Exception e) {
            log.error("retry patch task exception", e);
            return false;
        }
    }

    @Override
    public boolean pausePatchTask(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            patchTaskAppService.pausePatchTask(taskId);
            return true;
        } catch (Exception e) {
            log.error("pause patch task exception", e);
            return false;
        }
    }

    @Override
    public boolean resumePatchTask(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            patchTaskAppService.resumePatchTask(taskId);
            return true;
        } catch (Exception e) {
            log.error("resume patch task exception", e);
            return false;
        }
    }

    @Override
    public boolean copyPatchTask(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            patchTaskAppService.copyPatchTask(taskId);
            return true;
        } catch (Exception e) {
            log.error("resume patch task exception", e);
            return false;
        }
    }

    @Override
    public PatchOperateResult checkTaskCanRollback(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            return patchTaskAppService.checkTaskCanRollback(taskId);
        } catch (Exception e) {
            log.error("check patch task can rollback exception", e);
            return PatchOperateResult.fail("");
        }
    }

    @Override
    public PatchOperateResult checkTaskCanDuplicate(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            return patchTaskAppService.checkTaskCanDuplicate(taskId);
        } catch (Exception e) {
            log.error("check patch task can duplicate exception", e);
            return PatchOperateResult.fail("");
        }
    }

    @Override
    public List<ServiceInstanceUpgrade> queryServiceInstanceUpgrade(VersionQueryRequestInfo versionQueryRequestInfo)
        throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            return patchTaskAppService.queryServiceInstanceUpgrade(versionQueryRequestInfo);
        } catch (Exception e) {
            throw new DaipBaseException("query patch upgrade service instance exception", e);
        }
    }

    @Override
    public boolean rollbackPatchTask(PatchTaskDto patchTaskDto) {
        springAppCsrfProtector.validRequest();
        try {
            if (patchTaskDto == null) {
                log.error("rollback patch task exception: patch task is null");
                return false;
            }
            patchTaskAppService.rollbackPatchTask(patchTaskDto);
            return true;
        } catch (Exception e) {
            log.error("rollback patch task exception:", e);
            return false;
        }
    }

    @Override
    public List<ServiceInstance> queryNeedRestartService(PatchTaskDto patchTaskDto) throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            return patchTaskAppService.queryNeedRestartService(patchTaskDto);
        } catch (Exception e) {
            throw new DaipBaseException("query need restart service exception", e);
        }
    }
}