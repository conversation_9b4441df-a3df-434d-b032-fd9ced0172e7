package com.zte.daip.manager.patcher.interfaces.controller;

import com.alibaba.fastjson.JSONObject;
import com.zte.daip.manager.miniagent.seed.controller.SeedController;
import com.zte.daip.manager.patcher.api.PatchSeedControllerApi;
import com.zte.daip.manager.patcher.api.dto.Seed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
public class PatchSeedController implements PatchSeedControllerApi {

    @Autowired
    private SeedController seedController;

    @Override
    public List<Seed> queryAll() {
        log.info("query all seed.");
        String seedJsonStr = seedController.seedQuery().getMessage();
        return JSONObject.parseArray(seedJsonStr, Seed.class);
    }
}
