/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchRollbackController.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/3/21
 * </p>
 * <p>
 * 完成日期：2023/3/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.interfaces.controller;

import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.event.reporter.api.annotation.*;
import com.zte.daip.manager.patcher.api.PatchRollbackControllerApi;
import com.zte.daip.manager.patcher.api.dto.*;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.application.service.UpdateSchemaPatchService;
import com.zte.daip.manager.patcher.domain.rollback.PatchRollbackOperateService;
import com.zte.daip.manager.patcher.domain.rollback.progress.RollbackProgressService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
public class PatchRollbackController implements PatchRollbackControllerApi {

    @Autowired
    private SpringAppCsrfProtector springAppCsrfProtector;
    @Autowired
    private PatchRollbackOperateService patchRollbackOperateService;
    @Autowired
    private RollbackProgressService rollbackProgressService;

    @Autowired
    private UpdateSchemaPatchService updateSchemaPatchService;

    private final static String ROLLBACK_PATCH = "rollback_patch";

    @Override
    public List<PatchRollBackServiceDto> queryRollBackServices(@RequestParam("clusterId") String clusterId) {
        springAppCsrfProtector.validRequest();
        return patchRollbackOperateService.queryRollBackServices(clusterId);
    }

    @Override
    public List<PatchRollbackHostDto> queryRollBackHosts(String clusterId,
        List<PatchRollbackParam> patchRollbackParams) {
        return patchRollbackOperateService.queryRollBackHosts(clusterId, patchRollbackParams);
    }

    @Override
    public List<PatchOperateResult> rollbackSchemaPatches(@RequestParam("taskId") long taskId) {
        return updateSchemaPatchService.rollbackSchemaPatch(taskId);
    }

    @Override
    @BusinessControllerEvent(eventType = ROLLBACK_PATCH, clusterId = "#clusterId")
    public void rollbackPatches(@RequestParam("clusterId") String clusterId,
        @RequestBody List<PatchRollbackHostDto> patchRollbackHostDtos) {
        springAppCsrfProtector.validRequest();
        patchRollbackOperateService.rollbackPatches(clusterId, patchRollbackHostDtos);
    }

    @Override
    public RollBackPatchProgress queryRollBackProgress(@RequestParam("clusterId") String clusterId) {
        springAppCsrfProtector.validRequest();
        return rollbackProgressService.queryRollBackProgress(clusterId);

    }
}