package com.zte.daip.manager.patcher.interfaces.controller;

import java.util.List;
import java.util.Map;

import com.zte.daip.manager.patcher.application.service.PatchUpdateAppService;
import com.zte.daip.manager.patcher.inner.api.PatchUpdateInnerControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
public class PatchUpdateInnerController implements PatchUpdateInnerControllerApi {
    @Autowired
    private PatchUpdateAppService patchUpdateAppService;

    @Autowired
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Override
    public List<ServiceInstancePatchInfo> queryRollbackPoints(String clusterId, List<String> instanceIds)
        throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            return patchUpdateAppService.queryRollbackPoints(clusterId, instanceIds);
        } catch (Exception e) {
            throw new DaipBaseException("query rollback points exception", e);
        }
    }

    @Override
    public Map<String, List<String>> queryNeedUpdatePatchs(String clusterId, List<String> instanceIds) throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            return patchUpdateAppService.queryNeedUpdatePatchs(clusterId, instanceIds);
        } catch (Exception e) {
            throw new DaipBaseException("query need update patchs exception", e);
        }
    }
}