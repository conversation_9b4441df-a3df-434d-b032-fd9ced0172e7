/**
 * <p>
 * <owner>10168351</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatcherUpdateController.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/9
 * </p>
 * <p>
 * 完成日期：2021/4/9
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.interfaces.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import com.zte.daip.manager.event.reporter.api.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zte.daip.manager.patcher.api.PatcherUpdateControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.api.schema.SchemaPatchRegisterRequest;
import com.zte.daip.manager.patcher.application.service.PatchUpdateAppService;
import com.zte.daip.manager.patcher.application.service.UpdateSchemaPatchService;
import com.zte.daip.manager.patcher.domain.schema.utils.SchemaPatchUtils;
import com.zte.daip.manager.patcher.domain.update.service.PatchUpdateCacheService;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */

@Slf4j
@RestController
public class PatcherUpdateController implements PatcherUpdateControllerApi {
    @Autowired
    private PatchUpdateAppService patchUpdateAppService;
    @Autowired
    private PatchUpdateCacheService patchUpdateCacheService;

    @Autowired
    private SchemaPatchUtils schemaPatchUtils;

    @Autowired
    private UpdateSchemaPatchService updateSchemaPatchService;

    @Override
    @BusinessControllerEvent(eventType = I18nKeyConstants.UPGRADE_PATCH, clusterId = "#updateRequest.clusterId")
    public void updatePatch(@RequestBody UpdateRequest updateRequest) {
        patchUpdateAppService.updatePatch(updateRequest);
    }

    @Override
    public List<PatchOperateResult> updateSchemaPatch(@PathVariable("taskId") long taskId) {
        return updateSchemaPatchService.updateSchemaPatch(taskId);

    }

    @Override
    public PatchUpdateCacheDto queryUpdatePatchesProcess(@PathVariable("clusterId") String clusterId) {
        return patchUpdateCacheService.queryUpdatePatchProgress(clusterId);
    }

    @Override
    public boolean isUpdatePatchPermit(@PathVariable("clusterId") String clusterId) {
        return patchUpdateCacheService.isUpdatePatchPermit(clusterId);
    }

    @Override
    public String queryNeedUpdatePatchInfo(@RequestParam("patchRequestKey") String patchRequestKey,
        @RequestParam("hostIp") String hostIp) {

        String decodeKey = null;
        try {
            decodeKey = URLDecoder.decode(patchRequestKey, "utf-8");
        } catch (UnsupportedEncodingException e) {
            throw new IllegalArgumentException("URLDecoder decode param failed:" + patchRequestKey);
        }
        return patchUpdateCacheService.queryPatchInfo(decodeKey, hostIp);
    }

    @Override
    public PatchOperateResult registerSchemaPatch(@RequestBody SchemaPatchRegisterRequest schemaPatchRegisterRequest) {
        return schemaPatchUtils.registerSchemaPatchService(schemaPatchRegisterRequest);
    }
}