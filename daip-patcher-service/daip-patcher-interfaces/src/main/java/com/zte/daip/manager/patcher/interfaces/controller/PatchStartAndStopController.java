/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchStartAndStopController.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/21
 * </p>
 * <p>
 * 完成日期：2023/4/21
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.interfaces.controller;

import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.application.service.PatchStartAndStopAppService;
import com.zte.daip.manager.patcher.inner.api.PatchStartAndStopControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateServiceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
public class PatchStartAndStopController implements PatchStartAndStopControllerApi {

    @Autowired
    private PatchStartAndStopAppService patchStartAndStopAppService;

    @Autowired
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Override
    public List<String> queryCanOperateServices(String clusterId, boolean isStart,
        List<PatchOperateServiceDto> needOperateServiceDtos) throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            log.info("query can operate service:{}:{}:{}", clusterId, isStart, needOperateServiceDtos.toString());
            return patchStartAndStopAppService.queryCanOperateServices(clusterId, isStart, needOperateServiceDtos);
        } catch (Exception e) {
            throw new DaipBaseException("query can operate Service error", e);
        }
    }

    @Override
    public void startCluster(String clusterId, List<String> needOperateInstanceIds) throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            log.info("operate start service:{}:{}", clusterId, needOperateInstanceIds.toString());
            patchStartAndStopAppService.startCluster(clusterId, needOperateInstanceIds);
        } catch (Exception e) {
            throw new DaipBaseException("operate start service exception", e);
        }
    }

    @Override
    public void stopCluster(String clusterId, List<String> needOperateInstanceIds) throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            log.info("operate stop service:{}:{}", clusterId, needOperateInstanceIds.toString());
            patchStartAndStopAppService.stopCluster(clusterId, needOperateInstanceIds);
        } catch (Exception e) {
            throw new DaipBaseException("operate stop service exception", e);
        }
    }

    @Override
    public String queryOperatorProcess(String clusterId) throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        try {
            return patchStartAndStopAppService.queryOperatorProcess(clusterId);
        } catch (Exception e) {
            throw new DaipBaseException("operate stop service exception", e);
        }
    }
}