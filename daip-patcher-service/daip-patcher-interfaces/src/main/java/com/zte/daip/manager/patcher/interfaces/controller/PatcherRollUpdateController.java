package com.zte.daip.manager.patcher.interfaces.controller;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.PatcherRollUpdateControllerApi;
import com.zte.daip.manager.patcher.api.update.request.RollAutoPatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.api.update.response.RollAutoPatchUpdateInfo;
import com.zte.daip.manager.patcher.domain.update.cache.PatchUpdateReportCacheQueue;
import com.zte.daip.manager.patcher.domain.update.service.PatchQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class PatcherRollUpdateController implements PatcherRollUpdateControllerApi {

    @Autowired
    private PatchQueryService patchQueryService;
    @Autowired
    private PatchUpdateReportCacheQueue patchUpdateReportCacheQueue;
    @Autowired
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Override
    public List<RollAutoPatchUpdateInfo> queryUpdatePatchInfo(RollAutoPatchUpdateParam rollAutoPatchUpdateParam)
        throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        return patchQueryService.queryUpdatePatchInfo(rollAutoPatchUpdateParam);
    }

    @Override
    public void reportUpdateResult(PatchResult patchResult) {
        springAppCsrfProtector.validRequest();
        patchUpdateReportCacheQueue.add(Lists.newArrayList(patchResult));
    }
}
