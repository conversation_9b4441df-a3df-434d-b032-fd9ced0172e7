package com.zte.daip.manager.patcher.interfaces.controller;

import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.PatchOfflineRollbackControllerApi;
import com.zte.daip.manager.patcher.api.update.request.PatchServiceParam;
import com.zte.daip.manager.patcher.domain.update.cache.PatchRollbackCacheQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class PatchOfflineRollbackController implements PatchOfflineRollbackControllerApi {

    @Autowired
    private PatchRollbackCacheQueue patchRollbackCacheQueue;
    @Autowired
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Override
    public void deleteRollbackServicePatchHistory(List<PatchServiceParam> patchServiceParams) throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        patchRollbackCacheQueue.add(patchServiceParams);
    }
}
