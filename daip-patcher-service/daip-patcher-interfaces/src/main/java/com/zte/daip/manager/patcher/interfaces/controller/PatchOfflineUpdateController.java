package com.zte.daip.manager.patcher.interfaces.controller;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.PatchOfflineUpdateControllerApi;
import com.zte.daip.manager.patcher.api.update.request.PatchUpdateParam;
import com.zte.daip.manager.patcher.api.update.response.OfflinePatchUpdateInfo;
import com.zte.daip.manager.patcher.api.update.response.PatchResult;
import com.zte.daip.manager.patcher.domain.update.cache.PatchUpdateReportCacheQueue;
import com.zte.daip.manager.patcher.domain.update.service.PatchOfflineUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class PatchOfflineUpdateController implements PatchOfflineUpdateControllerApi {

    @Autowired
    private PatchOfflineUpdateService patchOfflineUpdateService;

    @Autowired
    private PatchUpdateReportCacheQueue patchUpdateReportCacheQueue;

    @Autowired
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Override
    public List<OfflinePatchUpdateInfo> queryUpdatePatchInfo(PatchUpdateParam offlinePatchUpdateParam)
        throws DaipBaseException {
        springAppCsrfProtector.validRequest();
        return patchOfflineUpdateService.queryUpdatePatchInfo(offlinePatchUpdateParam);
    }

    @Override
    public void reportUpdateResult(PatchResult patchResult) {
        springAppCsrfProtector.validRequest();
        patchUpdateReportCacheQueue.add(Lists.newArrayList(patchResult));
    }
}
