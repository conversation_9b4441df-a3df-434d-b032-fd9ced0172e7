/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatcherInfoController.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/8
 * </p>
 * <p>
 * 完成日期：2021/3/8
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.interfaces.controller;

import com.zte.daip.manager.common.utils.paging.jpa.QueryParam;
import com.zte.daip.manager.patcher.api.PatcherInfoControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchDispatchDto;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.api.dto.PendingUpdatePatchPageInfo;
import com.zte.daip.manager.patcher.domain.common.PatchDispatchService;
import com.zte.daip.manager.patcher.domain.common.PatchHistoryService;
import com.zte.daip.manager.patcher.domain.query.entity.PatchKeyDo;
import com.zte.daip.manager.patcher.domain.query.service.PatchDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
public class PatcherInfoController implements PatcherInfoControllerApi {

    @Autowired
    private PatchDetailService patchDetailService;

    @Autowired
    private PatchDispatchService patchDispatchService;

    @Autowired
    private PatchHistoryService patchHistoryService;

    @Override
    public List<PatchDetailDto> queryPatches() {
        return patchDetailService.queryAllPatch();
    }

    @Override
    public PendingUpdatePatchPageInfo queryPatchesByPaging(QueryParam query) {
        return patchDetailService.queryPatchesByPaging(query);
    }

    @Override
    public List<String> queryPatchesType() {
        return patchDetailService.queryAllPatchType();
    }

    @Override
    public boolean checkPatchIsLoaded(String patchName) {
        return patchDetailService.checkPatchIsLoaded(patchName);
    }

    @Override
    public List<PatchDispatchDto> queryPatchesDispatch(String patchName, String type) {

        return patchDispatchService.queryPatchDispatchInfoByPatchKey(new PatchKeyDo(patchName), type);

    }

    @Override
    public List<PatchHistoryDto> queryPatchesHistory(String patchName, String serviceName) {
        return patchHistoryService.queryPatchDispatchInfoByPatchKey(new PatchKeyDo(patchName, serviceName, ""));
    }

    @Override
    public List<PatchHistoryDto> queryPatchesHistory(String patchName, String serviceName, String version,
        String updateType) {
        return patchDetailService.queryPatchesHistoryByUpdateType(patchName, serviceName, version, updateType);
    }

}