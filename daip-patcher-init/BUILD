load("//:defs.bzl", "run_tests")
load("@rules_pkg//:pkg.bzl", "pkg_tar")

java_library(
    name = "daip-patcher-init",
    srcs = glob(
        include = ["src/main/java/**/*.java"],
        exclude = [],
    ),
    resources = glob(
        include = ["src/main/resources/**/*"],
        exclude = ["src/main/resources/**"],
    ),
    visibility = [
    ],
    deps = [
        "//3rdparty/jvm/antlr",
        "//3rdparty/jvm/ch/qos/logback:logback_access",
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/101tec:zkclient",
        "//3rdparty/jvm/com/alibaba:cooma",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/auth0:java_jwt",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_joda",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_afterburner",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names",
        "//3rdparty/jvm/com/github/ben_manes/caffeine",
        "//3rdparty/jvm/com/github/loki4j:loki_logback_appender_jdk8",
        "//3rdparty/jvm/com/github/luben:zstd_jni",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/errorprone:error_prone_annotations",
        "//3rdparty/jvm/com/google/guava",
        "//3rdparty/jvm/com/google/guava:failureaccess",
        "//3rdparty/jvm/com/google/guava:listenablefuture",
        "//3rdparty/jvm/com/google/j2objc:j2objc_annotations",
        "//3rdparty/jvm/com/helger:profiler",
        "//3rdparty/jvm/com/io7m/xom",
        "//3rdparty/jvm/com/mikesamuel:json_sanitizer",
        "//3rdparty/jvm/com/netflix/archaius:archaius_core",
        "//3rdparty/jvm/com/netflix/hystrix:hystrix_core",
        "//3rdparty/jvm/com/networknt:json_schema_validator",
        "//3rdparty/jvm/com/squareup/okhttp3:okhttp",
        "//3rdparty/jvm/com/squareup/okio",
        "//3rdparty/jvm/com/squareup/retrofit2:retrofit",
        "//3rdparty/jvm/com/sun/istack:istack_commons_runtime",
        "//3rdparty/jvm/com/typesafe/scala_logging:scala_logging_2_12",
        "//3rdparty/jvm/com/yammer/metrics:metrics_core",
        "//3rdparty/jvm/com/zaxxer:HikariCP",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_i18n",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_beans",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_httpclient",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_init_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_response",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_security",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_client",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_utils_msb",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_utils_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_common_utils",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_commons_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_configcenter_commons",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_configcenter_configclient",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_datetime_utils",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_db_jdbc_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_gr_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_initckeck_utils",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_internalcontrol_agent_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jersey_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jersey_swagger_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jetty_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_kafka_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_msb_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_rpc_retrofit_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_sm_impl",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_configcenter",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_db_jdbc",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_gr",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_internalcontrol_agent",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_jersey",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_jetty",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_msb",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_rpc_retrofit",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_sm",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_utils_service",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_web",
        "//3rdparty/jvm/com/zte/ums/zenap/dropwizard/ext:zenap_dropwizard_ext",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core_pom",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_circuitbreaker",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_istio",
        "//3rdparty/jvm/com/zte/ums/zenap/i18n:zenap_i18n_core",
        "//3rdparty/jvm/com/zte/ums/zenap/kafka:zenap_kafka_rule",
        "//3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_bundle",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service",
        "//3rdparty/jvm/com/zte/ums/zenap/okhttp",
        "//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_center_cipher_springboot_agent",
        "//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher",
        "//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_kms_agent_sdk",
        "//3rdparty/jvm/com/zte/zdh:zdh_commons",
        "//3rdparty/jvm/commons_beanutils",
        "//3rdparty/jvm/commons_codec",
        "//3rdparty/jvm/commons_collections",
        "//3rdparty/jvm/commons_configuration",
        "//3rdparty/jvm/commons_fileupload",
        "//3rdparty/jvm/commons_io",
        "//3rdparty/jvm/commons_lang",
        "//3rdparty/jvm/commons_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_configuration",
        "//3rdparty/jvm/io/dropwizard:dropwizard_core",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jackson",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jersey",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jetty",
        "//3rdparty/jvm/io/dropwizard:dropwizard_lifecycle",
        "//3rdparty/jvm/io/dropwizard:dropwizard_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_metrics",
        "//3rdparty/jvm/io/dropwizard:dropwizard_request_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_servlets",
        "//3rdparty/jvm/io/dropwizard:dropwizard_util",
        "//3rdparty/jvm/io/dropwizard:dropwizard_validation",
        "//3rdparty/jvm/io/dropwizard/logback:logback_throttling_appender",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_annotation",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_healthchecks",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jersey2",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jetty9",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jmx",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_json",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jvm",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_logback",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_servlets",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/io/github/openfeign:feign_core",
        "//3rdparty/jvm/io/github/openfeign:feign_hystrix",
        "//3rdparty/jvm/io/github/openfeign:feign_slf4j",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_circuitbreaker",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_circularbuffer",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_consumer",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_core",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_rxjava2",
        "//3rdparty/jvm/io/jsonwebtoken:jjwt",
        "//3rdparty/jvm/io/micrometer:micrometer_core",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_codec_dns",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/io/netty:netty_transport_classes_epoll",
        "//3rdparty/jvm/io/netty:netty_transport_native_epoll",
        "//3rdparty/jvm/io/netty:netty_transport_native_unix_common",
        "//3rdparty/jvm/io/projectreactor:reactor_core",
        "//3rdparty/jvm/io/reactivex:rxjava",
        "//3rdparty/jvm/io/reactivex/rxjava2:rxjava",
        "//3rdparty/jvm/io/reactivex/rxjava3:rxjava",
        "//3rdparty/jvm/io/springfox:springfox_core",
        "//3rdparty/jvm/io/springfox:springfox_schema",
        "//3rdparty/jvm/io/springfox:springfox_spi",
        "//3rdparty/jvm/io/springfox:springfox_spring_web",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_common",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/io/swagger:swagger_annotations",
        "//3rdparty/jvm/io/swagger:swagger_core",
        "//3rdparty/jvm/io/swagger:swagger_jaxrs",
        "//3rdparty/jvm/io/swagger:swagger_jersey2_jaxrs",
        "//3rdparty/jvm/io/swagger:swagger_models",
        "//3rdparty/jvm/io/vavr",
        "//3rdparty/jvm/io/vavr:vavr_match",
        "//3rdparty/jvm/io/zipkin/brave",
        "//3rdparty/jvm/io/zipkin/brave:brave_context_slf4j",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpasyncclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_jms",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_clients",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_streams",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_rpc",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_servlet",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_rabbit",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_web",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_webmvc",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_brave",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_metrics_micrometer",
        "//3rdparty/jvm/io/zipkin/zipkin2:zipkin",
        "//3rdparty/jvm/jakarta/activation:jakarta_activation_api",
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/persistence:jakarta_persistence_api",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/jakarta/transaction:jakarta_transaction_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/websocket:jakarta_websocket_api",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//3rdparty/jvm/javax/cache:cache_api",
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//3rdparty/jvm/javax/servlet:servlet_api",
        "//3rdparty/jvm/javax/validation:validation_api",
        "//3rdparty/jvm/joda_time",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy_agent",
        "//3rdparty/jvm/net/sf/ezmorph",
        "//3rdparty/jvm/net/sf/jopt_simple",
        "//3rdparty/jvm/net/sf/json_lib:json_lib_jar_jdk15",
        "//3rdparty/jvm/net/sourceforge/argparse4j",
        "//3rdparty/jvm/org/apache/commons:commons_collections4",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/apache/commons:commons_text",
        "//3rdparty/jvm/org/apache/httpcomponents:httpasyncclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore_nio",
        "//3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2",
        "//3rdparty/jvm/org/apache/kafka:kafka_2_12",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_api",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j",
        "//3rdparty/jvm/org/apache/sshd:sshd_common",
        "//3rdparty/jvm/org/apache/sshd:sshd_core",
        "//3rdparty/jvm/org/apache/sshd:sshd_scp",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_constants",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_css",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_i18n",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_util",
        "//3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons",
        "//3rdparty/jvm/org/apache/yetus:audience_annotations",
        "//3rdparty/jvm/org/apache/zookeeper",
        "//3rdparty/jvm/org/apache/zookeeper:zookeeper_jute",
        "//3rdparty/jvm/org/apache_extras/beanshell:bsh",
        "//3rdparty/jvm/org/aspectj:aspectjrt",
        "//3rdparty/jvm/org/aspectj:aspectjweaver",
        "//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on",
        "//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on",
        "//3rdparty/jvm/org/checkerframework:checker_qual",
        "//3rdparty/jvm/org/conscrypt:conscrypt_openjdk_uber",
        "//3rdparty/jvm/org/dom4j",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_alpn_conscrypt_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_alpn_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_annotations",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_client",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_continuation",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_http",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_io",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_plus",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_security",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlet",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlets",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util_ajax",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_webapp",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_xml",
        "//3rdparty/jvm/org/eclipse/jetty/http2:http2_common",
        "//3rdparty/jvm/org/eclipse/jetty/http2:http2_hpack",
        "//3rdparty/jvm/org/eclipse/jetty/http2:http2_server",
        "//3rdparty/jvm/org/eclipse/jetty/toolchain/setuid:jetty_setuid_java",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:javax_websocket_client_impl",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:javax_websocket_server_impl",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_api",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_client",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_common",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_server",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_servlet",
        "//3rdparty/jvm/org/glassfish/hk2",
        "//3rdparty/jvm/org/glassfish/hk2:class_model",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_api",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_core",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_locator",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_runlevel",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_utils",
        "//3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator",
        "//3rdparty/jvm/org/glassfish/hk2:spring_bridge",
        "//3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/jaxb:jaxb_runtime",
        "//3rdparty/jvm/org/glassfish/jaxb:txw2",
        "//3rdparty/jvm/org/glassfish/jersey/bundles/repackaged:jersey_guava",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_client",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_server",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_entity_filtering",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_spring5",
        "//3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2",
        "//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_jaxb",
        "//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_json_jackson",
        "//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_multipart",
        "//3rdparty/jvm/org/hdrhistogram:HdrHistogram",
        "//3rdparty/jvm/org/hibernate:hibernate_core",
        "//3rdparty/jvm/org/hibernate/common:hibernate_commons_annotations",
        "//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/htmlunit:neko_htmlunit",
        "//3rdparty/jvm/org/immutables:value",
        "//3rdparty/jvm/org/javassist",
        "//3rdparty/jvm/org/jboss:jandex",
        "//3rdparty/jvm/org/jboss/logging:jboss_logging",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river",
        "//3rdparty/jvm/org/jdom:jdom2",
        "//3rdparty/jvm/org/jodd:jodd_bean",
        "//3rdparty/jvm/org/jodd:jodd_core",
        "//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2",
        "//3rdparty/jvm/org/jvnet/mimepull",
        "//3rdparty/jvm/org/latencyutils:LatencyUtils",
        "//3rdparty/jvm/org/lz4:lz4_java",
        "//3rdparty/jvm/org/mapstruct",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/objenesis",
        "//3rdparty/jvm/org/ow2/asm",
        "//3rdparty/jvm/org/ow2/asm:asm_analysis",
        "//3rdparty/jvm/org/ow2/asm:asm_commons",
        "//3rdparty/jvm/org/ow2/asm:asm_tree",
        "//3rdparty/jvm/org/ow2/asm:asm_util",
        "//3rdparty/jvm/org/owasp/antisamy",
        "//3rdparty/jvm/org/owasp/esapi",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/reactivestreams:reactive_streams",
        "//3rdparty/jvm/org/redisson",
        "//3rdparty/jvm/org/reflections",
        "//3rdparty/jvm/org/scala_lang:scala_library",
        "//3rdparty/jvm/org/scala_lang:scala_reflect",
        "//3rdparty/jvm/org/slf4j:jcl_over_slf4j",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//3rdparty/jvm/org/springframework:spring_aspects",
        "//3rdparty/jvm/org/springframework:spring_beans",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_context_support",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/springframework:spring_expression",
        "//3rdparty/jvm/org/springframework:spring_jcl",
        "//3rdparty/jvm/org/springframework:spring_jdbc",
        "//3rdparty/jvm/org/springframework:spring_orm",
        "//3rdparty/jvm/org/springframework:spring_tx",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//3rdparty/jvm/org/springframework:spring_webmvc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_actuator",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_actuator_autoconfigure",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_actuator",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_data_jpa",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jdbc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jersey",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jetty",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_json",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_web",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_config_client",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_context",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_sleuth_core",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_config",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_openfeign",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth",
        "//3rdparty/jvm/org/springframework/data:spring_data_commons",
        "//3rdparty/jvm/org/springframework/data:spring_data_jpa",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//3rdparty/jvm/org/springframework/retry:spring_retry",
        "//3rdparty/jvm/org/springframework/security:spring_security_crypto",
        "//3rdparty/jvm/org/springframework/security:spring_security_rsa",
        "//3rdparty/jvm/org/xerial/snappy:snappy_java",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//3rdparty/jvm/xerces:xercesImpl",
        "//3rdparty/jvm/xml_apis",
        "//3rdparty/jvm/xml_apis:xml_apis_ext",
    ],
)

java_library(
    name = "daip-patcher-init-test-classes",
    srcs = [],
    resources = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
    ],
)

run_tests(
    name = "AllTests",
    size = "small",
    srcs = [],
    data = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
    ],
)

pkg_tar(
    name = "daip-patcher-init-lib",
    srcs = [
        "//3rdparty/jvm/xml_apis:xml_apis_ext",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_servlet",
    ],
    package_dir = "/lib",

    visibility = [
        "//:__pkg__",
    ],
)

filegroup(
    name = "daip-patcher-init-resource",
    srcs = glob([
        "src/main/resources/**",
    ])+ [":daip-patcher-init",],
    visibility = [
        "//:__pkg__",
    ],
)


