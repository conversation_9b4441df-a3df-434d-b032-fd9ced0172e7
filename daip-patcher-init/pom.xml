<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>daip-patcher</artifactId>
        <groupId>com.zte.daip.manager.patcher</groupId>
        <version>deletePatch</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zte.daip.manager.patcher</groupId>
    <artifactId>daip-patcher-init</artifactId>
    <version>deletePatch</version>

    <dependencies>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-web-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-db-jdbc-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-init-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-utils-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-utils-msb</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-sensitive-log</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-task-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-task-client</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**</exclude>
                </excludes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>paas</id>
            <properties>
                <app.mainclass>com.zte.daip.manager.patcher.init.PatcherInitApplication
                </app.mainclass>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <finalName>${init.microservice.name}</finalName>
                            <appendAssemblyId>false</appendAssemblyId>
                            <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                            <descriptors>
                                <descriptor>src/assembly/assembly_init_inner_tar.xml
                                </descriptor>
                            </descriptors>
                            <overrideUid>${non.root.uid}</overrideUid>
                            <overrideGid>${non.root.uid}</overrideGid>
                        </configuration>
                        <executions>
                            <execution>
                                <id>assembly</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>