#!/bin/sh

DIRNAME=`dirname $0`
RUNHOME=`cd $DIRNAME/; pwd`
echo @RUNHOME@ $RUNHOME

if [ -f "/home/<USER>/initGlobalEnv.sh" ]; then
. "/home/<USER>/initGlobalEnv.sh"
else
echo "can not found /home/<USER>/initGlobalEnv.sh"
fi

if [ -f "$RUNHOME/setenv.sh" ]; then
. "$RUNHOME/setenv.sh"
else
echo "can not found $RUNHOME/setenv.sh"
fi

DIRNAME=`dirname $0`
RUNHOME=`cd $DIRNAME/; pwd`
echo @RUNHOME@ $RUNHOME


echo ================== ENV_INFO  =============================================
echo @RUNHOME@  $RUNHOME
echo @JAVA_BASE@  $JAVA_BASE
echo @Main_Class@  $Main_Class
echo @APP_INFO@  $APP_INFO
echo @Main_JAR@  $Main_JAR
echo @Main_Conf@ $Main_Conf
echo ==========================================================================

echo start $APP_INFO ...

JAVA="$JAVA_HOME/bin/java"
JAVA_OPTS="$JAVA_OPTS -Xms50m -Xmx128m $JAVA_GLOBAL_OPTS $JVM_GC_OPTS"
JAVA_OPTS="$JAVA_OPTS -DMS_APP_NAME=JMS_DEMO_MICROSERVICENAME"
#port=8888
#JAVA_OPTS="$JAVA_OPTS -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:$port"
CLASS_PATH="$LIB_DIRS:$RUNHOME/:$RUNHOME/$Main_JAR"

echo ================== RUN_INFO  =============================================
echo @JAVA_HOME@ $JAVA_HOME
echo @JAVA@ $JAVA
echo @JAVA_OPTS@ $JAVA_OPTS
echo @CLASS_PATH@ $CLASS_PATH
echo @LIB_DIRS@ $LIB_DIRS
echo ==========================================================================

echo @JAVA@ $JAVA
echo @JAVA_CMD@

set -e
"$JAVA" $JAVA_OPTS -classpath "$CLASS_PATH" -Dapplication.basekey.home.path=/opt/dap_security/AES $Main_Class

set +e
if [ -f "$RUNHOME/modify_volume_permission.sh" ]; then
  sh "$RUNHOME/modify_volume_permission.sh" /data1/version /home/<USER>/patch /cloud-disk-notification-volume
else
  chown -R @non.root.uid@:@non.root.gid@ /data1/version /home/<USER>/patch /cloud-disk-notification-volume
fi