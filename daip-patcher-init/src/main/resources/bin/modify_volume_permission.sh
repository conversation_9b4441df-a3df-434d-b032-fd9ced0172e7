#!/bin/sh

function modify_file_permission_in_dir {
  local dir=$1
  if [ ! -d ${dir} ]; then
    echo "${dir} is a not directory, skip."
    return
  fi

  testPermFile=${dir}/perm-`hostname`.tmp
  touch ${testPermFile}
  owner=`stat -c '%U' ${testPermFile}`
  rm -f ${testPermFile}
  if [ `whoami` != ${owner} ]; then
    echo "the directory may be a squashed nfs directory, skip change permission"
    return
  fi

# Started by AICoder, pid:c75c4fdea2ra002144850bc210030f1621e0a1aa
files=$(find ${dir} ! -user oes ! -user nobody)
if [ $? -eq 0 ]; then
    for f in ${files}
    do
      chown @non.root.uid@:@non.root.gid@ ${f}
    done
else
    echo "MayBe find command error."
    chown -R @non.root.uid@:@non.root.gid@ ${dir}
fi
# Ended by AICoder, pid:c75c4fdea2ra002144850bc210030f1621e0a1aa
}

function modify_file_permission_in_dirs {
  local dirs=$@
  for dir in ${dirs}
  do
    if [ -e ${dir} ]; then
      modify_file_permission_in_dir ${dir}
    else
      echo "the ${dir} does not exist"
    fi
  done
}

modify_file_permission_in_dirs $@
