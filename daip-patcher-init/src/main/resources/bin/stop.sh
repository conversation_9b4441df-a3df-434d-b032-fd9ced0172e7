#!/bin/bash
DIRNAME=`dirname $0`
RUNHOME=`cd $DIRNAME/; pwd`
echo @RUNHOME@ $RUNHOME


if [ -f "$RUNHOME/setenv.sh" ]; then
  . "$RUNHOME/setenv.sh"
else
echo "can not found $RUNHOME/setenv.sh"
fi


echo ================== ENV_INFO  =============================================
echo RUNHOME=$RUNHOME
echo JAVA_BASE=$JAVA_BASE
echo Main_Class=$Main_Class
echo APP_INFO=$APP_INFO
echo ==========================================================================


cd $RUNHOME; pwd;

function save_app_pid(){
	app_id=`ps -ef | grep $Main_Class| grep $RUNHOME | grep -v grep | awk '{print $2}'`
	echo @app_id@ $app_id
}

function kill_app_process(){

    if [ $app_id -gt 0 ];then
         echo "kill  pid is: "${app_id}
         kill -9 $app_id
    else
         echo "Warn: pid exit but no this pid."
    fi
}

save_app_pid;
echo @C_CMD@ kill -9 $app_id
kill_app_process;