
debug: false

server:
  servlet:
      context-path: /api/@init.microservice.name@/v1
  port: 56191

dexcloud:
  base:
    microservice:
      name: @init.microservice.name@
      version: v1
      organization: zdh
      metaInfo:
        scope: test
  web:
    swagger:
      beanConfig:
        title: 'DAIP patcher Service API Documentation'
        version: '1.0'
  serviceinfo:
    serviceName: @init.microservice.name@
  clusterJob:
    enabled: false
  es:
    enabled: false

spring:
  cloud:
    zookeeper:
      enabled: false
  jersey:
      application-path: /api/@init.microservice.name@/v1
  jpa:
    generate-ddl: false
    show-sql: true
    properties:
      hibernate:
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      ddl-auto: none
    database: postgresql

logging:
  config: classpath:logback.xml
  path: ./logs

management:
  endpoints:
    web:
      expose: '*'
      base-path: /actuator

LOCALE: zh_CN
