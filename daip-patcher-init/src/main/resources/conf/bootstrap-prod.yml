server:
  servlet:
      context-path: /api/@init.microservice.name@/v1
  port: 56191

spring:
  jersey:
    application-path: /api/@init.microservice.name@/v1
  cloud:
    zookeeper:
      enabled: false
  datasource:
    url: jdbc:postgresql://${rdb_ip}:${rdb_port}/${rdb_dbname}
    username: ${rdb_user}
    password: ${rdb_password}
    driver-class-name: ${rdb_driver}
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      maximumPoolSize: 10
      minimumIdle: 2
      idleTimeout: 600000
      connectionTimeout: 30000
      maxLifetime: 1800000
  jpa:
    generate-ddl: false
    show-sql: true
    properties:
      hibernate:
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      ddl-auto: none
    database: postgresql

logging:
  config: classpath:logback.xml
  path: ./logs
dexcloud:
  base:
    microservice:
      name: @init.microservice.name@
      version: v1
      organization: zdh
  logagent:
    enabled: false
  discovery:
    msb:
      enabled: true
      server:
        address: ${msb_svrIp}
        port: ${msb_svrPort}
        namespace: ${msb_nameSpace}
  clusterJob:
    enabled: false
  es:
    enabled: false

kafkaclientconf:
  bootstrapServers: ${kafka_brokers}:${kafka_port}
  kafkaServiceName: kafka
  kafkaServiceVersion: v1
  consumerConf:
    properties:
      value.deserializer: org.apache.kafka.common.serialization.StringDeserializer
  producerConf:
    properties:
      key.serializer: org.apache.kafka.common.serialization.StringSerializer
      value.serializer: org.apache.kafka.common.serialization.StringSerializer
      compression.type: gzip
      retries: 1