<!-- Logback configuration. See http://logback.qos.ch/manual/index.html -->
<configuration scan="true" scanPeriod="10 seconds">
	<include resource="org/springframework/boot/logging/logback/defaults.xml" />

	<conversionRule conversionWord="m" converterClass="com.zte.daip.manager.common.sensitive.log.logback.LogMessageConverter"/>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss SSS} %-5p [%c][%t] - %m%n</pattern>
		</encoder>
	</appender>

	<appender name="INFO_FILE"
			  class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
		<File>./logs/info.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
			<fileNamePattern>./logs/info.log.%i.gz</fileNamePattern>
			<minIndex>1</minIndex>
			<maxIndex>10</maxIndex>
		</rollingPolicy>
		<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<maxFileSize>10MB</maxFileSize>
		</triggeringPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>%d{yyyy-MM-dd HH:mm:ss SSS} %-5p [%c][%t] - %m%n</Pattern>
		</layout>
	</appender>

	<root level="INFO">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="INFO_FILE" />
	</root>

	<!-- <logger name="org.springframework" level="DEBUG" /> -->
</configuration>
