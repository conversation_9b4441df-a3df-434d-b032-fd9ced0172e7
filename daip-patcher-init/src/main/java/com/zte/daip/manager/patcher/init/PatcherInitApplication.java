package com.zte.daip.manager.patcher.init;

import com.zte.oes.dexcloud.commons.annotation.DexCloudApplication;
import com.zte.oes.dexcloud.commons.component.retrofit.annotation.EnableRetrofitRPC;
import org.dexcloud.springboot.kafka.config.annotation.EnableKafka;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 功能描述:<br>
 * <p>
 * <p>
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@DexCloudApplication
@EnableKafka
@EnableRetrofitRPC
@EnableAutoConfiguration
@ComponentScan(basePackages = "com.zte.daip.*")
@EntityScan(basePackages = "com.zte.daip.*")
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@EnableRetry
@EnableJpaRepositories(basePackages = "com.zte.daip.*")
public class PatcherInitApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(PatcherInitApplication.class, args);
    }

}