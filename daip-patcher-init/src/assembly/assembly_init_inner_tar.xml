<assembly>
    <id>product-init-inner-tar</id>
    <formats>
        <!-- zip,tar,tar.gz,tar.bz2,jar,dir,war -->
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/
            </directory>
            <excludes>
                <exclude>conf/**</exclude>
            </excludes>
            <outputDirectory>./${init.microservice.name}/</outputDirectory>
            <filtered>true</filtered>
            <directoryMode>0750</directoryMode>
            <fileMode>0750</fileMode>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/
            </directory>
            <includes>
                <include>conf/**</include>
            </includes>
            <outputDirectory>./${init.microservice.name}/</outputDirectory>
            <filtered>true</filtered>
            <directoryMode>0750</directoryMode>
            <fileMode>0640</fileMode>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/
            </directory>
            <includes>
                <include></include>
            </includes>
            <outputDirectory>./${init.microservice.name}/lib/</outputDirectory>
            <directoryMode>0750</directoryMode>
        </fileSet>
    </fileSets>

    <dependencySets>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>./${init.microservice.name}/lib/</outputDirectory>
            <fileMode>0640</fileMode>
            <includes>
                <include>org.antlr:antlr4-runtime</include>
                <include>com.zte.daip*:*</include>
                <include>org.apache.commons:*</include>
                <include>com.alibaba:*</include>
                <include>com.alibaba.fastjson2:fastjson2-extension</include>
                <include>com.alibaba.fastjson2:fastjson2</include>
                <include>log4j:log4j</include>
                <include>com.baomidou:*</include>
                <include>org.springframework.data:*</include>
                <include>org.apache.commons:commons-pool2</include>
                <include>org.springframework:spring-context-support</include>
                <include>org.springframework:spring-orm</include>
                <include>org.hibernate*:*</include>
                <include>org.dom4j:dom4j</include>
                <include>antlr:antlr</include>
                <include>commons-configuration:commons-configuration</include>
                <include>jakarta.transaction:jakarta.transaction-api</include>
                <include>org.jvnet.hudson:ganymed-ssh2</include>
                <include>com.zte.zdh:zdh-commons</include>
                <include>org.springframework:spring-messaging</include>
                <include>com.auth0:java-jwt</include>
                <include>jakarta.persistence:jakarta.persistence-api</include>
                <include>jakarta.transaction:jakarta.transaction-api</include>
                <include>com.zte.ums.zenap.sm.sso.agent:sm-sso-agent</include>
            </includes>
            <excludes>
                <exclude>${project.groupId}:${project.artifactId}</exclude>
            </excludes>
        </dependencySet>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>./${init.microservice.name}/</outputDirectory>
            <fileMode>0640</fileMode>
            <includes>
                <include>${project.groupId}:${project.artifactId}</include>
            </includes>
        </dependencySet>
    </dependencySets>

</assembly>