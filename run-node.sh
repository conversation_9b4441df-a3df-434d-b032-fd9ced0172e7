# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

set -e               # exit on error

cd "$(dirname "$0")" # connect to root

if [ "$(uname -s)" == "Linux" ]; then
  USER_NAME=${SUDO_USER:=$USER}
else # boot2docker uid and gid
  USER_NAME=$USER
fi

if [ -t 0 ]; then
  INTERACTIVE_OPTS="-ti"
else
  INTERACTIVE_OPTS=""
fi

docker run --rm=true ${INTERACTIVE_OPTS}\
  --dns="*********" \
  -v "${PWD}:/home/<USER>/"\
  -w "/home/<USER>/" \
  dap-release-docker.artnj.zte.com.cn/daip/obfuscated/gulp_base_image:1.0\
  sh -c "npm link gulp gulp-plumber gulp-uglify && gulp compress"
