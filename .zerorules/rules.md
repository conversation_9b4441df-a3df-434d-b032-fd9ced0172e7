# 代码库概述
## 代码库名称
Patcher 补丁管理系统
## 统一语言
DAIP：Data Analysis Intelligence Platform
Patcher：Patch Maintenance Service

## 代码库描述

DAIP Patcher作为数据智能分析平台的补丁管理系统，负责实现补丁的全生命周期管理，包括补丁的上传、验证、分发、应用、回滚及历史记录管理等功能。

## 系统架构

DAIP Patcher工程采用DDD分层结构，模块组成可划分为以下核心层次：

1、**接口层（Interfaces Layer）** 模块：daip-patcher-interfaces 

2、**应用层（Application Layer）** 模块：daip-patcher-application 

3、**领域层（Domain Layer）** 模块：daip-patcher-domain

4、**基础设施层（Infrastructure Layer）** 模块：daip-patcher-infrastructure-api

## 模块组成
### daip-patcher-application模块

**模块路径**：`PATCHER\daip-patcher-service\daip-patcher-application`

**核心职责**： 该模块作为应用层，主要处理补丁管理系统的业务逻辑协调工作，具体包括：

- 任务监听与处理
- 事件驱动架构实现（处理补丁分发、回滚等事件）
- 领域层与基础设施层的适配器

**关键包功能**：

- `PATCHER\daip-patcher-service\daip-patcher-application\src\main\java\com\zte\daip\manager\patcher\application\listener`主要负责补丁管理系统的事件监听与消息消费功能，包括补丁分发事件监听、补丁上传事件监听、服务安装消息监听等。

### daip-patcher-domain模块

**模块路径**：`PATCHER\daip-patcher-service\daip-patcher-domain`

**核心职责**： 该模块作为领域层，主要承载补丁管理系统的核心业务逻辑与数据模型，具体包括：

- 定义补丁生命周期的核心领域模型
- 实现补丁操作与调度的核心业务规则
- 提供领域服务接口与基础操作实现

**关键包功能**：

- `PATCHER\daip-patcher-service\daip-patcher-domain\src\main\java\com\zte\daip\manager\patcher\domain\common`包下的类主要负责放补丁分发、历史记录管理、补丁信息管理、补丁任务管理等关键操作。

### daip-patcher-interfaces模块

**模块路径**：`PATCHER\daip-patcher-service\daip-patcher-interfaces`

**核心职责**： 该模块作为接口层，主要负责处理外部请求与系统交互的入口，具体包括：

- 定义RESTful API接口规范
- 处理HTTP请求的路由与参数校验
- 将外部请求转换为应用层可处理的指令

**关键包功能**：

- `PATCHER\daip-patcher-service\daip-patcher-interfaces\src\main\java\com\zte\daip\manager\patcher\interfaces`包下的类主要承担rest接口访问职责，包括补丁分发接口、补丁上传接口、补丁信息查询接口、补丁回退接口等。

### daip-patcher-infrastructure-api模块

**模块路径**：`PATCHER\daip-patcher-service\daip-patcher-infrastructure-api`

**核心职责**： 该模块作为基础设施层，主要负责系统与外部组件的交互及底层资源管理，具体包括：

- 提供数据库访问接口与数据持久化实现。
- 管理与外部系统的通信（如环境配置、IP修改服务）
- 实现缓存与状态存储机制

**关键包功能**：

- `PATCHER\daip-patcher-service\daip-patcher-infrastructure-api\src\main\java\com\zte\daip\manager\patcher\infrastructure\repository`包下的类主要承担基础设施层的数据访问职责，使用Spring-jpa做ORM持久化实现。例如:补丁上传后的信息都通过类PatchInfoRepository持久化到数据库，里面提供了CRUD方法

# 技术栈
## 前端
- 框架: Vue.js 2.6.10
- 路由: Vue Router 3.0.3
- 状态管理: Vuex 3.0.1
- UI组件库: Vein UI 2.6.36
- 图表: ECharts 5.4.2 + Vue ECharts 6.6.0
- 国际化: Vue-i18n 8.22.2
## 后端
- 开发语言：Java21
- 开发框架：SpringBoot/SpringCloud 2.7x
- 容器编排: Kubernetes
- 配置管理: ConfigMap/Secret
- 服务发现: Kubernetes Service
- 日志框架：Slf4j
- 数据库连接方式：spring-jpa/Mybatis
- 数据库：Postgres
- 文档管理和测试工具：Swagger
## 开发工具
前端：
- 构建工具: Vue CLI 3.9.0

后端：

- 构建工具：Maven
- 单元测试框架：JUnit5
- Mock框架：JMockito
- 断言框架：Hamcrest

# 其他

## 测试标准
- 业务逻辑所需的单元测试
## 单元测试命令执行目录
请在最小的模块目录下验证您的修改，以最大限度地提高单元测试的执行效率。
## 代码质量提示
- **防止代码截断**
- 不要偷懒。不要省略代码

