load("//:defs.bzl", "run_tests")

java_library(
    name = "daip-patcher-task-worker",
    srcs = glob(
        include = ["src/main/java/**/*.java"],
        exclude = [],
    ),
    resources = glob(
        include = ["src/main/resources/**/*"],
        exclude = [],
    ),
    visibility = [
    ],
    deps = [
        "//3rdparty/jvm/antlr",
        "//3rdparty/jvm/ch/qos/logback:logback_access",
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/alibaba:cooma",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/auth0:java_jwt",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_joda",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_afterburner",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names",
        "//3rdparty/jvm/com/github/ben_manes/caffeine",
        "//3rdparty/jvm/com/github/loki4j:loki_logback_appender_jdk8",
        "//3rdparty/jvm/com/github/luben:zstd_jni",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/errorprone:error_prone_annotations",
        "//3rdparty/jvm/com/google/guava",
        "//3rdparty/jvm/com/google/guava:failureaccess",
        "//3rdparty/jvm/com/google/guava:listenablefuture",
        "//3rdparty/jvm/com/google/j2objc:j2objc_annotations",
        "//3rdparty/jvm/com/helger:profiler",
        "//3rdparty/jvm/com/io7m/xom",
        "//3rdparty/jvm/com/lmax:disruptor",
        "//3rdparty/jvm/com/mikesamuel:json_sanitizer",
        "//3rdparty/jvm/com/netflix/archaius:archaius_core",
        "//3rdparty/jvm/com/netflix/hystrix:hystrix_core",
        "//3rdparty/jvm/com/networknt:json_schema_validator",
        "//3rdparty/jvm/com/squareup/okhttp3:okhttp",
        "//3rdparty/jvm/com/squareup/okio",
        "//3rdparty/jvm/com/squareup/retrofit2:retrofit",
        "//3rdparty/jvm/com/sun/istack:istack_commons_runtime",
        "//3rdparty/jvm/com/zaxxer:HikariCP",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_cache_common",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_i18n",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_common",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_kafka",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_replyproducer",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_beans",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_client",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_common_util",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_httpclient",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_common",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_response",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_security",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_client",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_message",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_worker",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter",
        "//3rdparty/jvm/com/zte/daip/manager/miniagent:daip_miniagent_seed",
        "//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_domain",
        "//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_infrastructure_api",
        "//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api",
        "//3rdparty/jvm/com/zte/ums/zenap/dropwizard/ext:zenap_dropwizard_ext",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_circuitbreaker",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_istio",
        "//3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_bundle",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service",
        "//3rdparty/jvm/com/zte/ums/zenap/okhttp",
        "//3rdparty/jvm/com/zte/zdh:zdh_commons",
        "//3rdparty/jvm/commons_beanutils",
        "//3rdparty/jvm/commons_codec",
        "//3rdparty/jvm/commons_collections",
        "//3rdparty/jvm/commons_configuration",
        "//3rdparty/jvm/commons_fileupload",
        "//3rdparty/jvm/commons_io",
        "//3rdparty/jvm/commons_lang",
        "//3rdparty/jvm/commons_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_configuration",
        "//3rdparty/jvm/io/dropwizard:dropwizard_core",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jackson",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jersey",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jetty",
        "//3rdparty/jvm/io/dropwizard:dropwizard_lifecycle",
        "//3rdparty/jvm/io/dropwizard:dropwizard_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_metrics",
        "//3rdparty/jvm/io/dropwizard:dropwizard_request_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_servlets",
        "//3rdparty/jvm/io/dropwizard:dropwizard_util",
        "//3rdparty/jvm/io/dropwizard:dropwizard_validation",
        "//3rdparty/jvm/io/dropwizard/logback:logback_throttling_appender",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_annotation",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_healthchecks",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jersey2",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jetty9",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jmx",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_json",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jvm",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_logback",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_servlets",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/io/github/openfeign:feign_core",
        "//3rdparty/jvm/io/github/openfeign:feign_hystrix",
        "//3rdparty/jvm/io/github/openfeign:feign_slf4j",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_circuitbreaker",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_circularbuffer",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_consumer",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_core",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_rxjava2",
        "//3rdparty/jvm/io/jsonwebtoken:jjwt",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_codec_dns",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/io/netty:netty_transport_native_unix_common",
        "//3rdparty/jvm/io/projectreactor:reactor_core",
        "//3rdparty/jvm/io/reactivex:rxjava",
        "//3rdparty/jvm/io/reactivex/rxjava2:rxjava",
        "//3rdparty/jvm/io/reactivex/rxjava3:rxjava",
        "//3rdparty/jvm/io/springfox:springfox_core",
        "//3rdparty/jvm/io/springfox:springfox_schema",
        "//3rdparty/jvm/io/springfox:springfox_spi",
        "//3rdparty/jvm/io/springfox:springfox_spring_web",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_common",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/io/swagger:swagger_annotations",
        "//3rdparty/jvm/io/swagger:swagger_models",
        "//3rdparty/jvm/io/vavr",
        "//3rdparty/jvm/io/vavr:vavr_match",
        "//3rdparty/jvm/io/zipkin/brave",
        "//3rdparty/jvm/io/zipkin/brave:brave_context_slf4j",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpasyncclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_jms",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_clients",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_streams",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_rpc",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_servlet",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_rabbit",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_web",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_webmvc",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_brave",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_metrics_micrometer",
        "//3rdparty/jvm/io/zipkin/zipkin2:zipkin",
        "//3rdparty/jvm/jakarta/activation:jakarta_activation_api",
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/persistence:jakarta_persistence_api",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/jakarta/transaction:jakarta_transaction_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//3rdparty/jvm/javax/cache:cache_api",
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//3rdparty/jvm/javax/servlet:servlet_api",
        "//3rdparty/jvm/joda_time",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy_agent",
        "//3rdparty/jvm/net/sourceforge/argparse4j",
        "//3rdparty/jvm/org/apache/commons:commons_collections4",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/apache/commons:commons_text",
        "//3rdparty/jvm/org/apache/httpcomponents:httpasyncclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore_nio",
        "//3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_api",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j",
        "//3rdparty/jvm/org/apache/sshd:sshd_common",
        "//3rdparty/jvm/org/apache/sshd:sshd_core",
        "//3rdparty/jvm/org/apache/sshd:sshd_scp",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_constants",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_css",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_i18n",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_util",
        "//3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons",
        "//3rdparty/jvm/org/apache_extras/beanshell:bsh",
        "//3rdparty/jvm/org/aspectj:aspectjrt",
        "//3rdparty/jvm/org/aspectj:aspectjweaver",
        "//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on",
        "//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on",
        "//3rdparty/jvm/org/checkerframework:checker_qual",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_continuation",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_http",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_io",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_security",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlet",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlets",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util_ajax",
        "//3rdparty/jvm/org/eclipse/jetty/toolchain/setuid:jetty_setuid_java",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_api",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_locator",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_utils",
        "//3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator",
        "//3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/jaxb:jaxb_runtime",
        "//3rdparty/jvm/org/glassfish/jaxb:txw2",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_client",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_server",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation",
        "//3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2",
        "//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_jaxb",
        "//3rdparty/jvm/org/hdrhistogram:HdrHistogram",
        "//3rdparty/jvm/org/hibernate:hibernate_core",
        "//3rdparty/jvm/org/hibernate/common:hibernate_commons_annotations",
        "//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/htmlunit:neko_htmlunit",
        "//3rdparty/jvm/org/immutables:value",
        "//3rdparty/jvm/org/javassist",
        "//3rdparty/jvm/org/jboss:jandex",
        "//3rdparty/jvm/org/jboss/logging:jboss_logging",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river",
        "//3rdparty/jvm/org/jdom:jdom2",
        "//3rdparty/jvm/org/jodd:jodd_bean",
        "//3rdparty/jvm/org/jodd:jodd_core",
        "//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2",
        "//3rdparty/jvm/org/lz4:lz4_java",
        "//3rdparty/jvm/org/mapstruct",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/objenesis",
        "//3rdparty/jvm/org/ow2/asm",
        "//3rdparty/jvm/org/owasp/antisamy",
        "//3rdparty/jvm/org/owasp/esapi",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/reactivestreams:reactive_streams",
        "//3rdparty/jvm/org/redisson",
        "//3rdparty/jvm/org/reflections",
        "//3rdparty/jvm/org/slf4j:jcl_over_slf4j",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//3rdparty/jvm/org/springframework:spring_aspects",
        "//3rdparty/jvm/org/springframework:spring_beans",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_context_support",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/springframework:spring_expression",
        "//3rdparty/jvm/org/springframework:spring_jcl",
        "//3rdparty/jvm/org/springframework:spring_jdbc",
        "//3rdparty/jvm/org/springframework:spring_messaging",
        "//3rdparty/jvm/org/springframework:spring_orm",
        "//3rdparty/jvm/org/springframework:spring_tx",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//3rdparty/jvm/org/springframework:spring_webmvc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_data_jpa",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jdbc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_context",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_sleuth_core",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_openfeign",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth",
        "//3rdparty/jvm/org/springframework/data:spring_data_commons",
        "//3rdparty/jvm/org/springframework/data:spring_data_jpa",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//3rdparty/jvm/org/springframework/retry:spring_retry",
        "//3rdparty/jvm/org/springframework/security:spring_security_crypto",
        "//3rdparty/jvm/org/springframework/security:spring_security_rsa",
        "//3rdparty/jvm/org/springframework/statemachine:spring_statemachine_core",
        "//3rdparty/jvm/org/xerial/snappy:snappy_java",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//3rdparty/jvm/org/zeroturnaround:zt_zip",
        "//3rdparty/jvm/xerces:xercesImpl",
        "//3rdparty/jvm/xml_apis",
        "//3rdparty/jvm/xml_apis:xml_apis_ext",
    ],
)

java_library(
    name = "daip-patcher-task-worker-test-classes",
    srcs = glob(
        include = ["src/test/java/**/*.java"],
        exclude = [],
    ),
    resources = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
        ":daip-patcher-task-worker",
        "//3rdparty/jvm/antlr",
        "//3rdparty/jvm/ch/qos/logback:logback_access",
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/alibaba:cooma",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/auth0:java_jwt",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_joda",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_afterburner",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names",
        "//3rdparty/jvm/com/github/ben_manes/caffeine",
        "//3rdparty/jvm/com/github/loki4j:loki_logback_appender_jdk8",
        "//3rdparty/jvm/com/github/luben:zstd_jni",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/errorprone:error_prone_annotations",
        "//3rdparty/jvm/com/google/guava",
        "//3rdparty/jvm/com/google/guava:failureaccess",
        "//3rdparty/jvm/com/google/guava:listenablefuture",
        "//3rdparty/jvm/com/google/j2objc:j2objc_annotations",
        "//3rdparty/jvm/com/helger:profiler",
        "//3rdparty/jvm/com/io7m/xom",
        "//3rdparty/jvm/com/jayway/jsonpath:json_path",
        "//3rdparty/jvm/com/lmax:disruptor",
        "//3rdparty/jvm/com/mikesamuel:json_sanitizer",
        "//3rdparty/jvm/com/netflix/archaius:archaius_core",
        "//3rdparty/jvm/com/netflix/hystrix:hystrix_core",
        "//3rdparty/jvm/com/networknt:json_schema_validator",
        "//3rdparty/jvm/com/squareup/okhttp3:okhttp",
        "//3rdparty/jvm/com/squareup/okio",
        "//3rdparty/jvm/com/squareup/retrofit2:retrofit",
        "//3rdparty/jvm/com/sun/activation:jakarta_activation",
        "//3rdparty/jvm/com/sun/istack:istack_commons_runtime",
        "//3rdparty/jvm/com/vaadin/external/google:android_json",
        "//3rdparty/jvm/com/zaxxer:HikariCP",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_cache_common",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_i18n",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_common",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_kafka",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_replyproducer",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_beans",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_client",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_common_util",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_httpclient",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_common",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_response",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_security",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_client",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_message",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_worker",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter",
        "//3rdparty/jvm/com/zte/daip/manager/miniagent:daip_miniagent_seed",
        "//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_domain",
        "//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_infrastructure_api",
        "//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api",
        "//3rdparty/jvm/com/zte/ums/zenap/dropwizard/ext:zenap_dropwizard_ext",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_circuitbreaker",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_istio",
        "//3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_bundle",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service",
        "//3rdparty/jvm/com/zte/ums/zenap/okhttp",
        "//3rdparty/jvm/com/zte/zdh:zdh_commons",
        "//3rdparty/jvm/commons_beanutils",
        "//3rdparty/jvm/commons_codec",
        "//3rdparty/jvm/commons_collections",
        "//3rdparty/jvm/commons_configuration",
        "//3rdparty/jvm/commons_fileupload",
        "//3rdparty/jvm/commons_io",
        "//3rdparty/jvm/commons_lang",
        "//3rdparty/jvm/commons_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_configuration",
        "//3rdparty/jvm/io/dropwizard:dropwizard_core",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jackson",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jersey",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jetty",
        "//3rdparty/jvm/io/dropwizard:dropwizard_lifecycle",
        "//3rdparty/jvm/io/dropwizard:dropwizard_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_metrics",
        "//3rdparty/jvm/io/dropwizard:dropwizard_request_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_servlets",
        "//3rdparty/jvm/io/dropwizard:dropwizard_util",
        "//3rdparty/jvm/io/dropwizard:dropwizard_validation",
        "//3rdparty/jvm/io/dropwizard/logback:logback_throttling_appender",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_annotation",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_healthchecks",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jersey2",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jetty9",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jmx",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_json",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jvm",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_logback",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_servlets",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/io/github/openfeign:feign_core",
        "//3rdparty/jvm/io/github/openfeign:feign_hystrix",
        "//3rdparty/jvm/io/github/openfeign:feign_slf4j",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_circuitbreaker",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_circularbuffer",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_consumer",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_core",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_rxjava2",
        "//3rdparty/jvm/io/jsonwebtoken:jjwt",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_codec_dns",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/io/netty:netty_transport_native_unix_common",
        "//3rdparty/jvm/io/projectreactor:reactor_core",
        "//3rdparty/jvm/io/reactivex:rxjava",
        "//3rdparty/jvm/io/reactivex/rxjava2:rxjava",
        "//3rdparty/jvm/io/reactivex/rxjava3:rxjava",
        "//3rdparty/jvm/io/springfox:springfox_core",
        "//3rdparty/jvm/io/springfox:springfox_schema",
        "//3rdparty/jvm/io/springfox:springfox_spi",
        "//3rdparty/jvm/io/springfox:springfox_spring_web",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_common",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/io/swagger:swagger_annotations",
        "//3rdparty/jvm/io/swagger:swagger_models",
        "//3rdparty/jvm/io/vavr",
        "//3rdparty/jvm/io/vavr:vavr_match",
        "//3rdparty/jvm/io/zipkin/brave",
        "//3rdparty/jvm/io/zipkin/brave:brave_context_slf4j",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpasyncclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_jms",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_clients",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_streams",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_rpc",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_servlet",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_rabbit",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_web",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_webmvc",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_brave",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_metrics_micrometer",
        "//3rdparty/jvm/io/zipkin/zipkin2:zipkin",
        "//3rdparty/jvm/jakarta/activation:jakarta_activation_api",
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/persistence:jakarta_persistence_api",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/jakarta/transaction:jakarta_transaction_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//3rdparty/jvm/javax/cache:cache_api",
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//3rdparty/jvm/javax/servlet:servlet_api",
        "//3rdparty/jvm/joda_time",
        "//3rdparty/jvm/junit",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy_agent",
        "//3rdparty/jvm/net/minidev:accessors_smart",
        "//3rdparty/jvm/net/minidev:json_smart",
        "//3rdparty/jvm/net/sourceforge/argparse4j",
        "//3rdparty/jvm/org/apache/commons:commons_collections4",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/apache/commons:commons_text",
        "//3rdparty/jvm/org/apache/httpcomponents:httpasyncclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore_nio",
        "//3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_api",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j",
        "//3rdparty/jvm/org/apache/sshd:sshd_common",
        "//3rdparty/jvm/org/apache/sshd:sshd_core",
        "//3rdparty/jvm/org/apache/sshd:sshd_scp",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_constants",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_css",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_i18n",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_util",
        "//3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons",
        "//3rdparty/jvm/org/apache_extras/beanshell:bsh",
        "//3rdparty/jvm/org/apiguardian:apiguardian_api",
        "//3rdparty/jvm/org/aspectj:aspectjrt",
        "//3rdparty/jvm/org/aspectj:aspectjweaver",
        "//3rdparty/jvm/org/assertj:assertj_core",
        "//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on",
        "//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on",
        "//3rdparty/jvm/org/checkerframework:checker_qual",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_continuation",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_http",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_io",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_security",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlet",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlets",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util_ajax",
        "//3rdparty/jvm/org/eclipse/jetty/toolchain/setuid:jetty_setuid_java",
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_api",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_locator",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_utils",
        "//3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator",
        "//3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/jaxb:jaxb_runtime",
        "//3rdparty/jvm/org/glassfish/jaxb:txw2",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_client",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_server",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_metainf_services",
        "//3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2",
        "//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_jaxb",
        "//3rdparty/jvm/org/hamcrest",
        "//3rdparty/jvm/org/hdrhistogram:HdrHistogram",
        "//3rdparty/jvm/org/hibernate:hibernate_core",
        "//3rdparty/jvm/org/hibernate/common:hibernate_commons_annotations",
        "//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/htmlunit:neko_htmlunit",
        "//3rdparty/jvm/org/immutables:value",
        "//3rdparty/jvm/org/javassist",
        "//3rdparty/jvm/org/jboss:jandex",
        "//3rdparty/jvm/org/jboss/logging:jboss_logging",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river",
        "//3rdparty/jvm/org/jdom:jdom2",
        "//3rdparty/jvm/org/jodd:jodd_bean",
        "//3rdparty/jvm/org/jodd:jodd_core",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_engine",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_params",
        "//3rdparty/jvm/org/junit/platform:junit_platform_commons",
        "//3rdparty/jvm/org/junit/platform:junit_platform_engine",
        "//3rdparty/jvm/org/junit/vintage:junit_vintage_engine",
        "//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2",
        "//3rdparty/jvm/org/lz4:lz4_java",
        "//3rdparty/jvm/org/mapstruct",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/mockito:mockito_junit_jupiter",
        "//3rdparty/jvm/org/objenesis",
        "//3rdparty/jvm/org/opentest4j",
        "//3rdparty/jvm/org/ow2/asm",
        "//3rdparty/jvm/org/owasp/antisamy",
        "//3rdparty/jvm/org/owasp/esapi",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/reactivestreams:reactive_streams",
        "//3rdparty/jvm/org/redisson",
        "//3rdparty/jvm/org/reflections",
        "//3rdparty/jvm/org/skyscreamer:jsonassert",
        "//3rdparty/jvm/org/slf4j:jcl_over_slf4j",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//3rdparty/jvm/org/slf4j:log4j_over_slf4j",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//3rdparty/jvm/org/springframework:spring_aspects",
        "//3rdparty/jvm/org/springframework:spring_beans",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_context_support",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/springframework:spring_expression",
        "//3rdparty/jvm/org/springframework:spring_jcl",
        "//3rdparty/jvm/org/springframework:spring_jdbc",
        "//3rdparty/jvm/org/springframework:spring_messaging",
        "//3rdparty/jvm/org/springframework:spring_orm",
        "//3rdparty/jvm/org/springframework:spring_test",
        "//3rdparty/jvm/org/springframework:spring_tx",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//3rdparty/jvm/org/springframework:spring_webmvc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_data_jpa",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jdbc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_test",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_test",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_test_autoconfigure",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_context",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_sleuth_core",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_openfeign",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth",
        "//3rdparty/jvm/org/springframework/data:spring_data_commons",
        "//3rdparty/jvm/org/springframework/data:spring_data_jpa",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//3rdparty/jvm/org/springframework/retry:spring_retry",
        "//3rdparty/jvm/org/springframework/security:spring_security_crypto",
        "//3rdparty/jvm/org/springframework/security:spring_security_rsa",
        "//3rdparty/jvm/org/springframework/statemachine:spring_statemachine_core",
        "//3rdparty/jvm/org/xerial/snappy:snappy_java",
        "//3rdparty/jvm/org/xmlunit:xmlunit_core",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//3rdparty/jvm/org/zeroturnaround:zt_zip",
        "//3rdparty/jvm/xerces:xercesImpl",
        "//3rdparty/jvm/xml_apis",
        "//3rdparty/jvm/xml_apis:xml_apis_ext",
    ],
)

run_tests(
    name = "AllTests",
    size = "small",
    srcs = glob(
        include = [
            "src/test/java/**/Test*.java",
            "src/test/java/**/*Test.java",
        ],
        exclude = [],
    ),
    data = glob(
        include = ["src/test/resources/**/*"],
        exclude = [],
    ),
    deps = [
        ":daip-patcher-task-worker",
        ":daip-patcher-task-worker-test-classes",
        "//3rdparty/jvm/antlr",
        "//3rdparty/jvm/ch/qos/logback:logback_access",
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/alibaba:cooma",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/auth0:java_jwt",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_joda",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_afterburner",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names",
        "//3rdparty/jvm/com/github/ben_manes/caffeine",
        "//3rdparty/jvm/com/github/loki4j:loki_logback_appender_jdk8",
        "//3rdparty/jvm/com/github/luben:zstd_jni",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/errorprone:error_prone_annotations",
        "//3rdparty/jvm/com/google/guava",
        "//3rdparty/jvm/com/google/guava:failureaccess",
        "//3rdparty/jvm/com/google/guava:listenablefuture",
        "//3rdparty/jvm/com/google/j2objc:j2objc_annotations",
        "//3rdparty/jvm/com/helger:profiler",
        "//3rdparty/jvm/com/io7m/xom",
        "//3rdparty/jvm/com/jayway/jsonpath:json_path",
        "//3rdparty/jvm/com/lmax:disruptor",
        "//3rdparty/jvm/com/mikesamuel:json_sanitizer",
        "//3rdparty/jvm/com/netflix/archaius:archaius_core",
        "//3rdparty/jvm/com/netflix/hystrix:hystrix_core",
        "//3rdparty/jvm/com/networknt:json_schema_validator",
        "//3rdparty/jvm/com/squareup/okhttp3:okhttp",
        "//3rdparty/jvm/com/squareup/okio",
        "//3rdparty/jvm/com/squareup/retrofit2:retrofit",
        "//3rdparty/jvm/com/sun/activation:jakarta_activation",
        "//3rdparty/jvm/com/sun/istack:istack_commons_runtime",
        "//3rdparty/jvm/com/vaadin/external/google:android_json",
        "//3rdparty/jvm/com/zaxxer:HikariCP",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_cache_common",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_i18n",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_common",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_kafka",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_replyproducer",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_beans",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_client",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_common_util",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_httpclient",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_common",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_response",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_security",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_client",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_message",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_worker",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter",
        "//3rdparty/jvm/com/zte/daip/manager/miniagent:daip_miniagent_seed",
        "//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_domain",
        "//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_infrastructure_api",
        "//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api",
        "//3rdparty/jvm/com/zte/ums/zenap/dropwizard/ext:zenap_dropwizard_ext",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_circuitbreaker",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_istio",
        "//3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_bundle",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service",
        "//3rdparty/jvm/com/zte/ums/zenap/okhttp",
        "//3rdparty/jvm/com/zte/zdh:zdh_commons",
        "//3rdparty/jvm/commons_beanutils",
        "//3rdparty/jvm/commons_codec",
        "//3rdparty/jvm/commons_collections",
        "//3rdparty/jvm/commons_configuration",
        "//3rdparty/jvm/commons_fileupload",
        "//3rdparty/jvm/commons_io",
        "//3rdparty/jvm/commons_lang",
        "//3rdparty/jvm/commons_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_configuration",
        "//3rdparty/jvm/io/dropwizard:dropwizard_core",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jackson",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jersey",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jetty",
        "//3rdparty/jvm/io/dropwizard:dropwizard_lifecycle",
        "//3rdparty/jvm/io/dropwizard:dropwizard_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_metrics",
        "//3rdparty/jvm/io/dropwizard:dropwizard_request_logging",
        "//3rdparty/jvm/io/dropwizard:dropwizard_servlets",
        "//3rdparty/jvm/io/dropwizard:dropwizard_util",
        "//3rdparty/jvm/io/dropwizard:dropwizard_validation",
        "//3rdparty/jvm/io/dropwizard/logback:logback_throttling_appender",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_annotation",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_healthchecks",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jersey2",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jetty9",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jmx",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_json",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jvm",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_logback",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_servlets",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/io/github/openfeign:feign_core",
        "//3rdparty/jvm/io/github/openfeign:feign_hystrix",
        "//3rdparty/jvm/io/github/openfeign:feign_slf4j",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_circuitbreaker",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_circularbuffer",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_consumer",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_core",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_rxjava2",
        "//3rdparty/jvm/io/jsonwebtoken:jjwt",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_codec_dns",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/io/netty:netty_transport_native_unix_common",
        "//3rdparty/jvm/io/projectreactor:reactor_core",
        "//3rdparty/jvm/io/reactivex:rxjava",
        "//3rdparty/jvm/io/reactivex/rxjava2:rxjava",
        "//3rdparty/jvm/io/reactivex/rxjava3:rxjava",
        "//3rdparty/jvm/io/springfox:springfox_core",
        "//3rdparty/jvm/io/springfox:springfox_schema",
        "//3rdparty/jvm/io/springfox:springfox_spi",
        "//3rdparty/jvm/io/springfox:springfox_spring_web",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_common",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/io/swagger:swagger_annotations",
        "//3rdparty/jvm/io/swagger:swagger_models",
        "//3rdparty/jvm/io/vavr",
        "//3rdparty/jvm/io/vavr:vavr_match",
        "//3rdparty/jvm/io/zipkin/brave",
        "//3rdparty/jvm/io/zipkin/brave:brave_context_slf4j",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpasyncclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_jms",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_clients",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_streams",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_rpc",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_servlet",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_rabbit",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_web",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_webmvc",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_brave",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_metrics_micrometer",
        "//3rdparty/jvm/io/zipkin/zipkin2:zipkin",
        "//3rdparty/jvm/jakarta/activation:jakarta_activation_api",
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/persistence:jakarta_persistence_api",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/jakarta/transaction:jakarta_transaction_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//3rdparty/jvm/javax/cache:cache_api",
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//3rdparty/jvm/javax/servlet:servlet_api",
        "//3rdparty/jvm/joda_time",
        "//3rdparty/jvm/junit",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy_agent",
        "//3rdparty/jvm/net/minidev:accessors_smart",
        "//3rdparty/jvm/net/minidev:json_smart",
        "//3rdparty/jvm/net/sourceforge/argparse4j",
        "//3rdparty/jvm/org/apache/commons:commons_collections4",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/apache/commons:commons_text",
        "//3rdparty/jvm/org/apache/httpcomponents:httpasyncclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore_nio",
        "//3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5",
        "//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_api",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j",
        "//3rdparty/jvm/org/apache/sshd:sshd_common",
        "//3rdparty/jvm/org/apache/sshd:sshd_core",
        "//3rdparty/jvm/org/apache/sshd:sshd_scp",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_constants",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_css",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_i18n",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_util",
        "//3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons",
        "//3rdparty/jvm/org/apache_extras/beanshell:bsh",
        "//3rdparty/jvm/org/apiguardian:apiguardian_api",
        "//3rdparty/jvm/org/aspectj:aspectjrt",
        "//3rdparty/jvm/org/aspectj:aspectjweaver",
        "//3rdparty/jvm/org/assertj:assertj_core",
        "//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on",
        "//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on",
        "//3rdparty/jvm/org/checkerframework:checker_qual",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_continuation",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_http",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_io",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_security",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlet",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlets",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util_ajax",
        "//3rdparty/jvm/org/eclipse/jetty/toolchain/setuid:jetty_setuid_java",
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_api",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_locator",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_utils",
        "//3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator",
        "//3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/jaxb:jaxb_runtime",
        "//3rdparty/jvm/org/glassfish/jaxb:txw2",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_client",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_server",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_metainf_services",
        "//3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2",
        "//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_jaxb",
        "//3rdparty/jvm/org/hamcrest",
        "//3rdparty/jvm/org/hdrhistogram:HdrHistogram",
        "//3rdparty/jvm/org/hibernate:hibernate_core",
        "//3rdparty/jvm/org/hibernate/common:hibernate_commons_annotations",
        "//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/htmlunit:neko_htmlunit",
        "//3rdparty/jvm/org/immutables:value",
        "//3rdparty/jvm/org/javassist",
        "//3rdparty/jvm/org/jboss:jandex",
        "//3rdparty/jvm/org/jboss/logging:jboss_logging",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river",
        "//3rdparty/jvm/org/jdom:jdom2",
        "//3rdparty/jvm/org/jodd:jodd_bean",
        "//3rdparty/jvm/org/jodd:jodd_core",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_engine",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_params",
        "//3rdparty/jvm/org/junit/platform:junit_platform_commons",
        "//3rdparty/jvm/org/junit/platform:junit_platform_engine",
        "//3rdparty/jvm/org/junit/vintage:junit_vintage_engine",
        "//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2",
        "//3rdparty/jvm/org/lz4:lz4_java",
        "//3rdparty/jvm/org/mapstruct",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/mockito:mockito_junit_jupiter",
        "//3rdparty/jvm/org/objenesis",
        "//3rdparty/jvm/org/opentest4j",
        "//3rdparty/jvm/org/ow2/asm",
        "//3rdparty/jvm/org/owasp/antisamy",
        "//3rdparty/jvm/org/owasp/esapi",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/reactivestreams:reactive_streams",
        "//3rdparty/jvm/org/redisson",
        "//3rdparty/jvm/org/reflections",
        "//3rdparty/jvm/org/skyscreamer:jsonassert",
        "//3rdparty/jvm/org/slf4j:jcl_over_slf4j",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//3rdparty/jvm/org/slf4j:log4j_over_slf4j",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//3rdparty/jvm/org/springframework:spring_aspects",
        "//3rdparty/jvm/org/springframework:spring_beans",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_context_support",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/springframework:spring_expression",
        "//3rdparty/jvm/org/springframework:spring_jcl",
        "//3rdparty/jvm/org/springframework:spring_jdbc",
        "//3rdparty/jvm/org/springframework:spring_messaging",
        "//3rdparty/jvm/org/springframework:spring_orm",
        "//3rdparty/jvm/org/springframework:spring_test",
        "//3rdparty/jvm/org/springframework:spring_tx",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//3rdparty/jvm/org/springframework:spring_webmvc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_data_jpa",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jdbc",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_test",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_test",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_test_autoconfigure",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_context",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_sleuth_core",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_openfeign",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth",
        "//3rdparty/jvm/org/springframework/data:spring_data_commons",
        "//3rdparty/jvm/org/springframework/data:spring_data_jpa",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//3rdparty/jvm/org/springframework/retry:spring_retry",
        "//3rdparty/jvm/org/springframework/security:spring_security_crypto",
        "//3rdparty/jvm/org/springframework/security:spring_security_rsa",
        "//3rdparty/jvm/org/springframework/statemachine:spring_statemachine_core",
        "//3rdparty/jvm/org/xerial/snappy:snappy_java",
        "//3rdparty/jvm/org/xmlunit:xmlunit_core",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//3rdparty/jvm/org/zeroturnaround:zt_zip",
        "//3rdparty/jvm/xerces:xercesImpl",
        "//3rdparty/jvm/xml_apis",
        "//3rdparty/jvm/xml_apis:xml_apis_ext",
    ],
)
