package com.zte.daip.manager.patcher.task.worker.update;
/* Started by AICoder, pid:x42b5b1a6eje9bc14c25088ca24b8875d222f638 */
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.common.deployer.model.controller.api.ClusterVersionControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.common.task.common.exception.TaskManagementException;
import com.zte.daip.manager.common.task.common.task.request.ProcessorParam;
import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.common.task.common.worker.ServiceInfo;
import com.zte.daip.manager.common.task.common.worker.WfContext;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.task.worker.processor.WorkflowContext;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.PatcherUpdateControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateResult;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import com.zte.daip.manager.patcher.inner.api.PatchTaskControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatcherUpdateProcessorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/6/30</p>
 * <p>完成日期：2023/6/30</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatcherUpdateProcessorTest {
    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Mock
    private ClusterVersionControllerApi clusterVersionControllerApi;

    @Mock
    private SpectControllerApi spectControllerApi;

    @Mock
    private PatcherUpdateControllerApi patcherUpdateControllerApi;

    @Mock
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @Mock
    private PatchTaskControllerApi patchTaskControllerApi;
    @Mock
    private PatchTaskOperateService patchTaskOperateService;

    @Mock
    private PatchTaskService patchTaskService;

    @InjectMocks
    private PatcherUpdateProcessor patcherUpdateProcessor;

    private TaskContext context = new TaskContext();

    private TaskContext context4EmptyHosts = new TaskContext();

    private ServiceModel serviceModel = new ServiceModel();

    private List<ClusterProject> clusterProjects = Lists.newArrayList();

    private List<ServiceRoleInfo> serviceRoleInfos = Lists.newArrayList();

    private ProductSpect productSpect = new ProductSpect();

    private PatchUpdateCacheDto successPatchUpdateCacheDto = new PatchUpdateCacheDto();

    private PatchUpdateCacheDto failedPatchUpdateCacheDto = new PatchUpdateCacheDto();

    private static String clusterId = "1";

    private static String version = "V20.19.40.R4.B2";

    private static String serviceId = "dap.manager.hdfs";

    private static String serviceName = "hdfs";

    @Before
    public void setUp() throws DaipBaseException {
        String hostIp1 = "***********";
        long taskId = 1L;
        String serviceInstanceId = "hdfs";

        // serviceInfo
        List<ServiceInfo> services = Lists.newArrayList();
        ServiceInfo serviceInfo = new ServiceInfo();
        serviceInfo.setServiceId(serviceId);
        services.add(serviceInfo);

        // context
        List<ProcessorParam> processorParams = Lists.newArrayList();
        ProcessorParam processorParam =
                new ProcessorParam(clusterId, hostIp1, serviceId, serviceInstanceId, Sets.newHashSet());
        processorParams.add(processorParam);
        WfContext wfContext2 = new WfContext();
        wfContext2.setClusterId(NumberUtils.toInt(clusterId));
        wfContext2.setHostIp(Lists.newArrayList(hostIp1));
        wfContext2.setService(services);
        WorkflowContext workflowContext2 = new WorkflowContext(1L, wfContext2);
        context.setAtomicTaskParams(processorParams);
        context.setWorkflowContext(workflowContext2);
        context.setTimeout(10);
        context.setParentId(taskId);

        WfContext wfContext1 = new WfContext();
        wfContext1.setClusterId(NumberUtils.toInt(clusterId));
        wfContext1.setHostIp(Lists.newArrayList());
        wfContext1.setService(services);
        WorkflowContext workflowContext1 = new WorkflowContext(1L, wfContext1);
        context4EmptyHosts.setTaskId(1L);
        context4EmptyHosts.setAtomTaskId(2L);
        context4EmptyHosts.setAtomicTaskParams(processorParams);
        context4EmptyHosts.setWorkflowContext(workflowContext1);
        context4EmptyHosts.setParentId(taskId);

        // serviceModel
        serviceModel.setServiceName(serviceName);


        // clusterProjects
        ClusterProject clusterProject = new ClusterProject();
        clusterProject.setVersion(version);
        clusterProjects.add(clusterProject);

        // productSpect
        productSpect.setProjectName("zdh");
        productSpect.setServiceName(serviceName);
        productSpect.setVersionNo(version);
        productSpect.setDependServiceName(String.format("zdh@common@%s|zdh@hadoop@%s", version, version));

        // patchUpdateCacheDto
        successPatchUpdateCacheDto.setAllFinished(true);
        successPatchUpdateCacheDto.setSuccess(true);

        failedPatchUpdateCacheDto.setAllFinished(true);
        failedPatchUpdateCacheDto.setSuccess(false);
        PatchUpdateResult patchUpdateResult = new PatchUpdateResult();
        patchUpdateResult.setPatchName("patch1");
        failedPatchUpdateCacheDto.setPatchResults(Lists.newArrayList(patchUpdateResult));

        // serviceRoleInfos
        ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setClusterId(clusterId);
        serviceRoleInfo.setServiceId(serviceId);
        serviceRoleInfo.setIpAddress(hostIp1);
        serviceRoleInfos.add(serviceRoleInfo);

        PatchTaskDto patchTaskDto = new PatchTaskDto();
        patchTaskDto.setTaskId(taskId);
        patchTaskDto.setClusterId(clusterId);
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceId(serviceId);
        serviceInstance.setServiceInstanceId(serviceInstanceId);
        serviceInstance.setServiceName(serviceInstanceId);
        serviceInstance.setServiceInstanceName(serviceInstanceId);
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);
        List<ServiceInstancePatchInfo> serviceInstancePatchInfos = Lists.newArrayList(serviceInstancePatchInfo);
        patchTaskDto.setContext(serviceInstancePatchInfos);
        when(patchTaskControllerApi.queryPatchTaskByTaskId(taskId)).thenReturn(patchTaskDto);
        doNothing().when(patchTaskOperateService).updateRollbackInfo(patchTaskDto);
        when(patchTaskService.queryByTaskId(taskId)).thenReturn(new PatchTaskPo());
        when(patchTaskOperateService.queryNeedRestartService(patchTaskDto)).thenReturn(new ArrayList<>());
        doNothing().when(patchTaskService).updatePatchTask(any());
    }

    @Test
    public void test_process(){
        /* Started by AICoder, pid:c7ab38844cub495146d0097390f238009d959e41 */
// 设置服务ID
        serviceModel.setServiceId(serviceId);

// 设置补丁类型为"role"
        serviceModel.setPatchType("role");
        /* Ended by AICoder, pid:c7ab38844cub495146d0097390f238009d959e41 */
        when(serviceResourceControllerApi.queryByClusterId(clusterId)).thenReturn(serviceRoleInfos);

        ProcessResult process = patcherUpdateProcessor.process(context);
        assertFalse(process.isSuccess());

        when(productModelInfoControllerApi.queryByClusterIdAndServiceId(clusterId, serviceId)).thenReturn(serviceModel);
        /* Started by AICoder, pid:g5c52b7ffbd95a5143cc09eae0b31908b1a3ab80 */
// 当调用productModelInfoControllerApi的queryByClusterId方法并传入clusterId参数时，
// 返回一个包含serviceModel的服务模型列表。
        when(productModelInfoControllerApi.queryByClusterId(clusterId)).thenReturn(Lists.newArrayList(serviceModel));

        /* Ended by AICoder, pid:g5c52b7ffbd95a5143cc09eae0b31908b1a3ab80 */
        when(clusterVersionControllerApi.queryVersions(NumberUtils.toInt(clusterId), serviceName))
                .thenReturn(clusterProjects);
        when(spectControllerApi.queryByServiceNameAndVersionNo(serviceName, version)).thenReturn(null);

        process = patcherUpdateProcessor.process(context);
        assertFalse(process.isSuccess());

        when(spectControllerApi.queryByServiceNameAndVersionNo(serviceName, version)).thenReturn(productSpect);
        process = patcherUpdateProcessor.process(context);
        assertTrue(process.isSuccess());

        when(patcherUpdateControllerApi.queryUpdatePatchesProcess(clusterId)).thenReturn(successPatchUpdateCacheDto);
        process = patcherUpdateProcessor.process(context);
        assertTrue(process.isSuccess());

        process = patcherUpdateProcessor.process(context4EmptyHosts);
        assertFalse(process.isSuccess());

        when(patcherUpdateControllerApi.queryUpdatePatchesProcess(clusterId)).thenReturn(failedPatchUpdateCacheDto);
        process = patcherUpdateProcessor.process(context);
        assertFalse(process.isSuccess());
    }
    /* Started by AICoder, pid:p49bbb7f37u75cb14c800976400310560b94ef11 */
    @Test
    public void test_process1(){
        // 模拟查询集群ID为clusterId的服务资源，并返回serviceRoleInfos
        when(serviceResourceControllerApi.queryByClusterId(clusterId)).thenReturn(serviceRoleInfos);

        // 执行更新处理并断言结果不是成功的
        ProcessResult process = patcherUpdateProcessor.process(context);
        assertFalse(process.isSuccess());

        // 模拟根据集群ID和服务中心ID查询服务模型，并返回serviceModel
        when(productModelInfoControllerApi.queryByClusterIdAndServiceId(clusterId, serviceId)).thenReturn(serviceModel);

        /* Started by AICoder, pid:g5c52b7ffbd95a5143cc09eae0b31908b1a3ab80 */
        // 当调用productModelInfoControllerApi的queryByClusterId方法并传入clusterId参数时，
        // 返回一个包含serviceModel的服务模型列表。
        when(productModelInfoControllerApi.queryByClusterId(clusterId)).thenReturn(Lists.newArrayList(serviceModel));
        /* Ended by AICoder, pid:g5c52b7ffbd95a5143cc09eae0b31908b1a3ab80 */

        // 模拟查询集群版本，返回clusterProjects
        when(clusterVersionControllerApi.queryVersions(NumberUtils.toInt(clusterId), serviceName))
                .thenReturn(clusterProjects);

        // 模拟查询产品规格，返回null
        when(spectControllerApi.queryByServiceNameAndVersionNo(serviceName, version)).thenReturn(null);

        // 再次执行更新处理并断言结果不是成功的
        process = patcherUpdateProcessor.process(context);
        assertFalse(process.isSuccess());

        // 模拟查询产品规格，返回productSpect
        when(spectControllerApi.queryByServiceNameAndVersionNo(serviceName, version)).thenReturn(productSpect);

        // 再次执行更新处理并断言结果是成功的
        process = patcherUpdateProcessor.process(context);
        assertTrue(process.isSuccess());

        // 模拟查询更新补丁过程，返回successPatchUpdateCacheDto
        when(patcherUpdateControllerApi.queryUpdatePatchesProcess(clusterId)).thenReturn(successPatchUpdateCacheDto);

        // 再次执行更新处理并断言结果是成功的
        process = patcherUpdateProcessor.process(context);
        assertTrue(process.isSuccess());

        // 使用空主机列表执行更新处理并断言结果不是成功的
        process = patcherUpdateProcessor.process(context4EmptyHosts);
        assertFalse(process.isSuccess());

        // 模拟查询更新补丁过程，返回failedPatchUpdateCacheDto
        when(patcherUpdateControllerApi.queryUpdatePatchesProcess(clusterId)).thenReturn(failedPatchUpdateCacheDto);

        // 再次执行更新处理并断言结果不是成功的
        process = patcherUpdateProcessor.process(context);
        assertFalse(process.isSuccess());
    }

    /* Ended by AICoder, pid:p49bbb7f37u75cb14c800976400310560b94ef11 */

    @Test
    public void test_process_empty() {
        TaskContext emptyContext = new TaskContext();
        List<ProcessorParam> processorParams = Lists.newArrayList();
        emptyContext.setAtomicTaskParams(processorParams);
        Assert.assertThrows(TaskManagementException.class, () -> patcherUpdateProcessor.process(emptyContext));

        processorParams = Lists.newArrayList(new ProcessorParam());
        emptyContext.setAtomicTaskParams(processorParams);
        emptyContext.setWorkflowContext(null);
        Assert.assertThrows(TaskManagementException.class, () -> patcherUpdateProcessor.process(emptyContext));

        WorkflowContext workflowContext = new WorkflowContext(1L, null);
        emptyContext.setAtomicTaskParams(processorParams);
        emptyContext.setWorkflowContext(workflowContext);
        Assert.assertThrows(TaskManagementException.class, () -> patcherUpdateProcessor.process(emptyContext));
    }

    @Test
    public void test_rollback() {
        ProcessResult result = patcherUpdateProcessor.rollback(new TaskContext());
        boolean flag = result == null;
        assertTrue(flag);
    }
}

/* Ended by AICoder, pid:x42b5b1a6eje9bc14c25088ca24b8875d222f638 */