package com.zte.daip.manager.patcher.task.worker.cluster;
/* Started by AICoder, pid:i2bb9952a2l6e6c14447091d30f4a16d66b25d7f */
import static org.mockito.Mockito.when;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.StartAndStopClusterControllerApi;
import com.zte.daip.manager.common.deployer.bean.cluster.operation.ClusterOperationInfoBean;
import com.zte.daip.manager.common.deployer.bean.exception.DaipDeployerException;
import com.zte.daip.manager.patcher.task.worker.bean.ClusterOperateResult;

/**
 * 功能描述:<br>
 * <p/>
 * <p/>
 * <p/>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/29
 */
@RunWith(SpringRunner.class)
public class ClusterOperationListenerTest {

    @InjectMocks
    ClusterOperationListener startOrStopClusterListener; // 被测试的监听器类

    @Mock
    StartAndStopClusterControllerApi startAndStopClusterControllerApi; // 模拟的API接口

    @Test
    public void doListen() throws DaipDeployerException {
        final ClusterOperateResult build = ClusterOperateResult.builder().operate("0").clusterName("name")
                .clusterId("id").serviceInstanceIds(Lists.newArrayList()).build();
        when(startAndStopClusterControllerApi.queryClusterOperatorProcess("id"))
                .thenReturn(new ClusterOperationInfoBean());
        startOrStopClusterListener.doListen(build);
    }

    @Test
    public void doListen1() throws DaipDeployerException {
        final ClusterOperateResult build = ClusterOperateResult.builder().operate("0").eventId("1").clusterName("name")
                .clusterId("id").serviceInstanceIds(Lists.newArrayList()).build();
        when(startAndStopClusterControllerApi.queryClusterOperatorProcess("id"))
                .thenReturn(new ClusterOperationInfoBean());
        startOrStopClusterListener.doListen(build);
    }

    @Test
    public void doListen2() throws DaipDeployerException {
        final ClusterOperateResult build = ClusterOperateResult.builder().operate("0").eventId("1").clusterName("name")
                .clusterId("id").serviceInstanceIds(Lists.newArrayList()).build();
        final ClusterOperationInfoBean value = new ClusterOperationInfoBean();
        value.setAllServiceStatusRight(true);
        when(startAndStopClusterControllerApi.queryClusterOperatorProcess("id")).thenReturn(value);
        startOrStopClusterListener.doListen(build);
    }
}

/* Ended by AICoder, pid:i2bb9952a2l6e6c14447091d30f4a16d66b25d7f */