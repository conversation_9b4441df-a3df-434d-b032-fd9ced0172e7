package com.zte.daip.manager.patcher.task.worker.bean;
/* Started by AICoder, pid:0f567q3786maa19144c908ad20762c466915c250 */
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertSame;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;

public class ClusterOperateResultTest {
    @Test
    public void testConstructor() {
        // 测试构造函数
        ClusterOperateResult actualClusterOperateResult = new ClusterOperateResult();
        actualClusterOperateResult.setClusterId("42");
        actualClusterOperateResult.setClusterName("Cluster Name");
        actualClusterOperateResult.setEventId("42");
        actualClusterOperateResult.setOperate("Operate");
        ArrayList<String> stringList = new ArrayList<>();
        actualClusterOperateResult.setServiceInstanceIds(stringList);
        assertEquals("42", actualClusterOperateResult.getClusterId());
        assertEquals("Cluster Name", actualClusterOperateResult.getClusterName());
        assertEquals("42", actualClusterOperateResult.getEventId());
        assertEquals("Operate", actualClusterOperateResult.getOperate());
        assertSame(stringList, actualClusterOperateResult.getServiceInstanceIds());
    }

    @Test
    public void testConstructor2() {
        // 测试带参数的构造函数
        ArrayList<String> stringList = new ArrayList<>();
        ClusterOperateResult actualClusterOperateResult = new ClusterOperateResult("42", "Cluster Name", "42",
                stringList, "Operate");
        actualClusterOperateResult.setClusterId("42");
        actualClusterOperateResult.setClusterName("Cluster Name");
        actualClusterOperateResult.setEventId("42");
        actualClusterOperateResult.setOperate("Operate");
        actualClusterOperateResult.setServiceInstanceIds(stringList);
        assertEquals("42", actualClusterOperateResult.getClusterId());
        assertEquals("Cluster Name", actualClusterOperateResult.getClusterName());
        assertEquals("42", actualClusterOperateResult.getEventId());
        assertEquals("Operate", actualClusterOperateResult.getOperate());
        assertSame(stringList, actualClusterOperateResult.getServiceInstanceIds());
    }

}

/* Ended by AICoder, pid:0f567q3786maa19144c908ad20762c466915c250 */