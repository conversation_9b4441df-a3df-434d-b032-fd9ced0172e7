package com.zte.daip.manager.patcher.task.worker.bean;

import org.junit.Assert;
import org.junit.Test;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchDistributeInfoTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2024/1/19</p>
 * <p>完成日期：2024/1/19</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class PatchDistributeInfoTest
{
    @Test
    public void setDistributeId()
    {
        PatchDistributeInfo patchDistributeInfo = new PatchDistributeInfo();
        patchDistributeInfo.setDistributeId("1");
        assertThat("1", is(patchDistributeInfo.getDistributeId()));
    }

    @Test
    public void setFileSize()
    {
        PatchDistributeInfo patchDistributeInfo = new PatchDistributeInfo("1", 1);
        patchDistributeInfo.setFileSize(1);
        assertThat(1, is(patchDistributeInfo.getFileSize()));
    }

    @Test
    public void testEqualsAndHashCode()
    {
        PatchDistributeInfo patchDistributeInfo1 = new PatchDistributeInfo();
        patchDistributeInfo1.setDistributeId("1");
        patchDistributeInfo1.setFileSize(1);
        PatchDistributeInfo patchDistributeInfo2 = new PatchDistributeInfo();
        patchDistributeInfo2.setDistributeId("1");
        patchDistributeInfo2.setFileSize(1);
        Assert.assertEquals(patchDistributeInfo1.hashCode(), patchDistributeInfo2.hashCode());
        Assert.assertEquals(patchDistributeInfo1, patchDistributeInfo2);
    }
}