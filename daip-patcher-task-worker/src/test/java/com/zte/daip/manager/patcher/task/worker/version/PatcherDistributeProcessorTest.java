package com.zte.daip.manager.patcher.task.worker.version;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.CommandResult;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.common.deployer.model.controller.api.ClusterVersionControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.common.task.common.exception.TaskManagementException;
import com.zte.daip.manager.common.task.common.task.request.ProcessorParam;
import com.zte.daip.manager.common.task.common.worker.ServiceInfo;
import com.zte.daip.manager.common.task.common.worker.WfContext;
import com.zte.daip.manager.common.task.worker.processor.IProcessorListener;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.task.worker.processor.WorkflowContext;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;
import com.zte.daip.manager.filemanagement.api.bean.DistributeFileBean;
import com.zte.daip.manager.filemanagement.api.progress.DistributeSummaryProgress;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.dispatch.DistributeFileService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.task.worker.bean.PatchDistributeInfo;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatcherDistributeProcessorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/5/31</p>
 * <p>完成日期：2023/5/31</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatcherDistributeProcessorTest {
    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Mock
    private ClusterVersionControllerApi clusterVersionControllerApi;

    @Mock
    private SpectControllerApi spectControllerApi;

    @Mock
    private DistributeFileService distributeFileService;

    @Mock
    private FileDistributeControllerApi fileDistributeControllerApi;

    @Mock
    private PatcherDistributeListener multiPatcherDistributeListener;

    @Mock
    private PatchInfoService patchInfoService;

    @Mock
    private DaipEventReporter daipEventReporter;

    @Mock
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @InjectMocks
    private PatcherDistributeProcessor patcherDistributeProcessor;

    private TaskContext context = new TaskContext();

    private TaskContext context4EmptyHosts = new TaskContext();

    private ServiceModel serviceModel = new ServiceModel();

    private List<ClusterProject> clusterProjects = Lists.newArrayList();

    private ProductSpect productSpect = new ProductSpect();

    private CommandResult commandResult = new CommandResult();

    private List<ServiceRoleInfo> serviceRoleInfos = Lists.newArrayList();

    private List<PatchDetailPo> patchDetailPos1 = Lists.newArrayList();

    private List<PatchDetailPo> patchDetailPos2 = Lists.newArrayList();

    private static String clusterId = "1";

    private static String version = "V20.19.40.R4.B2";

    private static String serviceId = "dap.manager.hdfs";

    private static String serviceName = "hdfs";

    private static String hostIp1 = "***********";

    private static String hostIp2 = "***********";

    @Before
    public void setUp() {
        String serviceInstanceId = "hdfs";
        String dnRoleId = "dap.manager.hdfs.DateNode";
        String nnRoleId = "dap.manager.hdfs.NameNode";

        // serviceInfo
        List<ServiceInfo> services = Lists.newArrayList();
        ServiceInfo serviceInfo = new ServiceInfo();
        serviceInfo.setServiceId(serviceId);
        services.add(serviceInfo);

        // context
        List<ProcessorParam> processorParams = Lists.newArrayList();
        ProcessorParam processorParam =
            new ProcessorParam(clusterId, hostIp1, serviceId, serviceInstanceId, Sets.newHashSet(dnRoleId, nnRoleId));
        processorParams.add(processorParam);
        context4EmptyHosts.setTaskId(1L);
        context4EmptyHosts.setAtomTaskId(2L);
        context4EmptyHosts.setAtomicTaskParams(processorParams);
        WfContext wfContext1 = new WfContext();
        wfContext1.setClusterId(NumberUtils.toInt(clusterId));
        wfContext1.setHostIp(Lists.newArrayList());
        wfContext1.setService(services);
        WorkflowContext workflowContext1 = new WorkflowContext(1L, wfContext1);
        context4EmptyHosts.setWorkflowContext(workflowContext1);

        context.setAtomicTaskParams(processorParams);
        WfContext wfContext2 = new WfContext();
        wfContext2.setClusterId(NumberUtils.toInt(clusterId));
        wfContext2.setHostIp(Lists.newArrayList(hostIp1, hostIp2));
        wfContext2.setService(services);
        WorkflowContext workflowContext2 = new WorkflowContext(1L, wfContext2);
        context.setWorkflowContext(workflowContext2);
        context.setTaskId(1L);
        context.setAtomTaskId(2L);

        // serviceRoleInfos
        ServiceRoleInfo serviceRoleInfo = new ServiceRoleInfo();
        serviceRoleInfo.setClusterId(clusterId);
        serviceRoleInfo.setServiceId(serviceId);
        serviceRoleInfo.setIpAddress(hostIp1);
        serviceRoleInfos.add(serviceRoleInfo);

        // serviceModel
        serviceModel.setServiceName(serviceName);

        // clusterProjects
        ClusterProject clusterProject = new ClusterProject();
        clusterProject.setVersion(version);
        clusterProjects.add(clusterProject);

        // productSpect
        productSpect.setProjectName("zdh");
        productSpect.setServiceName(serviceName);
        productSpect.setVersionNo(version);
        productSpect.setDependServiceName(String.format("zdh@common@%s|zdh@hadoop@%s", version, version));

        // commandResult
        commandResult.setSuccess(true);
        commandResult.setInfo("success");

        // patchDetailPo
        PatchDetailPo patchDetailPo1 = new PatchDetailPo();
        patchDetailPo1.setPatchName("patch-SP01.zip");
        patchDetailPos1.add(patchDetailPo1);
        PatchDetailPo patchDetailPo2 = new PatchDetailPo();
        patchDetailPo2.setPatchName("patch-SP01.zip");
        patchDetailPos2.add(patchDetailPo2);
    }

    @Test
    public void test_process() throws Exception {
        when(serviceResourceControllerApi.queryByClusterId(clusterId)).thenReturn(serviceRoleInfos);

        // Assert.assertThrows(TaskManagementException.class, () -> patcherDistributeProcessor.process(context));

        when(productModelInfoControllerApi.queryByClusterIdAndServiceId(clusterId, serviceId)).thenReturn(serviceModel);

        // Assert.assertThrows(TaskManagementException.class, () -> patcherDistributeProcessor.process(context));

        when(clusterVersionControllerApi.queryVersions(NumberUtils.toInt(clusterId), serviceName))
            .thenReturn(clusterProjects);
        when(spectControllerApi.queryByServiceNameAndVersionNo(serviceName, version)).thenReturn(null);

        // Assert.assertThrows(TaskManagementException.class, () -> patcherDistributeProcessor.process(context));

        when(spectControllerApi.queryByServiceNameAndVersionNo(serviceName, version)).thenReturn(productSpect);

        when(patchInfoService.findByServiceAndBaseVersion(productSpect.getProjectName(), productSpect.getVersionNo()))
            .thenReturn(patchDetailPos1);
        when(patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(productSpect.getServiceName(),
            productSpect.getVersionNo())).thenReturn(patchDetailPos2);

        PatchDistributeInfo process = patcherDistributeProcessor.process(context);

        List<PatchDetailPo> allPatchDetailPo = Lists.newArrayList();
        allPatchDetailPo.addAll(patchDetailPos1);
        allPatchDetailPo.addAll(patchDetailPos2);
        List<DistributeFileBean> distributeFileBeans = Lists.newArrayList();
        DistributeFileBean distributeFileBean = new DistributeFileBean();
        distributeFileBeans.add(distributeFileBean);
        when(distributeFileService.organizeDistributeFileBeans(Sets.newHashSet(hostIp1), allPatchDetailPo))
            .thenReturn(distributeFileBeans);
        process = patcherDistributeProcessor.process(context);

        process = patcherDistributeProcessor.process(context4EmptyHosts);

    }

    @Test
    public void test_process_empty() {
        TaskContext emptyContext = new TaskContext();
        List<ProcessorParam> processorParams = Lists.newArrayList();
        emptyContext.setAtomicTaskParams(processorParams);
        Assert.assertThrows(TaskManagementException.class, () -> patcherDistributeProcessor.process(emptyContext));

        processorParams = Lists.newArrayList(new ProcessorParam());
        emptyContext.setAtomicTaskParams(processorParams);
        emptyContext.setWorkflowContext(null);
        Assert.assertThrows(TaskManagementException.class, () -> patcherDistributeProcessor.process(emptyContext));

        WorkflowContext workflowContext = new WorkflowContext(1L, null);
        emptyContext.setAtomicTaskParams(processorParams);
        emptyContext.setWorkflowContext(workflowContext);
        Assert.assertThrows(TaskManagementException.class, () -> patcherDistributeProcessor.process(emptyContext));
    }

    @Test
    public void test_iprocessorlistener() {
        IProcessorListener<PatchDistributeInfo, DistributeSummaryProgress> listener = patcherDistributeProcessor.getListener();
        assertSame(listener, multiPatcherDistributeListener);
    }

    @Test
    public void test_rollback() {
        PatchDistributeInfo result = patcherDistributeProcessor.rollback(new TaskContext());
        boolean flag = result == null;
        assertTrue(flag);
    }
}