package com.zte.daip.manager.patcher.task.worker.common;
/* Started by AICoder, pid:0ae6d32cd5yc56614d6c0826914a0d1dd262cfdb */
import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

import com.zte.daip.manager.common.task.common.exception.TaskManagementException;
import com.zte.daip.manager.common.task.common.step.TaskExecutor;
import com.zte.daip.manager.common.task.common.worker.WfContext;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.task.worker.processor.WorkflowContext;
import java.util.ArrayList;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbstractOperationProcessorTest {
    @Mock(answer = Answers.CALLS_REAL_METHODS)
    private AbstractOperationProcessor processor; // 被测试的类实例

    @Mock
    private TaskContext context; // 模拟的TaskContext对象

    @Mock
    private WorkflowContext workflowContext; // 模拟的WorkflowContext对象

    @Mock
    private WfContext wfContext; // 模拟的WfContext对象

    private TaskContext taskContext; // 真实的TaskContext对象

    @Before
    public void setUp() throws Exception {
        taskContext = new TaskContext(); // 初始化真实的TaskContext对象
    }

    @Test(expected = TaskManagementException.class)
    public void testValidTaskContext_NullWorkflowContextService() throws TaskManagementException {
        taskContext.setAtomTaskId(1L);
        processor.validTaskContextService(taskContext);
    }

    @Test(expected = TaskManagementException.class)
    public void testValidTaskContext_NullWfContextService() throws TaskManagementException {
        taskContext.setAtomTaskId(1L);
        taskContext.setWorkflowContext(new WorkflowContext(1L, new WfContext()));
        processor.validTaskContextService(taskContext);
    }

    @Test(expected = TaskManagementException.class)
    public void testValidTaskContext_Service_NullClusterId() throws TaskManagementException {
        taskContext.setAtomTaskId(1L);
        taskContext.setWorkflowContext(new WorkflowContext(1L, new WfContext()));
        taskContext.getWorkflowContext().setWfContext(new WfContext());
        processor.validTaskContextService(taskContext);
    }

    @Test
    public void testIsAllService_False() {
        taskContext.setTaskExecutor(new TaskExecutor());
        assertFalse(processor.isAllService(taskContext));
    }

    @Test
    public void testGetInstanceIdFromContext_EmptyService() {
        taskContext.setWorkflowContext(new WorkflowContext(1L, new WfContext()));
        taskContext.getWorkflowContext().setWfContext(new WfContext());
        taskContext.getWorkflowContext().getWfContext().setService(new ArrayList<>());
        assertTrue(processor.getInstanceIdFromContext(taskContext).isEmpty());
    }

    @Test(expected = TaskManagementException.class)
    public void testValidTaskContext_WhenContextServiceIsNull() throws TaskManagementException {
        when(context.getWorkflowContext()).thenReturn(null);
        assertThrows(TaskManagementException.class, () -> processor.validTaskContextService(context));
        assertThrows(TaskManagementException.class, () -> processor.validTaskContextHost(context));
        processor.validTaskContext(context);
    }

    @Test(expected = TaskManagementException.class)
    public void testValidTaskContext_WhenWfContextServiceIsNull() throws TaskManagementException {
        when(context.getWorkflowContext()).thenReturn(workflowContext);
        when(workflowContext.getWfContext()).thenReturn(null);
        assertThrows(TaskManagementException.class, () -> processor.validTaskContextService(context));
        assertThrows(TaskManagementException.class, () -> processor.validTaskContextHost(context));
        processor.validTaskContext(context);
    }

    @Test(expected = TaskManagementException.class)
    public void testValidTaskContext_Service_WhenClusterIdIsNull() throws TaskManagementException {
        when(context.getWorkflowContext()).thenReturn(workflowContext);
        when(workflowContext.getWfContext()).thenReturn(wfContext);
        when(wfContext.getClusterId()).thenReturn(null);
        assertThrows(TaskManagementException.class, () -> processor.validTaskContextService(context));
        assertThrows(TaskManagementException.class, () -> processor.validTaskContextHost(context));
        processor.validTaskContext(context);
    }

    @Test(expected = TaskManagementException.class)
    public void testValidTaskContext_Service_WhenServiceIsEmptyAndNotAllService() throws TaskManagementException {
//        context.setTaskExecutor(new TaskExecutor().setParam("{\"isAllService\": true}"))
        when(context.getWorkflowContext()).thenReturn(workflowContext);
        when(workflowContext.getWfContext()).thenReturn(wfContext);
        when(wfContext.getClusterId()).thenReturn(123);
        when(wfContext.getService()).thenReturn(new ArrayList<>());
        when(wfContext.getHostIp()).thenReturn(new ArrayList<>());
        processor.validTaskContext(context);
        assertThrows(TaskManagementException.class, () -> processor.validTaskContextHost(context));
        processor.validTaskContextService(context);
    }
}

/* Ended by AICoder, pid:0ae6d32cd5yc56614d6c0826914a0d1dd262cfdb */