package com.zte.daip.manager.patcher.task.worker.cluster;

/* Started by AICoder, pid:n834bpfea5f9c1314dd4091ba1908531f1223814 */

import static org.mockito.Mockito.*;

import java.util.Collections;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.api.StartAndStopClusterControllerApi;
import com.zte.daip.manager.common.deployer.bean.exception.DaipDeployerException;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModelInfo;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.monitor.api.ServiceStatusQueryControllerApi;
import com.zte.daip.manager.common.monitor.bean.ServiceStatusEnum;
import com.zte.daip.manager.common.task.common.task.request.ProcessorParam;
import com.zte.daip.manager.common.task.common.worker.ServiceInfo;
import com.zte.daip.manager.common.task.common.worker.WfContext;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.task.worker.processor.WorkflowContext;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class StartClusterProcessorTest {
    @InjectMocks
    private StartClusterProcessor startClusterProcessor;

    @Mock
    private StartAndStopClusterControllerApi startAndStopClusterControllerApi;

    @Mock
    private ServiceStatusQueryControllerApi serviceStatusQueryControllerApi;
    @Mock
    private ClusterOperationListener clusterOperationListener;

    @Mock
    private PatchTaskService patchTaskService;

    @Mock
    private PatchTaskOperateService patchTaskOperateService;

    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Mock
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;

    private TaskContext context = new TaskContext();

    private ProcessorParam processorParam = new ProcessorParam();

    @Before
    public void before() {
        context.setAtomTaskId(1L);
        context.setTaskName("test");
        processorParam.setClusterId("0");
        processorParam.setHostIp("127.0.0.1");
    }
    /* Started by AICoder, pid:u59c3f8e49ec3e61401e088cf0152f65cbe73ba7 */
    /**
     * 测试处理方法
     *
     * @throws DaipDeployerException 如果在部署过程中出现异常
     * @throws DaipBaseException      如果在基础操作中出现异常
     */
    @Test
    public void processTest() throws DaipDeployerException, DaipBaseException {
        // 创建一个ProcessorParam列表
        List<ProcessorParam> processorParams = Lists.newArrayList(processorParam);

        // 创建ServiceInfo对象并设置属性值
        ServiceInfo serviceInfo = new ServiceInfo();
        serviceInfo.setServiceId("dap.manager.hdfs");
        serviceInfo.setTargetVersion("version");

        // 设置工作流上下文，包括集群ID、集群名称和服务信息
        context.setWorkflowContext(new WorkflowContext(1L, new WfContext().setClusterId(0)
                .setClusterName("defaultCluster").setService(Lists.newArrayList(serviceInfo))));

        // 设置原子任务参数
        context.setAtomicTaskParams(processorParams);

        // 设置父任务ID
        context.setParentId(1L);

        // 创建PatchTaskPo对象并设置需要重启的服务实例信息
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        ServiceInstance instance = new ServiceInstance();
        instance.setServiceInstanceId("hdfs");
        patchTaskPo.setNeedRestartServices(JSONObject.toJSONString(Collections.singletonList(instance)));
        patchTaskPo.setClusterId(String.valueOf(0));

        // 模拟patchTaskService的queryByTaskId方法返回patchTaskPo对象
        Mockito.when(patchTaskService.queryByTaskId(anyLong())).thenReturn(patchTaskPo);

        // 创建DeploymentServiceInstance对象并设置属性值
        DeploymentServiceInstance deploymentServiceInstance = new DeploymentServiceInstance();
        deploymentServiceInstance.setClusterId("0");
        deploymentServiceInstance.setServiceId("dap.manager.hdfs");
        deploymentServiceInstance.setServiceInstanceId("hdfs");

        // 模拟deploymentInstanceServiceControllerApi的queryByClusterIdAndServiceInstanceId方法返回deploymentServiceInstance对象
        Mockito
                .when(deploymentInstanceServiceControllerApi.queryByClusterIdAndServiceInstanceId(anyString(), anyString()))
                .thenReturn(deploymentServiceInstance);

        // 模拟serviceStatusQueryControllerApi的calculate方法返回"STOPPED"状态
        Mockito.when(serviceStatusQueryControllerApi.calculate(anyString(), anyString(), anyString()))
                .thenReturn(ServiceStatusEnum.STOPPED.getStatus());

        // 创建ServiceModelInfo和ServiceModel对象，并设置属性值
        ServiceModelInfo serviceModelInfo = new ServiceModelInfo();
        serviceModelInfo.setVersion("v1");
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setClusterRestart(true);

        // 模拟productModelInfoControllerApi的queryByClusterIdAndServiceId方法返回serviceModel对象
        Mockito.when(productModelInfoControllerApi.queryByClusterIdAndServiceId(anyString(), anyString()))
                .thenReturn(serviceModel);

        // 模拟startAndStopClusterControllerApi的startCluster方法返回"1"
        Mockito.when(startAndStopClusterControllerApi.startCluster(anyString(), any())).thenReturn("1");

        // 调用startClusterProcessor的process方法
        startClusterProcessor.process(context);
    }

    /* Ended by AICoder, pid:u59c3f8e49ec3e61401e088cf0152f65cbe73ba7 */

    @Test
    public void rollback() throws DaipDeployerException, DaipBaseException {
        startClusterProcessor.rollback(context);
    }

    @Test
    public void getListener() throws DaipDeployerException, DaipBaseException {
        startClusterProcessor.getListener();
    }

}

/* Ended by AICoder, pid:n834bpfea5f9c1314dd4091ba1908531f1223814 */