package com.zte.daip.manager.patcher.task.worker.version;

import com.zte.daip.manager.common.task.common.exception.TaskManagementException;
import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.filemanagement.api.FileDistributeQueryControllerApi;
import com.zte.daip.manager.filemanagement.api.progress.DistributeSummaryProgress;
import com.zte.daip.manager.patcher.task.worker.bean.PatchDistributeInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatcherDistributeListenerTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/9/1</p>
 * <p>完成日期：2023/9/1</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatcherDistributeListenerTest {
    @InjectMocks
    private PatcherDistributeListener patcherDistributeListener;

    @Mock
    private FileDistributeQueryControllerApi fileDistributeQueryControllerApi;

    @Test
    public void doListen() throws Exception {
        Assert.assertThrows(TaskManagementException.class, () -> patcherDistributeListener.doListen(null));
        Assert.assertThrows(TaskManagementException.class,
            () -> patcherDistributeListener.doListen(new PatchDistributeInfo("", 0)));
        String distributeId = "distributeId1";
        DistributeSummaryProgress distributeSummaryProgress = new DistributeSummaryProgress();
        distributeSummaryProgress.setFinish(true);
        Mockito.when(fileDistributeQueryControllerApi.querySummaryProgress(distributeId))
            .thenReturn(distributeSummaryProgress);
        ProcessResult<PatchDistributeInfo, DistributeSummaryProgress> result =
            patcherDistributeListener.doListen(new PatchDistributeInfo(distributeId, 1));
        assertNotNull(result);
        result = patcherDistributeListener.doListen(new PatchDistributeInfo(distributeId, 0));
        assertNotNull(result);
    }
}