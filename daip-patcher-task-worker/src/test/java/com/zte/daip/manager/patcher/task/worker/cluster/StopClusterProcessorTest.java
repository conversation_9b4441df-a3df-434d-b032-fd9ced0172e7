package com.zte.daip.manager.patcher.task.worker.cluster;

/* Started by AICoder, pid:c24a4ib9dexc1b514e0709204182c07a7291fce1 */

import static org.mockito.ArgumentMatchers.*;

import java.util.Collections;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.api.StartAndStopClusterControllerApi;
import com.zte.daip.manager.common.deployer.bean.exception.DaipDeployerException;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModelInfo;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.monitor.api.ServiceStatusQueryControllerApi;
import com.zte.daip.manager.common.monitor.bean.ServiceStatusEnum;
import com.zte.daip.manager.common.task.common.task.request.ProcessorParam;
import com.zte.daip.manager.common.task.common.worker.ServiceInfo;
import com.zte.daip.manager.common.task.common.worker.WfContext;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.task.worker.processor.WorkflowContext;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;

@RunWith(SpringRunner.class)
public class StopClusterProcessorTest {
    @InjectMocks
    private StopClusterProcessor stopClusterProcessor;

    @Mock
    private StartAndStopClusterControllerApi startAndStopClusterControllerApi;

    @Mock
    private ServiceStatusQueryControllerApi serviceStatusQueryControllerApi;
    @Mock
    private ClusterOperationListener clusterOperationListener;

    @Mock
    private PatchTaskService patchTaskService;

    @Mock
    private PatchTaskOperateService patchTaskOperateService;

    @Mock
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Mock
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;

    private TaskContext context = new TaskContext();

    private ProcessorParam processorParam = new ProcessorParam();

    @Before
    public void before() {
        context.setAtomTaskId(1L);
        context.setTaskName("test");
        processorParam.setClusterId("0");
        processorParam.setHostIp("127.0.0.1");
    }
    /* Started by AICoder, pid:6246cm9aa4836321458f0b8ab07bc0708120f827 */
    /**
     * 测试停止集群处理器的process方法
     *
     * @throws DaipDeployerException 如果在部署过程中出现异常
     * @throws DaipBaseException      如果在基础操作中出现异常
     */
    @Test
    public void processTest() throws DaipDeployerException, DaipBaseException {
        // 创建一个ProcessorParam列表
        List<ProcessorParam> processorParams = Lists.newArrayList(processorParam);

        // 创建一个ServiceInfo对象，并设置其serviceId和targetVersion属性
        ServiceInfo serviceInfo = new ServiceInfo();
        serviceInfo.setServiceId("dap.manager.hdfs");
        serviceInfo.setTargetVersion("version");

        // 创建一个WorkflowContext对象，并设置其clusterId、clusterName和service属性
        context.setWorkflowContext(new WorkflowContext(1L, new WfContext().setClusterId(0)
                .setClusterName("defaultCluster").setService(Lists.newArrayList(serviceInfo))));

        // 设置原子任务参数
        context.setAtomicTaskParams(processorParams);

        // 设置父任务ID
        context.setParentId(1L);

        // 创建一个PatchTaskPo对象，并设置其needRestartServices、clusterId和taskType属性
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        ServiceInstance instance = new ServiceInstance();
        instance.setServiceInstanceId("hdfs");
        patchTaskPo.setNeedRestartServices(JSONObject.toJSONString(Collections.singletonList(instance)));
        patchTaskPo.setClusterId(String.valueOf(0));
        patchTaskPo.setTaskType(1);

        // 使用Mockito模拟patchTaskService的queryByTaskId方法，使其返回patchTaskPo对象
        Mockito.when(patchTaskService.queryByTaskId(anyLong())).thenReturn(patchTaskPo);

        // 创建一个DeploymentServiceInstance对象，并设置其clusterId、serviceId和serviceInstanceId属性
        DeploymentServiceInstance deploymentServiceInstance = new DeploymentServiceInstance();
        deploymentServiceInstance.setClusterId("0");
        deploymentServiceInstance.setServiceId("dap.manager.hdfs");
        deploymentServiceInstance.setServiceInstanceId("hdfs");

        // 使用Mockito模拟deploymentInstanceServiceControllerApi的queryByClusterIdAndServiceInstanceId方法，使其返回deploymentServiceInstance对象
        Mockito
                .when(deploymentInstanceServiceControllerApi.queryByClusterIdAndServiceInstanceId(anyString(), anyString()))
                .thenReturn(deploymentServiceInstance);

        // 使用Mockito模拟serviceStatusQueryControllerApi的calculate方法，使其返回"STOPPED"状态
        Mockito.when(serviceStatusQueryControllerApi.calculate(anyString(), anyString(), anyString()))
                .thenReturn(ServiceStatusEnum.STOPPED.getStatus());

        // 创建一个ServiceModelInfo对象，并设置其version属性
        ServiceModelInfo serviceModelInfo = new ServiceModelInfo();
        serviceModelInfo.setVersion("v1");

        // 创建一个ServiceModel对象，并设置其clusterRestart属性为true
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setClusterRestart(true);

        // 使用Mockito模拟productModelInfoControllerApi的queryByClusterIdAndServiceId方法，使其返回serviceModel对象
        Mockito.when(productModelInfoControllerApi.queryByClusterIdAndServiceId(anyString(), anyString()))
                .thenReturn(serviceModel);

        // 使用Mockito模拟startAndStopClusterControllerApi的startCluster方法，使其返回"1"
        Mockito.when(startAndStopClusterControllerApi.startCluster(anyString(), any())).thenReturn("1");

        // 调用stopClusterProcessor的process方法
        stopClusterProcessor.process(context);
    }

    /* Ended by AICoder, pid:6246cm9aa4836321458f0b8ab07bc0708120f827 */
    /* Started by AICoder, pid:36245b6dfbic375146000a1b20441e7d4bd545b7 */
    @Test
    public void processTest1() throws DaipDeployerException, DaipBaseException {
        // 创建一个ProcessorParam列表，并添加processorParam对象
        List<ProcessorParam> processorParams = Lists.newArrayList(processorParam);

        // 创建一个ServiceInfo对象，并设置serviceId和targetVersion属性
        ServiceInfo serviceInfo = new ServiceInfo();
        serviceInfo.setServiceId("dap.manager.hdfs");
        serviceInfo.setTargetVersion("version");

        // 设置工作流上下文，包括clusterId、clusterName和service信息
        context.setWorkflowContext(new WorkflowContext(1L, new WfContext().setClusterId(0)
                .setClusterName("defaultCluster").setService(Lists.newArrayList(serviceInfo))));

        // 设置原子任务参数
        context.setAtomicTaskParams(processorParams);

        // 设置父任务ID
        context.setParentId(1L);

        // 创建一个PatchTaskPo对象，并设置需要重启的服务实例、集群ID、任务类型等信息
        PatchTaskPo patchTaskPo = new PatchTaskPo();
        ServiceInstance instance = new ServiceInstance();
        instance.setServiceInstanceId("hdfs");
        patchTaskPo.setNeedRestartServices(JSONObject.toJSONString(Collections.singletonList(instance)));
        patchTaskPo.setClusterId(String.valueOf(0));
        patchTaskPo.setTaskType(0);

        // 创建一个ServiceInstancePatchInfo对象，并设置服务实例信息
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceInstanceId("hdfs");
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);
        patchTaskPo.setContext(JSONObject.toJSONString(Collections.singletonList(serviceInstancePatchInfo)));

        // 模拟patchTaskOperateService的queryNeedRestartServiceInstance方法返回值
        Mockito.when(patchTaskOperateService.queryNeedRestartServiceInstance(anyString(), any()))
                .thenReturn(Collections.singletonList("hdfs"));

        // 模拟patchTaskService的queryByTaskId方法返回值
        Mockito.when(patchTaskService.queryByTaskId(anyLong())).thenReturn(patchTaskPo);

        // 创建一个DeploymentServiceInstance对象，并设置集群ID、服务ID和服务实例ID
        DeploymentServiceInstance deploymentServiceInstance = new DeploymentServiceInstance();
        deploymentServiceInstance.setClusterId("0");
        deploymentServiceInstance.setServiceId("dap.manager.hdfs");
        deploymentServiceInstance.setServiceInstanceId("hdfs");

        // 模拟deploymentInstanceServiceControllerApi的queryByClusterIdAndServiceInstanceId方法返回值
        Mockito
                .when(deploymentInstanceServiceControllerApi.queryByClusterIdAndServiceInstanceId(anyString(), anyString()))
                .thenReturn(deploymentServiceInstance);

        // 模拟serviceStatusQueryControllerApi的calculate方法返回值
        Mockito.when(serviceStatusQueryControllerApi.calculate(anyString(), anyString(), anyString()))
                .thenReturn(ServiceStatusEnum.STOPPED.getStatus());

        // 创建一个ServiceModelInfo对象，并设置版本号
        ServiceModelInfo serviceModelInfo = new ServiceModelInfo();
        serviceModelInfo.setVersion("v1");

        // 创建一个ServiceModel对象，并设置是否需要集群重启
        ServiceModel serviceModel = new ServiceModel();
        serviceModel.setClusterRestart(true);

        // 模拟productModelInfoControllerApi的queryByClusterIdAndServiceId方法返回值
        Mockito.when(productModelInfoControllerApi.queryByClusterIdAndServiceId(anyString(), anyString()))
                .thenReturn(serviceModel);

        // 模拟startAndStopClusterControllerApi的startCluster方法返回值
        Mockito.when(startAndStopClusterControllerApi.startCluster(anyString(), any())).thenReturn("1");

        // 调用stopClusterProcessor的process方法
        stopClusterProcessor.process(context);
    }

    /* Ended by AICoder, pid:36245b6dfbic375146000a1b20441e7d4bd545b7 */

    @Test
    public void rollback() throws DaipDeployerException, DaipBaseException {
        stopClusterProcessor.rollback(context);
    }

    @Test
    public void getListener() throws DaipDeployerException, DaipBaseException {
        stopClusterProcessor.getListener();
    }
}

/* Ended by AICoder, pid:c24a4ib9dexc1b514e0709204182c07a7291fce1 */