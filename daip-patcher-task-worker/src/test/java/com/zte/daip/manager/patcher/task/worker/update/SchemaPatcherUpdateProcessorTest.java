/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: SchemaPatcherUpdateProcessorTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2024/1/22
 * </p>
 * <p>
 * 完成日期：2024/1/22
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.task.worker.update;

import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.PatcherUpdateControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class SchemaPatcherUpdateProcessorTest {

    @Mock
    private PatcherUpdateControllerApi patcherUpdateControllerApi;

    @InjectMocks
    private SchemaPatcherUpdateProcessor schemaPatcherUpdateProcessor;

    @Before
    public void setUp() throws DaipBaseException {

        when(patcherUpdateControllerApi.updateSchemaPatch(anyLong()))
            .thenReturn(Lists.newArrayList(new PatchOperateResult(true, "")));
    }

    @Test
    public void test_update() {
        TaskContext context = new TaskContext();
        context.setParentId(1L);
        ProcessResult result = schemaPatcherUpdateProcessor.process(context);
        assertTrue(result.isSuccess());
    }
}