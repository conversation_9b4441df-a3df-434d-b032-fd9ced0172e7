package com.zte.daip.manager.patcher.task.worker.rollback;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.task.common.task.request.ProcessorParam;
import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.common.task.common.worker.WfContext;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.task.worker.processor.WorkflowContext;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.api.PatchRollbackControllerApi;
import com.zte.daip.manager.patcher.api.dto.RollBackPatchProgress;
import com.zte.daip.manager.patcher.api.dto.RollbackHostProgressDto;
import com.zte.daip.manager.patcher.api.dto.RollbackServiceProgressDto;
import com.zte.daip.manager.patcher.inner.api.PatchTaskControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatcherRollbackProcessorTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/11/6</p>
 * <p>完成日期：2023/11/6</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class PatcherRollbackProcessorTest
{
    @Mock
    private PatchRollbackControllerApi patchRollbackControllerApi;

    @Mock
    private PatchTaskControllerApi patchTaskControllerApi;

    @InjectMocks
    private PatcherRollbackProcessor patcherRollbackProcessor;

    private TaskContext context = new TaskContext();

    private PatchTaskDto patchTaskDto = new PatchTaskDto();

    private PatchHostInfoDto patchHostInfoDto1 = new PatchHostInfoDto();

    private RollBackPatchProgress successPatchRollbackProgress = new RollBackPatchProgress();

    private RollBackPatchProgress failedPatchRollbackProgress = new RollBackPatchProgress();

    private static long taskId = 1L;

    private static String clusterId = "1";

    private static String serviceId = "dap.manager.hdfs";

    private String ip1 = "*******";

    private String taskName = "1";

    private String hdfsServiceId = "dap.amanager.hdfs";

    private String hdfsService = "hdfs";

    private String rollbackPatchPoint1 = "V20.23.40.04-CP01";

    @Before
    public void setUp() throws DaipBaseException
    {
        String hostIp1 = "***********";
        String serviceInstanceId = "hdfs";

        // context
        List<ProcessorParam> processorParams = Lists.newArrayList();
        ProcessorParam processorParam =
            new ProcessorParam(clusterId, hostIp1, serviceId, serviceInstanceId, Sets
                .newHashSet());
        processorParams.add(processorParam);
        context.setParentId(taskId);
        context.setAtomicTaskParams(processorParams);
        WfContext wfContext2 = new WfContext();
        wfContext2.setClusterId(NumberUtils.toInt(clusterId));
        wfContext2.setHostIp(Lists.newArrayList(hostIp1));
        WorkflowContext workflowContext2 = new WorkflowContext(1L, wfContext2);
        context.setWorkflowContext(workflowContext2);
        context.setTimeout(10);

        // patchTaskDto
        patchHostInfoDto1.setIp(ip1);
        patchHostInfoDto1.setHostName("host1");
        RollBackPatchPointInfo rollBackPatchPointInfo1 = new RollBackPatchPointInfo();
        rollBackPatchPointInfo1.setRollBackPatchPoint(rollbackPatchPoint1);
        rollBackPatchPointInfo1.setPatchHostInfos(Lists.newArrayList(patchHostInfoDto1));
        List<RollBackPatchPointInfo> rollBackPatchPoints1 = org.assertj.core.util.Lists.newArrayList(rollBackPatchPointInfo1);

        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceId(hdfsServiceId);
        serviceInstance.setServiceName(hdfsService);
        serviceInstance.setServiceInstanceId(hdfsService);
        serviceInstance.setServiceInstanceName(hdfsServiceId);
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);
        serviceInstancePatchInfo.setRollBackPatchPoints(rollBackPatchPoints1);

        patchTaskDto.setTaskId(taskId);
        patchTaskDto.setClusterId(clusterId);
        patchTaskDto.setTaskName(taskName);
        patchTaskDto.setAllowModify(true);
        patchTaskDto.setContext(org.assertj.core.util.Lists.newArrayList(serviceInstancePatchInfo));

        //patchRollbackProgress
        successPatchRollbackProgress.setFinish(true);
        successPatchRollbackProgress.setSuccess(true);

        failedPatchRollbackProgress.setFinish(true);
        failedPatchRollbackProgress.setSuccess(false);
        RollbackServiceProgressDto rollbackServiceProgressDto = new RollbackServiceProgressDto();
        RollbackHostProgressDto rollbackHostProgressDto = new RollbackHostProgressDto();
        rollbackHostProgressDto.setIpAddress(ip1);
        rollbackServiceProgressDto.setRollbackHostProgressDtos(Lists.newArrayList(rollbackHostProgressDto));
        failedPatchRollbackProgress.setRollbackServiceProgressDtos(Lists.newArrayList(rollbackServiceProgressDto));

        when(patchTaskControllerApi.queryPatchTaskByTaskId(taskId)).thenReturn(patchTaskDto);
    }

    @Test
    public void test_process()
    {
        when(patchRollbackControllerApi.queryRollBackProgress(clusterId)).thenReturn(null);
        ProcessResult process = patcherRollbackProcessor.process(context);
        assertTrue(process.isSuccess());

        when(patchRollbackControllerApi.queryRollBackProgress(clusterId)).thenReturn(successPatchRollbackProgress);
        process = patcherRollbackProcessor.process(context);
        assertTrue(process.isSuccess());

        when(patchRollbackControllerApi.queryRollBackProgress(clusterId)).thenReturn(failedPatchRollbackProgress);
        process = patcherRollbackProcessor.process(context);
        assertFalse(process.isSuccess());
    }

    @Test
    public void test_rollback() {
        ProcessResult result = patcherRollbackProcessor.rollback(new TaskContext());
        boolean flag = result == null;
        assertTrue(flag);
    }
}