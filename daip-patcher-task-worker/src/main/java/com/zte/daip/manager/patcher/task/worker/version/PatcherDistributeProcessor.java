package com.zte.daip.manager.patcher.task.worker.version;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.common.deployer.model.controller.api.ClusterVersionControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.common.task.common.worker.ServiceInfo;
import com.zte.daip.manager.common.task.worker.processor.IProcessorListener;
import com.zte.daip.manager.common.task.worker.processor.ListenableBasicProcessor;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.event.reporter.api.annotation.BusinessDomainEvent;
import com.zte.daip.manager.event.reporter.api.util.SpanUtil;
import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;
import com.zte.daip.manager.filemanagement.api.bean.DistributeFileBean;
import com.zte.daip.manager.filemanagement.api.bean.DistributeFileParam;
import com.zte.daip.manager.filemanagement.api.progress.DistributeSummaryProgress;
import com.zte.daip.manager.patcher.domain.common.PatchInfoService;
import com.zte.daip.manager.patcher.domain.constants.DomainI18nKey;
import com.zte.daip.manager.patcher.domain.dispatch.DistributeFileService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchDetailPo;
import com.zte.daip.manager.patcher.task.worker.bean.PatchDistributeInfo;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.daip.manager.common.task.common.exception.TaskManagementException;
import com.zte.daip.manager.common.task.common.worker.WfContext;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.task.worker.processor.WorkflowContext;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatcherDistributeProcessor
    implements ListenableBasicProcessor<PatchDistributeInfo, DistributeSummaryProgress> {
    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Autowired
    private ClusterVersionControllerApi clusterVersionControllerApi;

    @Autowired
    private SpectControllerApi spectControllerApi;

    @Autowired
    private DistributeFileService distributeFileService;

    @Autowired
    private PatchInfoService patchInfoService;

    @Autowired
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @Autowired
    private DaipEventReporter daipEventReporter;

    @Autowired
    private FileDistributeControllerApi fileDistributeControllerApi;

    @Autowired
    private PatcherDistributeListener multiPatcherDistributeListener;

    @Override
    @BusinessDomainEvent(eventName = DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_EXECUTOR_DISTRIBUTE_PATCH)
    public PatchDistributeInfo process(TaskContext context) {
        log.info("[ DistributePatchAtomTaskId-{} ] start to process {} with param:{}", context.getAtomTaskId(),
            context.getTaskName(), context.getAtomicTaskParams().toString());
        WorkflowContext workflowContext = context.getWorkflowContext();
        if (workflowContext == null) {
            daipEventReporter.error(DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_EXECUTOR_DISTRIBUTE_PATCH,
                "Workflow context can not null, please check taskContext!");
            throw new TaskManagementException("workflow context can not be null!");
        }
        WfContext wfContext = workflowContext.getWfContext();
        if (wfContext == null) {
            daipEventReporter.error(DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_EXECUTOR_DISTRIBUTE_PATCH,
                "wfContext can not be null, please check taskContext!");
            throw new TaskManagementException("wf context can not be null!");
        }
        String taskId = SpanUtil.getTraceId() + "-" + context.getAtomTaskId();
        List<ServiceInfo> services = wfContext.getService();
        List<String> hostIps = wfContext.getHostIp();
        PatchDistributeInfo patchDistributeInfo = getProcessResult(taskId, wfContext, services, hostIps);
        String distributeId = patchDistributeInfo.getDistributeId();
        daipEventReporter.info(DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_EXECUTOR_DISTRIBUTE_PATCH,
            "Executor distribute patch success, detail: " + distributeId);
        log.info("Executor distributeid:{}", distributeId);
        return patchDistributeInfo;
    }

    private PatchDistributeInfo getProcessResult(String taskId, WfContext wfContext, List<ServiceInfo> services,
        List<String> hostIps) {
        try {
            String clusterId = String.valueOf(wfContext.getClusterId());

            List<DistributeFileBean> distributeFileBeans = generateDistributeFileBeans(services, clusterId, hostIps);

            if (CollectionUtils.isEmpty(distributeFileBeans)) {
                log.info("Cluster: {} no patch need distribute.", clusterId);
                return new PatchDistributeInfo(taskId, 0);
            }
            DistributeFileParam distributeFileParam = new DistributeFileParam();
            distributeFileParam.setDistributeId(taskId);
            distributeFileParam.setDistributeFileBeans(distributeFileBeans);
            distributeFileParam.setSshPort(String.valueOf(wfContext.getPort()));
            distributeFileParam.setUserName(wfContext.getUsername());
            distributeFileParam.setPassword(wfContext.getPassword());
            distributeFileParam.setUseDistributeServerIp(true);
            return new PatchDistributeInfo(fileDistributeControllerApi.distributeFiles(distributeFileParam),
                distributeFileBeans.size());
        } catch (Exception e) {
            log.error("Failed to distribute patcher ", e);
            daipEventReporter.error(DomainI18nKey.COM_ZTE_DAIP_MANAGER_PATCHER_EXECUTOR_DISTRIBUTE_PATCH,
                String.format(" Executor distribute patcher failed, detail:%s", e.getMessage()));
            throw new TaskManagementException("distribute patcher occur an error!", e);
        }
    }

    private List<DistributeFileBean> generateDistributeFileBeans(List<ServiceInfo> services, String clusterId,
        List<String> hostIps) throws DaipBaseException {
        List<DistributeFileBean> distributeFileBeans = Lists.newArrayList();
        for (ServiceInfo serviceInfo : services) {
            String serviceId = serviceInfo.getServiceId();
            List<String> distributeHosts = generateDistributeHosts(clusterId, serviceId, hostIps);
            log.info("cluster:{}, serviceid:{}, distribute hosts:{}", clusterId, serviceId, distributeHosts.toString());
            List<PatchDetailPo> distributePatches = generateDistributePatches(clusterId, serviceId);
            distributeFileBeans.addAll(distributeFileService
                .organizeDistributeFileBeans(Sets.newConcurrentHashSet(distributeHosts), distributePatches));
        }
        return distributeFileBeans;
    }

    private List<PatchDetailPo> generateDistributePatches(String clusterId, String serviceId) {
        ProductSpect productSpect = queryProductSpect(clusterId, serviceId);
        if (productSpect == null) {
            log.warn("product spect is null :{},{}", clusterId, serviceId);
            return Lists.newArrayList();
        }
        List<PatchDetailPo> needDispatchAndUpdatePatches = Lists.newArrayList();
        List<PatchDetailPo> projectPatches =
            patchInfoService.findByServiceAndBaseVersion(productSpect.getProjectName(), productSpect.getVersionNo());
        if (!CollectionUtils.isEmpty(projectPatches)) {
            needDispatchAndUpdatePatches.addAll(projectPatches);
        }
        List<PatchDetailPo> servicePatches = patchInfoService.findByServiceAndBaseVersionNotSchemeAndRepository(
            productSpect.getServiceName(), productSpect.getVersionNo());
        if (!CollectionUtils.isEmpty(servicePatches)) {
            needDispatchAndUpdatePatches.addAll(servicePatches);
        }
        return needDispatchAndUpdatePatches;
    }

    private ProductSpect queryProductSpect(String clusterId, String serviceId) {
        ServiceModel serviceModel = productModelInfoControllerApi.queryByClusterIdAndServiceId(clusterId, serviceId);
        if (serviceModel == null) {
            return null;
        }
        String serviceName = serviceModel.getServiceName();
        List<ClusterProject> clusterProjects =
            clusterVersionControllerApi.queryVersions(NumberUtils.toInt(clusterId), serviceName);
        if (CollectionUtils.isEmpty(clusterProjects)) {
            return null;
        }
        String version = clusterProjects.stream().findFirst().orElse(new ClusterProject()).getVersion();
        return spectControllerApi.queryByServiceNameAndVersionNo(serviceName, version);
    }

    private List<String> generateDistributeHosts(String clusterId, String serviceId, List<String> hostIps) {
        List<ServiceRoleInfo> serviceRoleInfos = serviceResourceControllerApi.queryByClusterId(clusterId);
        if (CollectionUtils.isEmpty(serviceRoleInfos)) {
            return Lists.newArrayList();
        }
        Set<String> hostsSet = serviceRoleInfos.stream().filter(s -> StringUtils.equals(s.getServiceId(), serviceId))
            .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(hostIps)) {
            return Lists.newArrayList(hostsSet);
        } else {
            return hostIps.stream().filter(hostsSet::contains).collect(Collectors.toList());
        }
    }

    @Override
    public IProcessorListener<PatchDistributeInfo, DistributeSummaryProgress> getListener() {
        return multiPatcherDistributeListener;
    }

    @Override
    public PatchDistributeInfo rollback(TaskContext context) {
        return null;
    }
}
