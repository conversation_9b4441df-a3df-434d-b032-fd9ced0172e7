/* Started by AICoder, pid:pbca203d4373046146130a7e70b7c56206656ca2 */
package com.zte.daip.manager.patcher.task.worker.cluster;

import java.util.HashMap;
import java.util.Map;

import com.zte.daip.manager.common.deployer.api.StartAndStopClusterControllerApi;
import com.zte.daip.manager.common.deployer.bean.exception.DaipDeployerException;
import com.zte.daip.manager.patcher.task.worker.bean.ClusterOperateResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.google.common.base.Joiner;
import com.zte.daip.manager.common.deployer.bean.cluster.operation.ClusterOperationInfoBean;
import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.common.task.worker.processor.IProcessorListener;

/**
 * 功能描述:<br>
 * <p/>
 * <p/>
 * <p/>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/29
 */
@Service
public class ClusterOperationListener implements IProcessorListener<ClusterOperateResult, String> {

    @Autowired
    private StartAndStopClusterControllerApi startAndStopClusterControllerApi;

    @Override
    public ProcessResult<ClusterOperateResult, String> doListen(ClusterOperateResult data) throws DaipDeployerException {
        ProcessResult<ClusterOperateResult, String> result = new ProcessResult<>();
        String clusterId = data.getClusterId();
        String clusterName = data.getClusterName();

        if (StringUtils.isEmpty(data.getEventId())) {
            return result.toBuilder().success(true).finished(true)
                    .listenerResult("100").listenerParam(data).build();
        }
        ClusterOperationInfoBean query = startAndStopClusterControllerApi.queryClusterOperatorProcess(clusterId);
        result = getClusterOperateResultStringProcessResult(data, clusterId, clusterName, query);
        return result;
    }

    private ProcessResult<ClusterOperateResult, String> getClusterOperateResultStringProcessResult(ClusterOperateResult data, String clusterId, String clusterName, ClusterOperationInfoBean query) {
        ProcessResult<ClusterOperateResult, String> result = new ProcessResult<>();

        Map<String, String> params = new HashMap<>();
        String instanceIds = Joiner.on(',').skipNulls().join(data.getServiceInstanceIds());
        params.put("clusterId", clusterId);
        params.put("clusterName", clusterName);
        params.put("operation", data.getOperate());
        params.put("instanceIds", instanceIds);
        result.setListenerParam(data);
        result.setParams(params);
        result.setType("operateType");
        result.setOperateType("clusterOperate");
        result.setSuccess(query.isSuccess());
        result.setFinished(isFinished(query));
        result.setListenerResult(String.valueOf(query.getProgress()));
        return result;
    }

    private boolean isFinished(ClusterOperationInfoBean data) {
        return data.isUninstall() ? data.isFinished() : (data.isFinished() && (!data.isSuccess() || data.isAllServiceStatusRight()));
    }
}

/* Ended by AICoder, pid:pbca203d4373046146130a7e70b7c56206656ca2 */