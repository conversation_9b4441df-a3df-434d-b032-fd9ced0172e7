package com.zte.daip.manager.patcher.task.worker.update;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.api.ServiceResourceControllerApi;
import com.zte.daip.manager.common.deployer.bean.loadmodel.ProductSpect;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.CommandResult;
import com.zte.daip.manager.common.deployer.bean.service.ServiceRoleInfo;
import com.zte.daip.manager.common.deployer.bean.version.ClusterProject;
import com.zte.daip.manager.common.deployer.model.controller.api.ClusterVersionControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.deployer.model.controller.api.SpectControllerApi;
import com.zte.daip.manager.common.task.common.worker.WfContext;
import com.zte.daip.manager.common.task.worker.processor.WorkflowContext;
import com.zte.daip.manager.patcher.api.PatcherUpdateControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateResult;
import com.zte.daip.manager.patcher.api.dto.ServiceInstanceInfo;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.domain.dispatch.ServiceNameEnum;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.PatchTaskControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.zte.daip.manager.common.task.common.exception.TaskManagementException;
import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.common.task.worker.processor.BasicProcessor;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;

import lombok.extern.slf4j.Slf4j;

import static com.zte.daip.manager.patcher.domain.utils.Constants.BIG_DATA_SERVICE_ID;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatcherUpdateProcessor implements BasicProcessor {

    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Autowired
    private ClusterVersionControllerApi clusterVersionControllerApi;

    @Autowired
    private PatchTaskOperateService patchTaskOperateService;

    @Autowired
    private PatchTaskService patchTaskService;

    @Autowired
    private SpectControllerApi spectControllerApi;

    @Autowired
    private PatcherUpdateControllerApi patcherUpdateControllerApi;

    @Autowired
    private ServiceResourceControllerApi serviceResourceControllerApi;

    @Autowired
    private PatchTaskControllerApi patchTaskControllerApi;

    @Value("${patcher.update.timeout.inMinutes:30}")
    private int updateTimeout;

    @Override
    public ProcessResult process(TaskContext context) {
        log.info("[PatcherUpdateProcessor-{}] start to process {} with {}", context.getAtomTaskId(),
            context.getTaskName(), context.getAtomicTaskParams());
        WorkflowContext processorWorkflowContext = context.getWorkflowContext();
        if (processorWorkflowContext == null) {
            throw new TaskManagementException("update patch: workflow context can not be null!");
        }
        WfContext wfContext = processorWorkflowContext.getWfContext();
        if (wfContext == null) {
            throw new TaskManagementException("update patch: wf context can not be null!");
        }
        List<String> hostIps = wfContext.getHostIp();
        long taskTimeout = context.getTimeout();
        if (taskTimeout <= 0) {
            taskTimeout = updateTimeout;
        }
        long parentTaskId = context.getParentId();
        ProcessResult processResult = updatePatch(hostIps, parentTaskId, taskTimeout * 60 * 1000);
        log.info("processResult: {}", processResult.toString());
        return processResult;
    }

    private ProcessResult updatePatch(List<String> hostIps, long taskId, long timeout) {
        boolean isFailed = false;
        List<CommandResult> commandResults = Lists.newArrayList();
        try {
            PatchTaskDto patchTaskDto = patchTaskControllerApi.queryPatchTaskByTaskId(taskId);
            if (patchTaskDto != null) {
                patchTaskOperateService.updateRollbackInfo(patchTaskDto);
                PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
                patchTaskPo.setContext(JSONObject.toJSONString(patchTaskDto.getContext()));
                List<ServiceInstance> needRestartServiceInstances =
                    patchTaskOperateService.queryNeedRestartService(patchTaskDto);
                patchTaskPo.setNeedRestartServices(JSONObject.toJSONString(needRestartServiceInstances));
                patchTaskService.updatePatchTask(patchTaskPo);
                UpdateRequest updateRequest = organizeUpdateRequest(hostIps, patchTaskDto);
                patcherUpdateControllerApi.updatePatch(updateRequest);
                CommandResult commandResult = checkUpdateProgress(patchTaskDto.getClusterId(), timeout);
                if (!commandResult.isSuccess()) {
                    isFailed = true;
                    commandResults.add(commandResult);
                    log.info("cluster: {}, failed to update patch", patchTaskDto.getClusterId());
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            return new ProcessResult(false, e.getMessage());
        }
        List<String> infos = commandResults.stream().map(CommandResult::getInfo).collect(Collectors.toList());
        return isFailed ? ProcessResult.failure(JSON.toJSONString(infos).replace("/", "*")) : ProcessResult.success();
    }

    private List<String> generateDistributeHosts(String clusterId, ServiceInstance serviceInfo, List<String> hostIps,
        List<ServiceModel> serviceModels) {
        List<ServiceRoleInfo> serviceRoleInfos = serviceResourceControllerApi.queryByClusterId(clusterId);
        String serviceInstanceId = serviceInfo.getServiceInstanceId();
        String serviceId = serviceInfo.getServiceId();
        ServiceModel serviceModel =
            serviceModels.stream().filter(model -> StringUtils.equals(model.getServiceId(), serviceId)).findFirst()
                .orElse(new ServiceModel());

        if (CollectionUtils.isEmpty(serviceRoleInfos)) {
            return Lists.newArrayList();
        }
        Set<String> hostsSet;
        if (StringUtils.equals(serviceId, ServiceNameEnum.ZDH.getServiceName())) {
            Set<String> serviceIds = serviceModels.stream()
                .filter(e -> StringUtils.equalsIgnoreCase(e.getComponentType(), BIG_DATA_SERVICE_ID))
                .map(ServiceModel::getServiceId).collect(Collectors.toSet());
            hostsSet = serviceRoleInfos.stream().filter(s -> serviceIds.contains(s.getServiceId()))
                .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        } else if (StringUtils.equalsIgnoreCase(serviceModel.getPatchType(), "role")
            || StringUtils.equalsIgnoreCase(serviceModel.getPatchType(), "instance")) {
            hostsSet =
                serviceRoleInfos.stream().filter(s -> StringUtils.equals(s.getServiceInstanceId(), serviceInstanceId))
                    .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        } else {
            hostsSet = serviceRoleInfos.stream().filter(s -> StringUtils.equals(s.getServiceId(), serviceId))
                .map(ServiceRoleInfo::getIpAddress).collect(Collectors.toSet());
        }
        if (CollectionUtils.isEmpty(hostIps)) {
            return Lists.newArrayList(hostsSet);
        } else {
            return hostIps.stream().filter(hostsSet::contains).collect(Collectors.toList());
        }
    }

    private CommandResult checkUpdateProgress(String clusterId, long timeout) throws InterruptedException {
        CommandResult commandResult = new CommandResult();
        long startCheckerTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startCheckerTime < timeout) {
            PatchUpdateCacheDto patchUpdateCacheDto = patcherUpdateControllerApi.queryUpdatePatchesProcess(clusterId);
            if (patchUpdateCacheDto == null) {
                log.info("cluster: {} update patch result :true, patch update progress is null.", clusterId);
                commandResult.setInfo("patch update progress is null.");
                commandResult.setSuccess(true);
                return commandResult;
            } else if (patchUpdateCacheDto.isAllFinished()) {
                if (patchUpdateCacheDto.isSuccess()) {
                    log.info("cluster: {} update patch result :success", clusterId);
                    commandResult.setSuccess(true);
                    return commandResult;
                } else {
                    List<PatchUpdateResult> patchResults = patchUpdateCacheDto.getPatchResults();
                    String failedResult = JSON.toJSONString(patchResults);
                    log.info("cluster: {} update patch result:{}", clusterId, failedResult);
                    commandResult.setInfo(failedResult);
                    commandResult.setSuccess(false);
                    return commandResult;
                }
            }
            TimeUnit.MILLISECONDS.sleep(DateUtils.MILLIS_PER_SECOND * 10);
        }
        return new CommandResult("update patch timeout", false, "");
    }

    private UpdateRequest organizeUpdateRequest(List<String> hostIps, PatchTaskDto patchTaskDto) {
        List<ServiceInstancePatchInfo> context = patchTaskDto.getContext();
        UpdateRequest updateRequest = new UpdateRequest();
        if (CollectionUtils.isEmpty(context)) {
            log.warn("get update request is empty: {}", patchTaskDto.getTaskName());
            return updateRequest;
        }
        List<ServiceInstance> services =
            context.stream().map(ServiceInstancePatchInfo::getServiceInstance).collect(Collectors.toList());
        Map<String, ServiceInstance> uniqueMap = services.stream().collect(Collectors
            .toMap(ServiceInstance::getServiceInstanceId, service -> service, (service1, service2) -> service1));
        List<ServiceInstance> uniqueServices = new ArrayList<>(uniqueMap.values());
        List<ServiceInstanceInfo> serviceInstanceInfos =
            organizeServiceInstanceInfos(hostIps, patchTaskDto, uniqueServices);
        updateRequest.setClusterId(patchTaskDto.getClusterId());
        updateRequest.setServiceInstanceInfos(serviceInstanceInfos);
        return updateRequest;
    }

    private List<ServiceInstanceInfo> organizeServiceInstanceInfos(List<String> hostIps, PatchTaskDto patchTaskDto,
        List<ServiceInstance> services) {
        List<ServiceInstanceInfo> serviceInstanceInfos = Lists.newArrayList();
        List<ServiceModel> serviceModels = productModelInfoControllerApi.queryByClusterId(patchTaskDto.getClusterId());
        String zdhVersion = getZdhVersion(serviceModels);
        for (ServiceInstance serviceInfo : services) {
            ServiceInstanceInfo serviceInstanceInfo =
                generateServiceInstanceInfo(patchTaskDto, serviceInfo, zdhVersion, hostIps, serviceModels);

            serviceInstanceInfos.add(serviceInstanceInfo);
        }
        return serviceInstanceInfos;
    }

    private ServiceInstanceInfo generateServiceInstanceInfo(PatchTaskDto patchTaskDto, ServiceInstance serviceInfo,
        String zdhVersion, List<String> hostIps, List<ServiceModel> serviceModels) {
        ProductSpect productSpect = queryProductSpect(patchTaskDto.getClusterId(), serviceInfo.getServiceId());
        ServiceInstanceInfo serviceInstanceInfo = new ServiceInstanceInfo();
        if (productSpect == null) {
            if (!StringUtils.equals(serviceInfo.getServiceId(), ServiceNameEnum.ZDH.getServiceName())
                || StringUtils.equals(zdhVersion, "")) {
                throw new TaskManagementException("product spect is null. {}", serviceInfo.getServiceId());
            }
            serviceInstanceInfo.setServiceName(ServiceNameEnum.ZDH.getServiceName());
            serviceInstanceInfo.setVersion(zdhVersion);
        } else {
            serviceInstanceInfo.setServiceName(productSpect.getServiceName());
            serviceInstanceInfo.setVersion(productSpect.getVersionNo());
        }
        serviceInstanceInfo.setServiceId(serviceInfo.getServiceId());
        serviceInstanceInfo.setServiceInstanceId(serviceInfo.getServiceInstanceId());
        List<String> distributeHosts =
            generateDistributeHosts(patchTaskDto.getClusterId(), serviceInfo, hostIps, serviceModels);
        serviceInstanceInfo.setIps(distributeHosts);
        return serviceInstanceInfo;
    }

    private String getZdhVersion(List<ServiceModel> serviceModels) {
        if (CollectionUtils.isEmpty(serviceModels)) {
            return "";
        } else {
            return serviceModels.stream()
                .filter(e -> StringUtils.equalsIgnoreCase(e.getComponentType(), BIG_DATA_SERVICE_ID))
                .map(ServiceModel::getVersion).findAny().orElse("");
        }
    }

    private ProductSpect queryProductSpect(String clusterId, String serviceId) {
        ServiceModel productServiceModel =
            productModelInfoControllerApi.queryByClusterIdAndServiceId(clusterId, serviceId);
        if (productServiceModel == null) {
            return null;
        }
        String productServiceName = productServiceModel.getServiceName();
        List<ClusterProject> productClusterProjects =
            clusterVersionControllerApi.queryVersions(NumberUtils.toInt(clusterId), productServiceName);
        if (CollectionUtils.isEmpty(productClusterProjects)) {
            return null;
        }
        String version = productClusterProjects.stream().findFirst().orElse(new ClusterProject()).getVersion();
        return spectControllerApi.queryByServiceNameAndVersionNo(productServiceName, version);
    }

    @Override
    public ProcessResult rollback(TaskContext context) {
        return null;
    }
}
