package com.zte.daip.manager.patcher.task.worker.rollback;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.zte.daip.manager.patcher.api.PatchRollbackControllerApi;
import com.zte.daip.manager.patcher.api.dto.*;
import com.zte.daip.manager.patcher.inner.api.PatchTaskControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.*;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.deployer.bean.service.CommandResult;
import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.common.task.worker.processor.BasicProcessor;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class PatcherRollbackProcessor implements BasicProcessor {
    @Autowired
    private PatchRollbackControllerApi patchRollbackControllerApi;

    @Autowired
    private PatchTaskControllerApi patchTaskControllerApi;

    @Value("${patcher.update.timeout.inMinutes:30}")
    private int timeout;

    @Override
    public ProcessResult process(TaskContext context) {
        log.info("[PatcherRollbackProcessor-{}] start to process {} with {}", context.getAtomTaskId(),
            context.getTaskName(), context.getAtomicTaskParams());
        long taskTimeout = context.getTimeout();
        if (taskTimeout <= 0) {
            taskTimeout = timeout;
        }
        long parentTaskId = context.getParentId();
        ProcessResult processResult = rollbackPatch(parentTaskId, taskTimeout * 60 * 1000);
        log.info("processResult: {}", processResult.toString());
        return processResult;
    }

    private ProcessResult rollbackPatch(long taskId, long timeout) {
        boolean isFailed = false;
        CommandResult commandResult = new CommandResult();
        try {
            PatchTaskDto patchTaskDto = patchTaskControllerApi.queryPatchTaskByTaskId(taskId);
            if (patchTaskDto != null) {
                String clusterId = patchTaskDto.getClusterId();
                List<ServiceInstancePatchInfo> context = patchTaskDto.getContext();
                if (!CollectionUtils.isEmpty(context)) {
                    rollback(clusterId, context);
                    commandResult = checkRollbackProgress(clusterId, timeout);
                    if (!commandResult.isSuccess()) {
                        isFailed = true;
                        log.warn("cluster: {}, patch task: {}, failed to rollback patch", clusterId, patchTaskDto);
                    }
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return new ProcessResult(false, e.getMessage());
        } catch (Exception e) {
            return new ProcessResult(false, e.getMessage());
        }
        return isFailed ? ProcessResult.failure(JSON.toJSONString(commandResult.getInfo()).replace("/", "*"))
            : ProcessResult.success();
    }

    private void rollback(String clusterId, List<ServiceInstancePatchInfo> serviceInstancePatchInfos) {
        List<PatchRollbackHostDto> patchRollbackHostDtos = generatePatchRollbackHostDtos(serviceInstancePatchInfos);

        patchRollbackControllerApi.rollbackPatches(clusterId, patchRollbackHostDtos);
    }

    private List<PatchRollbackHostDto>
        generatePatchRollbackHostDtos(List<ServiceInstancePatchInfo> serviceInstancePatchInfos) {
        List<PatchRollbackHostDto> patchRollbackHostDtos = Lists.newArrayList();
        for (ServiceInstancePatchInfo s : serviceInstancePatchInfos) {
            List<RollBackPatchPointInfo> rollBackPatchPoints = s.getRollBackPatchPoints();
            ServiceInstance serviceInstance = s.getServiceInstance();
            if (!CollectionUtils.isEmpty(rollBackPatchPoints)) {
                for (RollBackPatchPointInfo rollBackPatchPointInfo : rollBackPatchPoints) {
                    List<PatchHostInfoDto> patchHostInfos = rollBackPatchPointInfo.getPatchHostInfos();
                    if (!CollectionUtils.isEmpty(patchHostInfos)) {
                        List<PatchHostInfo> patchHosts = patchHostInfos.stream()
                            .map(h -> new PatchHostInfo(h.getIp(), h.getHostName())).collect(Collectors.toList());
                        patchRollbackHostDtos.add(new PatchRollbackHostDto(serviceInstance.getServiceName(),
                            serviceInstance.getRollbackServiceInstanceId(), serviceInstance.getRoleName(),
                            rollBackPatchPointInfo.getRollBackPatchPoint(), patchHosts));
                    }
                }
            }
        }
        return patchRollbackHostDtos;
    }

    private CommandResult checkRollbackProgress(String clusterId, long timeout) throws InterruptedException {
        CommandResult commandResult = new CommandResult();
        long startCheckerTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startCheckerTime < timeout) {
            RollBackPatchProgress rollBackPatchProgress = patchRollbackControllerApi.queryRollBackProgress(clusterId);
            if (rollBackPatchProgress == null) {
                log.info("cluster: {} rollback patch result :true, patch rollback progress is null.", clusterId);
                commandResult.setInfo("patch rollback progress is null.");
                commandResult.setSuccess(true);
                return commandResult;
            } else if (rollBackPatchProgress.isFinish()) {
                return generateCommandResult(clusterId, commandResult, rollBackPatchProgress);
            }
            TimeUnit.MILLISECONDS.sleep(DateUtils.MILLIS_PER_SECOND * 10);
        }
        return new CommandResult("rollback patch timeout", false, "");
    }

    private CommandResult generateCommandResult(String clusterId, CommandResult commandResult,
        RollBackPatchProgress rollBackPatchProgress) {
        if (rollBackPatchProgress.isSuccess()) {
            log.info("cluster: {} rollback patch result :success", clusterId);
            commandResult.setSuccess(true);
            return commandResult;
        } else {
            List<RollbackServiceProgressDto> rollbackServiceProgressDtos =
                rollBackPatchProgress.getRollbackServiceProgressDtos();
            String failedResult;
            if (CollectionUtils.isEmpty(rollbackServiceProgressDtos)) {
                failedResult = "rollback service message is empty";
            } else {
                failedResult = JSON.toJSONString(rollbackServiceProgressDtos.stream()
                    .map(RollbackServiceProgressDto::getRollbackHostProgressDtos).flatMap(Collection::stream)
                    .filter(r -> !r.isSuccess()).map(RollbackHostProgressDto::getMessage).collect(Collectors.toList()));
            }
            log.info("cluster: {} rollback patch result:{}", clusterId, failedResult);
            commandResult.setInfo(failedResult);
            commandResult.setSuccess(false);
            return commandResult;
        }
    }

    @Override
    public ProcessResult rollback(TaskContext context) {
        return null;
    }
}
