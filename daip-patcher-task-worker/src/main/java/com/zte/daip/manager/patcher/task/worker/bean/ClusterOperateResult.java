package com.zte.daip.manager.patcher.task.worker.bean;

import java.util.List;

import lombok.*;

/**
 * 功能描述:<br>
 * <p/>
 * <p/>
 * <p/>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/29
 */
@Getter
@Setter
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ClusterOperateResult {
    private String clusterId;
    private String clusterName;
    private String eventId;
    private List<String> serviceInstanceIds;
    private String operate;
}
