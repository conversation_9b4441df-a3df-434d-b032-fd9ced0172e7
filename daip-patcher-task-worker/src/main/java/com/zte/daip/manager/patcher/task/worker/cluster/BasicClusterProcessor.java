package com.zte.daip.manager.patcher.task.worker.cluster;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zte.daip.manager.common.deployer.api.DeploymentInstanceServiceControllerApi;
import com.zte.daip.manager.common.deployer.bean.model.ServiceModel;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.deployer.model.controller.api.ProductModelInfoControllerApi;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.domain.common.PatchTaskService;
import com.zte.daip.manager.patcher.domain.task.service.PatchTaskOperateService;
import com.zte.daip.manager.patcher.infrastructure.po.PatchTaskPo;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstance;
import com.zte.daip.manager.patcher.inner.api.dto.ServiceInstancePatchInfo;
import com.zte.daip.manager.patcher.task.worker.common.AbstractOperationProcessor;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class BasicClusterProcessor extends AbstractOperationProcessor {

    @Autowired
    private PatchTaskService patchTaskService;

    @Autowired
    private PatchTaskOperateService patchTaskOperateService;

    @Autowired
    private ProductModelInfoControllerApi productModelInfoControllerApi;

    @Autowired
    private DeploymentInstanceServiceControllerApi deploymentInstanceServiceControllerApi;

    protected List<String> filterInstanceForStop(TaskContext context) throws DaipBaseException {
        PatchTaskPo patchTaskPo = queryPatchTaskPo(context);
        if (patchTaskPo.getTaskType() == 1) {
            return queryRestartInstanceFromPo(patchTaskPo);
        } else {
            return queryUpdateRestartService(patchTaskPo);
        }

    }

    protected List<String> filterInstanceForStart(TaskContext context) throws DaipBaseException {
        return queryRestartInstanceFromPo(queryPatchTaskPo(context));
    }

    private List<String> filterCanRestart(String clusterId, List<String> instanceIds) {
        List<DeploymentServiceInstance> serviceInstances = queryInstanceById(clusterId, instanceIds);
        return serviceInstances.stream().filter(this::isServiceStatusValid).filter(this::isSupportClusterRestart)
            .map(DeploymentServiceInstance::getServiceInstanceId).distinct().collect(Collectors.toList());
    }

    private PatchTaskPo queryPatchTaskPo(TaskContext context) throws DaipBaseException {
        Long taskId = context.getParentId();
        PatchTaskPo patchTaskPo = patchTaskService.queryByTaskId(taskId);
        if (patchTaskPo == null) {
            throw new DaipBaseException(String.format("failed to query patch task from db, taskId: %d .", taskId));
        }
        return patchTaskPo;
    }

    private List<String> queryRestartInstanceFromPo(PatchTaskPo patchTaskPo) {
        if (!StringUtils.isEmpty(patchTaskPo.getNeedRestartServices())) {
            List<ServiceInstance> needRestartServices = JSONObject.parseObject(patchTaskPo.getNeedRestartServices(),
                new TypeReference<List<ServiceInstance>>() {});
            List<String> instanceIds = needRestartServices.stream().map(ServiceInstance::getServiceInstanceId)
                .distinct().collect(Collectors.toList());
            return filterCanRestart(patchTaskPo.getClusterId(), instanceIds);
        } else {
            log.warn("failed to query need restart services during rollback");
            return new ArrayList<>();
        }
    }

    private List<String> queryUpdateRestartService(PatchTaskPo patchTaskPo) throws DaipBaseException {
        String clusterId = patchTaskPo.getClusterId();
        List<ServiceInstancePatchInfo> serviceInstancePatchInfos =
            JSONObject.parseObject(patchTaskPo.getContext(), new TypeReference<List<ServiceInstancePatchInfo>>() {});
        List<String> serviceInstanceIds = serviceInstancePatchInfos.stream()
            .map(info -> info.getServiceInstance().getServiceInstanceId()).distinct().collect(Collectors.toList());
        List<String> instanceIds =
            patchTaskOperateService.queryNeedRestartServiceInstance(clusterId, serviceInstanceIds);
        return filterCanRestart(patchTaskPo.getClusterId(), instanceIds);
    }

    private List<DeploymentServiceInstance> queryInstanceById(String clusterId, List<String> instanceIds) {
        return instanceIds.stream()
            .map(id -> deploymentInstanceServiceControllerApi.queryByClusterIdAndServiceInstanceId(clusterId, id))
            .collect(Collectors.toList());
    }

    /* Started by AICoder, pid:d6ef174cc6xcb8b14c200b7c50b2fc12728497d8 */
    /**
     * 检查服务实例是否支持集群重启
     *
     * @param deploymentServiceInstance 服务实例部署信息
     * @return 如果支持集群重启返回true，否则返回false
     */
    private boolean isSupportClusterRestart(DeploymentServiceInstance deploymentServiceInstance) {
        // 查询集群ID和服务ID对应的服务模型
        ServiceModel serviceModel = productModelInfoControllerApi.queryByClusterIdAndServiceId(
                deploymentServiceInstance.getClusterId(), deploymentServiceInstance.getServiceId());

        // 如果服务模型不为空并且支持集群重启，则返回true，否则返回false
        return serviceModel != null && serviceModel.isClusterRestart();
    }

    /* Ended by AICoder, pid:d6ef174cc6xcb8b14c200b7c50b2fc12728497d8 */

    protected abstract boolean isServiceStatusValid(DeploymentServiceInstance deploymentServiceInstance);
}
