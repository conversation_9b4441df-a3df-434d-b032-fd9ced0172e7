/**
 * <p>
 * <owner>10332891</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: StartClusterProcessor.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/8/10
 * </p>
 * <p>
 * 完成日期：2023/8/10
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.task.worker.cluster;

import java.util.List;

import com.zte.daip.manager.common.deployer.api.StartAndStopClusterControllerApi;
import com.zte.daip.manager.common.monitor.api.ServiceStatusQueryControllerApi;
import com.zte.daip.manager.common.monitor.bean.ServiceStatusEnum;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.task.worker.bean.ClusterOperateResult;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.zte.daip.manager.common.deployer.bean.exception.DaipDeployerException;
import com.zte.daip.manager.common.deployer.bean.service.DeploymentServiceInstance;
import com.zte.daip.manager.common.task.common.exception.TaskManagementException;
import com.zte.daip.manager.common.task.worker.processor.IProcessorListener;
import com.zte.daip.manager.common.task.worker.processor.ListenableBasicProcessor;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class StopClusterProcessor extends BasicClusterProcessor
    implements ListenableBasicProcessor<ClusterOperateResult, String> {
    @Autowired
    private StartAndStopClusterControllerApi startAndStopClusterControllerApi;

    @Autowired
    private ServiceStatusQueryControllerApi serviceStatusQueryControllerApi;

    @Autowired
    private ClusterOperationListener clusterOperationListener;

    @Override
    public ClusterOperateResult process(TaskContext context) throws DaipDeployerException, TaskManagementException, DaipBaseException {
        validTaskContext(context);
        String clusterId = String.valueOf(context.getWorkflowContext().getWfContext().getClusterId());
        String clusterName = context.getWorkflowContext().getWfContext().getClusterName();
        List<String> instanceIds = filterInstanceForStop(context);
        log.info("[StopClusterProcessor]: Status is valid and supports start/stop instances: {}", instanceIds);
        if (CollectionUtils.isEmpty(instanceIds)) {
            return ClusterOperateResult.builder().clusterId(clusterId).clusterName(clusterName)
                .serviceInstanceIds(instanceIds).eventId("").operate("stop").build();
        }
        String eventId = startAndStopClusterControllerApi.stopCluster(clusterId, instanceIds);

        return ClusterOperateResult.builder().clusterId(clusterId).clusterName(clusterName)
            .serviceInstanceIds(instanceIds).eventId(eventId).operate("stop").build();
    }

    @Override
    protected boolean isServiceStatusValid(DeploymentServiceInstance deploymentServiceInstance) {
        String serviceStatus = serviceStatusQueryControllerApi.calculate(deploymentServiceInstance.getClusterId(),
            deploymentServiceInstance.getServiceId(), deploymentServiceInstance.getServiceInstanceId());
        return !StringUtils.equals(serviceStatus, ServiceStatusEnum.STOPPED.getStatus());
    }

    @Override
    public ClusterOperateResult rollback(TaskContext context) {
        return null;
    }

    @Override
    public IProcessorListener<ClusterOperateResult, String> getListener() {
        return clusterOperationListener;
    }
}