package com.zte.daip.manager.patcher.task.worker.host;

import com.alibaba.fastjson.JSON;
import com.zte.daip.manager.common.cache.service.RedisCacheService;
import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.common.task.common.worker.WfContext;
import com.zte.daip.manager.common.task.worker.processor.BasicProcessor;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.common.task.worker.processor.WorkflowContext;
import com.zte.daip.manager.patcher.infrastructure.bean.IpMigrationBean;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchDispatchModifyIpService;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchHistoryModifyIpService;
import com.zte.daip.manager.patcher.infrastructure.repository.PatchRollbackModifyIpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class IpAddressModifyProcessor implements BasicProcessor<ProcessResult> {

    @Autowired
    private PatchHistoryModifyIpService patchHistoryModifyIpService;

    @Autowired
    private PatchRollbackModifyIpService patchRollbackModifyIpService;

    @Autowired
    private PatchDispatchModifyIpService patchDispatchModifyIpService;

    @Autowired
    private RedisCacheService redisCacheService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessResult process(TaskContext context) {
        log.info("receive patch ip migration request:taskId={},taskName={}", context.getTaskId(),
                context.getTaskName());

        List<IpMigrationBean> ipMigrationBeans = parseIpMigrationBean(context);
        if (CollectionUtils.isEmpty(ipMigrationBeans)) {
            return ProcessResult.failure("Ip migration data error");
        }

        //修改数据库ip字段
        patchHistoryModifyIpService.batchModifyHostIps(ipMigrationBeans);
        patchRollbackModifyIpService.batchModifyHostIps(ipMigrationBeans);
        patchDispatchModifyIpService.batchModifyHostIps(ipMigrationBeans);
        //清理redis缓存
        redisCacheService.clearAll();
        return ProcessResult.success();
    }

    private static List<IpMigrationBean> parseIpMigrationBean(TaskContext context) {
        try {
            String data =
                    Optional.ofNullable(context).map(TaskContext::getWorkflowContext).map(WorkflowContext::getWfContext)
                            .map(WfContext::getData).orElseThrow(() -> new IllegalArgumentException("TaskContext"));
            List<IpMigrationBean> ipMigrationInfos = JSON.parseArray(data, IpMigrationBean.class);
            return ipMigrationInfos;
        } catch (Exception e) {
            log.error("occur error in parse processorJson", e);
        }
        return new ArrayList<>();
    }

    @Override
    public ProcessResult rollback(TaskContext context) {
        return null;
    }
}
