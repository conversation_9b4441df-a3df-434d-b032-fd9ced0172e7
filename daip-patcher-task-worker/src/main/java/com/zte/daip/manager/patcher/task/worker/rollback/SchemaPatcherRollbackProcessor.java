/**
 * <p>
 * <owner>10233482</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: SchemaPatcherRollbackProcessor.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/11/30
 * </p>
 * <p>
 * 完成日期：2023/11/30
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.task.worker.rollback;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.common.task.worker.processor.BasicProcessor;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;
import com.zte.daip.manager.patcher.api.PatchRollbackControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.inner.api.PatchTaskControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR> 10233482
 * @version 1.0
 */
@Slf4j
@Service
public class SchemaPatcherRollbackProcessor implements BasicProcessor {

    @Autowired
    private PatchRollbackControllerApi patchRollbackControllerApi;

    @Autowired
    private PatchTaskControllerApi patchTaskControllerApi;

    @Override
    public ProcessResult process(TaskContext context) {
        log.info("[PatcherRollbackProcessor-{}] start to process {} with {}", context.getAtomTaskId(),
            context.getTaskName(), context.getAtomicTaskParams());
        long parentTaskId = context.getParentId();
        ProcessResult processResult = rollbackSchemaPatch(parentTaskId);
        log.info("processResult: {}", processResult.toString());
        return processResult;
    }

    @Override
    public Object rollback(TaskContext context) {
        return null;
    }

    private ProcessResult rollbackSchemaPatch(long taskId) {
        boolean isFailed = false;
        List<String> infos = Lists.newArrayList();
        try {

            PatchTaskDto patchTaskDto = patchTaskControllerApi.queryPatchTaskByTaskId(taskId);
            if (patchTaskDto != null) {
                List<PatchOperateResult> schemaPatchUpdateResults =
                    patchRollbackControllerApi.rollbackSchemaPatches(taskId);
                for (PatchOperateResult schemaPatchUpdateResult : schemaPatchUpdateResults) {
                    if (!schemaPatchUpdateResult.isStatus()) {
                        isFailed = true;
                        infos.add(schemaPatchUpdateResult.getMessage());
                        log.info("failed to rollback schema patch,message={}", schemaPatchUpdateResult.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            return new ProcessResult(false, e.getMessage());
        }
        return isFailed ? ProcessResult.failure(JSON.toJSONString(infos).replace("/", "*")) : ProcessResult.success();
    }
}