package com.zte.daip.manager.patcher.task.worker.version;

import com.zte.daip.manager.filemanagement.api.FileDistributeQueryControllerApi;
import com.zte.daip.manager.filemanagement.api.progress.DistributeSummaryProgress;
import com.zte.daip.manager.patcher.task.worker.bean.PatchDistributeInfo;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.daip.manager.common.task.common.exception.TaskManagementException;
import com.zte.daip.manager.common.task.common.task.result.ProcessResult;
import com.zte.daip.manager.common.task.worker.processor.IProcessorListener;
import com.zte.daip.manager.filemanagement.api.FileDistributeControllerApi;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 * <p/>
 * <p/>
 * <p/>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/23
 */
@Slf4j
@Service
public class PatcherDistributeListener implements IProcessorListener<PatchDistributeInfo, DistributeSummaryProgress> {
    @Autowired
    private FileDistributeControllerApi fileDistributeControllerApi;

    @Autowired
    private FileDistributeQueryControllerApi fileDistributeQueryControllerApi;

    @Override
    public ProcessResult<PatchDistributeInfo, DistributeSummaryProgress>
        doListen(PatchDistributeInfo patchDistributeInfo) {
        log.info("start to check patcher distribute.");
        if (patchDistributeInfo == null || StringUtils.isBlank(patchDistributeInfo.getDistributeId())) {
            throw new TaskManagementException("distributeId an not be blank!");
        }
        try {
            String distributeId = patchDistributeInfo.getDistributeId();
            log.info("PatcherDistributeListener {}", distributeId);
            int fileSize = patchDistributeInfo.getFileSize();
            ProcessResult<PatchDistributeInfo, DistributeSummaryProgress> result = new ProcessResult<>();
            if (fileSize == 0) {
                log.info("no patch need distribute {}", distributeId);
                result.setSuccess(true);
                result.setFinished(true);
                result.setListenerResult(genDefaultTrueDistributeSummaryProgress(distributeId));
            } else {
                DistributeSummaryProgress distributeSummaryProgress =
                    fileDistributeQueryControllerApi.querySummaryProgress(distributeId);
                log.info("query {} distribute progress {}", distributeId, distributeSummaryProgress.toString());
                result.setSuccess(distributeSummaryProgress.isSuccess());
                result.setFinished(distributeSummaryProgress.isFinish());
                result.setListenerResult(distributeSummaryProgress);
            }
            return result;
        } catch (Exception e) {
            throw new TaskManagementException("distributeId listener error!", e);
        }

    }

    private DistributeSummaryProgress genDefaultTrueDistributeSummaryProgress(String distributeId) {
        DistributeSummaryProgress distributeSummaryProgress = new DistributeSummaryProgress();
        distributeSummaryProgress.setDistributeId(distributeId);
        distributeSummaryProgress.setSuccess(true);
        distributeSummaryProgress.setFinish(true);
        return distributeSummaryProgress;
    }
}
