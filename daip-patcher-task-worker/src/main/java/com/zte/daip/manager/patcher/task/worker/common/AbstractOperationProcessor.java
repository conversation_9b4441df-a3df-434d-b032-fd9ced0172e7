package com.zte.daip.manager.patcher.task.worker.common;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.daip.manager.common.task.common.exception.TaskManagementException;
import com.zte.daip.manager.common.task.common.step.TaskExecutor;
import com.zte.daip.manager.common.task.common.worker.ServiceInfo;
import com.zte.daip.manager.common.task.worker.processor.TaskContext;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractOperationProcessor {

    protected void validTaskContext(TaskContext context) throws TaskManagementException {
        if (context.getWorkflowContext() == null
                || context.getWorkflowContext().getWfContext() == null
                || context.getWorkflowContext().getWfContext().getClusterId() == null) {
            log.error("[atomTaskId-{}] workflow context can not be null!", context.getAtomTaskId());
            throw new TaskManagementException("workflow context can not be null!");
        }
    }

    protected void validTaskContextService(TaskContext context) throws TaskManagementException {
        if (context.getWorkflowContext() == null
                || context.getWorkflowContext().getWfContext() == null
                || context.getWorkflowContext().getWfContext().getClusterId() == null
                || (org.springframework.util.CollectionUtils.isEmpty(context.getWorkflowContext().getWfContext().getService()) && !isAllService(context))) {
            log.error("[atomTaskId-{}] workflow context service can not be null!", context.getAtomTaskId());
            throw new TaskManagementException("workflow context service can not be null!");
        }
    }

    protected void validTaskContextHost(TaskContext context) throws TaskManagementException {
        if (context.getWorkflowContext() == null
                || context.getWorkflowContext().getWfContext() == null
                || context.getWorkflowContext().getWfContext().getClusterId() == null
                || (org.springframework.util.CollectionUtils.isEmpty(context.getWorkflowContext().getWfContext().getHostIp()) && !isAllHost(context))) {
            log.error("[atomTaskId-{}] workflow context host can not be null!", context.getAtomTaskId());
            throw new TaskManagementException("workflow context host can not be null!");
        }
    }

    protected Boolean isAllService(TaskContext context) {
        JSONObject jsonObject = JSON.parseObject(Optional.ofNullable(context.getTaskExecutor()).orElse(new TaskExecutor()).getParam());
        return Optional.ofNullable(jsonObject).map(obj -> obj.getObject("isAllService", Boolean.class)).orElse(false);
    }

    protected Boolean isAllHost(TaskContext context) {
        JSONObject jsonObject = JSON.parseObject(Optional.ofNullable(context.getTaskExecutor()).orElse(new TaskExecutor()).getParam());
        return Optional.ofNullable(jsonObject).map(obj -> obj.getObject("isAllHost", Boolean.class)).orElse(false);
    }

    protected Set<String> getInstanceIdFromContext(TaskContext context) {
        List<ServiceInfo> service = context.getWorkflowContext().getWfContext().getService();
        Set<String> instanceIdFromContext = service.stream().map(ServiceInfo::getServiceInstanceId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        return instanceIdFromContext;
    }
}
