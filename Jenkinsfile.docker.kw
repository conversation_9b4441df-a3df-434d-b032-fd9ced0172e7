@Library('zy-share-lib') _


def set_kw_config_string(report_path, report_name, kw_compile_cmd, check_type)
	{
		config  = """docker_image=atm-build-kw
		docker_run_parameters="--rm -i -v ${WORKSPACE}:/local/code -v ${base_data_path}:/local/${report_name} -v ${rule_path}:/local/code/klocwork/conf --add-host ${nexusHost}:${nexusIp} -u ${user}"
		kw_project_url=http://************:8082/${ccaProjectName}
		kw_tables_path=/local/${report_name}
		report_path=${report_path}
		report_name=${report_name}
		check_type=${check_type}
		check_level_max=${check_leve}
		kw_compile_cmd="${kw_compile_cmd}"
		kw_compile_code_path=/local/code
		kw_before_check_cmd=""
		kw_after_check_cmd=""
		kw_check_rules_path=/local/code/klocwork/conf
		kw_check_lang=java
		kw_user=kw
		kw_passwd=""
		"""
		return config

	}

node('KW') {
	timestamps {
		stage('Preparation') {
			checkout scm
		}
		stage('KW Scan') {
            sh "rm -rf git.diff"
            sh "git diff HEAD^ HEAD --numstat > git.diff"
            if(! readFile("git.diff").contains(".java")) {
                return
            }
		    sh 'sh kw-env.sh'
			report_path = "$WORKSPACE/klocwork/report"
			report_name = "${report_name}"
			kw_compile_cmd ="mvn clean package -DskipTests"
			check_type = "${checkType}"
			config_string = set_kw_config_string(report_path, report_name, kw_compile_cmd, check_type)
			mybgkw_dfs_flr = kw.kw_check(config_string)
		}
	}
}
