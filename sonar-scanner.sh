#!/bin/bash

# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

set -e               # exit on error

cd "$(dirname "$0")" # connect to root

if [ "$(uname -s)" == "Linux" ]; then
  USER_NAME=${SUDO_USER:=$USER}
  USER_ID=$(id -u "${USER_NAME}")
  GROUP_ID=$(id -g "${USER_NAME}")
else # boot2docker uid and gid
  USER_NAME=$USER
  USER_ID=1000
  GROUP_ID=50
fi

docker build -t "feagle-sonar-${USER_NAME}" - <<UserSpecificDocker
FROM dap-release-docker.artnj.zte.com.cn/cci/dap/sonar_scanner:v1
RUN groupadd --non-unique -g ${GROUP_ID} ${USER_NAME}
RUN useradd -g ${GROUP_ID} -u ${USER_ID} -k /root -m ${USER_NAME}
ENV HOME /home/<USER>
RUN locale-gen en_US.UTF-8
ENV LANG='en_US.UTF-8' LANGUAGE='en_US:en' LC_ALL='en_US.UTF-8'
ENV YETUS_VERSION 0.7.1
UserSpecificDocker

if [ -t 0 ]; then
  INTERACTIVE_OPTS="-ti"
else
  INTERACTIVE_OPTS=""
fi

docker run --rm=true ${INTERACTIVE_OPTS}\
  --dns="10.40.8.8" --dns="10.30.8.8"\
  -v "${PWD}:/home/<USER>/feagle" \
  -w "/home/<USER>/feagle" \
  -v "${HOME}/.m2:/home/<USER>/.m2" \
  -v "/tmp:/tmp" \
  -u "${USER_NAME}" \
  feagle-sonar-${USER_NAME} $*
