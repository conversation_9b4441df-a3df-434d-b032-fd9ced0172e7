# -*-coding:utf-8 -*-
##1.读取mergeCI中代码库指定位置的csv文件
##2.checkout Testcase代码到当前workspace指定文件夹位置,使用http方式
##解析csv为txt

from  openpyxl import Workbook
from openpyxl import load_workbook
import codecs,os
import sys
reload(sys)
sys.setdefaultencoding('utf-8')
def obtain_xlsx_book(xlsx_path , xlsx_name):
  wb = load_workbook(os.path.join(xlsx_path, xlsx_name))  ##加载xlsx
  return wb

def get_sheet_dest_path(sheet_content):
  dict_row = {}
  column_list = [cell.value for cell in sheet_content[1]]
  for i in range(1,len(column_list)+1):
    dict_row[column_list[i-1]]   = sheet_content[2][i-1].value
  return dict_row['dest_path']

def write_content_into_txt(txt_name, sheet_temp):
  with codecs.open(txt_name, 'w+', 'utf-8') as f:
    f.write('*** Test Cases ***' + '\n')
    row_len = sheet_temp.max_row
    col_len = sheet_temp.max_column
    column_list = [cell.value for cell in sheet_temp[1]]  ##将每一行数据暂存在字典中，字典的键为列名，值为单元格的值
    for row in range(2, row_len + 1):
      dict_temp = {}
      for col in range(1, col_len + 1):
        dict_temp[column_list[col - 1]] = sheet_temp.cell(row=row, column=col).value
      print dict_temp
      for key, value in dict_temp.items():
        print key,value
        if dict_temp[key] is None:
          dict_temp[key] = "    "
        elif  "\n" or "\r" in dict_temp[key]:
          dict_temp[key] = value.replace("\n","")

      f.write(dict_temp['TestCaseName'] + '\n')
      f.write(
        '    ' + '[Tags]' + '    ' + dict_temp['PR'] + '    ' + dict_temp['priority'] + '    ' + dict_temp['layer-ST'] +
        '    ' + dict_temp['author'] + '    ' + dict_temp['auto'] + '    ' + dict_temp['canauto-yes'] + '    ' + dict_temp[
          'type'] + '\n')
      f.write('    Given ' + dict_temp['Given'] + '\n')
      f.write('    When ' + dict_temp['When'] + '\n')
      f.write('    Then ' + dict_temp['Then'] + '\n')
      f.write('\n')

def xlsx2txt(xlsx_path, xlsx_name, txt_path):
    wb1 = obtain_xlsx_book(xlsx_path, xlsx_name)
    for sheetname in wb1.sheetnames:  ##遍历读取sheet
      sheet_temp = wb1[sheetname]
      testcase_path = get_sheet_dest_path(sheet_temp)
      txt_name = unicode(os.path.join(txt_path, testcase_path,sheetname +'.txt'))  ##中文编码解决
      write_content_into_txt(txt_name, sheet_temp)

if __name__ == '__main__':
  xlsx_path = sys.argv[1]
  xlsx_name = sys.argv[2]
  txt_path = sys.argv[3]
  if len(sys.argv) != 4:
    print("parameter count error,current parameter count is {0}".format(len(sys.argv)))
  # try:
  xlsx2txt(xlsx_path, xlsx_name, txt_path)
  # except:
  #   print("transfer xlsx to txt failed")