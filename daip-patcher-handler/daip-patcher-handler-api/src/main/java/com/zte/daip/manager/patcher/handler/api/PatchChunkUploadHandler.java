/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchChunkUploadHandler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/11/17
 * </p>
 * <p>
 * 完成日期：2021/11/17
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.api;

import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.file.chunk.FileEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Validated
public interface PatchChunkUploadHandler {

    @ApiOperation(value = "上传文件分片", notes = "上传文件分片")
    @PostMapping("/patches/chunk/upload")
    String uploadFileChunk(@RequestPart("file") MultipartFile file, @RequestParam("chunk") Integer chunk,
                           @Pattern(regexp = "^[A-Za-z0-9]+$", message = "md5 type error") @RequestParam("fileMd5") String fileMd5);

    @ApiOperation(value = "验证文件是否存在", notes = "验证文件是否存在")
    @PostMapping("/patches/file/exists")
    String checkFileExists(@RequestBody FileEntity fileEntity);

    @ApiOperation(value = "获取已上传的分片下标", notes = "获取已上传的分片下标")
    @PostMapping("/patches/chunk/index")
    String getUploadChunkIndex(@RequestBody FileEntity fileEntity);

    @ApiOperation(value = "补丁分片合并", notes = "补丁分片合并")
    @PostMapping("/patches/chunk/merge")
    String mergeFile(@Valid @RequestBody FileEntity fileEntity);

    @ApiOperation(value = "补丁分片删除", notes = "补丁分片删除")
    @PostMapping("/patches/chunk/delete")
    String deleteChunkFile(
        @Pattern(regexp = "^[A-Za-z0-9]+$", message = "md5 type error") @RequestParam("fileMd5") String fileMd5);

    @ApiOperation(value = "上传补丁通知", notes = "上传补丁通知")
    @PostMapping(value = "/patches/merge/notify")
    String notifyUploadPatches(@RequestBody List<String> pathNameList) throws DaipBaseException;
}