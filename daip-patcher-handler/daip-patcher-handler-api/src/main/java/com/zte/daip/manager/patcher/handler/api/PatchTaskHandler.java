package com.zte.daip.manager.patcher.handler.api;

import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateResult;
import com.zte.daip.manager.patcher.inner.api.dto.VersionQueryRequestInfo;
import com.zte.daip.manager.patcher.inner.api.dto.PatchTaskDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface PatchTaskHandler {
    @ApiOperation(value = "新增补丁任务", notes = "新增补丁任务")
    @PostMapping(value = "/patch/task/create")
    String createPatchTask(@RequestBody PatchTaskDto patchTaskDto);

    @ApiOperation(value = "修改补丁任务", notes = "修改补丁任务")
    @PostMapping(value = "/patch/task/modify")
    String modifyPatchTask(@RequestBody PatchTaskDto patchTaskDto);

    @ApiOperation(value = "删除补丁任务", notes = "删除补丁任务")
    @PostMapping(value = "/patch/task/remove")
    String removePatchTask(@RequestBody List<Long> taskIds);

    @ApiOperation(value = "查询补丁任务", notes = "查询补丁任务")
    @GetMapping(value = "/patch/tasks")
    String queryPatchTasks(@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize,
        @RequestParam(value = "taskName", required = false) String taskName);

    @ApiOperation(value = "根据任务ID查询补丁任务详情", notes = "根据任务ID查询补丁任务详情")
    @GetMapping(value = "/patch/task/detail/{taskId}")
    String queryPatchTaskDetailByTaskId(@PathVariable(value = "taskId") long taskId);

    /**
     * 获取跳转任务详情的url，金库二次授权使用
     * @return
     */
    @ApiOperation(value = "获取跳转任务详情的url", notes = "获取跳转任务详情的url，金库二次授权使用")
    @GetMapping("/patch/task/get/detail/url")
    String queryPatchTaskDetailUrl();

    @ApiOperation(value = "根据任务ID查询补丁任务", notes = "根据任务ID查询补丁任务")
    @GetMapping(value = "/patch/task/{taskId}")
    String queryPatchTaskByTaskId(@PathVariable(value = "taskId") long taskId);

    @ApiOperation(value = "执行补丁任务", notes = "执行补丁任务")
    @PostMapping(value = "/patch/task/trigger")
    String triggerPatchTask(@RequestParam(value = "taskId") long taskId, @RequestParam("taskName") String taskName);

    @ApiOperation(value = "重试补丁任务", notes = "重试补丁任务")
    @PostMapping(value = "/patch/task/retry")
    String retryPatchTask(@RequestParam(value = "taskId") long taskId, @RequestParam("taskName") String taskName);

    @ApiOperation(value = "复制补丁任务", notes = "复制补丁任务")
    @PostMapping(value = "/patch/task/copy")
    String copyPatchTask(@RequestParam(value = "taskId") long taskId, @RequestParam("taskName") String taskName);

    @ApiOperation(value = "检查任务是否可回退", notes = "检查任务是否可回退")
    @PostMapping(value = "/patch/task/checkRollback")
    PatchOperateResult checkTaskRollback(@RequestParam(value = "taskId") long taskId);

    @ApiOperation(value = "检查任务是否可复制", notes = "检查任务是否可复制")
    @PostMapping(value = "/patch/task/checkDuplicate")
    PatchOperateResult checkTaskDuplicate(@RequestParam(value = "taskId") long taskId);

    @ApiOperation(value = "暂停补丁任务", notes = "暂停补丁任务")
    @PostMapping(value = "/patch/task/pause")
    String pausePatchTask(@RequestParam(value = "taskId") long taskId, @RequestParam("taskName") String taskName);

    @ApiOperation(value = "继续补丁任务", notes = "继续补丁任务")
    @PostMapping(value = "/patch/task/resume")
    String resumePatchTask(@RequestParam(value = "taskId") long taskId, @RequestParam("taskName") String taskName);

    @ApiOperation(value = "查询补丁升级服务实例", notes = "查询补丁升级服务实例")
    @PostMapping(value = "/patch/serviceInstance/query")
    String queryServiceInstanceUpgrade(@RequestBody @Valid VersionQueryRequestInfo versionQueryRequestInfo);

    @ApiOperation(value = "回退补丁任务", notes = "回退补丁任务")
    @PostMapping(value = "/patch/task/rollback")
    String rollbackPatchTask(@RequestBody PatchTaskDto patchTaskDto);

    @ApiOperation(value = "查询待重启服务", notes = "查询待重启服务")
    @PostMapping(value = "/patch/restartService/query")
    String queryNeedRestartService(@RequestBody PatchTaskDto patchTaskDto);
}
