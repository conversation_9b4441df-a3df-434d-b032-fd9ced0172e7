/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatcherUpdateHandler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/14
 * </p>
 * <p>
 * 完成日期：2021/4/14
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.api;

import org.springframework.web.bind.annotation.*;

import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface PatcherUpdateHandler {

    @PostMapping(value = "/patches/update")
    String updatePatch(@RequestBody UpdateRequest updateRequest);

    @GetMapping(value = "/patches/updating/process/cluster/{clusterId}")
    PatchUpdateCacheDto updatePatchesProcess(@PathVariable("clusterId") String clusterId);

    @GetMapping(value = "/patches/update/permit/cluster/{clusterId}")
    boolean isUpdatePatchPermit(@PathVariable("clusterId") String clusterId);

    @GetMapping(value = "/patches/update/query")
    String queryNeedUpdatePatchInfo(@RequestParam("patchRequestKey") String patchRequestKey,
        @RequestParam("hostIp") String hostIp);
}