/**
 * <p><owner>10208178</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PendingUpdatePatchPageTableBean.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2021/3/16</p>
 * <p>完成日期：2021/3/16</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.api.dto;

import com.zte.daip.manager.common.utils.paging.bean.PageTableBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "补丁页面查询分页对象")
public class PatchPageTableDto extends PageTableBean
{
    @ApiModelProperty(value = "补丁类型", example = "zookeeper", dataType = "String")
    private String patchType;
    @ApiModelProperty (value = "搜索关键字", example = "DAP-ZOOKEEPER-V20.20.40.08-SP001-20210111", dataType = "String")
    private String searchKeyWord;
}