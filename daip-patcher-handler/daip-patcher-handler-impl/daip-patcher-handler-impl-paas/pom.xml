<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>daip-patcher-handler-impl</artifactId>
        <groupId>com.zte.daip.manager.patcher</groupId>
        <version>deletePatch</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>daip-patcher-handler-impl-paas</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-web-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-starter-rpc-retrofit-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-handler-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-patcher-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-i18n-tcf</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-patcher-impl-client-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-inner-api</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.patcher</groupId>
            <artifactId>daip-patcher-impl-inner-client-paas</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-event-reporter-client</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zte.oes.dexcloud</groupId>
            <artifactId>dexcloud-springboot-uiframe-agent-impl-jdk21</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zte.daip.manager.common</groupId>
            <artifactId>daip-sensitive-log</artifactId>
            <version>${depend.svr.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>


    <profiles>
        <profile>
            <id>paas</id>
            <properties>
                <app.mainclass>
                    com.zte.daip.manager.patcher.handler.impl.paas.PatcherHandlerApplication
                </app.mainclass>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <finalName>${handler.microservice.name}</finalName>
                            <appendAssemblyId>false</appendAssemblyId>
                            <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                            <descriptors>
                                <descriptor>src/assembly/assembly_handler_inner_tar.xml</descriptor>
                            </descriptors>
                            <overrideUid>${non.root.uid}</overrideUid>
                            <overrideGid>${non.root.uid}</overrideGid>
                        </configuration>
                        <executions>
                            <execution>
                                <id>assembly</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>cpaas</id>
            <properties>
                <kafka.bootstrap.servers.url>${kafka_brokers}:${kafka_port}
                </kafka.bootstrap.servers.url>
            </properties>
        </profile>

        <profile>
            <id>tcf</id>
            <properties>
                <kafka.bootstrap.servers.url>'[daip-kafka-default]:19092'</kafka.bootstrap.servers.url>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>**</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <!--<plugin>
                <groupId>com.github.kongchen</groupId>
                <artifactId>swagger-maven-plugin</artifactId>
                <version>3.1.7</version>
                <configuration>
                    <apiSources>
                        <apiSource>
                            <springmvc>true</springmvc>
                            <locations>
                                <location>com.zte.daip.manager.patcher.handler</location>
                                <location>com.zte.daip.manager.patcher.api</location>
                                <location>com.zte.daip.manager.common.utils.paging.bean</location>
                            </locations>
                            <schemes>
                                <scheme>
                                    http
                                </scheme>
                            </schemes>
                            <host>server:28001</host>
                            <basePath>/api/daip-patcher-handler/v1</basePath>
                            <info>
                                <title>Patcher-Handler-API</title>
                                <version>v1</version>
                                <description>
                                    Patcher-Handler-API
                                </description>
                            </info>
                            <templatePath>${project.basedir}/src/templates/strapdown.html.hbs
                            </templatePath>
                            <outputPath>${project.basedir}/target/generated/document.html
                            </outputPath>
                            <swaggerDirectory>${project.basedir}/target/generated/swagger-ui
                            </swaggerDirectory>
                        </apiSource>
                    </apiSources>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>-->
        </plugins>
    </build>
</project>