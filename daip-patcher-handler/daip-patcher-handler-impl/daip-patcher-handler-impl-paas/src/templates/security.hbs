{{#each securityDefinitions}}
### {{@key}}
{{#this}}
{{#ifeq type "oauth2"}}
<table>
    <tr>
        <th>type</th>
        <th colspan="2">{{type}}</th>
    </tr>
{{#if description}}
        <tr>
            <th>description</th>
            <th colspan="2">{{description}}</th>
        </tr>
{{/if}}
{{#if authorizationUrl}}
        <tr>
            <th>authorizationUrl</th>
            <th colspan="2">{{authorizationUrl}}</th>
        </tr>
{{/if}}
{{#if flow}}
        <tr>
            <th>flow</th>
            <th colspan="2">{{flow}}</th>
        </tr>
{{/if}}
{{#if tokenUrl}}
        <tr>
            <th>tokenUrl</th>
            <th colspan="2">{{tokenUrl}}</th>
        </tr>
{{/if}}
{{#if scopes}}
    <tr>
        <td rowspan="3">scopes</td>
{{#each scopes}}
            <td>{{@key}}</td>
            <td>{{this}}</td>
        </tr>
        <tr>
{{/each}}
    </tr>
{{/if}}
</table>
{{/ifeq}}
{{#ifeq type "apiKey"}}
<table>
    <tr>
        <th>type</th>
        <th colspan="2">{{type}}</th>
    </tr>
{{#if description}}
        <tr>
            <th>description</th>
            <th colspan="2">{{description}}</th>
        </tr>
{{/if}}
{{#if name}}
        <tr>
            <th>name</th>
            <th colspan="2">{{name}}</th>
        </tr>
{{/if}}
{{#if in}}
        <tr>
            <th>in</th>
            <th colspan="2">{{in}}</th>
        </tr>
{{/if}}
</table>
{{/ifeq}}
{{#ifeq type "basic"}}
<table>
    <tr>
        <th>type</th>
        <th colspan="2">{{type}}</th>
    </tr>
{{#if description}}
        <tr>
            <th>description</th>
            <th colspan="2">{{description}}</th>
        </tr>
{{/if}}
</table>
{{/ifeq}}
{{/this}}
{{/each}}