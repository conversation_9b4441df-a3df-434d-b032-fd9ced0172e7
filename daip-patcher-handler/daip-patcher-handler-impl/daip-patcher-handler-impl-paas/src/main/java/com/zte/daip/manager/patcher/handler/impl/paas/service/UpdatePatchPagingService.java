/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UpdatePatchPagingService.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/16
 * </p>
 * <p>
 * 完成日期：2021/3/16
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.service;

import com.google.common.collect.ImmutableMap;
import com.zte.daip.manager.common.utils.paging.AbstractPaging;
import com.zte.daip.manager.common.utils.paging.bean.PageTableBean;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.handler.api.dto.PatchPageTableDto;

/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class UpdatePatchPagingService extends AbstractPaging<PatchDetailDto, String> {

    @Override
    protected Function<PatchDetailDto, String> getKeyExtractor(int i) {

        Map<Integer, Function<PatchDetailDto, String>> integerFunctionImmutableMap =
            new ImmutableMap.Builder<Integer, Function<PatchDetailDto, String>>()
                .put(0, PatchDetailDto::getPatchName).put(1, PatchDetailDto::getRealPatchType)
                .put(2, PatchDetailDto::getBaseVersion).put(3, PatchDetailDto::getPatchCreateDateStr)
                .put(4, PatchDetailDto::getPatchSizeStr).put(5, PatchDetailDto::getPatchUploadTimeStr)
                .put(6, PatchDetailDto::getSupplementDesc).build();

        return integerFunctionImmutableMap.getOrDefault(i, PatchDetailDto::getPatchName);

    }

    @Override
    protected List<PatchDetailDto> customFilter(PageTableBean pageTableBean, List<PatchDetailDto> allData) {
        PatchPageTableDto patchPageTableDto = (PatchPageTableDto)pageTableBean;
        final String patchType = patchPageTableDto.getPatchType();
        final String allPatchTypeTag = "-1";
        if (StringUtils.isBlank(patchType) || StringUtils.equalsIgnoreCase(allPatchTypeTag, patchType)) {
            return allData;
        }
        return allData.stream().filter(
            patchDetailDto -> StringUtils.equalsIgnoreCase(patchType, patchDetailDto.getRealPatchType()))
            .collect(Collectors.toList());
    }

    @Override
    protected String getFilterKey(PatchDetailDto patchDetailDto) {
        return String.format("%s#%s#%s#%s#%s#%s#%s#%s", patchDetailDto.getPatchName(),
            patchDetailDto.getRealPatchType(), patchDetailDto.getBaseVersion(),
            patchDetailDto.getPatchCreateDate(), patchDetailDto.getPatchSize(),
            patchDetailDto.getPatchUploadTime(), patchDetailDto.getDescriptionZh(),
            patchDetailDto.getSupplementDesc());
    }
}