/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchRollbackHandlerImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/11
 * </p>
 * <p>
 * 完成日期：2021/3/11
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import com.zte.daip.manager.common.utils.handler.JsonHandlerUtils;
import com.zte.daip.manager.event.reporter.api.annotation.HandlerEvent;
import com.zte.daip.manager.patcher.api.PatchRollbackControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchRollBackServiceDto;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackHostDto;
import com.zte.daip.manager.patcher.api.dto.PatchRollbackParam;
import com.zte.daip.manager.patcher.api.dto.RollBackPatchProgress;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.handler.api.PatchRollbackHandler;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
public class PatchRollbackHandlerImpl implements PatchRollbackHandler {

    @Autowired
    private PatchRollbackControllerApi patchRollbackControllerApi;

    private final static String ROLLBACK_PATCH = "rollback_patch";

    @Override
    @ApiOperation(value = "查询补丁回滚点")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁回滚点", response = List.class))
    public List<PatchRollBackServiceDto> queryRollBackServices(@RequestParam("clusterId") String clusterId) {
        return patchRollbackControllerApi.queryRollBackServices(clusterId);
    }

    @Override
    public List<PatchRollbackHostDto> queryRollBackHosts(String clusterId,
        List<PatchRollbackParam> patchRollbackParams) {
        return patchRollbackControllerApi.queryRollBackHosts(clusterId, patchRollbackParams);
    }

    @Override
    @ApiOperation(value = "补丁回滚")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁回滚"))
    @HandlerEvent(eventType = ROLLBACK_PATCH, clusterId = "#clusterId")
    public String rollbackPatches(@RequestParam("clusterId") String clusterId,
        @RequestBody List<PatchRollbackHostDto> patchRollbackHostDtos) {
        patchRollbackControllerApi.rollbackPatches(clusterId, patchRollbackHostDtos);
        return JsonHandlerUtils.SUCCESS_MESSAGE;
    }

    @Override
    @ApiOperation(value = "查询补丁回滚进度")
    @ApiResponses(@ApiResponse(code = 200, message = "查询补丁回滚进度", response = List.class))
    public RollBackPatchProgress queryRollBackProgress(@RequestParam("clusterId") String clusterId) {
        return patchRollbackControllerApi.queryRollBackProgress(clusterId);
    }

}