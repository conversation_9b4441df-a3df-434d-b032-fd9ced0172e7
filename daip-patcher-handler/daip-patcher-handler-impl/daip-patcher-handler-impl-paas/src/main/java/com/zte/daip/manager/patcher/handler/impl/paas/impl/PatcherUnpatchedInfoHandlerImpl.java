/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatcherUnpatchedInfoHandlerImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/13
 * </p>
 * <p>
 * 完成日期：2021/4/13
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import com.zte.daip.manager.patcher.api.PatcherUnpatchedInfoControllerApi;
import com.zte.daip.manager.patcher.api.dto.ClusterAndServiceHostBean;
import com.zte.daip.manager.patcher.api.dto.ClustersAndServiceInstanceBean;
import com.zte.daip.manager.patcher.api.dto.ClustersAndServicesBean;
import com.zte.daip.manager.patcher.api.dto.PatchHostDto;
import com.zte.daip.manager.patcher.api.dto.PatchServiceHostDto;
import com.zte.daip.manager.patcher.handler.api.PatcherUnpatchedInfoHandler;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
public class PatcherUnpatchedInfoHandlerImpl implements PatcherUnpatchedInfoHandler {

    @Autowired
    private PatcherUnpatchedInfoControllerApi patcherUnpatchedInfoControllerApi;

    /**
     * 功能说明：查询未打补丁的集群服务实例信息。
     *
     * 业务背景：用于获取所有未应用补丁的集群服务实例，帮助运维人员快速定位需要处理的实例。
     *
     * @return 包含未打补丁集群服务实例信息的ClustersAndServiceInstanceBean对象。
     */
    @Override
    @ApiOperation(value = "查询未打补丁集群服务实例信息")
    @ApiResponses(@ApiResponse(code = 200, message = "集群服务结果", response = ClustersAndServiceInstanceBean.class))
    public ClustersAndServiceInstanceBean queryUnpatchedClusterAndServiceInstance() {
        return patcherUnpatchedInfoControllerApi.queryUnpatchedClusterAndServiceInstance();
    }

    /**
     * 功能说明：查询未打补丁的集群服务信息。
     *
     * 业务背景：提供未应用补丁的集群服务列表，便于运维团队进行批量处理或分析。
     *
     * @return 包含未打补丁集群服务信息的ClustersAndServicesBean对象。
     */
    @Override
    @ApiOperation(value = "查询未打补丁集群服务信息")
    @ApiResponses(@ApiResponse(code = 200, message = "集群服务结果", response = ClustersAndServicesBean.class))
    public ClustersAndServicesBean queryUnpatchedClusterAndServiceInfo() {
        return patcherUnpatchedInfoControllerApi.queryUnpatchedClusterAndServicesInfo();
    }

    /**
     * 功能说明：根据集群服务参数查询未打补丁的主机信息。
     *
     * 业务背景：通过指定的集群和服务参数，精准定位未打补丁的主机，支持定向维护操作。
     *
     * @param clusterAndServiceHostBean 包含集群和服务信息的参数对象，不能为空。
     * @return 未打补丁主机信息的List<PatchHostDto>集合。
     */
    @Override
    @ApiOperation(value = "查询未打补丁补丁主机信息")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁主机结果", response = List.class))
    public List<PatchHostDto> queryUnpatchedPatchesAndHost(
        @ApiParam(value = "集群服务参数对象", required = true) @RequestBody ClusterAndServiceHostBean clusterAndServiceHostBean) {
        return patcherUnpatchedInfoControllerApi.queryUnpatchedPatchesAndHost(clusterAndServiceHostBean);
    }

    /**
     * 功能说明：根据集群服务参数查询未打补丁的服务主机信息。
     *
     * 业务背景：用于获取特定集群服务下未打补丁的主机详情，支持精细化运维管理。
     *
     * @param clustersAndServicesBean 包含集群服务信息的参数对象，不能为空。
     * @return 未打补丁服务主机信息的List<PatchServiceHostDto>集合。
     */
    @Override
    @ApiOperation(value = "查询未打补丁补丁服务主机信息")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁主机结果", response = ClustersAndServicesBean.class))
    public List<PatchServiceHostDto> queryUnpatchedPatchesAndServiceAndHost(
        @ApiParam(value = "集群服务参数对象", required = true) @RequestBody ClustersAndServicesBean clustersAndServicesBean) {
        return patcherUnpatchedInfoControllerApi.queryUnpatchedPatchesAndServiceAndHost(clustersAndServicesBean);
    }
}