/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatcherUpdateHandlerImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/4/14
 * </p>
 * <p>
 * 完成日期：2021/4/14
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import com.zte.daip.manager.event.reporter.api.annotation.*;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zte.daip.manager.common.utils.handler.JsonHandlerUtils;
import com.zte.daip.manager.patcher.api.PatcherUpdateControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchUpdateCacheDto;
import com.zte.daip.manager.patcher.api.dto.UpdateRequest;
import com.zte.daip.manager.patcher.handler.api.PatcherUpdateHandler;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
public class PatcherUpdateHandlerImpl implements PatcherUpdateHandler {

    @Autowired
    private PatcherUpdateControllerApi patcherUpdateControllerApi;

    @Override
    @ApiOperation(value = "打补丁")
    @ApiResponses(@ApiResponse(code = 200, message = "打补丁操作结果", response = String.class))
    @OperationLog(operationName = @OperationName(i18nKey = I18nKeyConstants.UPGRADE_PATCH),
            operationObject = @OperationObject(value = I18nKeyConstants.PATCH_ZIP),
            operationDesc = @OperationDesc(clusterId = "#updateRequest.clusterId"))
    public String updatePatch(@RequestBody UpdateRequest updateRequest) {
        patcherUpdateControllerApi.updatePatch(updateRequest);
        return JsonHandlerUtils.SUCCESS_MESSAGE;
    }

    @Override
    @ApiOperation(value = "升级补丁进度", notes = "升级补丁")
    @ApiResponses(@ApiResponse(code = 200, message = "升级补丁进度结果", response = PatchUpdateCacheDto.class))
    public PatchUpdateCacheDto updatePatchesProcess(@ApiParam(name = "clusterId", value = "clusterId",
        required = true) @PathVariable("clusterId") String clusterId) {
        return patcherUpdateControllerApi.queryUpdatePatchesProcess(clusterId);
    }

    @Override
    @ApiOperation(value = "是否允许升级补丁", notes = "升级补丁")
    @ApiResponses(@ApiResponse(code = 200, message = "是否允许升级补丁", response = Boolean.class))
    public boolean isUpdatePatchPermit(@ApiParam(name = "clusterId", value = "clusterId",
        required = true) @PathVariable("clusterId") String clusterId) {
        return patcherUpdateControllerApi.isUpdatePatchPermit(clusterId);
    }

    @Override
    @ApiOperation(value = "查询需要打的补丁")
    @ApiResponses(@ApiResponse(code = 200, message = "查询需要打的补丁", response = String.class))
    public String queryNeedUpdatePatchInfo(@RequestParam String patchRequestKey, @RequestParam String hostIp) {
        return patcherUpdateControllerApi.queryNeedUpdatePatchInfo(patchRequestKey, hostIp);
    }
}