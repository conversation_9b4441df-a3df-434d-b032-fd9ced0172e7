/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchUploadHandlerImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/18
 * </p>
 * <p>
 * 完成日期：2021/3/18
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.handler.JsonHandlerUtils;
import com.zte.daip.manager.common.utils.validator.ValidFile;
import com.zte.daip.manager.event.reporter.api.annotation.HandlerEvent;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.PatcherUploadControllerApi;
import com.zte.daip.manager.patcher.api.dto.UploadResponseResult;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.handler.api.PatchUploadHandler;
import com.zte.zdh.commons.FilePathCleaner;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
@Validated
public class PatchUploadHandlerImpl implements PatchUploadHandler {

    @Autowired
    private PatcherUploadControllerApi patcherUploadControllerApi;
    @Autowired
    private DaipEventReporter daipEventReporter;

    @Override
    @ApiOperation(value = "上传补丁")
    @ApiResponses(@ApiResponse(code = 200, message = "上传补丁操作结果", response = String.class))
    @HandlerEvent(eventType = I18nKeyConstants.UPLOAD_PATCH_ZIP)
    public String
        uploadFile(@ValidFile(endsWith = ".zip", multiFile = true,
            message = "The uploaded file type error, need zipFile.") @ApiParam(value = "上传补丁文件流对象",
                required = true) @RequestParam("file") MultipartFile[] files) {

        log.info("start upload patcher. size {}", files == null ? 0 : files.length);
        try {
            if (files == null || files.length == 0) {
                return JsonHandlerUtils.FALIURE_MESSAGE;
            }
            generateFileToTempDir(files);

            final List<String> collect =
                Lists.newArrayList(files).stream().map(MultipartFile::getOriginalFilename).collect(Collectors.toList());
            UploadResponseResult uploadResponseResult = patcherUploadControllerApi.notifyUploadPatches(collect);
            log.info(JsonHandlerUtils.genSuccessfulRespData(uploadResponseResult));
            return JsonHandlerUtils.genSuccessfulRespData(uploadResponseResult);
        } catch (Exception e) {
            log.error("upload patch file failed:", e);
            return JsonHandlerUtils.FALIURE_MESSAGE;
        }
    }

    private void generateFileToTempDir(MultipartFile[] patchMultipartFiles) {
        daipEventReporter.info(I18nKeyConstants.GENERATE_PATCH_FILE_2_TEMP_DIR, "");
        String uploadDirTemp = getRepositoryHomeEnv() + "/upload/";
        copyFileToTempDir(patchMultipartFiles, uploadDirTemp);
    }

    private String getRepositoryHomeEnv() {
        String repositoryHome = System.getenv("daip-patcher-env");
        return StringUtils.isNotEmpty(repositoryHome) ? repositoryHome : "/data1/version";
    }

    private void copyFileToTempDir(MultipartFile[] patchMultipartFiles, String patchUploadTemp) {
        for (MultipartFile patch : patchMultipartFiles) {
            String filename = patch.getOriginalFilename();
            try {
                File destination = FilePathCleaner.newFile(patchUploadTemp, filename);
                if (destination != null) {
                    FileUtils.copyInputStreamToFile(patch.getInputStream(), destination);
                }
            } catch (IOException e) {
                log.error("copy upload-file error : ", e);
                daipEventReporter.error(I18nKeyConstants.GENERATE_PATCH_FILE_2_TEMP_DIR,
                    String.format("copy upload-file %s error.", filename));
            }
        }
    }
}