package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;
import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.handler.JsonHandlerUtils;
import com.zte.daip.manager.event.reporter.api.annotation.HandlerEvent;
import com.zte.daip.manager.patcher.handler.api.PatchTaskHandler;
import com.zte.daip.manager.patcher.handler.impl.paas.constant.I18nKeyConstants;
import com.zte.daip.manager.patcher.inner.api.PatchTaskControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.*;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
@Validated
public class PatchTaskHandlerImpl implements PatchTaskHandler {
    @Autowired
    private PatchTaskControllerApi patchTaskControllerApi;

    @Autowired
    private SpringAppCsrfProtector springAppCsrfProtector;

    @Override
    @ApiOperation(value = "新增补丁任务", notes = "新增补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "新增补丁任务"))
    @HandlerEvent(eventType = I18nKeyConstants.COM_ZTE_DAIP_PATCHER_PATCH_TASK_CREATE,
        clusterId = "#patchTaskDto.clusterId")
    public String createPatchTask(PatchTaskDto patchTaskDto) {
        springAppCsrfProtector.validRequest();
        try {
            log.info("start to create patch task:{} {}", patchTaskDto.getClusterId(), patchTaskDto.getTaskName());
            long taskId = patchTaskControllerApi.createPatchTask(patchTaskDto);
            log.info("finished to create patch task:{} {}", patchTaskDto.getClusterId(), patchTaskDto.getTaskName());
            return taskId != -1 ? JsonHandlerUtils.SUCCESS_MESSAGE
                : JsonHandlerUtils.genFailureRespResult("Failed to create task");
        } catch (Exception e) {
            log.error("create patch task.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "修改补丁任务", notes = "修改补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "修改补丁任务"))
    @HandlerEvent(eventType = I18nKeyConstants.COM_ZTE_DAIP_PATCHER_PATCH_TASK_MODIFY,
        clusterId = "#patchTaskDto.clusterId")
    public String modifyPatchTask(PatchTaskDto patchTaskDto) {
        springAppCsrfProtector.validRequest();
        try {
            log.info("start to modify patch task:{} {}", patchTaskDto.getClusterId(), patchTaskDto.getTaskName());
            boolean isSuccess = patchTaskControllerApi.modifyPatchTask(patchTaskDto);
            log.info("finished to modify patch task:{} {}", patchTaskDto.getClusterId(), patchTaskDto.getTaskName());
            return isSuccess ? JsonHandlerUtils.SUCCESS_MESSAGE
                : JsonHandlerUtils.genFailureRespResult("Failed to modify task");
        } catch (Exception e) {
            log.error("modify patch task.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "删除补丁任务", notes = "删除补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "删除补丁任务"))
    @HandlerEvent(eventType = I18nKeyConstants.COM_ZTE_DAIP_PATCHER_PATCH_TASK_DELETE)
    public String removePatchTask(List<Long> taskIds) {
        springAppCsrfProtector.validRequest();
        try {
            log.info("start to remove patch task:{}", taskIds);
            boolean isSuccess = patchTaskControllerApi.removePatchTask(taskIds);
            log.info("finished to remove patch task:{}", taskIds);
            return isSuccess ? JsonHandlerUtils.SUCCESS_MESSAGE
                : JsonHandlerUtils.genFailureRespResult("Failed to remove task");
        } catch (Exception e) {
            log.error("remove patch task.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "查询补丁任务", notes = "查询补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "查询补丁任务"))
    public String queryPatchTasks(int currentPage, int pageSize, String taskName) {
        springAppCsrfProtector.validRequest();
        try {
            List<PatchTaskDto> patchTaskDtos = patchTaskControllerApi.queryPatchTasks();
            List<PatchTaskDto> taskSummaries;
            int totalSize;
            if (StringUtils.isEmpty(taskName)) {
                if (pageSize <= 0 || currentPage <= 0) {
                    taskSummaries = patchTaskDtos;
                    totalSize = taskSummaries.size();
                } else {
                    currentPage = Optional.ofNullable(currentPage).orElse(1);
                    taskSummaries = patchTaskDtos.stream().skip((long)(currentPage - 1) * pageSize).limit(pageSize)
                        .collect(Collectors.toList());
                    totalSize = patchTaskDtos.size();
                }
            } else {
                taskSummaries = patchTaskDtos.stream()
                    .filter(task -> org.apache.commons.lang.StringUtils.equals(taskName, task.getTaskName()))
                    .collect(Collectors.toList());
                totalSize = taskSummaries.size();
            }
            Map<String, Object> result = new HashMap<>();
            result.put("taskList", taskSummaries);
            result.put("total", totalSize);
            return JsonHandlerUtils.genSuccessfulRespData(result);
        } catch (Exception e) {
            log.error("query patch tasks.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "根据任务ID查询补丁任务详情", notes = "根据任务ID查询补丁任务详情")
    @ApiResponses(@ApiResponse(code = 200, message = "根据任务ID查询补丁任务详情"))
    public String queryPatchTaskDetailByTaskId(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            PatchTaskDto patchTaskDto = patchTaskControllerApi.queryPatchDetailTasks(taskId);
            return JsonHandlerUtils.genSuccessfulRespData(patchTaskDto);
        } catch (Exception e) {
            log.error("query patch task by id.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "获取跳转任务详情的url", notes = "获取跳转任务详情的url，金库二次授权使用")
    @ApiResponses(@ApiResponse(code = 200, message = "获取跳转任务详情的url"))
    public String queryPatchTaskDetailUrl() {
        return JsonHandlerUtils.genSuccessfulRespResult("/upgrade/taskDetail");
    }

    @Override
    @ApiOperation(value = "根据任务ID查询补丁任务", notes = "根据任务ID查询补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "根据任务ID查询补丁任务"))
    public String queryPatchTaskByTaskId(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            PatchTaskDto patchTaskDto = patchTaskControllerApi.queryPatchTaskByTaskId(taskId);
            return JsonHandlerUtils.genSuccessfulRespData(patchTaskDto);
        } catch (Exception e) {
            log.error("query patch task by id.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "执行补丁任务", notes = "执行补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "执行补丁任务"))
    @HandlerEvent(eventType = I18nKeyConstants.COM_ZTE_DAIP_PATCHER_PATCH_TASK_TRIGGER, i18nArgs = {"#taskName"})
    public String triggerPatchTask(long taskId, String taskName) {
        springAppCsrfProtector.validRequest();
        try {
            log.info("start to trigger patch task:{}", taskName);
            boolean isSuccess = patchTaskControllerApi.triggerPatchTask(taskId);
            log.info("finished to trigger patch task:{}", taskName);
            return isSuccess ? JsonHandlerUtils.SUCCESS_MESSAGE
                : JsonHandlerUtils.genFailureRespResult("Failed to trigger task");
        } catch (Exception e) {
            log.error("trigger patch task.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "重试补丁任务", notes = "重试补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "重试补丁任务"))
    @HandlerEvent(eventType = I18nKeyConstants.COM_ZTE_DAIP_PATCHER_PATCH_TASK_RETRY, i18nArgs = {"#taskName"})
    public String retryPatchTask(long taskId, String taskName) {
        springAppCsrfProtector.validRequest();
        try {
            log.info("start to retry patch task:{}", taskName);
            boolean isSuccess = patchTaskControllerApi.retryPatchTask(taskId);
            log.info("finished to retry patch task:{}", taskName);
            return isSuccess ? JsonHandlerUtils.SUCCESS_MESSAGE
                : JsonHandlerUtils.genFailureRespResult("Failed to retry task");
        } catch (Exception e) {
            log.error("retry patch task.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "复制补丁任务", notes = "复制补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "复制补丁任务"))
    @HandlerEvent(eventType = I18nKeyConstants.COM_ZTE_DAIP_PATCHER_PATCH_TASK_RETRY, i18nArgs = {"#taskName"})
    public String copyPatchTask(long taskId, String taskName) {
        springAppCsrfProtector.validRequest();
        try {
            log.info("start to copy patch task:{}", taskName);
            boolean isSuccess = patchTaskControllerApi.copyPatchTask(taskId);
            log.info("finished to copy patch task:{}", taskName);
            return isSuccess ? JsonHandlerUtils.SUCCESS_MESSAGE
                : JsonHandlerUtils.genFailureRespResult("Failed to copy task");
        } catch (Exception e) {
            log.error("copy patch task.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "检查任务是否可以回退", notes = "检查任务是否可以回退")
    @ApiResponses(@ApiResponse(code = 200, message = "检查任务是否可以回退"))
    public PatchOperateResult checkTaskRollback(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            return patchTaskControllerApi.checkTaskCanRollback(taskId);
        } catch (Exception e) {
            return PatchOperateResult.fail("");
        }
    }

    @Override
    @ApiOperation(value = "检查任务是否可以复制", notes = "检查任务是否可以复制")
    @ApiResponses(@ApiResponse(code = 200, message = "检查任务是否可以复制"))
    public PatchOperateResult checkTaskDuplicate(long taskId) {
        springAppCsrfProtector.validRequest();
        try {
            return patchTaskControllerApi.checkTaskCanDuplicate(taskId);
        } catch (Exception e) {
            return PatchOperateResult.fail("");
        }
    }

    @Override
    @ApiOperation(value = "暂停补丁任务", notes = "暂停补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "暂停补丁任务"))
    @HandlerEvent(eventType = I18nKeyConstants.COM_ZTE_DAIP_PATCHER_PATCH_TASK_PAUSE, i18nArgs = {"#taskName"})
    public String pausePatchTask(long taskId, String taskName) {
        springAppCsrfProtector.validRequest();
        try {
            log.info("start to pause patch task:{}", taskName);
            boolean isSuccess = patchTaskControllerApi.pausePatchTask(taskId);
            log.info("finished to pause patch task:{}", taskName);
            return isSuccess ? JsonHandlerUtils.SUCCESS_MESSAGE
                : JsonHandlerUtils.genFailureRespResult("Failed to pause task");
        } catch (Exception e) {
            log.error("pause patch task.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "继续补丁任务", notes = "继续补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "继续补丁任务"))
    @HandlerEvent(eventType = I18nKeyConstants.COM_ZTE_DAIP_PATCHER_PATCH_TASK_RESUME, i18nArgs = {"#taskName"})
    public String resumePatchTask(long taskId, String taskName) {
        springAppCsrfProtector.validRequest();
        try {
            log.info("start to resume patch task:{}", taskName);
            boolean isSuccess = patchTaskControllerApi.resumePatchTask(taskId);
            log.info("finished to resume patch task:{}", taskName);
            return isSuccess ? JsonHandlerUtils.SUCCESS_MESSAGE
                : JsonHandlerUtils.genFailureRespResult("Failed to resume task");
        } catch (Exception e) {
            log.error("resume patch task.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "查询补丁升级服务实例", notes = "查询补丁升级服务实例")
    @ApiResponses(@ApiResponse(code = 200, message = "查询补丁升级服务实例"))
    public String queryServiceInstanceUpgrade(VersionQueryRequestInfo versionQueryRequestInfo) {
        springAppCsrfProtector.validRequest();
        try {
            if (CollectionUtils.isEmpty(versionQueryRequestInfo.getServiceVersionInfoList())) {
                return JsonHandlerUtils.genSuccessfulRespData(Maps.newHashMap());
            }
            List<ServiceInstanceUpgrade> versionUpgradeMap =
                patchTaskControllerApi.queryServiceInstanceUpgrade(versionQueryRequestInfo);
            return JsonHandlerUtils.genSuccessfulRespData(versionUpgradeMap);
        } catch (DaipBaseException e) {
            log.error("Fail to query service instance upgrade, accessType: {}",
                versionQueryRequestInfo.getAccessType());
            return JsonHandlerUtils.genFailureRespResult(e.getMessage());
        }
    }

    @Override
    @ApiOperation(value = "回退补丁任务", notes = "回退补丁任务")
    @ApiResponses(@ApiResponse(code = 200, message = "回退补丁任务"))
    @HandlerEvent(eventType = I18nKeyConstants.COM_ZTE_DAIP_PATCHER_PATCH_TASK_ROLLBACK,
        clusterId = "#patchTaskDto.clusterId")
    public String rollbackPatchTask(PatchTaskDto patchTaskDto) {
        springAppCsrfProtector.validRequest();
        try {
            log.info("start to rollback patch task:{} {}", patchTaskDto.getClusterId(), patchTaskDto.getTaskName());
            boolean isSuccess = patchTaskControllerApi.rollbackPatchTask(patchTaskDto);
            log.info("finished to rollback patch task:{} {}, result: {}", patchTaskDto.getClusterId(),
                patchTaskDto.getTaskName(), isSuccess);
            return isSuccess ? JsonHandlerUtils.SUCCESS_MESSAGE
                : JsonHandlerUtils.genFailureRespResult("Failed to rollback patch.");
        } catch (Exception e) {
            log.error("rollback patch task.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }

    @Override
    @ApiOperation(value = "查询待重启服务", notes = "查询待重启服务")
    @ApiResponses(@ApiResponse(code = 200, message = "查询待重启服务"))
    public String queryNeedRestartService(PatchTaskDto patchTaskDto) {
        springAppCsrfProtector.validRequest();
        try {
            if (CollectionUtils.isEmpty(patchTaskDto.getContext())
                || CollectionUtils.isEmpty(patchTaskDto.getRelationServices())
                || StringUtils.isEmpty(patchTaskDto.getClusterId())) {
                return JsonHandlerUtils.FALIURE_MESSAGE;
            }
            List<ServiceInstance> needRestartService = patchTaskControllerApi.queryNeedRestartService(patchTaskDto);
            Map<String, Object> result = new HashMap<>();
            result.put("needRestartService", needRestartService);
            return JsonHandlerUtils.genSuccessfulRespData(result);
        } catch (DaipBaseException e) {
            log.error("Fail to query need restart service instance", e);
            return JsonHandlerUtils.FALIURE_MESSAGE;
        }
    }
}