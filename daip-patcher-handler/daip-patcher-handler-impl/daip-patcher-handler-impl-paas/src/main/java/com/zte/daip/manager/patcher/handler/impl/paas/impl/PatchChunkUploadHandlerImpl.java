/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchChunkUploadHandlerImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/11/17
 * </p>
 * <p>
 * 完成日期：2021/11/17
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import com.google.common.annotations.VisibleForTesting;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.file.chunk.FileChunkEntity;
import com.zte.daip.manager.common.utils.file.chunk.FileEntity;
import com.zte.daip.manager.common.utils.file.chunk.FileUploadPathEntity;
import com.zte.daip.manager.common.utils.file.chunk.upload.FileChunkUploadUtil;
import com.zte.daip.manager.common.utils.file.cleaner.FilePathCleaner;
import com.zte.daip.manager.common.utils.handler.JsonHandlerUtils;
import com.zte.daip.manager.common.utils.i18n.DaipI18nService;
import com.zte.daip.manager.event.reporter.api.annotation.*;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.PatcherUploadControllerApi;
import com.zte.daip.manager.patcher.api.i18n.I18nKeyConstants;
import com.zte.daip.manager.patcher.api.util.PatchDapLogBuilder;
import com.zte.daip.manager.patcher.handler.api.PatchChunkUploadHandler;
import com.zte.daip.manager.patcher.handler.impl.paas.service.PatchPathUtil;
import com.zte.daip.manager.patcher.handler.impl.paas.service.event.LogEvent;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 功能描述: 分片上传处理实现类<br>
 * 
 * <p>
 * Note: 处理大文件分片上传的具体实现，包括分片保存、合并、删除等操作。
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
@Validated
public class PatchChunkUploadHandlerImpl implements PatchChunkUploadHandler {

    @Autowired
    private LogEvent logEvent;
    @Autowired
    private DaipI18nService daipI18nService;
    @Autowired
    private PatcherUploadControllerApi patcherUploadControllerApi;
    @Autowired
    private PatchDapLogBuilder patchDapLogBuilder;

    private FileUploadPathEntity fileUploadPathEntity =
        new FileUploadPathEntity(PatchPathUtil.getUploadChunkDirTemp(), PatchPathUtil.getUploadDirTemp());

    /**
     * 功能说明：上传文件分片。
     *
     * 业务背景：支持大文件分片上传，每个分片单独上传以提高传输效率和容错性。
     *
     * @param file     分片文件，不能为空。
     * @param chunk    分片编号，从0开始递增。
     * @param fileMd5  文件MD5值，用于唯一标识整个文件。
     * @return 返回当前分片上传结果，包含是否成功及错误信息（如有）。
     *
     * @throws IllegalArgumentException 当文件或分片编号为空时抛出。
     */
    @Override
    @ApiOperation(value = "上传文件分片")
    @ApiResponses(@ApiResponse(code = 200, message = "上传文件分片结果", response = String.class))
    public String uploadFileChunk(
        @ApiParam(value = "上传文件分片流对象", required = true) @RequestPart("file") MultipartFile file,
        @RequestParam("chunk") Integer chunk,
        @Pattern(regexp = "^[A-Za-z0-9]+$", message = "md5 type error") @RequestParam("fileMd5") String fileMd5) {
        try {
            FileChunkEntity fileChunkEntity =
                new FileChunkEntity(file, fileMd5, Integer.toString(chunk), fileUploadPathEntity.getChunkPath());

            FileChunkUploadUtil.saveFile(fileChunkEntity);

            return JsonHandlerUtils.SUCCESS_MESSAGE;
        } catch (DaipBaseException e) {
            logEvent.logOpt(I18nKeyConstants.PATCH_ZIP_UPLOAD, I18nKeyConstants.PATCH_ZIP,
                daipI18nService.getLabel("upload_patch_chunk_failed", new String[] {e.getMessage()}));
            log.error("upload file chunk failed.", e);
            return JsonHandlerUtils.FALIURE_MESSAGE;
        }
    }

    /**
     * 功能说明：验证文件是否存在。
     *
     * 业务背景：在合并分片前需确认目标文件是否已存在，避免重复上传。
     *
     * @param fileEntity 包含文件元数据的对象，不能为空。
     * @return 存在返回true，否则false。
     *
     * @throws NullPointerException 当fileEntity为null时抛出。
     */
    @Override
    @ApiOperation(value = "验证文件是否存在")
    @ApiResponses(@ApiResponse(code = 200, message = "验证文件是否存在结果", response = String.class))
    public String checkFileExists(@ApiParam(value = "文件实体对象", required = true) @RequestBody @Validated FileEntity fileEntity) {
        return JsonHandlerUtils.genSuccessfulRespData(
            FileChunkUploadUtil.checkFileExists(fileEntity, fileUploadPathEntity.getMergePath()));
    }

    /**
     * 功能说明：获取已上传的分片下标。
     *
     * 业务背景：用于续传场景，获取已成功上传的分片列表，避免重复上传。
     *
     * @param fileEntity 包含文件元数据的对象，不能为空。
     * @return 已上传分片的下标列表，JSON格式字符串。
     *
     * @throws NullPointerException 当fileEntity为null时抛出。
     */
    @Override
    @ApiOperation(value = "获取已上传的分片下标")
    @ApiResponses(@ApiResponse(code = 200, message = "获取已上传的分片下标结果", response = String.class))
    public String getUploadChunkIndex(@ApiParam(value = "文件实体对象", required = true) @RequestBody @Validated FileEntity fileEntity) {
        return JsonHandlerUtils.genSuccessfulRespData(
            FileChunkUploadUtil.getUploadChunkIndex(fileEntity, fileUploadPathEntity.getChunkPath()));
    }

    /**
     * 功能说明：补丁分片合并。
     *
     * 业务背景：所有分片上传完成后，将分片合并为完整文件。
     *
     * @param fileEntity 包含文件元数据的对象，不能为空。
     * @return 合并结果，成功返回文件路径，失败返回错误信息。
     *
     * @throws NullPointerException 当fileEntity为null时抛出。
     * @throws DaipBaseException 当合并过程中发生异常时抛出。
     */
    @Override
    @ApiOperation(value = "补丁分片合并")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁分片合并结果", response = String.class))
    public String mergeFile(@ApiParam(value = "文件实体对象", required = true) @Valid @RequestBody @Validated FileEntity fileEntity) {
        try {
            FileChunkUploadUtil.mergeFile(fileUploadPathEntity, fileEntity);

            return JsonHandlerUtils.SUCCESS_MESSAGE;
        } catch (DaipBaseException e) {
            logEvent.logOpt(I18nKeyConstants.PATCH_CHUNK_MERGE, I18nKeyConstants.PATCH_ZIP,
                daipI18nService.getLabel("merge_patch_chunk_failed", new String[] {e.getMessage()}));
            log.error("merge patch file failed.", e);
            return JsonHandlerUtils.FALIURE_MESSAGE;
        }
    }

    /**
     * 功能说明：补丁分片删除。
     *
     * 业务背景：删除指定文件的所有分片，用于上传失败后的清理操作。
     *
     * @param fileMd5 文件MD5值，用于唯一标识文件。
     * @return 删除结果，成功返回true，失败返回false。
     *
     * @throws IllegalArgumentException 当fileMd5格式不正确时抛出。
     */
    @Override
    public String deleteChunkFile(
        @Pattern(regexp = "^[A-Za-z0-9]+$", message = "md5 type error") @RequestParam("fileMd5") String fileMd5) {
        File uploadChunkDir = FilePathCleaner.newFile(fileUploadPathEntity.getChunkPath() + File.separator + fileMd5);
        if (uploadChunkDir != null && uploadChunkDir.exists() && uploadChunkDir.isDirectory()) {
            try {
                FileUtils.deleteDirectory(uploadChunkDir);
                return JsonHandlerUtils.SUCCESS_MESSAGE;
            } catch (IOException e) {
                log.error("delete chunk dir fail.", e);
                return JsonHandlerUtils.FALIURE_MESSAGE;
            }
        }
        return JsonHandlerUtils.genFailureRespData("chunk dir is incorrect.");
    }

    /**
     * 功能说明：上传补丁通知。
     *
     * 业务背景：通知系统有新补丁可用，触发后续处理流程。
     *
     * @param pathNameList 补丁文件路径列表，不能为空。
     * @return 通知结果，成功返回true，失败返回false。
     *
     * @throws NullPointerException 当pathNameList为null时抛出。
     * @throws DaipBaseException 当通知过程中发生异常时抛出。
     */
    @Override
    @ApiOperation(value = "合并后通知")
    @ApiResponses(@ApiResponse(code = 200, message = "合并后通知结果", response = String.class))
    @OperationLog(operationName = @OperationName(i18nKey = I18nKeyConstants.UPLOAD_PATCH_NOTIFICATION),
        operationObject = @OperationObject(value = I18nKeyConstants.PATCH_ZIP),
        operationDesc = @OperationDesc(extraI18nKey = I18nKeyConstants.PATCH_INFO,
            extraI18nArgs = "#{patchDapLogBuilder.buildStringListInfo(#pathNameList)}"))
    public String notifyUploadPatches(@ApiParam(value = "文件名称集合", required = true) List<String> pathNameList)
        throws DaipBaseException {
        return JsonHandlerUtils.genSuccessfulRespData(patcherUploadControllerApi.notifyUploadPatches(pathNameList));
    }

    @VisibleForTesting
    protected void setFileUploadPathEntity(FileUploadPathEntity fileUploadPathEntity) {
        this.fileUploadPathEntity = fileUploadPathEntity;
    }
}