/**
 * <p>
 * <owner>10259451</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchStartAndStopHandlerImpl.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2023/4/23
 * </p>
 * <p>
 * 完成日期：2023/4/23
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import com.alibaba.fastjson.JSON;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.common.utils.handler.JsonHandlerUtils;
import com.zte.daip.manager.patcher.handler.api.PatchStartAndStopHandler;
import com.zte.daip.manager.patcher.inner.api.PatchStartAndStopControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.PatchOperateServiceDto;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
public class PatchStartAndStopHandlerImpl implements PatchStartAndStopHandler {

    @Autowired
    private PatchStartAndStopControllerApi patchStartAndStopControllerApi;

    @Override
    @ApiOperation(value = "查询补丁升级（回退）过程中的可启动（停止）的服务实例id.（集群启停方式）")
    @ApiResponses(@ApiResponse(code = 200, message = "查询补丁升级（回退）过程中的可启动（停止）的服务实例id.（集群启停方式）"))
    public String queryCanOperateServices(@RequestParam("clusterId") String clusterId,
        @RequestParam("isStart") boolean isStart, @RequestBody List<PatchOperateServiceDto> needOperateServiceDtos) {
        log.info("start query can operate services:{},{},{}", clusterId, isStart, needOperateServiceDtos.toString());
        try {
            List<String> services =
                patchStartAndStopControllerApi.queryCanOperateServices(clusterId, isStart, needOperateServiceDtos);
            return JSON.toJSONString(services);
        } catch (DaipBaseException e) {
            log.error("can operate services query exception.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }

    }

    @Override
    @ApiOperation(value = "补丁升级（回退）过程中的服务启动.（集群启动方式）")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁升级（回退）过程中的服务启动.（集群启动方式）"))
    public String startCluster(@RequestParam("clusterId") String clusterId,
        @RequestBody List<String> needOperateInstanceIds) {
        log.info("start services:{}:{}", clusterId, needOperateInstanceIds.toString());
        try {
            patchStartAndStopControllerApi.startCluster(clusterId, needOperateInstanceIds);
        } catch (DaipBaseException e) {
            log.error("start services exception.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }

        return JsonHandlerUtils.SUCCESS_MESSAGE;
    }

    @Override
    @ApiOperation(value = "补丁升级（回退）过程中的服务停止.（集群停止方式）")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁升级（回退）过程中的服务停止.（集群停止方式）"))
    public String stopCluster(@RequestParam("clusterId") String clusterId,
        @RequestBody List<String> needOperateInstanceIds) {
        log.info("stop services:{}:{}", clusterId, needOperateInstanceIds.toString());
        try {
            patchStartAndStopControllerApi.stopCluster(clusterId, needOperateInstanceIds);
        } catch (DaipBaseException e) {
            log.error("stop services exception.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
        return JsonHandlerUtils.SUCCESS_MESSAGE;
    }

    @Override
    @ApiOperation(value = "查询补丁升级（回退）过程中的启动（停止）的进度.（集群启停方式）")
    @ApiResponses(@ApiResponse(code = 200, message = "查询补丁升级（回退）过程中的启动（停止）的进度.（集群启停方式）"))
    public String queryOperatorProcess(@RequestParam("clusterId") String clusterId) {
        log.info("start query operator process:{}", clusterId);
        try {
            return patchStartAndStopControllerApi.queryOperatorProcess(clusterId);
        } catch (DaipBaseException e) {
            log.error("operation progress query exception.", e);
            return JsonHandlerUtils.genFailureRespResult(e.getLocalizedMessage());
        }
    }
}