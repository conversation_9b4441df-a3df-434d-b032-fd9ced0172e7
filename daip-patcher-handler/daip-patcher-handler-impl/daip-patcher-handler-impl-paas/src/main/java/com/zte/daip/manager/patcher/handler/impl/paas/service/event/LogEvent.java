package com.zte.daip.manager.patcher.handler.impl.paas.service.event;

import static com.zte.daip.manager.event.reporter.api.DaipEventReporter.getEnUsI18n;
import static com.zte.daip.manager.event.reporter.api.DaipEventReporter.getZhCnI18n;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zte.daip.manager.event.beans.*;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;

@Component
public class LogEvent {

    @Autowired
    private DaipEventReporter daipEventReporter;

    public void logOpt(String operationNameI18nKey, String operationObjI18nKey, String failReason) {
        logOpt(operationNameI18nKey, null, operationObjI18nKey, OperationDesc.builder().build(), failReason);
    }

    public void logOpt(String operationNameI18nKey, String[] operationNameI18nArgs, String operationObjI18n<PERSON><PERSON>,
                       OperationDesc optDesc, String failReason) {
        logOpt(operationNameI18nKey, operationNameI18nArgs, operationObjI18nKey, optDesc,
                OperationResult.failed(failReason));
    }

    public void logOpt(String operationNameI18nKey, String[] operationNameI18nArgs, String operationObjI18nKey,
                       OperationDesc optDesc, OperationResult operationResult) {
        OperationName optName = OperationName.of(getEnUsI18n(operationNameI18nKey, operationNameI18nArgs),
                getZhCnI18n(operationNameI18nKey, operationNameI18nArgs));
        OperationObject optObj =
                OperationObject.of(getEnUsI18n(operationObjI18nKey, null), getZhCnI18n(operationObjI18nKey, null));
        AbstractEvent event = daipEventReporter.buildOperationLog(optName, optObj, optDesc, operationResult);

        daipEventReporter.report(event);
    }
}
