/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDetailPagingAssembler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2022/3/1
 * </p>
 * <p>
 * 完成日期：2022/3/1
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.service;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.paging.jpa.*;
import com.zte.daip.manager.patcher.handler.api.dto.PageConstants;
import com.zte.daip.manager.patcher.handler.api.dto.PatchPageTableDto;
/* Started by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.apache.commons.lang3.StringUtils;
/* Ended by AICoder, pid:674ee27924b84885bb99b095601d7fc5 */
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
public class PatchDetailPagingAssembler {
    private static final Map<Integer, String> SORT_MAP = new HashMap<>();

    private static final Pattern COMPILE =
        Pattern.compile("(.*)\\((.*)\\)$", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    static {
        SORT_MAP.put(0, PageConstants.HEADER_PATCH_NAME);
        SORT_MAP.put(1, PageConstants.HEADER_SERVICE);
        SORT_MAP.put(2, PageConstants.HEADER_BASE_VERSION);
        SORT_MAP.put(3, PageConstants.HEADER_PATCH_SIZE);
        SORT_MAP.put(4, PageConstants.HEADER_PATCH_UPLOAD_TIME);
    }

    public QueryParam patchPageTableDto2QueryParamDo(PatchPageTableDto patchPageTableDto) {
        final String patchType = patchPageTableDto.getPatchType();

        FieldQuery fieldQuery = new FieldQuery(getPredicationByPatchType(patchType));

        final int pageDisplayStart = patchPageTableDto.getPageDisplayStart();

        final int pageDisplayLength = patchPageTableDto.getPageDisplayLength();

        final String sortDir = StringUtils.equalsIgnoreCase(patchPageTableDto.getSortDir(), PagingConstants.SORT_DESC)
            ? PagingConstants.SORT_DESC : PagingConstants.SORT_ASC;

        QueryParam queryParam = QueryParam.builder()
            .fuzzyQuery(new FuzzyQuery(patchPageTableDto.getSearchKeyWord(),
                Lists.newArrayList(new Predication(PageConstants.HEADER_PATCH_NAME),
                    new Predication(PageConstants.HEADER_SERVICE), new Predication(PageConstants.HEADER_BASE_VERSION),
                    new Predication(PagingConstants.DATE_LIKE_OPERATE, PageConstants.HEADER_PATCH_UPLOAD_TIME))))
            .fieldQuery(fieldQuery).pageNum(pageDisplayStart / pageDisplayLength + 1).pageSize(pageDisplayLength)
            .sortKey(SORT_MAP.getOrDefault(patchPageTableDto.getSortCol(), PageConstants.HEADER_PATCH_NAME))
            .sortOrder(sortDir).build();

        return queryParam;
    }

    private List<Predication> getPredicationByPatchType(String patchType) {

        if (StringUtils.isBlank(patchType) || StringUtils.equalsIgnoreCase(PageConstants.ALL_FLAG, patchType)) {
            return Lists.newArrayList();
        }

        Matcher matcher = COMPILE.matcher(patchType);
        if (matcher.find()) {
            return Lists.newArrayList(
                new Predication(PagingConstants.EQUAL_OPERATE, PageConstants.HEADER_SERVICE, matcher.group(1)),
                new Predication(PagingConstants.EQUAL_OPERATE, PageConstants.HEADER_ROLES, matcher.group(2)));
        }
        return Lists
            .newArrayList(new Predication(PagingConstants.EQUAL_OPERATE, PageConstants.HEADER_SERVICE, patchType));
    }
}