/**
 * <p>
 * <owner>王明宇</owner>
 * </p>
 * <p>
 * <createdate>2016-02-23</createdate>
 * </p>
 * <p>
 * 文件名称: TimeQueryHandler.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.handler.JsonHandlerUtils;
import com.zte.daip.manager.common.utils.paging.bean.PageInfo;
import com.zte.daip.manager.common.utils.paging.jpa.QueryParam;
import com.zte.daip.manager.patcher.api.PatcherInfoControllerApi;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.api.dto.PatchDispatchDto;
import com.zte.daip.manager.patcher.api.dto.PatchHistoryDto;
import com.zte.daip.manager.patcher.handler.api.PatchHandler;
import com.zte.daip.manager.patcher.handler.api.dto.PatchPageTableDto;
import com.zte.daip.manager.patcher.handler.api.dto.PendingUpdatePatchPageInfo;
import com.zte.daip.manager.patcher.handler.impl.paas.service.PatchDetailPagingAssembler;
import com.zte.daip.manager.patcher.handler.impl.paas.service.UpdatePatchPagingService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
public class PatchHandlerImpl implements PatchHandler {
    @Autowired
    private UpdatePatchPagingService updatePatchPagingService;

    @Autowired
    private PatchDetailPagingAssembler patchDetailPagingAssembler;

    @Autowired
    private PatcherInfoControllerApi patcherInfoControllerApi;

    @Override
    @ApiOperation(value = "查询补丁", notes = "查询补丁")
    @ApiResponses(@ApiResponse(code = 200, message = "符合dataTable返回格式", response = String.class))
    public String
        queryPatch(@ApiParam(value = "补丁查询分页参数对象", required = true) @RequestBody PatchPageTableDto patchPageTableDto) {
        PendingUpdatePatchPageInfo pendingUpdatePatchPageInfo = new PendingUpdatePatchPageInfo();

        List<PatchDetailDto> patchList = patcherInfoControllerApi.queryPatches();

        if (!CollectionUtils.isEmpty(patchList)) {
            pendingUpdatePatchPageInfo.setDependPatchIsOkFlg(patchList.get(0).isDependPatchIsOkFlg());
        }
        final List<String> patchType = getPatchType(patchList);

        pendingUpdatePatchPageInfo.setPatchType(patchType);

        PageInfo<PatchDetailDto> page =
            updatePatchPagingService.page(patchList, patchPageTableDto.getSearchKeyWord(), patchPageTableDto);

        pendingUpdatePatchPageInfo.setPageInfo(page);

        return JsonHandlerUtils.genSuccessfulRespData(pendingUpdatePatchPageInfo);
    }

    @Override
    public String queryPatchByPaging(PatchPageTableDto patchPageTableDto) {
        final QueryParam queryParam = patchDetailPagingAssembler.patchPageTableDto2QueryParamDo(patchPageTableDto);

        return JsonHandlerUtils.genSuccessfulRespData(patcherInfoControllerApi.queryPatchesByPaging(queryParam));
    }

    @Override
    public String queryPatchType() {
        return JsonHandlerUtils.genSuccessfulRespData(patcherInfoControllerApi.queryPatchesType());
    }

    @Override
    @ApiOperation(value = "查询补丁分发", notes = "查询补丁分发信息")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁分发记录", response = List.class))
    public List<PatchDispatchDto> queryPatchesDispatch(
        @ApiParam(name = "patchName", value = "补丁名称", required = true) @RequestParam("patchName") String patchName,
        @ApiParam(name = "resultType", value = "分发结果类型",
            allowableValues = "success,error") @RequestParam("resultType") String resultType) {

        return patcherInfoControllerApi.queryPatchesDispatch(patchName, resultType);
    }

    @Override
    @ApiOperation(value = "查询补丁记录", notes = "查询补丁记录信息")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁升级记录", response = List.class))
    public List<PatchHistoryDto> queryPatchHistoryHostInfo(
        @ApiParam(name = "patchName", value = "补丁名称", required = true) @RequestParam("patchName") String patchName,
        @ApiParam(name = "serviceName", value = "服务名称",
            required = true) @RequestParam("serviceName") String serviceName) {
        return patcherInfoControllerApi.queryPatchesHistory(patchName, serviceName);
    }

    @Override
    @ApiOperation(value = "根据补丁升级类型查询补丁记录", notes = "查询补丁记录信息")
    @ApiResponses(@ApiResponse(code = 200, message = "补丁升级记录", response = List.class))
    public List<PatchHistoryDto> queryPatchHistoryHostInfo(
        @ApiParam(name = "patchName", value = "补丁名称", required = true) @RequestParam("patchName") String patchName,
        @ApiParam(name = "serviceName", value = "服务名称",
            required = true) @RequestParam("serviceName") String serviceName,
        @ApiParam(name = "version", value = "补丁版本") @RequestParam("version") String version,
        @ApiParam(name = "updateType", value = "升级结果类型",
            allowableValues = "updated,pending") @RequestParam("updateType") String updateType) {
        return patcherInfoControllerApi.queryPatchesHistory(patchName, serviceName, version, updateType);
    }

    private List<String> getPatchType(List<PatchDetailDto> patchList) {
        return Optional.ofNullable(patchList).orElse(Lists.newArrayList()).stream()
            .map(PatchDetailDto::getRealPatchType).distinct().collect(Collectors.toList());
    }
}
