/**
 * <p><owner>00139549</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: I18nKeyConstants.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/9/25</p>
 * <p>完成日期：2023/9/25</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas.constant;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public class I18nKeyConstants
{
    private I18nKeyConstants()
    {
    }
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_CREATE = "com_zte_daip_patcher_patch_task_create";
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_MODIFY = "com_zte_daip_patcher_patch_task_modify";
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_DELETE = "com_zte_daip_patcher_patch_task_delete";
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_QUERY = "com_zte_daip_patcher_patch_task_query";
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_TRIGGER = "com_zte_daip_patcher_patch_task_trigger";
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_RETRY = "com_zte_daip_patcher_patch_task_retry";
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_PAUSE = "com_zte_daip_patcher_patch_task_pause";
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_RESUME = "com_zte_daip_patcher_patch_task_resume";
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_ROLLBACK = "com_zte_daip_patcher_patch_task_rollback";
    public static final String COM_ZTE_DAIP_PATCHER_PATCH_TASK_RESOURCE = "com_zte_daip_patcher_patch_task_resource";

}