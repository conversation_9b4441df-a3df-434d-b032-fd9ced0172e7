#!/bin/sh

function modify_dir_permission {
  local dir=$1
  if [ ! -d ${dir} ]; then
    echo "${dir} is a not directory, skip."
    return
  fi

  testPermFile=${dir}/perm-`hostname`.tmp
  touch ${testPermFile}
  owner=`stat -c '%U' ${testPermFile}`
  rm -f ${testPermFile}
  if [ `whoami` != ${owner} ]; then
    echo "the directory may be a squashed nfs directory, skip change permission"
    return
  fi

  files=`find ${dir} ! -user oes ! -user nobody`
  for f in ${files}
  do
    chown @non.root.uid@:@non.root.gid@ ${f}
  done
}

function modify_dirs_permission {
  local dirs=$@
  for dir in ${dirs}
  do
    modify_dir_permission ${dir}
  done
}

modify_dirs_permission /data1/version /cloud-disk-notification-volume
