upload_patch_zip=Upload patch package
generate_patch_file_2_temp_dir=Generate patch files to temp dir
delete_patch=Patch Delete File
dispatch_patch=Dispatch patch
upgrade_patch=Upgrade patch
rollback_patch=Rollback patch
merge_patch_zip=Merge patch zip
upload_patch_notification=Patch Upload File
query_update_patch_permit=Query update patch permit
query_update_patch_progress=Query update patch progress
com_zte_daip_patcher_patch_task_create=Create patch task
com_zte_daip_patcher_patch_task_modify=Modify patch task
com_zte_daip_patcher_patch_task_delete=Delete patch task
com_zte_daip_patcher_patch_task_query=Query patch task
com_zte_daip_patcher_patch_task_trigger=Trigger patch task
com_zte_daip_patcher_patch_task_retry=Retry patch task
com_zte_daip_patcher_patch_task_pause=Pause patch task
com_zte_daip_patcher_patch_task_resume=Resume patch task
com_zte_daip_patcher_patch_task_rollback=Rollback patch task
com_zte_daip_patcher_patch_task_resource=Query patch task resource
patch_zip_upload=Upload patch zip
patch_zip=Patch
upload_patch_chunk_failed=Upload patch chunk failed:{0}
patch_chunk_merge=Merge patch chunk
merge_patch_chunk_failed=Merge patch chunk failed:{0}
patch_info=Patch info:{0}
daip-patcher-handler=patcher