upload_patch_zip=\u4E0A\u4F20\u8865\u4E01\u5305
generate_patch_file_2_temp_dir=\u5728\u4E34\u65F6\u76EE\u5F55\u751F\u6210\u8865\u4E01\u6587\u4EF6
delete_patch=\u8865\u4E01\u5220\u9664\u6587\u4EF6
dispatch_patch=\u5206\u53D1\u8865\u4E01
upgrade_patch=\u5347\u7EA7\u8865\u4E01
rollback_patch=\u56DE\u9000\u8865\u4E01
merge_patch_zip=\u5408\u5E76\u8865\u4E01\u5305
upload_patch_notification=\u8865\u4E01\u4E0A\u4F20\u6587\u4EF6
query_update_patch_permit=\u67E5\u8BE2\u8865\u4E01\u5347\u7EA7\u8BB8\u53EF
query_update_patch_progress=\u67E5\u8BE2\u8865\u4E01\u5347\u7EA7\u8FDB\u5EA6
com_zte_daip_patcher_patch_task_create=\u521B\u5EFA\u8865\u4E01\u4EFB\u52A1
com_zte_daip_patcher_patch_task_modify=\u4FEE\u6539\u8865\u4E01\u4EFB\u52A1
com_zte_daip_patcher_patch_task_delete=\u5220\u9664\u8865\u4E01\u4EFB\u52A1
com_zte_daip_patcher_patch_task_query=\u67E5\u8BE2\u8865\u4E01\u4EFB\u52A1
com_zte_daip_patcher_patch_task_trigger=\u89E6\u53D1\u8865\u4E01\u4EFB\u52A1
com_zte_daip_patcher_patch_task_retry=\u91CD\u8BD5\u8865\u4E01\u4EFB\u52A1
com_zte_daip_patcher_patch_task_pause=\u6682\u505C\u8865\u4E01\u4EFB\u52A1
com_zte_daip_patcher_patch_task_resume=\u91CD\u8BD5\u8865\u4E01\u4EFB\u52A1
com_zte_daip_patcher_patch_task_rollback=\u56DE\u9000\u8865\u4E01\u4EFB\u52A1
com_zte_daip_patcher_patch_task_resource=\u67E5\u8BE2\u8865\u4E01\u4EFB\u52A1\u8D44\u6E90
patch_zip_upload=\u4E0A\u4F20\u8865\u4E01\u5305
patch_zip=\u8865\u4E01
upload_patch_chunk_failed=\u8865\u4E01\u5206\u7247\u4E0A\u4F20\u5931\u8D25:{0}
patch_chunk_merge=\u8865\u4E01\u5206\u7247\u5408\u5E76
merge_patch_chunk_failed=\u8865\u4E01\u5206\u7247\u5408\u5E76\u5931\u8D25:{0}
patch_info=\u8865\u4E01\u4FE1\u606F:{0}
daip-patcher-svr=\u8865\u4E01\u7BA1\u7406