[{"url": "/api/daip-patcher-handler/v1/patches/view", "method": "POST", "operation": "operation.daip.patcher.update.patcher.view"}, {"url": "/api/daip-patcher-handler/v1/patches/uploading", "method": "POST", "operation": "operation.daip.patcher.update.patcher.upload"}, {"url": "/api/daip-patcher-handler/v1/patches/dispatch", "method": "POST", "operation": "operation.daip.patcher.update.patcher.dispatcher"}, {"url": "/api/daip-patcher-handler/v1/patches/update", "method": "POST", "operation": "operation.daip.patcher.update.patcher.update"}, {"url": "/api/daip-patcher-handler/v1/patches/deletion", "method": "POST", "operation": "operation.daip.patcher.update.patcher.delete"}, {"url": "/api/{respath:.+}", "method": "GET", "operation": "operation.daip.deployer.management.cluster.view"}, {"url": "/api/{respath:.+}", "method": "POST", "operation": "operation.daip.deployer.management.cluster.view"}, {"url": "/api/daip-patcher-handler/v1/patches/rollback/services", "method": "POST", "operation": "operation.daip.patcher.update.patcher.rollback"}, {"url": "/api/daip-patcher-handler/v1/patch/task/create", "method": "POST", "operation": "operation.daip.patcher.oku.task.create"}, {"url": "/api/daip-patcher-handler/v1/patch/task/modify", "method": "POST", "operation": "operation.daip.patcher.oku.task.modify"}, {"url": "/api/daip-patcher-handler/v1/patch/task/remove", "method": "POST", "operation": "operation.daip.patcher.oku.task.remove"}, {"url": "/api/daip-patcher-handler/v1/patch/tasks", "method": "GET", "operation": "operation.daip.patcher.oku.task.view"}, {"url": "/api/daip-patcher-handler/v1/patch/task/trigger", "method": "POST", "operation": "operation.daip.patcher.oku.task.run"}, {"url": "/api/daip-patcher-handler/v1/patch/task/pause", "method": "POST", "operation": "operation.daip.patcher.oku.task.pause"}, {"url": "/api/daip-patcher-handler/v1/patch/task/resume", "method": "POST", "operation": "operation.daip.patcher.oku.task.resume"}, {"url": "/api/daip-patcher-handler/v1/patch/task/get/detail/url", "method": "GET", "operation": "operation.daip.patcher.oku.task.detail.view"}, {"url": "/api/daip-patcher-handler/v1/patch/task/retry", "method": "POST", "operation": "operation.daip.patcher.oku.task.retry"}, {"url": "/api/daip-patcher-handler/v1/patch/task/rollback", "method": "POST", "operation": "operation.daip.patcher.oku.task.rollback"}]