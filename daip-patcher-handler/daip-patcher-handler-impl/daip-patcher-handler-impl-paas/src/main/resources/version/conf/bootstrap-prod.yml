spring:
  main:
    allow-circular-references: true
  messages:
    basename: i18n/daip-patcher-handler-i18n
    encoding: UTF-8
    fallback-to-system-locale: true
  application:
    name: @handler.microservice.name@
  jersey:
    application-path: /api/@handler.microservice.name@/v1
  cloud:
    zookeeper:
      enabled: false
  kafka:
    bootstrap-servers: @kafka.bootstrap.servers.url@
    producer:
      retries: 0
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: @handler.microservice.name@
      auto-offset-reset: latest
      enable-auto-commit: true
      auto-commit-interval: 100
      session.timeout.ms: 15000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 10240MB

logging:
  config: classpath:logback.xml
  path: ./logs
dexcloud:
  base:
    microservice:
      name: @handler.microservice.name@
      version: v1
      organization: zdh
  rpc:
    httpconf:
      readTimeout: 600000
      connectTimeout: 600000
      writeTimeout: 600000
  discovery:
    msb:
      enabled: true
      server:
        address: ${msb_svrIp}
        port: ${msb_svrPort}
        namespace: ${msb_nameSpace}
contentlengthlimitconf:
  defaultlimit: -1
  limits:
    api: /api/daip-patcher-handler/v1/patches/uploading
    limit: -1

commonpgservice:
  user: daip_metadata_U_name

daip:
  msb:
    server:
      ip: ${msb_svrIp}
      port: ${msb_svrPort}

kafkaclientconf:
  bootstrapServers: ${kafka_brokers}:${kafka_port}
  kafkaServiceName: kafka
  kafkaServiceVersion: v1
  consumerConf:
    properties:
      group.id: @handler.microservice.name@
      value.deserializer: org.apache.kafka.common.serialization.StringDeserializer
  producerConf:
    properties:
      key.serializer: org.apache.kafka.common.serialization.StringSerializer
      value.serializer: org.apache.kafka.common.serialization.StringSerializer
      compression.type: gzip
      retries: 1