{"service_type": "daip-patcher", "service_version": "@product.version@", "operation_list": [{"nodeid": "operation.daip.deployer.management", "nodename": ["Cluster Management", "集群部署"], "leaf": false, "level": 4, "description": ["Cluster Management", "集群部署"], "parent_id": "global.operation"}, {"nodeid": "operation.daip.deployer.update", "nodename": ["Update Management", "升级管理"], "leaf": false, "level": 4, "description": ["Update Management", "升级管理"], "parent_id": "operation.daip.deployer.management", "menuID": "daip-update-management"}, {"nodeid": "operation.daip.patcher.update.patcher", "nodename": ["Patcher Update", "补丁升级"], "leaf": false, "level": 3, "description": ["Patcher Update", "补丁升级"], "parent_id": "operation.daip.deployer.update", "menuID": "daip-patcher-update"}, {"nodeid": "operation.daip.patcher.update.patcher.view", "nodename": ["Patcher View", "查看补丁"], "leaf": true, "level": 4, "description": ["Patcher View", "查看补丁"], "parent_id": "operation.daip.patcher.update.patcher", "pre_operation": "operation.daip.patcher.management.host.view"}, {"nodeid": "operation.daip.patcher.update.patcher.upload", "nodename": ["Patcher Upload", "上传补丁"], "leaf": true, "level": 3, "description": ["Patcher Upload", "上传补丁"], "parent_id": "operation.daip.patcher.update.patcher", "pre_operation": "operation.daip.patcher.update.patcher.view"}, {"nodeid": "operation.daip.patcher.update.patcher.dispatcher", "nodename": ["<PERSON><PERSON>", "分发补丁"], "leaf": true, "level": 3, "description": ["<PERSON><PERSON>", "分发补丁"], "parent_id": "operation.daip.patcher.update.patcher", "pre_operation": "operation.daip.deployer.management.host.view:operation.daip.deployer.management.service.view:operation.daip.patcher.update.patcher.view"}, {"nodeid": "operation.daip.patcher.update.patcher.update", "nodename": ["Patcher Update", "升级补丁"], "leaf": true, "level": 3, "description": ["Patcher Update", "升级补丁"], "parent_id": "operation.daip.patcher.update.patcher", "pre_operation": "operation.daip.deployer.management.host.view:operation.daip.deployer.management.service.view:operation.daip.patcher.update.patcher.view:operation.daip.task.management.workflow.view:operation.daip.task.management.workflow.run:operation.daip.task.management.workflow.retry"}, {"nodeid": "operation.daip.patcher.update.patcher.delete", "nodename": ["<PERSON><PERSON> Delete", "删除补丁"], "leaf": true, "level": 3, "description": ["<PERSON><PERSON> Delete", "删除补丁"], "parent_id": "operation.daip.patcher.update.patcher", "pre_operation": "operation.daip.deployer.management.host.view:operation.daip.patcher.update.patcher.view"}, {"nodeid": "operation.daip.patcher.update.patcher.rollback", "nodename": ["<PERSON><PERSON>", "回退补丁"], "leaf": true, "level": 3, "description": ["<PERSON><PERSON>", "回退补丁"], "parent_id": "operation.daip.patcher.update.patcher", "pre_operation": "operation.daip.deployer.management.host.view:operation.daip.deployer.management.service.view:operation.daip.patcher.update.patcher.view"}, {"nodeid": "operation.daip.patcher.oku.task", "nodename": ["One-click patch upgrade", "补丁一键升级"], "leaf": false, "level": 3, "description": ["One-click patch upgrade", "补丁一键升级"], "parent_id": "operation.daip.patcher.update.patcher"}, {"nodeid": "operation.daip.patcher.oku.task.view", "nodename": ["View tasks", "查看任务"], "leaf": true, "level": 4, "description": ["View tasks", "查看任务"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.management.host.view"}, {"nodeid": "operation.daip.patcher.oku.task.create", "nodename": ["Create task", "创建任务"], "leaf": true, "level": 3, "description": ["Create task", "创建任务"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.oku.task.view"}, {"nodeid": "operation.daip.patcher.oku.task.modify", "nodename": ["Modify task", "修改任务"], "leaf": true, "level": 3, "description": ["Modify task", "修改任务"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.oku.task.view:operation.daip.patcher.oku.task.create"}, {"nodeid": "operation.daip.patcher.oku.task.remove", "nodename": ["Delete task", "删除任务"], "leaf": true, "level": 3, "description": ["Delete task", "删除任务"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.oku.task.view:operation.daip.patcher.oku.task.create:operation.daip.patcher.oku.task.modify"}, {"nodeid": "operation.daip.patcher.oku.task.run", "nodename": ["Run Task", "执行任务"], "leaf": true, "level": 3, "description": ["Run Task", "执行任务"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.oku.task.view:operation.daip.patcher.oku.task.create:operation.daip.patcher.oku.task.modify:operation.daip.patcher.oku.task.remove", "doubleAuth": true, "doubleAuthResource": false}, {"nodeid": "operation.daip.patcher.oku.task.pause", "nodename": ["Pause task", "暂停任务"], "leaf": true, "level": 3, "description": ["Pause task", "暂停任务"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.oku.task.view:operation.daip.patcher.oku.task.run:operation.daip.patcher.oku.task.detail.view", "doubleAuth": true, "doubleAuthResource": false}, {"nodeid": "operation.daip.patcher.oku.task.resume", "nodename": ["Resume task execution", "恢复任务执行"], "leaf": true, "level": 3, "description": ["Resume task execution", "恢复任务执行"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.oku.task.view:operation.daip.patcher.oku.task.detail.view:operation.daip.patcher.oku.task.pause:operation.daip.patcher.oku.task.run", "doubleAuth": true, "doubleAuthResource": false}, {"nodeid": "operation.daip.patcher.oku.task.detail.view", "nodename": ["View task details", "查看任务详情"], "leaf": true, "level": 4, "description": ["View task details", "查看任务详情"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.oku.task.view:operation.daip.patcher.oku.task.run", "doubleAuth": true, "doubleAuthResource": false}, {"nodeid": "operation.daip.patcher.oku.task.retry", "nodename": ["Retry Task", "重试任务"], "leaf": true, "level": 3, "description": ["Retry Task", "重试任务"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.oku.task.view:operation.daip.patcher.oku.task.run:operation.daip.patcher.oku.task.detail.view", "doubleAuth": true, "doubleAuthResource": false}, {"nodeid": "operation.daip.patcher.oku.task.rollback", "nodename": ["Rollback Task", "回退任务"], "leaf": true, "level": 3, "description": ["Rollback Task", "回退任务"], "parent_id": "operation.daip.patcher.oku.task", "pre_operation": "operation.daip.patcher.oku.task.view:operation.daip.patcher.oku.task.run:operation.daip.patcher.oku.task.detail.view", "doubleAuth": true, "doubleAuthResource": false}]}