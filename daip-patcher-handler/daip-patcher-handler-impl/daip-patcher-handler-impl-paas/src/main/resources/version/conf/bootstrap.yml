debug: false
server:
  servlet:
    context-path: /api/@handler.microservice.name@/v1
  port: 13001
  jetty:
    threads:
      max: ${jetty_threadpool_maxthreads}
dexcloud:
  base:
    microservice:
      name: @handler.microservice.name@
      version: v1
      organization: zdh
      metaInfo:
        scope: test
  web:
    swagger:
      beanConfig:
        title: 'DAIP patcher Service API Documentation'
        version: '1.0'
  serviceinfo:
    serviceName: @handler.microservice.name@

spring:
  main:
    allow-circular-references: true
  cloud:
    zookeeper:
      enabled: false
  jersey:
    application-path: /api/@handler.microservice.name@/v1
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration

logging:
  config: classpath:logback.xml
  path: ./logs

management:
  endpoints:
    web:
      expose: '*'
      base-path: /actuator

LOCALE: zh_CN

daip:
  version:
    main: @paas.service.version@

