<assembly>
    <id>product-handler-inner-tar</id>
    <formats>
        <!-- zip,tar,tar.gz,tar.bz2,jar,dir,war -->
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/version/
            </directory>
            <excludes>
                <exclude>conf/**</exclude>
            </excludes>
            <outputDirectory>./${handler.microservice.name}/</outputDirectory>
            <filtered>true</filtered>
            <directoryMode>0750</directoryMode>
            <fileMode>0750</fileMode>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/version/
            </directory>
            <includes>
                <include>conf/**</include>
            </includes>
            <outputDirectory>./${handler.microservice.name}/</outputDirectory>
            <filtered>true</filtered>
            <directoryMode>0750</directoryMode>
            <fileMode>0640</fileMode>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/resources/
            </directory>
            <includes>
                <include></include>
            </includes>
            <outputDirectory>./${handler.microservice.name}/lib/</outputDirectory>
            <directoryMode>0750</directoryMode>
        </fileSet>
    </fileSets>

    <dependencySets>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>./${handler.microservice.name}/lib/</outputDirectory>
            <fileMode>0640</fileMode>
            <includes>
                <include>com.zte.daip.*:*</include>
                <include>org.apache.xmlgraphics:batik-css</include>
                <include>org.owasp.esapi:esapi</include>
                <include>commons-fileupload:commons-fileupload</include>
                <include>xerces:xercesImpl</include>
                <include>commons-configuration:commons-configuration</include>
                <include>org.apache.commons:commons-lang3</include>
                <include>com.alibaba:fastjson</include>
                <include>com.alibaba.fastjson2:fastjson2-extension</include>
                <include>com.alibaba.fastjson2:fastjson2</include>
                <include>com.alibaba:cooma</include>
                <include>io.github.openfeign:feign-core</include>
                <include>log4j:log4j</include>
                <include>com.baomidou:*</include>
                <include>com.zte.zdh:zdh-commons</include>
                <include>org.apache.commons:commons-pool2</include>
                <include>org.springframework:spring-messaging</include>
                <include>org.springframework:spring-context-support</include>
                <include>org.springframework.data:spring-data-commons</include>
                <include>org.codehaus.jackson:jackson-core-asl</include>
                <include>org.codehaus.jackson:jackson-mapper-asl</include>
                <include>org.springframework.plugin:spring-plugin-core</include>
                <include>org.springframework.plugin:spring-plugin-metadata</include>
                <include>commons-lang:commons-lang</include>
                <include>io.springfox:*</include>
                <include>org.dom4j:dom4j</include>
                <include>org.springframework.security:spring-security-rsa</include>
                <include>aopalliance:aopalliance</include>
                <include>io.micrometer:*</include>
<!--                <include>org.springframework.cloud:spring-cloud-sleuth-*</include>-->
                <include>io.zipkin.*:*</include>
                <include>org.aspectj:aspectjrt</include>
                <include>com.lmax:disruptor</include>
                <include>dev.failsafe:failsafe</include>
                <include>org.apache.commons:commons-collections4</include>
                <include>com.zte.ums.zenap.sm.sso.agent:sm-sso-agent</include>
            </includes>
            <excludes>
                <exclude>${project.groupId}:${project.artifactId}</exclude>
                <exclude>com.zte.daip.manager.common:daip-cache*</exclude>
            </excludes>
        </dependencySet>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>./${handler.microservice.name}/</outputDirectory>
            <fileMode>0640</fileMode>
            <includes>
                <include>${project.groupId}:${project.artifactId}</include>
            </includes>
        </dependencySet>
    </dependencySets>
</assembly>