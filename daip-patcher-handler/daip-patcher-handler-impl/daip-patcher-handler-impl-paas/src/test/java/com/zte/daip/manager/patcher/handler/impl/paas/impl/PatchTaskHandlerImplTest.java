package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.List;

import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import com.zte.daip.manager.common.security.csrf.SpringAppCsrfProtector;
import com.zte.daip.manager.common.utils.exception.DaipBaseException;
import com.zte.daip.manager.patcher.inner.api.PatchTaskControllerApi;
import com.zte.daip.manager.patcher.inner.api.dto.*;

/**
 * <p><owner>10231312</owner> </p>
 * <p><createdate>2015-2-28</createdate></p>
 * <p>文件名称: PatchTaskHandlerImplTest.java</p>
 * <p>文件描述: 无</p>
 * <p>版权所有: 版权所有(C)2001-2020</p>
 * <p>公司名称: 深圳市中兴通讯股份有限公司</p>
 * <p>内容摘要: 无</p>
 * <p>其他说明: 无</p>
 * <p>创建日期：2023/10/10</p>
 * <p>完成日期：2023/10/10</p>
 * <p>修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容</p>
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容</p>
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */

@RunWith(SpringRunner.class)
public class PatchTaskHandlerImplTest {
    @Mock
    private PatchTaskControllerApi patchTaskControllerApi;

    @Mock
    private SpringAppCsrfProtector springAppCsrfProtector;

    @InjectMocks
    private PatchTaskHandlerImpl patchTaskHandlerImpl;

    private PatchTaskDto patchTaskDto = new PatchTaskDto();

    private List<RollBackPatchPointInfo> rollBackPatchPoints = Lists.newArrayList();

    private long taskId = 1L;

    private String clusterId = "1";

    private String taskName = "1";

    private String hdfsServiceId = "dap.amanager.hdfs";

    private String hdfsService = "hdfs";

    @Before
    public void init() {
        doNothing().when(springAppCsrfProtector).validRequest();

        PatchHostInfoDto patchHostInfoDto = new PatchHostInfoDto();
        patchHostInfoDto.setIp("*******");
        patchHostInfoDto.setHostName("host1");
        RollBackPatchPointInfo rollBackPatchPointInfo = new RollBackPatchPointInfo();
        rollBackPatchPointInfo.setRollBackPatchPoint("V20.23.40.04-CP03");
        rollBackPatchPointInfo.setPatchHostInfos(Lists.newArrayList(patchHostInfoDto));
        rollBackPatchPoints.add(rollBackPatchPointInfo);

        // serviceInstancePatchInfo
        ServiceInstancePatchInfo serviceInstancePatchInfo = new ServiceInstancePatchInfo();
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceId(hdfsServiceId);
        serviceInstance.setServiceName(hdfsService);
        serviceInstance.setServiceInstanceId(hdfsService);
        serviceInstance.setServiceInstanceName(hdfsServiceId);
        serviceInstancePatchInfo.setServiceInstance(serviceInstance);
        serviceInstancePatchInfo.setRollBackPatchPoints(rollBackPatchPoints);

        // patchTaskDto
        patchTaskDto.setTaskId(taskId);
        patchTaskDto.setTaskName(taskName);
        patchTaskDto.setClusterId(clusterId);
        patchTaskDto.setAllowModify(true);
        patchTaskDto.setContext(Lists.newArrayList(serviceInstancePatchInfo));
        patchTaskDto.setRelationServices(Lists.newArrayList(serviceInstance));
    }

    @Test
    public void createPatchTask() {
        String result = patchTaskHandlerImpl.createPatchTask(patchTaskDto);
        assertNotNull(result);
    }

    @Test
    public void modifyPatchTask() {
        String result = patchTaskHandlerImpl.modifyPatchTask(patchTaskDto);
        assertNotNull(result);
    }

    @Test
    public void removePatchTask() {
        String result = patchTaskHandlerImpl.removePatchTask(Lists.newArrayList(taskId));
        assertNotNull(result);
    }

    @Test
    public void queryPatchTasks() throws DaipBaseException {
        Mockito.when(patchTaskControllerApi.queryPatchTasks()).thenReturn(Lists.newArrayList(patchTaskDto));
        String result = patchTaskHandlerImpl.queryPatchTasks(1, 1, taskName);
        assertNotNull(result);
    }

    @Test
    public void queryPatchTasks1() throws DaipBaseException {
        Mockito.when(patchTaskControllerApi.queryPatchTasks()).thenReturn(Lists.newArrayList(patchTaskDto));
        String result = patchTaskHandlerImpl.queryPatchTasks(1, 1, "");
        assertNotNull(result);
    }

    @Test
    public void queryPatchTasks2() throws DaipBaseException {
        Mockito.when(patchTaskControllerApi.queryPatchTasks()).thenReturn(Lists.newArrayList(patchTaskDto));
        String result = patchTaskHandlerImpl.queryPatchTasks(0, 0, "");
        assertNotNull(result);
    }

    @Test
    public void queryPatchTaskByTaskId() throws DaipBaseException {
        Mockito.when(patchTaskControllerApi.queryPatchTaskByTaskId(taskId)).thenReturn(patchTaskDto);
        String result = patchTaskHandlerImpl.queryPatchTaskByTaskId(taskId);
        assertNotNull(result);
    }

    @Test
    public void triggerPatchTask() {
        String result = patchTaskHandlerImpl.triggerPatchTask(taskId, taskName);
        assertNotNull(result);
    }

    @Test
    public void retryPatchTask() {
        String result = patchTaskHandlerImpl.retryPatchTask(taskId, taskName);
        assertNotNull(result);
    }

    @Test
    public void pausePatchTask() {
        String result = patchTaskHandlerImpl.pausePatchTask(taskId, taskName);
        assertNotNull(result);
    }

    @Test
    public void resumePatchTask() {
        String result = patchTaskHandlerImpl.resumePatchTask(taskId, taskName);
        assertNotNull(result);
    }

    @Test
    public void queryServiceInstanceUpgrade() throws DaipBaseException {
        VersionQueryRequestInfo versionQueryRequestInfo = new VersionQueryRequestInfo();
        List<ServiceVersionInfo> serviceVersionInfoList = new ArrayList<>();
        versionQueryRequestInfo.setServiceVersionInfoList(serviceVersionInfoList);
        String result = patchTaskHandlerImpl.queryServiceInstanceUpgrade(versionQueryRequestInfo);
        assertNotNull(result);
    }

    @Test
    public void queryServiceInstanceUpgrade1() throws DaipBaseException {
        VersionQueryRequestInfo versionQueryRequestInfo = new VersionQueryRequestInfo();
        List<ServiceVersionInfo> serviceVersionInfoList = new ArrayList<>();
        ServiceVersionInfo serviceVersionInfo = new ServiceVersionInfo();
        serviceVersionInfoList.add(serviceVersionInfo);
        versionQueryRequestInfo.setServiceVersionInfoList(serviceVersionInfoList);
        Mockito.when(patchTaskControllerApi.queryServiceInstanceUpgrade(versionQueryRequestInfo))
            .thenReturn(Lists.newArrayList());
        String result = patchTaskHandlerImpl.queryServiceInstanceUpgrade(versionQueryRequestInfo);
        assertNotNull(result);
    }

    @Test
    public void rollbackPatchTask() {
        String result = patchTaskHandlerImpl.rollbackPatchTask(patchTaskDto);
        assertNotNull(result);
    }
    /* Started by AICoder, pid:8eaf1cec6b6091014992095700461a446708b349 */
    @Test
    public void queryNeedRestartService() throws DaipBaseException {
        // 调用patchTaskHandlerImpl的queryNeedRestartService方法，传入patchTaskDto对象
        String result = patchTaskHandlerImpl.queryNeedRestartService(patchTaskDto);

        // 使用Mockito模拟patchTaskControllerApi的queryNeedRestartService方法返回一个空列表
        Mockito.when(patchTaskControllerApi.queryNeedRestartService(patchTaskDto)).thenReturn(new ArrayList<>());

        // 断言结果不为null
        assertNotNull(result);
    }

    @Test
    public void queryNeedRestartServiceFailed2() throws DaipBaseException {
        // 设置patchTaskDto的relationServices属性为空列表
        patchTaskDto.setRelationServices(Lists.newArrayList());

        // 调用patchTaskHandlerImpl的queryNeedRestartService方法，传入patchTaskDto对象
        String result = patchTaskHandlerImpl.queryNeedRestartService(patchTaskDto);

        // 使用Mockito模拟patchTaskControllerApi的queryNeedRestartService方法抛出DaipBaseException异常
        Mockito.doThrow(new DaipBaseException()).when(patchTaskControllerApi).queryNeedRestartService(patchTaskDto);

        // 断言结果不为null
        assertNotNull(result);
    }

    @Test
    public void queryNeedRestartServiceFailed1() throws DaipBaseException {
        // 创建一个ServiceInstance对象，并设置其属性
        ServiceInstance serviceInstance = new ServiceInstance();
        serviceInstance.setServiceId(hdfsServiceId);
        serviceInstance.setServiceName(hdfsService);
        serviceInstance.setServiceInstanceId(hdfsService);
        serviceInstance.setServiceInstanceName(hdfsServiceId);

        // 设置patchTaskDto的relationServices属性为包含serviceInstance的列表
        patchTaskDto.setRelationServices(Lists.newArrayList(serviceInstance));

        // 调用patchTaskHandlerImpl的queryNeedRestartService方法，传入patchTaskDto对象
        String result = patchTaskHandlerImpl.queryNeedRestartService(patchTaskDto);

        // 使用Mockito模拟patchTaskControllerApi的queryNeedRestartService方法返回一个空列表
        Mockito.when(patchTaskControllerApi.queryNeedRestartService(patchTaskDto)).thenReturn(new ArrayList<>());

        // 断言结果不为null
        assertNotNull(result);
    }

    /* Ended by AICoder, pid:8eaf1cec6b6091014992095700461a446708b349 */

    @Test
    public void checkTaskRollback() throws DaipBaseException {
        Mockito.when(patchTaskControllerApi.checkTaskCanRollback(1)).thenReturn(PatchOperateResult.success(""));
        PatchOperateResult result = patchTaskHandlerImpl.checkTaskRollback(1);
        assertNotNull(result);
    }

    @Test
    public void checkTaskDuplicate() throws DaipBaseException {
        Mockito.when(patchTaskControllerApi.checkTaskCanDuplicate(1)).thenReturn(PatchOperateResult.success(""));
        PatchOperateResult result = patchTaskHandlerImpl.checkTaskDuplicate(1);
        assertNotNull(result);
    }

    @Test
    public void copyPatchTask() throws DaipBaseException {
        Mockito.when(patchTaskControllerApi.copyPatchTask(1)).thenReturn(true);
        String result = patchTaskHandlerImpl.copyPatchTask(1, "");
        assertNotNull(result);
    }

    @Test
    public void queryPatchTaskDetailByTaskId() throws DaipBaseException {
        Mockito.when(patchTaskControllerApi.queryPatchDetailTasks(1)).thenReturn(new PatchTaskDto());
        String result = patchTaskHandlerImpl.queryPatchTaskDetailByTaskId(1);
        assertNotNull(result);
    }

    /* Started by AICoder, pid:ka61555492c4e2714d1a08ea00fc9e17cb12a697 */
    @Test
    public void testQueryPatchTaskDetailUrlSuccess() {
        // Given


        // When
        String result = patchTaskHandlerImpl.queryPatchTaskDetailUrl();

        // Then
        assertEquals("{\"status\":\"0\",\"data\":\"/upgrade/taskDetail\"}", result);
    }
    /* Ended by AICoder, pid:ka61555492c4e2714d1a08ea00fc9e17cb12a697 */
}