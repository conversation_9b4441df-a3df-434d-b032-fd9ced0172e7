/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchDispatchHandlerTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/29
 * </p>
 * <p>
 * 完成日期：2021/3/29
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas;

import com.zte.daip.manager.patcher.api.PatcherDispatchControllerApi;
import com.zte.daip.manager.patcher.handler.impl.paas.impl.PatchDispatchHandlerImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static junit.framework.TestCase.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchDispatchHandlerTest {
    private MockMvc mockMvc;

    @Mock
    private PatcherDispatchControllerApi patcherDispatchControllerApi;

    @InjectMocks
    private PatchDispatchHandlerImpl controller;

    @Before
    public void setUp() {

        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();

        doNothing().when(patcherDispatchControllerApi).dispatchPatches();

    }

    @Test
    public void 分发补丁UI接口状态正常() throws Exception {

        final String contentAsString = mockMvc.perform(MockMvcRequestBuilders.post("/patches/dispatch"))
            .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        assertTrue(contentAsString.contains("0"));
    }

    @Test
    public void 分发补丁检查UI接口状态正常() throws Exception {

        final String contentAsString = mockMvc.perform(MockMvcRequestBuilders.post("/patches/dispatch/precheck"))
            .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        assertTrue(contentAsString.contains("0"));
    }
}