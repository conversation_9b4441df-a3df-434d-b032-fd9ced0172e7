package com.zte.daip.manager.patcher.handler.impl.paas.service.event;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.zte.daip.manager.event.beans.OperationDesc;
import com.zte.daip.manager.event.beans.OperationResult;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;

@RunWith(MockitoJUnitRunner.class)
public class LogEventTest {
    @InjectMocks
    private LogEvent logEvent;
    @Mock
    private DaipEventReporter daipEventReporter;

    @Test
    public void logOpt() {
        String operationNameI18nKey = "";
        String operationObjI18nKey = "";
        String failReason = "";

        logEvent.logOpt(operationNameI18nKey, operationObjI18nKey, failReason);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void testLogOpt2() {
        String operationNameI18nKey = "";
        String[] operationNameI18nArgs = new String[0];
        String operationObjI18nKey = "";
        OperationDesc optDesc = null;
        String failReason = "";

        logEvent.logOpt(operationNameI18nKey, operationNameI18nArgs, operationObjI18nKey, optDesc, failReason);
        verify(daipEventReporter, times(1)).report(any());
    }

    @Test
    public void testLogOpt3() {
        String operationNameI18nKey = "";
        String[] operationNameI18nArgs = new String[0];
        String operationObjI18nKey = "";
        OperationDesc optDesc = null;
        OperationResult operationResult = OperationResult.succeed();

        logEvent.logOpt(operationNameI18nKey, operationNameI18nArgs, operationObjI18nKey, optDesc, operationResult);
        verify(daipEventReporter, times(1)).report(any());
    }

}
