/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: UpdatePatchPagingServiceTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/30
 * </p>
 * <p>
 * 完成日期：2021/3/30
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.daip.manager.common.utils.paging.bean.PageInfo;
import com.zte.daip.manager.patcher.api.dto.PatchDetailDto;
import com.zte.daip.manager.patcher.handler.api.dto.PatchPageTableDto;
import com.zte.daip.manager.patcher.handler.api.dto.PendingUpdatePatchPageInfo;
import com.zte.daip.manager.patcher.handler.impl.paas.service.UpdatePatchPagingService;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.assertEquals;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
public class UpdatePatchPagingServiceTest {

    private UpdatePatchPagingService updatePatchPagingService;

    @Before
    public void setUp() {
        updatePatchPagingService = new UpdatePatchPagingService();
    }

    @Test
    public void 前台分页测试() {
        PendingUpdatePatchPageInfo pendingUpdatePatchPageInfo = new PendingUpdatePatchPageInfo();
        String requestJsonString =
            "{\"patchType\":\"ZOOKEEPER\",\"searchKeyWord\":\"DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP01\",\"sEcho\":1,\"iDisplayStart\":0,\"iDisplayLength\":10,\"iSortCol_0\":1,\"sSortDir_0\":\"desc\"}";

        PatchPageTableDto patchPageTableDto = jsonString2Obj(requestJsonString);

        final List<PatchDetailDto> patchDetailDtos = mockPatchDetailDto();
        PageInfo<PatchDetailDto> page =
            updatePatchPagingService.page(patchDetailDtos, patchPageTableDto.getSearchKeyWord(), patchPageTableDto);

        pendingUpdatePatchPageInfo.setPageInfo(page);

        assertEquals(10, page.getData().size());
    }

    private PatchPageTableDto jsonString2Obj(String str) {
        ObjectMapper objectMapper = new ObjectMapper();
        PatchPageTableDto patchPageTableDto = null;

        try {
            patchPageTableDto = objectMapper.readValue(str, PatchPageTableDto.class);
        } catch (JsonProcessingException e) {
        }
        return patchPageTableDto;

    }

    private List<PatchDetailDto> mockPatchDetailDto() {
        int count = 100;
        List<PatchDetailDto> patchDetailDtos = Lists.newArrayList();
        for (int i = 0; i < count; i++) {
            PatchDetailDto patchDetailDto =
                new PatchDetailDto("DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP0" + i, "V20.19.40.R4.B2", "ZOOKEEPER");
            patchDetailDtos.add(patchDetailDto);
        }

        return patchDetailDtos;
    }
}