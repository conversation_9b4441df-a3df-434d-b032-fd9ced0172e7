package com.zte.daip.manager.patcher.handler.impl.paas.service;

import com.zte.daip.manager.common.utils.paging.jpa.QueryParam;
import com.zte.daip.manager.patcher.handler.api.dto.PatchPageTableDto;
import org.junit.Before;
import org.junit.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PatchDetailPagingAssemblerTest {

    private PatchDetailPagingAssembler patchDetailPagingAssemblerUnderTest;

    @Before
    public void before() {
        patchDetailPagingAssemblerUnderTest = new PatchDetailPagingAssembler();
    }

    @Test
    public void testPatchPageTableDto2QueryParamDo() {
        // Setup
        final PatchPageTableDto patchPageTableDto = new PatchPageTableDto("patchType(123)", "searchKeyWord");
        patchPageTableDto.setPageDisplayLength(10);
        patchPageTableDto.setPageDisplayStart(0);

        // Run the test
        final QueryParam result = patchDetailPagingAssemblerUnderTest.patchPageTableDto2QueryParamDo(patchPageTableDto);

        // Verify the results
        assertEquals(10, result.getPageSize());
    }
}
