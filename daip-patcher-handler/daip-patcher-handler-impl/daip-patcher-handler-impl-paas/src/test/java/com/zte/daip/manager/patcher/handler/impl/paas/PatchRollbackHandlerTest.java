/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchRollbackHandlerTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/30
 * </p>
 * <p>
 * 完成日期：2021/3/30
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 * 
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 * 
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas;

import com.google.common.collect.Lists;
import com.zte.daip.manager.patcher.api.PatchRollbackControllerApi;
import com.zte.daip.manager.patcher.api.dto.RollBackPatchProgress;
import com.zte.daip.manager.patcher.api.dto.RollbackServiceProgressDto;
import com.zte.daip.manager.patcher.handler.impl.paas.impl.PatchRollbackHandlerImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static junit.framework.TestCase.assertTrue;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.anyString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchRollbackHandlerTest {
    private MockMvc mockMvc;

    @InjectMocks
    private PatchRollbackHandlerImpl patchRollbackHandler;
    @Mock
    private PatchRollbackControllerApi patchRollbackControllerApi;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(new PatchRollbackHandlerImpl()).build();
    }

    @Test
    public void queryProgress() throws Exception {
        List<RollbackServiceProgressDto> rollbackServiceProgressDtos = Lists.newArrayList();
        RollbackServiceProgressDto rollbackServiceProgressDto = new RollbackServiceProgressDto();
        rollbackServiceProgressDto.setFailed(0);
        rollbackServiceProgressDto.setSuccess(2);
        rollbackServiceProgressDto.setTotal(2);
        rollbackServiceProgressDtos.add(rollbackServiceProgressDto);

        RollbackServiceProgressDto rollbackServiceProgressDto1 = new RollbackServiceProgressDto();
        rollbackServiceProgressDto1.setFailed(0);
        rollbackServiceProgressDto1.setSuccess(2);
        rollbackServiceProgressDto1.setTotal(2);
        rollbackServiceProgressDtos.add(rollbackServiceProgressDto1);

        RollBackPatchProgress rollBackPatchProgress =
            new RollBackPatchProgress(rollbackServiceProgressDtos, true, true);

        Mockito.when(patchRollbackControllerApi.queryRollBackProgress(anyString())).thenReturn(rollBackPatchProgress);
        RollBackPatchProgress r = patchRollbackHandler.queryRollBackProgress("q1");
        assertTrue(r.isSuccess());
        assertTrue(r.isFinish());
    }
}