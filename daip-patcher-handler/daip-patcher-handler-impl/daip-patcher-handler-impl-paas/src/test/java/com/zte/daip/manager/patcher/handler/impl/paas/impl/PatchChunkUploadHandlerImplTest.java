
package com.zte.daip.manager.patcher.handler.impl.paas.impl;

import com.zte.daip.manager.common.utils.file.chunk.FileUploadPathEntity;
import com.zte.daip.manager.common.utils.handler.JsonHandlerUtils;
import com.zte.daip.manager.common.utils.i18n.DaipI18nService;
import com.zte.daip.manager.event.reporter.api.DaipEventReporter;
import com.zte.daip.manager.patcher.api.PatcherUploadControllerApi;
import com.zte.daip.manager.patcher.api.util.PatchDapLogBuilder;
import com.zte.daip.manager.patcher.handler.impl.paas.service.event.LogEvent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.File;

@ContextConfiguration(classes = {PatchChunkUploadHandlerImpl.class})

@RunWith(SpringRunner.class)
public class PatchChunkUploadHandlerImplTest {

    @Autowired
    private PatchChunkUploadHandlerImpl patchChunkUploadHandlerImpl;

    @MockBean
    private DaipEventReporter mockDaipEventReporter;

    @MockBean
    private PatcherUploadControllerApi mockPatcherUploadControllerApi;

    @MockBean
    private LogEvent logEvent;

    @MockBean
    private DaipI18nService daipI18nService;

    @MockBean
    private PatchDapLogBuilder patchDapLogBuilder;

    private String applicationPath =
        Thread.currentThread().getContextClassLoader().getResource("application.yml").getPath();

    @Test
    public void testDeleteChunkFile_DIR_INCORRECT() throws Exception {
        // Setup

        MockHttpServletRequestBuilder requestBuilder =
            MockMvcRequestBuilders.post("/patches/chunk/delete").param("fileMd5", "test");

        patchChunkUploadHandlerImpl.setFileUploadPathEntity(new FileUploadPathEntity("", ""));
        // Verify the results
        MockMvcBuilders.standaloneSetup(this.patchChunkUploadHandlerImpl).build().perform(requestBuilder)
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    public void testDeleteChunkFile_SUCCESS() throws Exception {
        // Setup

        MockHttpServletRequestBuilder requestBuilder =
            MockMvcRequestBuilders.post("/patches/chunk/delete").param("fileMd5", "test");

        patchChunkUploadHandlerImpl.setFileUploadPathEntity(
            new FileUploadPathEntity(new File(applicationPath).getParent() + File.separator + "chunk", ""));

        // Verify the results
        MockMvcBuilders.standaloneSetup(this.patchChunkUploadHandlerImpl).build().perform(requestBuilder)
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.content().string(JsonHandlerUtils.SUCCESS_MESSAGE));
    }

}
