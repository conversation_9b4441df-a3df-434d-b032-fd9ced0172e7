/**
 * <p>
 * <owner>10208178</owner>
 * </p>
 * <p>
 * <createdate>2015-2-28</createdate>
 * </p>
 * <p>
 * 文件名称: PatchHandlerTest.java
 * </p>
 * <p>
 * 文件描述: 无
 * </p>
 * <p>
 * 版权所有: 版权所有(C)2001-2020
 * </p>
 * <p>
 * 公司名称: 深圳市中兴通讯股份有限公司
 * </p>
 * <p>
 * 内容摘要: 无
 * </p>
 * <p>
 * 其他说明: 无
 * </p>
 * <p>
 * 创建日期：2021/3/29
 * </p>
 * <p>
 * 完成日期：2021/3/29
 * </p>
 * <p>
 * 修改记录1: // 修改历史记录，包括修改日期、修改者及修改内容
 * </p>
 *
 * <pre>
 *    修改日期：
 *    版 本 号：
 *    修 改 人：
 *    修改内容：
 * </pre>
 * <p>
 * 评审记录1: // 评审历史记录，包括评审日期、评审人及评审内容
 * </p>
 *
 * <pre>
 *    评审日期：
 *    版 本 号：
 *    评 审 人：
 *    评审内容：
 * </pre>
 *
 * @version 1.0
 * <AUTHOR>
 */
package com.zte.daip.manager.patcher.handler.impl.paas;

import com.alibaba.fastjson.JSONObject;
import com.zte.daip.manager.patcher.api.PatcherInfoControllerApi;
import com.zte.daip.manager.patcher.handler.api.dto.PatchPageTableDto;
import com.zte.daip.manager.patcher.handler.impl.paas.impl.PatchHandlerImpl;
import com.zte.daip.manager.patcher.handler.impl.paas.service.PatchDetailPagingAssembler;
import com.zte.daip.manager.patcher.handler.impl.paas.service.UpdatePatchPagingService;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.anyString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 功能描述:<br>
 *
 *
 * <p>
 * Note:
 *
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(SpringRunner.class)
public class PatchHandlerTest {
    @Mock
    private PatcherInfoControllerApi patcherInfoControllerApi;

    @Mock
    private PatchDetailPagingAssembler patchDetailPagingAssembler;

    @Spy
    private UpdatePatchPagingService updatePatchPagingService = new UpdatePatchPagingService();

    @InjectMocks
    private PatchHandlerImpl controller;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();

        Mockito.when(patcherInfoControllerApi.queryPatches()).thenReturn(Lists.newArrayList());

        Mockito.when(patcherInfoControllerApi.queryPatchesDispatch(anyString(), anyString()))
            .thenReturn(Lists.newArrayList());

        Mockito.when(patcherInfoControllerApi.queryPatchesHistory(anyString(), anyString()))
            .thenReturn(Lists.newArrayList());

        Mockito.when(patcherInfoControllerApi.queryPatchesType()).thenReturn(Lists.newArrayList());

    }

    @Test
    public void 查询补丁UI接口状态正常() throws Exception {
        PatchPageTableDto patchPageTableDto = new PatchPageTableDto("", "");

        String requestJson = JSONObject.toJSONString(patchPageTableDto);

        mockMvc
            .perform(
                MockMvcRequestBuilders.post("/patches").contentType(MediaType.APPLICATION_JSON).content(requestJson))
            .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void 查询补丁分发UI接口状态正常() throws Exception {

        mockMvc
            .perform(MockMvcRequestBuilders.get("/patches/dispatch/history").contentType(MediaType.APPLICATION_JSON)
                .param("patchName", "DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP030-20201215")
                .param("resultType", "success"))
            .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void 查询补丁升级情况UI接口状态正常() throws Exception {

        mockMvc
            .perform(MockMvcRequestBuilders.get("/patches/update/history").contentType(MediaType.APPLICATION_JSON)
                .param("patchName", "DAP-ZOOKEEPER-V20.19.40.R4.B2-schema-SP030-20201215")
                .param("serviceName", "zookeeper"))
            .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void 查询补丁分页接口状态正常() throws Exception {

        PatchPageTableDto patchPageTableDto = new PatchPageTableDto("", "");

        String requestJson = JSONObject.toJSONString(patchPageTableDto);

        mockMvc.perform(
            MockMvcRequestBuilders.post("/patches/paging").contentType(MediaType.APPLICATION_JSON).content(requestJson))
            .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void 查询补丁类型接口状态正常() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders.get("/patches/type").contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

}