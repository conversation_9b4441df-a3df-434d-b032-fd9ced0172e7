# 接口功能描述
该系统主要负责补丁管理功能。
1. 接收补丁上传命令，处理后存储到PG中。
2. 用户已上传的补丁进行补丁更新操作。
3. 用户不需要的补丁进行删除操作删除补丁。
4. 当前module包含很多子模块，各个子module职责功能如下：
- **[daip-patcher-handler](../daip-patcher-handler)** 模块实现页面操作相关功能
- **[daip-patcher-iui](../daip-patcher-iui)** 模块包含页面HTML、CSS、JS等相关文件
- **[daip-patcher-init](../daip-patcher-init)** 模块负责服务初始化相关功能
- **[daip-patcher-service](../daip-patcher-service)** 模块包含所有后端服务实现
  - **[daip-patcher-interface](../daip-patcher-service/daip-patcher-interface)** 模块是负责接口层相关功能
  - **[daip-patcher-application](../daip-patcher-service/daip-patcher-application)** 模块是负责应用层相关功能
  - **[daip-patcher-domain](../daip-patcher-service/daip-patcher-domain)** 模块是负责领域层相关功能
  - **[daip-patcher-infrastructure](../daip-patcher-service/daip-patcher-infrastructure)** 模块是负责基础设施层相关功能