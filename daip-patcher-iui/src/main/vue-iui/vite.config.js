/* Started by AICoder, pid:z29050d3f6fb613140170a7470d80d4490c24ea8 */
import { fileURLToPath, URL } from 'node:url';
import vue from '@vitejs/plugin-vue';
import basicSsl from '@vitejs/plugin-basic-ssl';
import { defineConfig } from 'vite';

export default defineConfig({
  base: '/iui/daip-patcher-iui/dist',
  plugins: [vue(), basicSsl()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0',
    https: {},
    proxy: proxy(),
    headers: {
      Forgerydefense: '123'
    }
  },
  build: {
//    sourcemap: true, // 启用 source maps
//    minify: false, // 可选：禁用压缩，以便更容易阅读源代码
  }
});

function proxy() {
  const urls = ['/api/daip-patcher-handler/v1', '/api/oauth2/v1', '/api', '/iui/sm', '/iui/component', '/uportal/'];
  return urls.reduce((prev, cur) => {
    prev[cur] = {
      target: 'https://*************:28001',
      changeOrigin: true,
      secure: false,
      headers: {
        'Referer': "https://*************:28001",
        'Cookie': "Z-AUTH-CODE-28001=1023141392_c910802d285548fb929ce79f1aa7dd55;",
      }
    };
    return prev;
  }, {});
}

/* Ended by AICoder, pid:z29050d3f6fb613140170a7470d80d4490c24ea8 */