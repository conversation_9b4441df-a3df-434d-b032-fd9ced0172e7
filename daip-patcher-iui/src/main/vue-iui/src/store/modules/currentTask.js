import {deepClone, isEmpty} from "@/utils/utils";

const currentTask = {
    state: {
        tabs: {},
        activeName: {}
    },
    mutations: {
        SET_CURRENT_TASK_ACTIVE_NAME: (state, {key, value}) => {
            let obj = {}
            obj[key] = String(value)
            state.activeName = Object.assign({}, state.activeName, obj)
        },
        ADD_CURRENT_TASK_TABS: (state, {key, value}) => {
            let obj = {}
            if (isEmpty(state.tabs[key])) {
                obj[key] = []
            } else {
                obj[key] = deepClone(state.tabs[key])
            }
            obj[key].push(value)
            state.tabs = Object.assign({}, state.tabs, obj)
        },
        REMOVE_CURRENT_TASK_TAB: (state, {key, value}) => {
            state.tabs[key] = state.tabs[key].filter(tab => {
                if (typeof value === 'string') {
                    return String(tab.wfId) != value
                } else {
                    return tab.wfId != value
                }
            })
        }
    },
    actions: {
        setCurrentTaskActiveName({commit}, {key, value}) {
            commit('SET_CURRENT_TASK_ACTIVE_NAME', {key, value})
        },
        addCurrentTaskTabs({commit}, {key, value}) {
            commit('ADD_CURRENT_TASK_TABS', {key, value})
        },
        removeCurrentTaskTab({commit}, {key, value}) {
            commit('REMOVE_CURRENT_TASK_TAB', {key, value})
        }
    }
}
export default currentTask