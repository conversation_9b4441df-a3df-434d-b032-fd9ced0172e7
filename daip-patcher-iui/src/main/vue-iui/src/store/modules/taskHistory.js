const taskHistory = {
  state: {
    tabs: [],
    activeName: 'task'
  },
  mutations: {
    SET_TASK_HISTORY_ACTIVE_NAME: (state, value) => {
      state.activeName = String(value)
    },
    ADD_TASK_HISTORY_TABS: (state, value) => {
      state.tabs.push(value)
    },
    REMOVE_TASK_HISTORY_TAB: (state, value) => {
      state.tabs = state.tabs.filter(tab => {
        if (typeof value === 'string') {
          return String(tab.instanceId) != value
        } else {
          return tab.instanceId != value
        }
      })
    }
  },
  actions: {
    setTaskHistoryActiveName({commit}, value) {
      commit('SET_TASK_HISTORY_ACTIVE_NAME', value)
    },
    addTaskHistoryTabs({commit}, value) {
      commit('ADD_TASK_HISTORY_TABS', value)
    },
    removeTaskHistoryTab({commit}, value) {
      commit('REMOVE_TASK_HISTORY_TAB', value)
    }
  }
}
export default taskHistory