const modelDetail = {
    state: {
        show: false,
        modelDetail: {}
    },
    mutations: {
        SET_MODEL_DETAIL_SHOW: (state, value) => {
            state.show = value
        },
        SET_TASK_MODEL: (state, value) => {
          state.modelDetail = value
        }
    },
    actions: {
        setModelDetailShow({commit}, value) {
            commit('SET_MODEL_DETAIL_SHOW', value)
        },
        setTaskModel({commit}, value) {
            commit('SET_TASK_MODEL', value)
        }
    }
}
export default modelDetail