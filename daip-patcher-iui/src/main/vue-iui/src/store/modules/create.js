const create = {
    state: {
        show: false,
        operate: '',
        clusters: null,
        taskModelList: []
    },
    mutations: {
        SET_CREATE_SHOW: (state, value) => {
            state.show = value
        },
        SET_CLUSTERS: (state, value) => {
            state.clusters = value
        },
        SET_TASK_MODEL_LIST: (state, value) => {
            state.taskModelList = value
        },
        SET_OPERATE: (state, value) => {
            state.operate = value
        }
    },
    actions: {
        setCreateShow({commit}, value) {
            commit('SET_CREATE_SHOW', value)
        },
        setClusters({commit}, value) {
            commit('SET_CLUSTERS', value)
        },
        setTaskModelList({commit}, value) {
            commit('SET_TASK_MODEL_LIST', value)
        },
        setOperate({commit}, value) {
            commit('SET_OPERATE', value)
        }
    }
}
export default create