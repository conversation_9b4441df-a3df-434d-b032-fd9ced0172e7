import {deepClone, isEmpty} from "@/utils/utils";

const taskDetail = {
  state: {
//    active: '',
    tabs: [],
    activeName: 'okuTask'
  },
  mutations: {
//    SET_TASK_DETAIL_ACTIVE: (state, value) => {
//      state.active = value
//    },
    SET_TASK_DETAIL_ACTIVE_NAME: (state, value) => {
//      let obj = {}
//      obj[key] = String(value)
//      state.activeName = Object.assign({}, state.activeName, obj)
      state.activeName = String(value)
    },
    ADD_TASK_DETAIL_TABS: (state, value) => {
//      let obj = {}
//      if (isEmpty(state.tabs[key])) {
//        obj[key] = []
//      } else {
//        obj[key] = deepClone(state.tabs[key])
//      }
//      obj[key].push(value)
//      state.tabs = Object.assign({}, state.tabs, obj)
      state.tabs.push(value)
    },
    REMOVE_TASK_DETAIL_TAB: (state, value) => {
      state.tabs = state.tabs.filter(tab => {
        if (typeof value === 'string') {
          return String(tab.taskId) != value
        } else {
          return tab.taskId != value
        }
      })
    }
  },
  actions: {
    setTaskDetailActiveName({commit}, value) {
      commit('SET_TASK_DETAIL_ACTIVE_NAME', value)
    },
    addTaskDetailTabs({commit}, value) {
      commit('ADD_TASK_DETAIL_TABS', value)
    },
    removeTaskDetailTab({commit}, value) {
      commit('REMOVE_TASK_DETAIL_TAB', value)
    },
  //  setTaskDetailActive({commit}, value) {
  //    commit('SET_TASK_DETAIL_ACTIVE', value)
  //  }
  }
}
export default taskDetail