const getCurrentClusterId = (clusterList, clusterName) => {
  const clusterInfo = clusterList.find(item => item.clusterName == clusterName)
  return clusterInfo ? (clusterInfo.clusterId || "") : ""
}

const getCurrentClusterName = (clusterList, clusterId) => {
  const clusterInfo = clusterList.find(item => item.clusterId == clusterId)
  return clusterInfo ? (clusterInfo.clusterName || "") : ""
}

const clusterSelect = {
  state: {
    currentClusterName: '',
    currentClusterId: '',
    allClusterInfo: []
  },
  mutations: {
    SET_CURRENT_CLUSTER_NAME: (state, value) => {
      state.currentClusterName = value
    },
    SET_CURRENT_CLUSTERID: (state, id) => {
      state.currentClusterId = id
    },
    SET_ALL_CLUSTER_INFO: (state, value) => {
      state.allClusterInfo = value
    }
  },
  actions: {
    setCurrentClusterName({
      commit,
      state
    }, value) {
      const currentClusterId = getCurrentClusterId(
        state.allClusterInfo,
        value
      )
      commit('SET_CURRENT_CLUSTER_NAME', value)
      commit('SET_CURRENT_CLUSTERID', currentClusterId)
    },
    setCurrentClusterId({
      commit,
      state
    }, value) {
      const currentClusterName = getCurrentClusterName(
        state.allClusterInfo,
        value
      )
      commit('SET_CURRENT_CLUSTER_NAME', currentClusterName)
      commit('SET_CURRENT_CLUSTERID', value)
    },
    setAllClusterInfo({
      commit
    }, value) {
      commit('SET_ALL_CLUSTER_INFO', value)
    }
  }
}

export default clusterSelect
