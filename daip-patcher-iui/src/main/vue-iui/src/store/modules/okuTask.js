const okuTask = {
    state: {
        tabs: [],
        activeName: 'okuTask',
        detailSrc: ''
    },
    mutations: {
        ADD_OKU_TASK_TABS: (state, value) => {
            state.tabs.push(value)
        },
        REMOVE_OKU_TASK_TABS: (state, value) => {
            state.tabs = state.tabs.filter(tab => {
                if (typeof value === 'string') {
                    return String(tab.wfId) != value
                } else {
                    return tab.wfId != value
                }
            })
        },
        SET_OKU_TASK_ACTIVE_NAME: (state, value) => {
            state.activeName = value
        },
        SET_OKU_TASK_DETAIL_SRC: (state, value) => {
            state.detailSrc = value
        }
    },
    actions: {
        addOkuTaskTabs({commit}, value) {
            commit('ADD_OKU_TASK_TABS', value)
        },
        removeOkuTaskTabs({commit}, value) {
            commit('REMOVE_OKU_TASK_TABS', value)
        },
        setOkuTaskActiveName({commit}, value) {
            commit('SET_OKU_TASK_ACTIVE_NAME', value)
        },
        setOkuTaskDetailSrc({commit}, value) {
            commit('SET_OKU_TASK_DETAIL_SRC', value)
        }
    }
}
export default okuTask