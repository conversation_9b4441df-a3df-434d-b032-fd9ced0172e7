const okuCreate = {
  state: {
    show: false,
    taskType: 0,
    modify: false,
    taskId: "",
    taskName: "",
    clusterId:"",
    operateType: 0,
    selectService: [],
    editTaskInfo: {}
  },
  mutations: {
    SET_OKU_CREATE_EDIT_TASK_INFO: (state, value) => {
      state.editTaskInfo = value
    },
    SET_OKU_CREATE_SHOW: (state, value) => {
      state.show = value
    },
    SET_OKU_CREATE_TASK_TYPE: (state, value) => {
      state.taskType = value
    },
    SET_OKU_CREATE_MODIFY: (state, value) => {
      state.modify = value
    },
    SET_OKU_CREATE_TASK_ID: (state, value) => {
      state.taskId = value
    },
    SET_OKU_CREATE_TASK_NAME: (state, value) => {
      state.taskName = value
    },
    SET_OKU_CREATE_CLUSTER_ID: (state, value) => {
      state.clusterId = value
    },
    SET_OKU_CREATE_OPERATE_TYPE: (state, value) => {
      state.operateType = value
    },
    SET_OKU_CREATE_SELECT_SERVICE: (state, value) => {
      state.selectService = value
    }
  },
  actions: {
    setOkuCreateEditTaskInfo({commit}, value) {
      commit('SET_OKU_CREATE_EDIT_TASK_INFO', value)
    },
    setOkuCreateShow({commit}, value) {
      commit('SET_OKU_CREATE_SHOW', value)
    },
    setOkuCreateTaskType({commit}, value) {
      commit('SET_OKU_CREATE_TASK_TYPE', value)
    },
    setOkuCreateModify({commit}, value) {
      commit('SET_OKU_CREATE_MODIFY', value)
    },
    setOkuCreateTaskId({commit}, value) {
      commit('SET_OKU_CREATE_TASK_ID', value)
    },
    setOkuCreateTaskName({commit}, value) {
      commit('SET_OKU_CREATE_TASK_NAME', value)
    },
    setOkuCreateClusterId({commit}, value) {
      commit('SET_OKU_CREATE_CLUSTER_ID', value)
    },
    setOkuCreateOperateType({commit}, value) {
      commit('SET_OKU_CREATE_OPERATE_TYPE', value)
    },
    setOkuCreateSelectService({commit}, value) {
      commit('SET_OKU_CREATE_SELECT_SERVICE', value)
    }
  }
}
export default okuCreate