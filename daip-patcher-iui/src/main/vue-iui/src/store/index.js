import { createStore } from 'vuex'
import clusterSelect from "./modules/clusterSelect";
import getters from './getters'
import currentTask from "./modules/currentTask";
import create from "./modules/create";
import modelDetail from "./modules/modelDetail";
import home from "./modules/home";
import taskHistory from "./modules/taskHistory";
import task from "./modules/task";
import okuTask from "./modules/okuTask";
import okuCreate from "./modules/okuCreate";
import taskDetail from "./modules/taskDetail";

export default createStore({
  modules: {
    clusterSelect,
    create,
    modelDetail,
    home,
    task,
    okuTask,
    okuCreate,
    taskDetail,
  },
  getters
})
