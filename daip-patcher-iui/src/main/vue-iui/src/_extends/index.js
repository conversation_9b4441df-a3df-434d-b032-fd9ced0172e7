import VeinUIPlus from 'vein-plus';

import 'vein-plus/dist/index.css'
import * as ElementPlusIconsVue from '@vein-plus/icons-vue';

import $api from '../api'
import i18n from '../i18n'
import zhCn from 'vein-plus/es/locale/lang/zh-cn'
import en from 'vein-plus/es/locale/lang/en'

const lang = window.top && window.top.getLanguage && window.top.getLanguage() || 'zh-CN'

const register = function (_vue) {

  _vue.use(VeinUIPlus, {
    locale: lang === 'zh-CN' ? zhCn : en
  })
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    _vue.component(key, component);
  }

  $api.install(_vue)
  _vue.use(i18n)
}

const install = e => register(e)

export default {
  install,
  register
}
