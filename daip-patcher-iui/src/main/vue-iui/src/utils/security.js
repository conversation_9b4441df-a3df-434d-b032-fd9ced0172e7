import $ from "jquery"
let cachedRights = null
 const ALL_OPERATION_CODE = [
    "operation.daip.patcher.oku.task.run",
    "operation.daip.patcher.oku.task.pause",
    "operation.daip.patcher.oku.task.resume",
    "operation.daip.patcher.oku.task.rollback",
    "operation.daip.patcher.oku.task.retry",
    "operation.daip.patcher.oku.task.detail.view"
  ]
/* Started by AICoder, pid:feadeh828fj33b8142f00bfc40f564016309314f */
const collectLeafIds = function (node, leafIds) {
  if (node.leaf && node.id) {
    // 如果是叶子节点且有id，则添加到Set中
    leafIds.add(node.id)
  } else if (node.children && node.children.length > 0) {
    // 如果有子节点，则递归处理每个子节点
    node.children.forEach((child) => collectLeafIds(child, leafIds))
  }
}
/* Ended by AICoder, pid:feadeh828fj33b8142f00bfc40f564016309314f */

export default {
  hasOperRights(operation) {
    if (!operation) {
      return false
    }
    if (typeof getAllOperCodeRights == "undefined") {
      return true
    }

    // 如果缓存的rights不存在，则执行getAllOperCodeRights并缓存结果
    if (cachedRights === null) {
      cachedRights = getAllOperCodeRights(ALL_OPERATION_CODE)
    }

    return hasRight(operation, cachedRights || [])
  },
  /* Started by AICoder, pid:kcddcd1d1aq45da141c40804303f28434ae3acba */
  queryDoubleOperationTree() {
    let doubleOperation = []
    $.ajax({
      url: "/api/daip-deployer-svr/v1/double/operationtree",
      type: "GET",
      async: false,
      timeout: 2000,
      success: function (response) {
        const leafIds = new Set()
        response.doubleAuthOperationTree.forEach((rootNode) => {
          collectLeafIds(rootNode, leafIds)
        })
        doubleOperation = Array.from(leafIds)
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error("error", textStatus, errorThrown)
        doubleOperation = []
      },
    })
    return doubleOperation
  },

  doubleAuthCheck(operation) {
    if (!operation) {
      return false
    }
    var needDoubleAuth = false
    $.ajax({
      url: "/api/oauth2/v1/users/double/auth/check",
      type: "POST",
      async: false, // 设置为同步请求
      data: JSON.stringify({ operation: operation }),
      dataType: "json",
      contentType: "application/json",
      success: function (response) {
        needDoubleAuth = response.result === 1
      },
      error: function (jqXHR, textStatus, errorThrown) {
        console.error("error", textStatus, errorThrown)
      },
    })
    return needDoubleAuth
  },
  /* Ended by AICoder, pid:kcddcd1d1aq45da141c40804303f28434ae3acba */
}
