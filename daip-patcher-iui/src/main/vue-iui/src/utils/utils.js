export const deepClone = function (obj) {
  // 定义一个变量 并判断是数组还是对象
  let objClone = Array.isArray(obj) ? [] : {}
  if (obj && typeof obj === 'object') {
    // 判断obj存在并且是对象类型的时候 因为null也是object类型，所以要单独做判断
    for (let key in obj) {
      // 循环对象类型的obj
      if (obj.hasOwnProperty(key)) {
        // 判断obj中是否存在key属性
        if (obj[key] && typeof obj[key] === 'object') {
          // 判断如果obj[key]存在并且obj[key]是对象类型的时候应该深拷贝，即在堆内存中开辟新的内存
          objClone[key] = deepClone(obj[key])
        } else {
          // 否则就是浅复制
          objClone[key] = obj[key]
        }
      }
    }
  }
  return objClone
}

export const locale = function () {
  return window.top && window.top.getLanguage && window.top.getLanguage()
}
/**
 *
 * @param func
 * @param vm
 * @param timeoutField
 * @param wait
 * @returns {(function(): void)|*}
 */
export const throttle = function (func, vm, timeoutField, wait = 100) {
  if (isBlank(timeoutField)) {
    timeoutField = 'timeoutField'
  }
  let timeout = vm[timeoutField]
  return function () {
    let context = vm, args = arguments;
    if (!timeout) {
      vm.$set(vm, timeoutField, setTimeout(function () {
        vm.$set(vm, timeoutField, null)
        setTimeout(() => func.apply(context, args), 100)
      }, wait))
    }
  }
}

export const errI18n = function (message) {
  if (locale() === 'zh-CN') {
    return message.errorMsgZhCn || message;
  } else {
    return message.errorMsgEnUs || message;
  }
}

export const isBlank = function (string) {
  if (string === undefined || string === null || string == '') {
    return true
  }
  let reg = new RegExp('^ +$');
  return reg.test(string)
}

export const isNumber = function (num) {
  return !isNaN(parseFloat(num)) && isFinite(num)
}

export const isPositiveNum = function (num) {
  return isNumber(num) && num > 0
}
// Object、Set、Map、Array
export const isEmpty = function (obj) {
  if (obj === undefined || obj === null) {
    return true
  }
  if (obj instanceof Array) {
    if (obj.length === 0) {
      return true
    }
  } else if (obj instanceof Set || obj instanceof Map) {
    if (obj.size === 0) {
      return true
    }
  }
  return false
}

export function getElementTop(elem) {
  let elemTop = elem.offsetTop; // 获取当前元素顶部距离父元素顶部的距离
  let parentElem = elem.offsetParent; // 获取当前元素的父元素
  while (parentElem) {
    elemTop += parentElem.offsetTop;
    parentElem = parentElem.offsetParent;
  }
  return elemTop;
}

export function equalIgnoreCase(str1, str2) {
  if (typeof str1 !== 'string' || typeof str2 !== 'string') {
    throw new Error(`param isn't a string`)
  }
  return str1.toUpperCase() === str2.toUpperCase()
}

export function formatExecuteTime(consumingTime) {
  let dayI18n = ''
  let hoursI18n = ''
  let minuteI18n = ''
  let secondI18n = ''
  if (locale() === 'zh-CN') {
    dayI18n = '天'
    hoursI18n = '时'
    minuteI18n = '分'
    secondI18n = '秒'
  } else {
    dayI18n = 'd'
    hoursI18n = 'h'
    minuteI18n = 'm'
    secondI18n = 's'
  }
  // 相差的天数
  var days = Math.floor(consumingTime / (24 * 3600 * 1000))
  // 计算天数后剩余的毫秒数
  var leave1 = consumingTime % (24 * 3600 * 1000)
  // 计算小时数
  var hours = Math.floor(leave1 / (3600 * 1000))
  // 计算小时后剩余的毫秒数
  var leave2 = leave1 % (3600 * 1000)
  // 计算相差的分钟数
  var minutes = Math.floor(leave2 / (60 * 1000))
  // 计算分钟数后剩余时间
  var second = Math.floor(leave2 % (60 * 1000) / 1000)
  if (days !== undefined && days !== 0) {
    return days + dayI18n + hours + hoursI18n + minutes + minuteI18n + second + secondI18n
  }
  if (hours !== undefined && hours !== 0) {
    return hours + hoursI18n + minutes + minuteI18n + second + secondI18n
  }
  if (minutes !== undefined && minutes !== 0) {
    return minutes + minuteI18n + second + secondI18n
  }
  return second + secondI18n
}

String.prototype.signMix = function () {
  if (arguments.length === 0) return this;
  let param = arguments[0], str = this;
  if (typeof (param) === 'object') {
    for (let key in param)
      str = str.replace(new RegExp("\\\{" + key + "\\\}", "g"), param[key]);
    return str;
  } else {
    for (let i = 0; i < arguments.length; i++)
      str = str.replace(new RegExp("\\\{" + i + "\\\}", "g"), arguments[i]);
    return str;
  }
}