import { ElLoading } from 'vein-plus';
import {locale, isBlank} from './utils'

let loadingCount = 0;
let loading;

const startLoading = function(text, spinner, background) {
  let options = {lock: true}
  if (!isBlank(text)) {
    options.text = text
  }
  if (!isBlank(spinner)) {
    options.spinner = spinner
  }
  if (isBlank(background)) {
    background = 'rgba(131,130,130,0.6)'
  }
  options.background = background
  loading = ElLoading.service(options);
};

const endLoading = () => {
  loading.close();
};

export const showLoading = () => {
  let text = '请等待...'
  if (locale() != 'zh-CN') {
    text = 'Please wait...'
  }
  if (loadingCount === 0) {
    startLoading(text, 'el-icon-loading');
  }
  loadingCount += 1;
};

export const hideLoading = () => {
  if (loadingCount <= 0) {
    return;
  }
  loadingCount -= 1;
  if (loadingCount === 0) {
    endLoading();
  }
};

export const showCustomLoading = function (text, spinner, background) {
  if (loadingCount === 0) {
    startLoading(text, spinner, background);
  }
  loadingCount += 1;
}
