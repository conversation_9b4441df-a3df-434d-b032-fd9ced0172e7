<template>
  <div class="current-wrap">
    <div class="template-tabs">
      <el-tabs ref="tabs" v-model="activeName" @tab-remove="removeTab" size="large"
               @tab-click="handleClick">
        <el-tab-pane key="okuTask" :label="$t('common.oku')" name="okuTask">
          <div v-if="activeName == 'okuTask'">
            <transition name="el-zoom-in-top">
              <OkuTask v-if="taskShow"></OkuTask>
            </transition>
            <transition name="el-zoom-in-bottom">
              <create v-if="createShow"></create>
            </transition>
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-for="(item, index) in tabs"
          :key="getTaskDetailTabName(item)"
          :closable="true"
          :label="item.taskName"
          :name="String(getTaskDetailTabName(item))"
        >
<!--          <el-tooltip slot="label" effect="light" :content="item.taskName" placement="top">-->
<!--            <span :style="{'white-space':'nowrap','overflow':'hidden','text-overflow': 'ellipsis','display':'inline-block', width: tabWidth + 'px', height: '100%'}">{{item.taskName}}</span>-->
<!--          </el-tooltip>-->
          <iframe id="okuTask" :src="detailSrc" :style="'height:' + height + 'px'"></iframe>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import OkuTask from "@/components/okuTask/OkuTask.vue";
import Create from "@/components/okuTask/Create.vue";
import {getElementTop, isEmpty} from "@/utils/utils";

export default {
  name: "OkuTaskManager",
  components: {Create, OkuTask},
  data() {
    return {
      createShow: false,
      taskShow: true,
      // tabWidth: 0,
      src: '../../daip-task-iui/dist/index.html#',
      height: 0
    }
  },
  computed: {
    //getParentId() {
    //  return String(this.$store.state.taskDetail.active)
    //},
    detailSrc() {
      return this.src + this.$store.state.okuTask.detailSrc
    },
    getCreateShow() {
      return this.$store.state.okuCreate.show
    },
    tabs() {
      return this.$store.state.taskDetail.tabs;
    },
    activeName: {
      get() {
        return this.$store.state.taskDetail.activeName
      }
    },
  },
  methods: {
    setActiveName(newValue) {
      this.$store.dispatch('setTaskDetailActiveName', newValue)
    },
    getTaskDetailTabName(item) {
      return item.taskId
    },
    removeTab(targetName) {
      let tabs = this.tabs;
      let activeName = this.activeName;
      if (activeName == targetName) {
        tabs.forEach((tab, index) => {
          if (this.getTaskDetailTabName(tab) == targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1];
            if (nextTab) {
              activeName = this.getTaskDetailTabName(nextTab);
            }
          }
        });
      }
      this.$store.dispatch('removeTaskDetailTab', targetName)
      if (this.tabs.length === 0) {
        this.setActiveName('okuTask')
      } else {
        this.setActiveName(activeName)
      }
    },
    handleClick(tab, event) {
      this.setActiveName(tab.paneName)
      let taskInfo = this.$store.state.taskDetail.tabs.find(item => item.taskId == tab.paneName);
      if (taskInfo) {
        window.self.taskInfo = taskInfo
      }
    },
  },
  mounted() {
    // let _this = this
    // this.$nextTick(() => {
    //   let oku = document.getElementById('tab-okuTask')
    //   _this.tabWidth = oku.clientWidth - 32 - 14
    // })
    // this.$store.dispatch('setTaskDetailActive', 'okuTask')
    if (isEmpty(this.activeName)) {
      this.setActiveName('okuTask')
    }
    this.$refs.tabs.$nextTick(() => {
        let tab_header = document.getElementsByClassName('vn-tabs__header')[0]
        let height = window.innerHeight - getElementTop(tab_header) - tab_header.offsetHeight - 15
        this.height = height
    })
  },
  watch: {
    getCreateShow(newVal) {
      let _this = this
      if (newVal) {
        this.taskShow = false
        setTimeout(() => _this.createShow = true, 300)
      } else {
        this.createShow = false
        setTimeout(() => _this.taskShow = true, 300)
      }
    },
  }
}
</script>

<style lang="scss" scoped>

.template-tabs :deep(.el-tabs__item){
  .plx-ico-close-16 {
    line-height: 32px;
    float: right;
  }
}
</style>

<style lang="scss">
#okuTask {
  height: 100%;
  width: 100%;
  border-top: none;
  padding: 0 0px;
  overflow-scrolling: auto;
  border: 0;
}

</style>