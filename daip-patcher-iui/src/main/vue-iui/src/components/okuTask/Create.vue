<template>
  <div :style="{width: '100%', height: appHeight + 'px','overflow-y': 'auto'}" class="custom_form">
    <el-form :model="form" label-width="100px" ref="form" :rules="rules">
      <el-form-item :label="$t('okuCreate.taskName')" prop="taskName">
        <el-input v-model="form.taskName" :placeholder="$t('common.input.inputPlaceholder')"
                  style="width: 210px"></el-input>
      </el-form-item>
      <el-form-item :label="$t('okuCreate.cluster')" prop="clusterId">
        <el-select style="width: 210px" v-model="form.clusterId" :placeholder="$t('common.input.inputPlaceholder')"
                   @change="clusterChange">
          <el-option v-for="cluster in clusters"
                     :label="cluster.clusterName" :value="cluster.clusterId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('okuCreate.upgradeMethod')">
        <div class="my-4 ml-4">
          <el-radio v-model="upgradeMethod" label="ROLLING_UPDATE_PATCH" :disabled="!checkService">{{
              $t('okuCreate.rollingUpgrade')
            }}
          </el-radio>
          <el-radio v-model="upgradeMethod" label="OFFLINE_UPDATE_PATCH" :disabled="!checkService">{{
              $t('okuCreate.offlineUpgrade')
            }}
          </el-radio>
        </div>
      </el-form-item>
      <el-form-item :label="$t('okuCreate.selectService')">
        <el-collapse v-model="serviceActive" type="ghost" class="ghost-collapse" accordion>
          <el-collapse-item ref="el_collapse" name="1" class="dynamic-collapse">
            <div style="border: 1px solid #d4d5d5; border-radius: 4px">
              <div style="margin: 6px">
                <div style="height: 32px; padding-bottom: 10px">
                  <div class="check-box-label"
                       style="font-size: 20px;display: inline-block;height: inherit; line-height: 32px">
                    {{ serviceSelectCount + '/' + (serviceData.length) }}
                  </div>
                  <el-button plain @click="serviceReverseSelect" style="float: right; height: inherit">
                    {{ $t('common.button.reverseElection') }}
                  </el-button>
                  <el-button plain @click="serviceAllSelect()"
                             style="margin-right: 5px; float: right; height: inherit">
                    {{ $t('common.button.allSelect') }}
                  </el-button>
                  <el-input v-model="serviceSearch" :placeholder="$t('common.input.inputPlaceholder')"
                            style="width: 30%; padding-right: 10px; float: right; height: inherit">
                    <template #prefix>
                      <el-icon>
                        <Search/>
                      </el-icon>
                    </template>
                  </el-input>
                </div>
                <el-table :data="currentService" class="custom_table" :border="true">
                  <el-table-column min-width="150px" :resizable="false" show-overflow-tooltip
                                   :label="$t('okuCreate.serviceInstance')">
                    <template #default="scope">
                      <div v-if="isBlank(scope.row.hidden) || scope.row.hidden">
                        <el-checkbox v-model="scope.row.selected"
                                     @change="serviceCheckChange(scope.row)"></el-checkbox>
                        <span style="margin-left: 10px">{{ scope.row.serviceInstanceName }}</span>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <div style="padding-top: 5px;float:right">
                  <el-pagination simple layout="total, prev, pager, next, jumper"
                                 :total="serviceSearchFilter.length"
                                 :page-size="servicePage.pageSize" :current-page="servicePage.currentPage"
                                 @current-change="serviceCurrentPageChange"></el-pagination>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">{{ $t('common.button.confirm') }}</el-button>
        <el-button plain @click="handleClose">{{ $t('common.button.cancel') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>

import {errI18n, isBlank, isEmpty, getElementTop} from "@/utils/utils";
import {Search} from '@element-plus/icons-vue'
import {h} from 'vue';

export default {
  name: 'Create',
  components: {
    Search
  },
  data() {
    return {
      showPreCheckErrorResult: false,
      agentTargetVersion: '',
      checkHost: true,
      checkService: true,
      hostActive: '',
      serviceActive: '',
      upgradeMethod: 'OFFLINE_UPDATE_PATCH',
      hostStatusFilter: [
        {
          statusId: 0,
          statusName: this.$t('okuCreate.hostStatus.good'),
          checked: true
        },
        {
          statusId: 1,
          statusName: this.$t('okuCreate.hostStatus.abnormal'),
          checked: true
        },
        {
          statusId: 2,
          statusName: this.$t('okuCreate.hostStatus.disconnect'),
          checked: true
        },
        {
          statusId: 3,
          statusName: this.$t('okuCreate.hostStatus.error'),
          checked: true
        },
      ],
      serviceInfoList: [],
      clusterPatchMap: {},
      needRestartServices: [],
      serviceDataMap: new Map(),
      hostSearch: '',
      hostPage: {
        currentPage: 1,
        pageSize: 10,
      },
      serviceSearch: '',
      servicePage: {
        currentPage: 1,
        pageSize: 10,
      },
      page: {
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 20, 50],
        total: 0,
        auto: true,
      },
      form: {
        clusterId: '',
        taskName: '',
        username: '',
        password: '',
        port: '22',
      },
      clusters: [],
      serviceTargetVersion: {},
      appHeight: 0,
      rules: {
        clusterId: [
          {required: true, message: this.$t('okuCreate.message.clusterNotEmpty'), trigger: 'blur'}
        ],
        taskName: [
          {validator: this.taskNameValidator, trigger: 'blur'},
          {required: true, message: this.$t('okuCreate.message.taskNameNotEmpty'), trigger: 'change'}
        ],
      },
      preCheckResult: {
        tableData: [],
        total: '',
        pass: '',
        error: '',
      }
    }
  },
  computed: {
    currentService: {
      get() {
        let len = this.serviceSearchFilter.length
        let currentPage = this.servicePage.currentPage
        let pageSize = this.servicePage.pageSize
        let offset1 = (currentPage - 1) * pageSize
        let offset2 = offset1 + pageSize
        // 修复：确保分页后的数据保留 selected 属性
        return this.serviceSearchFilter.slice(offset1, Math.min(offset2, len)).map(item => ({
          ...item,
          selected: item.hasOwnProperty('selected') ? item.selected : false
        }));
      },
    },
    serviceSelectCount() {
      return this.serviceData.filter(service => service.selected).length
    },
    serviceSearchFilter() {
      let _this = this
      return this.serviceData.filter(service => service.serviceName.includes(_this.serviceSearch))
    },
    serviceData: {
      get() {
        let patchInfo = null
        if (!isEmpty(this.clusterPatchMap)) {
          patchInfo = this.clusterPatchMap[this.form.clusterId]
        }
        let _serviceInfoList = this.serviceInfoList.filter(service => !isEmpty(service.targetVersions))
        this.serviceDataMap = new Map()
        for (let i = 0; i < _serviceInfoList.length; i++) {
          if (isEmpty(patchInfo) || !patchInfo.some(p => p.serviceName == _serviceInfoList[i].serviceName)) {
            _serviceInfoList[i].hidden = true
          }
          this.serviceDataMap.set(_serviceInfoList[i].serviceInstanceId, _serviceInfoList[i])
        }
        return _serviceInfoList.filter(service => !service.hidden)
      }
    },
    show: {
      get() {
        return this.$store.state.okuCreate.show
      },
      set(newVal) {
        this.$store.dispatch('setOkuCreateShow', newVal)
      }
    },
  },
  mounted() {
    let _this = this
    if (this.$store.state.okuCreate.modify && !isBlank(this.$store.state.okuCreate.editTaskInfo) && !isBlank(this.$store.state.okuCreate.editTaskInfo.taskName)) {
      _this.form.taskName = this.$store.state.okuCreate.editTaskInfo.taskName
    }
    if (this.$store.state.okuCreate.modify && !isBlank(this.$store.state.okuCreate.editTaskInfo) && this.$store.state.okuCreate.editTaskInfo.operateType === 1) {
      _this.upgradeMethod = "ROLLING_UPDATE_PATCH"
    }
    this.queryUnpatchedClusterAndServiceInstance()
    this.getClusters(() => {
      let clusterIds = _this.clusters.map(clusterInfo => parseInt(clusterInfo.clusterId))
      if (this.$store.state.okuCreate.modify && !isBlank(this.$store.state.okuCreate.editTaskInfo) && !isBlank(this.$store.state.okuCreate.editTaskInfo.clusterId) && clusterIds.includes(parseInt(this.$store.state.okuCreate.editTaskInfo.clusterId))) {
        _this.form.clusterId = parseInt(this.$store.state.okuCreate.editTaskInfo.clusterId)
      } else {
        _this.form.clusterId = _this.clusters[0].clusterId
      }
      this.queryServiceAndFirst()
    })
    this.$nextTick(() => {
      let tab_header = document.getElementsByClassName('vn-tabs__header')[0]
      _this.appHeight = window.innerHeight - getElementTop(tab_header) - tab_header.offsetHeight - 15
    })
  },
  watch: {
    show(newVal) {
      let _this = this
      if (newVal) {
        this.getClusters(() => {
          _this.form.terId = _this.clusters[0].clusterId
          _this.queryServices()
        })
      }
    },
    hostSearch(newVal) {
      this.hostPage.currentPage = 1;
      this.hostPage.pageSize = 10;
    },
    serviceSearch(newVal) {
      this.hostPage.currentPage = 1;
      this.hostPage.pageSize = 10;
    },
    upgradeMethod(newVal) {
      this.queryServiceTargetVersion();
      for (let i = 0; i < this.serviceInfoList.length; i++) {
        this.serviceInfoList[i].selected = false
      }
    },
  },
  methods: {
    taskNameValidator(rule, value, callback) {
      if (!isBlank(value)) {
        let reg = new RegExp('^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa50-9a-zA-Z-_]*$')
        if (reg.test(value)) {
          let length = value.length;
          let cnReg = /([\u4e00-\u9fa5]|[\u3000-\u303F]|[\uFF00-\uFF60])/g
          let match = value.match(cnReg)
          if (match !== null && match.length >= 0) {
            length = length + match.length
          }
          if (length > 20) {
            return callback(new Error(this.$t('common.validate.char_len_msg', {max: 20})))
          }
        } else {
          return callback(new Error(this.$t('common.validate.start_in_en_and_zh')))
        }
        //校验taskName是否存在
        if (!this.$store.state.okuCreate.modify) {
          this.$api.upgrade.queryTask(0, 0, value)
              .then(res => {
                if (res.status == '0' && res.data && res.data.taskList.length === 0) {
                  callback();
                } else {
                  callback(new Error(this.$t('common.validate.task_name_exist')))
                }
              })
        } else {
          callback();
        }
        //
      } else {
        return callback(new Error(this.$t('okuCreate.message.taskNameNotEmpty')))
      }
    },

    serviceCheckChange(row) {
      let select = row.selected
      let componentType = row.componentType
      if (componentType == 'dap.manager.common.bigdata') {
        let serviceInstanceIdOfCommonNodeOfSameService = row.serviceInstanceIdOfCommonNodeOfSameService
        if (!isEmpty(serviceInstanceIdOfCommonNodeOfSameService)) {
          for (let i = 0; i < serviceInstanceIdOfCommonNodeOfSameService.length; i++) {
            let service = this.serviceDataMap.get(serviceInstanceIdOfCommonNodeOfSameService[i])
            if (!isEmpty(service) && service.selected != select) {
              service.selected = select
              this.serviceDataMap.set(serviceInstanceIdOfCommonNodeOfSameService[i], service)
              this.serviceCheckChange(service)
            }
          }
        }else if(row.serviceInstanceId == 'zdh'){
          let service = this.serviceDataMap.get('zdh')
          if (!isEmpty(service) && service.selected != select) {
              service.selected = select
              this.serviceDataMap.set('zdh', service)
            }
        }
      }
    },

    isBlank(str) {
      return isBlank(str)
    },
    displayStatus(hostStatus) {
      for (let i = 0; i < this.hostStatusFilter.length; i++) {
        if (this.hostStatusFilter[i].statusId == hostStatus) {
          return this.hostStatusFilter[i].statusName
        }
      }
      return ''
    },
    hostStatusHandleCommand(command) {
      // this.$set(this.filter, command, isEmpty(this.filter[command]) ? true : !this.filter[command])
    },

    addZdhService() {
      let patchInfo = null
      if (!isEmpty(this.clusterPatchMap)) {
        patchInfo = this.clusterPatchMap[this.form.clusterId]
      }
      if (this.upgradeMethod == 'OFFLINE_UPDATE_PATCH' && !isEmpty(patchInfo) && patchInfo.some(p => p.serviceName == 'zdh')) {
        let targetVersions = patchInfo.filter(p => p.serviceName == 'zdh').map(p => p.version)
        if (!isEmpty(targetVersions)) {
          let zdhService = 'zdh'
          let zdhServiceInfo = {
            clusterId: this.form.clusterId,
            componentType: 'dap.manager.common.bigdata',
            serviceId: zdhService,
            serviceName: zdhService,
            serviceInstanceId: zdhService,
            serviceInstanceName: zdhService,
            targetVersions: targetVersions,
          }
          if (!this.serviceInfoList.some(p => p.serviceInstanceId == zdhService && !isEmpty(p.targetVersions)) && !isEmpty(zdhServiceInfo.targetVersions)) {
            this.serviceInfoList.push(zdhServiceInfo)
            let serviceSet = new Set(this.serviceInfoList)
            this.serviceInfoList = Array.from(serviceSet)
            this.setSelectedForModify()
          }
        }
      }
    },

    async queryServiceAndFirst() {
      await this.queryServices()
      this.setSelectedForModify()
    },
    setSelectedForModify() {
      if (this.$store.state.okuCreate.modify && this.$store.state.okuCreate.selectService.length > 0) {
        for (let i = this.$store.state.okuCreate.selectService.length - 1; i >= 0; i--) {
          let _service = this.serviceInfoList.find(s => s.serviceInstanceId == this.$store.state.okuCreate.selectService[i])
          if (!isEmpty(_service)) {
            _service.selected = true
            this.$store.state.okuCreate.selectService.splice(i, 1)
          }
        }
      }
    },
    async queryServices() {
      let _this = this
      try {
        let res = await this.$api.service.getServiceInstanceInfoByClusterId(this.form.clusterId)
        if (res.status == '0') {
          _this.serviceInfoList = res.data
          if (!isEmpty(_this.serviceInfoList)) {
            _this.queryServiceTargetVersion()
          } else {
            _this.serviceInfoList = []
            _this.serviceTargetVersion = {}
            _this.$message({
              type: 'warning',
              message: _this.$t('okuCreate.message.serviceInfoEmpty')
            })
          }
        } else {
          _this.serviceInfoList = []
          _this.serviceTargetVersion = {}
          _this.$message({
            type: 'error',
            message: _this.$t('okuCreate.message.fail2getServiceInfo')
          })
        }
      } catch (err) {
        _this.serviceInfoList = []
        _this.serviceTargetVersion = {}
        _this.$message({
          type: 'error',
          message: _this.$t('okuCreate.message.fail2getServiceInfo')
        })
      }
    },
    queryUnpatchedClusterAndServiceInstance() {
      let _this = this
      this.$api.upgrade.queryUnpatchedClusterAndServiceInstance()
          .then(res => {
            let clusterServiceInstanceMap = res.clusterServiceInstanceMap
            if (isEmpty(clusterServiceInstanceMap)) {
              _this.clusterPatchMap = {}
              _this.$message({
                type: 'warning',
                message: _this.$t('okuCreate.message.fail2getPatchInfo')
              })
            } else {
              _this.clusterPatchMap = clusterServiceInstanceMap
            }
          }).catch(err => {
        _this.clusterPatchMap = {}
        _this.$message({
          type: 'error',
          message: _this.$t('okuCreate.message.fail2getPatchInfo')
        })
      })
    },
    queryServiceTargetVersion() {
      let _this = this
      let versionQueryRequestInfo = {}
      versionQueryRequestInfo['accessType'] = _this.upgradeMethod
      let serviceVersionInfoList = []
      _this.serviceInfoList.map(service => {
        let serviceVersionInfo = {
          'serviceId': service.serviceId,
          'serviceName': service.serviceName,
          'componentType': service.componentType
        }
        serviceVersionInfoList.push(serviceVersionInfo)
      })
      versionQueryRequestInfo['serviceVersionInfoList'] = serviceVersionInfoList
      _this.$api.upgrade.queryServiceModelVersion(versionQueryRequestInfo).then(res => {
        for (let i = 0; i < _this.serviceInfoList.length; i++) {
          if (res.status == '0') {
            const first = res.data.find(service => service.serviceId == _this.serviceInfoList[i].serviceId);
            let targetVersions = isEmpty(first) ? null : first.targetVersions
            _this.serviceInfoList[i].targetVersions = targetVersions
            if (isEmpty(targetVersions)) {
              _this.serviceTargetVersion[_this.serviceInfoList[i].serviceInstanceId] = '';
            } else {
              let lastVersion = targetVersions[targetVersions.length - 1]
              _this.serviceTargetVersion[_this.serviceInfoList[i].serviceInstanceId] = lastVersion
            }
          } else {
            _this.serviceInfoList[i].targetVersions = []
            _this.serviceTargetVersion[_this.serviceInfoList[i].serviceInstanceId] = ''
          }
        }
        this.addZdhService()
      }).catch(err => {
        for (let i = 0; i < _this.serviceInfoList.length; i++) {
          _this.serviceInfoList[i].targetVersions = []
          _this.serviceTargetVersion[_this.serviceInfoList[i].serviceInstanceId] = ''
        }
        _this.$message({
          type: 'error',
          message: _this.$t('okuCreate.message.fail2getVersionInfo')
        })
      })
    },
    checkHostChange(value) {
      let _this = this
      if (!value && !this.checkService) {
        this.$ctConfirmBox(_this.$t('okuCreate.message.selectAtLeastOne'), 'warning', this)
        this.$nextTick(() => _this.checkHost = true)
      }
    },
    checkServiceChange(value) {
      let _this = this
      if (!value && !this.checkHost) {
        this.$ctConfirmBox(_this.$t('okuCreate.message.selectAtLeastOne'), 'warning', this)
        this.$nextTick(() => _this.checkService = true)
      }
    },
    hostCurrentPageChange(page) {
      this.hostPage.currentPage = page
    },
    serviceReverseSelect() {
      for (let i = 0; i < this.serviceSearchFilter.length; i++) {
        this.serviceSearchFilter[i].selected = !this.serviceSearchFilter[i].selected
      }
    },
    serviceAllSelect() {
      for (let i = 0; i < this.serviceSearchFilter.length; i++) {
        this.serviceSearchFilter[i].selected = true
      }
    },
    serviceCurrentPageChange(page) {
      this.servicePage.currentPage = page
    },
    handleClose() {
      this.show = false
    },
    clusterChange(item) {
      this.queryServices()
    },
    getClusters(successCB, errCb) {
      let _this = this
      this.$api.cluster.getClusters()
          .then(res => {
            try {
              res = JSON.parse(res)
            } catch (e) {
              console.log(typeof res)
            }
            if (res.status == 0) {
              this.clusters = res.data.allClusters
              if (!isEmpty(successCB) && typeof successCB === 'function') {
                successCB()
              }
            } else {
              this.$message({
                type: 'error',
                message: _this.$t('okuCreate.message.failed2ClusterInfo')
              })
              if (!isEmpty(errCb) && typeof errCb === 'function') {
                errCb()
              }
            }
          }).catch(err => {
        this.$message({
          type: 'error',
          message: _this.$t('okuCreate.message.failed2ClusterInfo')
        })
        if (!isEmpty(errCb) && typeof errCb === 'function') {
          errCb()
        }
      })
    },
    setRelatedService(key, service) {
      if (!isEmpty(service) && !service.related) {
        service.related = true
        this.serviceDataMap.set(key, service)
        this.checkRelatedService(service)
      }
    },
    checkRelatedService(row) {
      this.setRelatedService(row.serviceInstanceId, row)
      if (this.upgradeMethod == 'OFFLINE_UPDATE_PATCH') {
        let dependenceList = row.dependenceList
        if (!isEmpty(dependenceList)) {
          for (let i = 0; i < dependenceList.length; i++) {
            let service = this.serviceDataMap.get(dependenceList[i])
            this.setRelatedService(dependenceList[i], service)
          }
        }
      }
    },
    async queryNeedRestartServices(patchTask) {
      let _this = this
      try {
        let res = await this.$api.upgrade.queryNeedRestartService(patchTask)
        if (res.status == '0') {
          _this.needRestartServices = res.data.needRestartService
        } else {
          this.$message({
            type: 'error',
            message: _this.$t('okuTask.execute.failure')
          })
        }
      } catch (err) {
        _this.needRestartServices = []
        this.$message({
          type: 'error',
          message: _this.$t('okuTask.execute.failure')
        })
      }
    },
    async onSubmit() {
      let valid = await new Promise((resolve) => {
        this.$refs.form.validate((valid) => {
          resolve(valid);
        });
      });
      if (valid) {
        let _this = this
        let clusterName = null
        if (isEmpty(this.form.clusterId)) {
          this.$ctConfirmBox(_this.$t('okuCreate.message.clusterEmpty'), "warning", this)
          return
        }
        let serviceArr = []
        if (this.checkService) {
          if (isEmpty(this.serviceData)) {
            this.$ctConfirmBox(_this.$t('okuCreate.message.serviceEmpty'), "warning", this)
            return
          } else {
            for (let [_, service] of this.serviceDataMap) {
              if (service.selected) {
                _this.checkRelatedService(service)
                serviceArr.push({
                  serviceInstance: {
                    serviceId: service.serviceId,
                    serviceName: service.serviceName,
                    serviceInstanceId: service.serviceInstanceId,
                    serviceInstanceName: service.serviceInstanceName,
                  }
                })
              }
            }
            if (isEmpty(serviceArr)) {
              this.$ctConfirmBox(_this.$t('okuCreate.message.serviceEmpty'), "warning", this)
              return
            }
          }
        }
        let operateType = 0
        if (this.upgradeMethod == 'ROLLING_UPDATE_PATCH') {
          operateType = 1
        }
        for (let i = 0; i < this.clusters.length; i++) {
          if (this.clusters[i].clusterId == this.form.clusterId) {
            clusterName = this.clusters[i].clusterName
          }
        }
        let relationServiceArr = []
        for (let [_, service] of this.serviceDataMap) {
          if (service.related) {
            relationServiceArr.push({
              serviceId: service.serviceId,
              serviceName: service.serviceName,
              serviceInstanceId: service.serviceInstanceId,
              serviceInstanceName: service.serviceInstanceName,
            })
          }
        }


        let patchTask = {}
        patchTask['clusterId'] = this.form.clusterId
        patchTask['operateType'] = operateType
        patchTask['context'] = serviceArr
        patchTask['relationServices'] = relationServiceArr
        this.needRestartServices = []

        if (this.upgradeMethod == 'ROLLING_UPDATE_PATCH') {
          this.needRestartServices = serviceArr.map(item => item.serviceInstance)
        } else {
          await this.queryNeedRestartServices(patchTask)
        }

        let services = this.serviceData.filter(s => s.selected).map(s => s.serviceInstanceName)
        let relationServices = this.needRestartServices.filter(s => s.serviceInstanceName != 'zdh').map(s => s.serviceInstanceName)
        relationServices = [...new Set(relationServices)];

        let eleFun = h("div", {
          class: "wrap_text"
        }, [
          h("h3", _this.$t("okuCreate.message.taskConfirmServiceMsg", {services: services.join(", ")})),
          h("h3", _this.$t("okuCreate.message.taskConfirmRelationServicesMsg", {relationServices: relationServices.join(", ")}))
        ])

        if (!this.$store.state.okuCreate.modify) {
          patchTask['taskName'] = this.form.taskName
          patchTask['taskType'] = 0
          this.$ctStopOrDeleteBox(() => this.$api.upgrade.createTask(patchTask),
              _this, eleFun, null, _this.$t('common.taskMessage.checkMsg'), () => {
                _this.handleClose()
              }, err => {
                _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
                _this.$message({
                  type: 'error',
                  message: _this.$t('common.taskMessage.createFail')
                })
              })
        } else if (!isBlank(this.$store.state.okuCreate.editTaskInfo)) {
          let editTaskInfo = this.$store.state.okuCreate.editTaskInfo
          patchTask['taskId'] = editTaskInfo.taskId
          patchTask['taskName'] = this.form.taskName
          patchTask['taskType'] = editTaskInfo.taskType
          patchTask['createTime'] = editTaskInfo.createTime
          patchTask['allowModify'] = editTaskInfo.allowModify
          patchTask['relationTaskId'] = editTaskInfo.relationTaskId
          this.$ctStopOrDeleteBox(() => this.$api.upgrade.modifyTask(patchTask),
              _this, eleFun, null, _this.$t('common.taskMessage.checkMsg'), () => {
                _this.handleClose()
              }, err => {
                _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
                _this.$message({
                  type: 'error',
                  message: _this.$t('common.taskMessage.modifyFail')
                })
              })
        } else {
          this.$ctConfirmBox(_this.$t('okuCreate.message.taskIdNotEmpty'), "warning", this)
          return
        }

      } else {
        return false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.custom_form {
  :deep(.vn-form-item__content) {
    display: inline !important;
  }
}
</style>