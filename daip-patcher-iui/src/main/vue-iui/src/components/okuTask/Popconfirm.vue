<template>
  <el-popover
      v-bind="$attrs"
      trigger="click"
  >
    <div class="el-popconfirm">
      <p class="el-popconfirm__main">
        <i
            v-if="!hideIcon"
            :class="icon"
            class="el-popconfirm__icon"
            :style="{color: iconColor}"
        ></i>
        {{title}}
      </p>
      <slot name="operate"></slot>
    </div>
    <template #reference>
      <slot name="reference"></slot>
    </template>
  </el-popover>
</template>

<script>

export default {
  name: 'Popconfirm',
  props: {
    title: {
      type: String
    },
    icon: {
      type: String,
      default: 'plx-ico-warning-f-16'
      // 'el-icon-question'
    },
    iconColor: {
      type: String,
      default: '#f90'
    },
    hideIcon: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
};
</script>