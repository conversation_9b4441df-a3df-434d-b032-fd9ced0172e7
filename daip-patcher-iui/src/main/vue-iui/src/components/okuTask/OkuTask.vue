<template>
  <div class="okuTask">

    <el-button  type="primary" @click="handleOpen">{{ $t('common.button.create') }}</el-button>
    <el-button  type="danger" @click="batchDeleteTask" :disabled="multipleSelection.length === 0">
      {{ $t('common.button.delete') }}
    </el-button>
    <el-button  id="refresh" style="float: right" @click="refresh">
      <el-icon>
        <Refresh/>
      </el-icon>
    </el-button>
    <div style="width: 100%; height: 10px" ref="divider"></div>
    <el-table  :data="workflowList" @selection-change="handleSelectionChange" :max-height="tableHeight"
              :border="true">
      <el-table-column type="selection" :selectable="selectable" :resizable="false">
      </el-table-column>
      <el-table-column :label="$t('okuTask.taskName')" width="300px" prop="taskName" :resizable="false"
                       show-overflow-tooltip>
      </el-table-column>
      <el-table-column :label="$t('common.cluster')" :resizable="false" width="200px" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.patchCategory == 'schema' ? "" : scope.row.clusterName }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('okuTask.upgradeObject')" :resizable="false" show-overflow-tooltip>
        <template #default="scope">
          <el-link :underline="false" type="primary" @click="openServiceTable(scope.row)">
            {{ $t('common.service') }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('okuTask.createTime')" :resizable="false" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.createTime }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('okuTask.executionTime')" :resizable="false" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.latestExecutionTime }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('task.task.status')" width="100px" :resizable="false" show-overflow-tooltip>
        <template #default="scope">
          <span :style="columnStyle(scope.row)">
            {{ displayStatus(scope.row) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('okuTask.operation')" width="150px">
        <template #default="scope">

          <el-dropdown :hide-on-click="false">
             <span class="vn-dropdown-link">
                  {{ $t('common.label.operate') }}<el-icon class="vn-icon--right"><arrow-down/></el-icon>
                </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-permission="['operation.daip.patcher.oku.task.detail.view']" @click="openTask(scope.row)">{{
                    $t('common.label.detail')
                  }}
                </el-dropdown-item>
                <el-dropdown-item v-if="scope.row.allowModify" @click="editTask(scope.row)">{{
                    $t('common.button.edit')
                  }}
                </el-dropdown-item>
                <el-dropdown-item v-if="(!isBlank(getButtonTitle(scope.row)))"
                                  @click="stopOrStartOrRetryInstance(scope.row)">
                  {{ getButtonTitle(scope.row) }}
                </el-dropdown-item>
                <el-dropdown-item v-if="isDeleteShow(scope.row.status)" @click="deleteTask(scope.row)">
                  {{ $t('common.button.delete') }}
                </el-dropdown-item>
                <el-dropdown-item v-permission="['operation.daip.patcher.oku.task.rollback']" v-if="!isBlank(scope.row.status) && scope.row.status !== 'Running'
                                  && scope.row.status !== 'Initial' && scope.row.taskType !== 1"
                                  @click="createAndExecuteRollbackTask(scope.row)">
                  {{ $t('common.label.goBack') }}
                </el-dropdown-item>
                <el-dropdown-item
                    v-if="!isBlank(scope.row.status) &&isDeleteShow(scope.row.status)&& scope.row.taskType !== 1"
                    @click="copyTask(scope.row)">
                  {{ $t('common.button.copy') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div style="padding-top: 5px;float:right">
      <Paging ref="page" :total="total" @update="queryTask"></Paging>
    </div>

    <el-dialog v-model="updateShowDialog" width="1200px" :title="(operateModel == -1 ? '' : operateModel == 0 ? $t('okuTask.OFFLINE') : $t('okuTask.ROLLING'))
              + (taskModel == -1 ? '' : taskModel == 0 ? $t('okuTask.upgrade') : $t('okuTask.rollback'))
              + (isBlank(titleModel) ? '' : $t('okuTask.' + titleModel))">
      <el-form inline>
        <el-form-item style="float: right">
          <el-input v-model.trim="updateDialogSearchKeyWord"  @input="updateSearch"
                    :placeholder="$t('common.input.search')" style="width: 180px">
            <template #prefix>
              <el-icon>
                <Search/>
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <el-table  :data="updateDialogTableData" :page="dialogPage2" :border="true">
        <el-table-column v-for="column in updateDialogTableDisplayField" :label="$t('okuTask.dialog.' + column)"
                         :key="column" :prop="column" :resizable="false"
                         :class-name="column.indexOf('PatchPoint')>0||column==='version'||column==='serviceName'?'cell-wrap':''">
          <template #default="scope">
            <!-- Started by AICoder, pid:b5d15d28d56c44db91506ac144c7a74f -->
            <span v-if="column==='targetPatchPoint'||column==='historyPatchPoint'">
              <span class="" v-for="point in scope.row[column]" :key="point">
                {{ point }}<br/>
              </span>
            </span>
            <span class="wrap_text" v-else-if="column.indexOf('PatchPoints')<0">
              {{ scope.row[column] }}
            </span>
            <a v-else v-for="point in scope.row[column]" style="cursor:pointer;color: #0088ff"
               :key="point.rollBackPatchPoint"
               @click="openRollBackPatchHosts(point.rollBackPatchPoint, point.patchHostInfos)">
              {{ `${point.rollBackPatchPoint}` }}<br/></a>
            <!-- Ended by AICoder, pid:b5d15d28d56c44db91506ac144c7a74f -->
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog v-model="showDialogLvl2" width="900px">
      <el-form inline>
        <el-form-item>
          <span style="font-size: 16px">{{
              (operateModelLvl2 == -1 ? '' : operateModelLvl2 == 0 ? $t('okuTask.ROLLING') : $t('okuTask.OFFLINE'))
              + (taskModelLvl2 == -1 ? '' : taskModelLvl2 == 0 ? $t('okuTask.upgrade') : $t('okuTask.rollback'))
              + (isBlank(titleModelLvl2) ? '' : titleModelLvl2)
            }}</span>
        </el-form-item>
        <el-form-item>
          <el-input v-model.trim="dialogSearchKeyWordLvl2"  @input="searchLvl2"
                    :placeholder="$t('common.input.search')" style="width: 180px">
            <template #prefix>
              <el-icon>
                <Search/>
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
      <el-table :data="dialogTableDataLvl2" :page="dialogPage" :border="true">
        <el-table-column v-for="column in dialogTableDisplayFieldLvl2" :label="$t('okuTask.dialog.' + column)"
                         :key="column" :prop="column" :resizable="false" show-overflow-tooltip></el-table-column>
      </el-table>
    </el-dialog>
    <auth-dialog ref="authDialog"/>
  </div>
</template>

<script>

import {deepClone, getElementTop, isBlank, isEmpty, isPositiveNum, throttle} from "@/utils/utils";
import Paging from "@/components/common/Paging.vue";
import Popconfirm from "@/components/okuTask/Popconfirm.vue";
import AuthDialog from "@/components/global/AuthDialog.vue";
import {URL} from '@/api/urlConfig';
import $ from 'jquery';
import Security from '@/utils/security';
import {
  Search,
  RefreshRight,
  View,
  EditPen,
  VideoPlay,
  VideoPause,
  Delete,
  Back,
  RefreshLeft,
  Refresh
} from '@element-plus/icons-vue';

const env = import.meta.env.VITE_USER_NODE_ENV;
const refreshInterval = 30 * 1000
const timeout = 30 * 60 * 1000;//超时时间：30min

export default {
  name: "OkuTask",
  components: {
    Paging,
    Popconfirm,
    Search,
    RefreshRight,
    View,
    EditPen,
    VideoPlay,
    VideoPause,
    Delete,
    Back,
    AuthDialog,
    RefreshLeft,
    Refresh
  },
  data() {
    return {
      tableHeight: 'auto',
      total: 0,
      workflowList: [],
      multipleSelection: [],
      showTagNum: {},
      updateDialogTableData: [],
      dialogTableDataLvl2: [],
      dialogHostData: [],
      originDialogTableData: [],
      updateDialogTableDisplayField: [],
      dialogTableDisplayField: [],
      dialogTableDisplayFieldLvl2: [],
      updateShowDialog: false,
      showDialogLvl2: false,
      showHostDialog: false,
      updateDialogSearchKeyWord: '',
      dialogSearchKeyWordLvl2: '',
      dialogSearchHostKeyWord: '',
      dialogPage: {
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 15, 20, 30, 50],
        total: 0,
        auto: true,
      },
      dialogPage2: {
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 15, 20, 30, 50],
        total: 0,
        auto: true,
      },
      appOldClientWidth: '',
      visible: false,
      refreshTimer: null,
      checkTimeoutTimer: null,
      lastTime: null,
      currentTime: null,
      taskModel: '',
      operateModel: '',
      titleModel: '',
      taskModelLvl2: '',
      operateModelLvl2: '',
      titleModelLvl2: '',
      needRestartServices: []
    }
  },
  computed: {
    //getParentId() {
    //  return String(this.$store.state.taskDetail.active)
    //},
    tabList() {
      return this.$store.state.taskDetail.tabs
    },
  },
  mounted() {
    this.queryTask(1, 0)
    /* Started by AICoder, pid:c4e5768ecf1f48cfbcef92c31c71bd67 */
    this.refreshTimer = setInterval(() => {
      this.queryTask(this.$refs.page.currentPage || 1, this.$refs.page.pageSize || 10);
    }, refreshInterval);
    /* Ended by AICoder, pid:c4e5768ecf1f48cfbcef92c31c71bd67 */
    this.lastTime = new Date().getTime();
    this.currentTime = new Date().getTime();
    window.addEventListener('keyup', this.updateTime);
    window.addEventListener('click', this.updateTime);
    window.addEventListener('scroll', this.updateTime);
    this.checkTimeoutTimer = setInterval(this.testTime, refreshInterval)
  },
  beforeDestroy() {
    clearInterval(this.refreshTimer);
    this.refreshTimer = null;
    clearInterval(this.checkTimeoutTimer);
    this.checkTimeoutTimer = null;
    window.removeEventListener('keyup', this.updateTime);
    window.removeEventListener('click', this.updateTime);
    window.removeEventListener('scroll', this.updateTime);
  },
  methods: {
    testTime() {
      this.currentTime = new Date().getTime();
      if (this.currentTime - this.lastTime > timeout) {
        this.$api.upgrade.logout()
      }
    },
    getTableMaxHeight() {
      let _this = this
      this.$nextTick(() => {
        let divider = _this.$refs.divider
        let page = _this.$refs.page
        _this.appOldClientWidth = document.getElementById('app').clientWidth
        _this.tableHeight = window.innerHeight - getElementTop(divider) - divider.offsetHeight - 25
        if (!isEmpty(page)) {
          _this.tableHeight = _this.tableHeight - page.$el.offsetHeight
        }
        // window.addEventListener("resize", throttle(() => {
        //   let newClientWidth = document.getElementById('app').clientWidth
        //   if (_this.appOldClientWidth !== newClientWidth) {
        //     _this.calculateShowService()
        //   }
        //   _this.appOldClientWidth = newClientWidth
        // }, _this, null, 100), false)
      })
    },
    selectable(row, index) {
      return this.isDeleteShow(row.status)
    },
    columnStyle(item) {
      let status = item.status
      if (item.status === 0) {
        return {color: 'rgba(225,3,250,0.75)'}
      } else if (status === 'Success') {
        return {color: '#53bd73'}
      } else if (status === 'Ignore') {
        return {color: '#70706d'}
      } else if (status === 'Failure') {
        return {color: '#fa032f'}
      } else if (status === 'Partial Failure') {
        return {color: '#e6a23c'}
      } else if (status === 'Running') {
        return {color: '#1993ff'}
      } else if (status === 'Initial') {
        return {color: '#7186b7'}
      } else if (status === 'Terminate') {
        return {color: '#00abaf'}
      } else {
        return ''
      }
    },
    displayStatus(row) {
      if (row.status === undefined || row.status === null
          || row.status === 0 || row.status === '') {
        return ''
      }
      return this.$t('task.status.' + row.status)
    },
    isBlank(str) {
      return isBlank(str)
    },
    updateSearch() {
      if (!this.keyword) {
        this.updateDialogTableData = this.originDialogTableData;
      }
      this.updateDialogTableData = this.originDialogTableData.filter(item => {
        for (let column of this.updateDialogTableDisplayField) {
          if (!isBlank(item[column]) && item[column].indexOf(this.updateDialogSearchKeyWord) > -1) {
            return true
          }
        }
        return false
      });
    },
    searchLvl2() {
      if (!this.keyword) {
        this.dialogTableDataLvl2 = this.originDialogTableDataLvl2;
      }
      this.dialogTableDataLvl2 = this.originDialogTableDataLvl2.filter(item => {
        for (let column of this.dialogTableDisplayFieldLvl2) {
          if (!isBlank(item[column]) && item[column].indexOf(this.dialogSearchKeyWordLvl2) > -1) {
            return true
          }
        }
        return false
      });
    },
    resetUpdateDialogTable() {
      this.updateDialogTableData = []
      this.originDialogTableData = []
      this.updateDialogSearchKeyWord = ''
      this.taskModel = ''
      this.operateModel = ''
      this.titleModel = ''
      this.dialogPage2.currentPage = 1
      this.dialogPage2.pageSize = 10
    },
    resetDialogTableLvl2() {
      this.dialogTableDataLvl2 = []
      this.originDialogTableDataLvl2 = []
      this.dialogSearchKeyWordLvl2 = ''
      this.taskModelLvl2 = ''
      this.operateModelLvl2 = ''
      this.titleModelLvl2 = ''
      this.dialogPage.currentPage = 1
      this.dialogPage.pageSize = 10
    },
    parseRollbackPatchPoints(rollBackPatchPoints) {
      if (rollBackPatchPoints != null) {
        return rollBackPatchPoints
      } else {
        return ""
      }
    },

    async queryNeedRestartServices(patchTask) {
      let _this = this
      try {
        let res = await this.$api.upgrade.queryNeedRestartService(patchTask)
        if (res.status == '0') {
          _this.needRestartServices = res.data.needRestartService
        } else {
          this.$message({
            type: 'error',
            message: _this.$t('okuTask.execute.failure')
          })
        }
      } catch (err) {
        _this.needRestartServices = []
        this.$message({
          type: 'error',
          message: _this.$t('okuTask.execute.failure')
        })
      }
    },
    /* Started by AICoder, pid:kb2b9431cfwa1531401f0904107f1f41f063c9c1 */
    async openServiceTable(row) {
      if (isEmpty(row.context) || isEmpty(row.relationServices)) {
        this.$message({
          type: 'error',
          message: this.$t('okuTask.message.fail2getServiceInfo')
        })
        return
      }
      this.resetUpdateDialogTable()
      let taskDetail = await this.$api.upgrade.queryTaskDetail(row.taskId)
      let context = taskDetail.status == '0' ? taskDetail.data.context || [] : []

      let patchTask = {}
      patchTask['clusterId'] = row.clusterId
      patchTask['operateType'] = row.operateType
      patchTask['context'] = row.context
      patchTask['relationServices'] = row.relationServices
      this.needRestartServices = []

      if (row.operateType === 1) {
        this.needRestartServices = []
      } else if (row.taskType === 1) {
        if (taskDetail.data.needRestartServices !== undefined && taskDetail.data.needRestartServices !== null) {
          this.needRestartServices = taskDetail.data.needRestartServices
        }
      } else if (isBlank(this.getButtonTitle(row))) {
        if (taskDetail.data.needRestartServices !== undefined && taskDetail.data.needRestartServices !== null) {
          this.needRestartServices = taskDetail.data.needRestartServices
        }
      } else {
        await this.queryNeedRestartServices(patchTask)
      }
      const contextService = context.map(item => item.serviceInstance.serviceInstanceId);
      const restartService = this.needRestartServices.map(item => item.serviceInstanceId);
      const allService = [...contextService, ...restartService];
      const uniqueServices = [...new Set(allService)];

      row.relationServices.forEach(service => {
        let _service = context.find(s => s.serviceInstance.serviceInstanceId == service.serviceInstanceId)
        const multi_role_name = context.filter(s => s.serviceInstance.serviceInstanceId == service.serviceInstanceId && !isBlank(s.serviceInstance.roleName)).map(item => item.serviceInstance.roleName)
        let _rollBackPatchPoints = [], _currentPatchPoints = [], _historyPatchPoint = [], _version = '',
            _targetPatchPoint = '', _roleName = ''
        if (!isEmpty(_service)) {
          _rollBackPatchPoints = _service.rollBackPatchPoints
          _currentPatchPoints = _service.currentPatchPoints
          _version = _service.serviceInstance.version
          _roleName = isEmpty(multi_role_name) ? '' : [...new Set(multi_role_name)].join(",") // 确保 _roleName 不会因为 _service 为空而出错
          _targetPatchPoint = row.taskType == 0 ? [_service.targetPatchPoint] : _rollBackPatchPoints.map(function (item) {
            return item.displayRollbackPatchPoint
          })
          _historyPatchPoint = _rollBackPatchPoints.map(function (item) {
            return {rollBackPatchPoint: item.displayRollbackPatchPoint, patchHostInfos: item.patchHostInfos}
          })
        }
        const startOrStop = row.patchCategory == 'schema' ? "" : this.$t('okuTask.startOrStop')
        if (uniqueServices.includes(service.serviceInstanceId)) {
          this.originDialogTableData.push({
            serviceName: !!_roleName ? `${service.serviceInstanceName}(${_roleName})` : service.serviceInstanceName,
            operateName: isEmpty(this.parseRollbackPatchPoints(_rollBackPatchPoints)) ?
                startOrStop : service.serviceInstanceId == 'zdh' ?
                    this.$t('okuTask.upgrade') : restartService.includes(service.serviceInstanceId) ? this.$t('okuTask.upgrade') + '  ' + startOrStop : this.$t('okuTask.upgrade'),
            version: _version,
            targetPatchPoint: _targetPatchPoint,
            currentPatchPoints: _currentPatchPoints || [],
            historyPatchPoints: _historyPatchPoint || [],
          })
        }
      })
      console.log(this.originDialogTableData)
      const historyArray = row.taskType == 0 ? ['historyPatchPoints'] : []
      this.updateDialogTableDisplayField = ['serviceName', 'version', ...historyArray, 'currentPatchPoints', 'targetPatchPoint', 'operateName']

      this.updateDialogTableData = deepClone(this.originDialogTableData)
      this.updateShowDialog = true
      this.taskModel = row.taskType
      this.operateModel = row.operateType
      this.titleModel = 'detail'
    },
    /* Ended by AICoder, pid:kb2b9431cfwa1531401f0904107f1f41f063c9c1 */
    openRollBackPatchHosts(patchName, patchHostInfos) {
      if (isEmpty(patchHostInfos)) {
        this.$message({
          type: 'error',
          message: this.$t('okuTask.message.fail2getHostInfo')
        })
        return
      }
      this.resetDialogTableLvl2()

      patchHostInfos.forEach(hostInfo => this.originDialogTableDataLvl2.push({
        hostIp: hostInfo.ip,
        hostName: hostInfo.hostName
      }))
      this.dialogTableDisplayFieldLvl2 = ['hostIp', 'hostName']
      this.dialogTableDataLvl2 = deepClone(this.originDialogTableDataLvl2)
      this.taskModelLvl2 = -1
      this.operateModelLvl2 = -1
      this.titleModelLvl2 = patchName
      this.showDialogLvl2 = true
    },

    isExecuteShow(status) {
      return status === '' || status === 0 || status === null
    },
    getButtonType(row) {
      if (isBlank(row.status) || row.status === 'Terminate' || row.status === 'Success') {
        return 'el-icon el-icon-video-play' // el-icon
      } else if (row.status === 'Running') {
        return 'el-icon el-icon-video-pause'
      } else if (row.status === 'Failure') {
        return 'el-icon el-icon-refresh-right'
      }
    },
    getPermissionsForRow(row) {
      if (isBlank(row.status)) {
        return ['operation.daip.patcher.oku.task.run'];
      } else if (row.status === 'Running') {
        return ['operation.daip.patcher.oku.task.pause'];
      } else if (row.status === 'Terminate') {
        return ['operation.daip.patcher.oku.task.resume'];
      } else if (row.status === 'Failure') {
        return ['operation.daip.patcher.oku.task.retry'];
      }
    },
    getButtonTitle(row) {
      let _this = this
      let _relationTaskStatus = _this.getRelationTaskStatus(row)
      if (_relationTaskStatus && row.status === 'Terminate') {
        return _this.$t('common.button.continue')
      } else if (row.status === 'Running') {
        return _this.$t('common.button.stop')
      } else if (_relationTaskStatus && row.status === 'Failure') {
        return _this.$t('common.button.retry')
      } else if (_relationTaskStatus && isBlank(row.status)) {
        return _this.$t('common.button.execute')
      } else {
        return ""
      }
    },
    stopOrStartOrRetryInstance(row) {
      let _this = this
      let _relationTaskStatus = _this.getRelationTaskStatus(row)
      if (_relationTaskStatus && row.status === 'Terminate') {
        _this.resumeTask(row)
      } else if (row.status === 'Running') {
        _this.pauseTask(row)
      } else if (_relationTaskStatus && row.status === 'Failure') {
        _this.retryTask(row)
      } else if (_relationTaskStatus && isBlank(row.status)) {
        _this.executeTask(row)
      }
    },
    getRelationTaskStatus(row) {
      let _execute4RelationTask = true
      let res = null
      let _taskId = row.relationTaskId
      if (_taskId != 0) {
        let url = URL[env].upgrade.queryByTaskId
        $.ajax({
          url: url.replace(/\${taskId}/, _taskId),
          type: 'get',
          dataType: "json",
          async: false,
          success: function (result) {
            res = result
          }
        });
      }
      if (res != null && res.status == '0') {
        _execute4RelationTask = isBlank(res.data.status) || res.data.status != 'Running'
      }
      return _execute4RelationTask
    },
    isDeleteShow(status) {
      return status !== 'Running' && status !== 'Initial'
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    executeTask(row) {
      let _this = this
      this.$ctStopOrDeleteBoxWithAuth((doubleToken) => _this.$api.upgrade.triggerTask(row.taskId, row.taskName, doubleToken),
          {vm: _this}, _this.$t('common.confirmBox.executeText', {taskName: row.taskName}), null, _this.$t('common.taskMessage.checkMsg'), (doubleToken) => {
            _this.openTaskDetailWithoutBackend(row)
          }, err => {
            _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
            _this.$message({
              type: 'error',
              message: _this.$t('common.taskMessage.executeFail')
            })
          })
    },
    pauseTask(row) {
      let _this = this
      _this.$ctStopOrDeleteBoxWithAuth((doubleToken) => _this.$api.upgrade.pauseTask(row.taskId, row.taskName, doubleToken)
          , {vm: _this}, _this.$t('common.confirmBox.suspendTask') + row.taskName, null, _this.$t('common.taskMessage.checkMsg'), () => {
            _this.queryTask(1)
          }, err => {
            _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
            _this.$message({
              type: 'error',
              message: _this.$t('common.taskMessage.executeFail')
            })
          })
    },
    resumeTask(row) {
      let _this = this
      this.$ctStopOrDeleteBoxWithAuth((doubleToken) => _this.$api.upgrade.resumeTask(row.taskId, row.taskName, doubleToken),
          {vm: _this}, _this.$t('common.confirmBox.resumeText', {taskName: row.taskName}), null, _this.$t('common.taskMessage.checkMsg'), () => {
            _this.openTaskDetailWithoutBackend(row)
          }, err => {
            _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
            _this.$message({
              type: 'error',
              message: _this.$t('common.taskMessage.executeFail')
            })
          })
    },
    retryTask(row) {
      let _this = this
      this.$ctStopOrDeleteBoxWithAuth((doubleToken) => _this.$api.upgrade.retryTask(row.taskId, row.taskName, doubleToken),
          {vm: _this}, _this.$t('common.confirmBox.retryText', {taskName: row.taskName}), null, _this.$t('common.taskMessage.checkMsg'), () => {
            _this.openTaskDetailWithoutBackend(row)
          }, err => {
            _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
            _this.$message({
              type: 'error',
              message: _this.$t('common.taskMessage.executeFail')
            })
          })
    },
    /* Started by AICoder, pid:1dff2qa059w9ac5140580b93b0cfbb2ac2b625d8 */
    openTaskDetailWithoutBackend(row) {
      let _this = this;
      let hasPem = Security.hasOperRights('operation.daip.patcher.oku.task.detail.view');

      if (hasPem) {
        let isExist = false;

        if (!isEmpty(_this.tabList)) {
          _this.tabList.forEach(value => {
            if (value.taskId === row.taskId) {
              isExist = true;
            }
          });
        }
        row.taskContext = {
          clusterId: row.clusterId,
          clusterName: row.clusterName,
          hostIp: []
        };
        if (!isExist) {
          _this.$store.dispatch('addTaskDetailTabs', row);
        }

        window.self.taskInfo = row;
        _this.$store.dispatch('setOkuTaskDetailSrc', '/upgrade/taskDetail');
        _this.$store.dispatch('setTaskDetailActiveName', row.taskId);
      } else {
        _this.queryTask(1);
      }
    },

    /* Ended by AICoder, pid:1dff2qa059w9ac5140580b93b0cfbb2ac2b625d8 */
    timerRefreshTask(currentPage, pageSize) {
      let page = this.$refs.page
      if (isPositiveNum(currentPage)) {
        page.currentPage = currentPage
      } else {
        currentPage = page.currentPage
      }
      if (isPositiveNum(pageSize)) {
        page.pageSize = pageSize
      } else {
        pageSize = page.pageSize
      }
      this.$api.upgrade.timerRefreshTask(currentPage, pageSize)
          .then(res => {
            if (res.status == '0') {
              this.workflowList = res.data.taskList
              this.total = res.data.total
            }
          })
    },
    queryTask(currentPage, pageSize, successCb, errorCb) {
      let page = this.$refs.page
      if (isPositiveNum(currentPage)) {
        page.currentPage = currentPage
      } else {
        currentPage = page.currentPage
      }
      if (isPositiveNum(pageSize)) {
        page.pageSize = pageSize
      } else {
        pageSize = page.pageSize
      }
      this.$api.upgrade.queryTask(currentPage, pageSize)
          .then(res => {
            if (res.status == '0') {
              this.workflowList = res.data.taskList
              this.total = res.data.total
              if (!isEmpty(successCb) && typeof successCb === 'function') {
                successCb()
              }
            } else {
              this.$message({
                type: 'error',
                message: this.$t('okuTask.message.fail2getTask')
              })
              if (!isEmpty(errorCb) && typeof errorCb === 'function') {
                errorCb()
              }
            }
            this.getTableMaxHeight()
            // this.calculateShowService()
          }).catch(err => {
        this.$message({
          type: 'error',
          message: this.$t('okuTask.message.fail2getTask')
        })
        if (!isEmpty(errorCb) && typeof errorCb === 'function') {
          errorCb()
        }
      })
    },
    refresh() {
      this.queryTask(1,10)
      document.getElementById("refresh").blur()
    },
    handleOpen() {
      this.$store.dispatch('setOkuCreateShow', true)
      this.$store.dispatch('setOkuCreateModify', false)
      // this.$store.dispatch('setOkuCreateTaskId', "")
      // this.$store.dispatch('setOkuCreateTaskType', 0)
      // this.$store.dispatch('setOkuCreateTaskName', "")
      // this.$store.dispatch('setOkuCreateClusterId', "")
      // this.$store.dispatch('setOkuCreateOperateType', 0)
      this.$store.dispatch('setOkuCreateSelectService', [])
      this.$store.dispatch('setOkuCreateEditTaskInfo', {})
    },
    async queryTaskByTaskId(taskId) {
      let _this = this
      if (taskId != 0) {
        let res = await this.$api.upgrade.queryByTaskId(taskId)
        return res
      }
      return null
    },
    async createAndExecuteRollbackTask(row) {
      const oid = (event && event.currentTarget && event.currentTarget.getAttribute("operation")) || (event && event.target && event.target.getAttribute("operation"))
      const dop = (event && event.currentTarget && event.currentTarget.getAttribute("doubleOperation")) || (event && event.target && event.target.getAttribute("doubleOperation"))
      let _this = this
      let rollbackTaskName = row.taskName + '_' + _this.$t('okuTask.rollback')

      let res = await _this.$api.upgrade.checkRollback(row.taskId)
      if (!!res && res.status == false) {
        _this.$message({
          type: 'error',
          message: res.message
        })
        return false
      }

      let patchTask = {}
      patchTask['taskName'] = rollbackTaskName
      patchTask['taskType'] = 1
      patchTask['clusterId'] = row.clusterId
      patchTask['operateType'] = row.operateType
      patchTask['relationTaskId'] = row.taskId
      patchTask['patchCategory'] = row.patchCategory
      patchTask['context'] = row.context
      patchTask['relationServices'] = row.relationServices
      if (row.needRestartServices !== undefined && row.needRestartServices !== null) {
        patchTask['needRestartServices'] = row.needRestartServices
      }
      this.$ctStopOrDeleteBoxWithAuth((doubleToken) => this.$api.upgrade.rollbackTask(patchTask, doubleToken),
          {
            vm: _this,
            oid,
            dop
          }, _this.$t('common.confirmBox.rollbackText', {taskName: row.taskName}), null, _this.$t('common.taskMessage.checkMsg'),
          () => {
            _this.refreshAfterRollback(row)
          }, err => {
            _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
            _this.$message({
              type: 'error',
              message: _this.$t('common.taskMessage.executeFail')
            })
          })
    },
    refreshAfterRollback(row) {
      let _this = this
      _this.queryTask(1)
      _this.refresh()
    },
    openTask(item, doubleToken) {
      if (!doubleToken) {
        const operationId = (event.currentTarget && event.currentTarget.getAttribute("operation")) || (event.target && event.target.getAttribute("operation"))
        const doubleOperation = (event.currentTarget && event.currentTarget.getAttribute("doubleOperation")) || (event.target && event.target.getAttribute("doubleOperation"))
        this.$refs.authDialog.open(operationId, doubleOperation, (doubleToken) => this.gotoDetailUrl(item, doubleToken))

      } else {
        this.gotoDetailUrl(item, doubleToken)
      }
    },
    /* Started by AICoder, pid:k7b51u080bba72e14b5b09b80089f13ed986d79a */
    gotoDetailUrl(item, doubleToken) {
      let _this = this
      this.$api.upgrade.getDetailUrl(doubleToken)
          .then(res => {
            if (res.status == '0') {
              let isExist = false;
              if (!isEmpty(this.tabList)) {
                _this.tabList.forEach(value => {
                  if (value.taskId === item.taskId) {
                    isExist = true;
                  }
                });
              }
              if (!isExist) {
                _this.$store.dispatch('addTaskDetailTabs', item);
              }
              item.taskContext = {
                clusterId: item.clusterId,
                clusterName: item.clusterName,
                hostIp: []
              };
              window.self.taskInfo = item;
              _this.$store.dispatch('setOkuTaskDetailSrc', res.data);
              _this.$store.dispatch('setTaskDetailActiveName', item.taskId);
            } else {
              _this.$message({
                type: 'error',
                message: _this.$t('okuTask.message.fail2viewDetail')
              });
            }
          }).catch(err => {
        _this.$message({
          type: 'error',
          message: _this.$t('okuTask.message.fail2viewDetail')
        });
      });
    },

    /* Ended by AICoder, pid:k7b51u080bba72e14b5b09b80089f13ed986d79a */
    editTask(row) {
      this.$store.dispatch('setOkuCreateShow', true)
      this.$store.dispatch('setOkuCreateModify', true)
      // this.$store.dispatch('setOkuCreateTaskId', row.taskId)
      // this.$store.dispatch('setOkuCreateTaskType', row.taskType)
      // this.$store.dispatch('setOkuCreateTaskName', row.taskName)
      // this.$store.dispatch('setOkuCreateClusterId', row.clusterId)
      // this.$store.dispatch('setOkuCreateOperateType', row.operateType)
      let serviceInstanceIds = row.context.map(service => service.serviceInstance.serviceInstanceId)
      this.$store.dispatch('setOkuCreateSelectService', serviceInstanceIds)
      this.$store.dispatch('setOkuCreateEditTaskInfo', row)
    },
    deleteTask(item) {
      let wfIds = []
      wfIds.push(item.taskId)
      this.$ctStopOrDeleteBox(() => this.$api.upgrade.batchDeleteTask(wfIds), this,
          this.$t('common.confirmBox.deleteText'), this.$t('common.confirmBox.deleteDescribe'), this.$t('common.taskMessage.checkMsg'),
          () => this.queryTask(1),
          err => {
            _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
            _this.$message({
              type: 'error',
              message: _this.$t('common.taskMessage.deleteFail')
            })
          })
    },
    async copyTask(item) {
      let res = await this.$api.upgrade.checkDuplicate(item.taskId)
      if (!!res && res.status == false) {
        this.$message({
          type: 'error',
          message: res.message
        })
        return false
      }
      this.$ctStopOrDeleteBox(() => this.$api.upgrade.copyTask(item.taskId, item.taskName), this,
          this.$t('common.confirmBox.copyText'), null, this.$t('common.taskMessage.checkMsg'),
          () => this.queryTask(1),
          err => {
            _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
            _this.$message({
              type: 'error',
              message: _this.$t('common.taskMessage.copyFail')
            })
          })
    },
    batchDeleteTask() {
      let wfIds = []
      wfIds = this.multipleSelection.map(workflow => workflow.taskId)
      this.$ctStopOrDeleteBox(() => this.$api.upgrade.batchDeleteTask(wfIds), this,
          this.$t('common.confirmBox.deleteText'), this.$t('common.confirmBox.deleteDescribe'),
          this.$t('common.taskMessage.checkMsg'), () => this.queryTask(1),
          err => {
            _this.$ctConfirmBox(_this.$t('common.taskMessage.checkMsg'), 'danger', _this, true)
            _this.$message({
              type: 'error',
              message: _this.$t('common.taskMessage.deleteFail')
            })
          })
    }
  }
}
</script>

<style lang="scss" scoped>
.okuTask .vn-dropdown + .vn-dropdown {
  margin-left: 15px;
}

.okuTask .vn-dropdown-link {
  cursor: pointer;
  color: var(--vn-color-primary);
  display: flex;
  align-items: center;
}
</style>