<template>
  <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                 :current-page="currentPage" :page-sizes="[10,15,25,50]" :page-size="pageSize"
                 layout="total, sizes, prev, pager, next, jumper" :total="total">
  </el-pagination>
</template>

<script>

import { isPositiveNum} from "@/utils/utils";

export default {
  name: "Paging",
  props: {
    total: {
      type: Number,
      default: 0
    },
    params: {
      type: Object,
      default: function (){
        return {}
      }
    }
  },
  emits: ['update'],
  data() {
    return {
      pageSize: 10,
      currentPage: 1,
    }
  },
  computed: {
    getLayout() {
      if (this.total === undefined || this.total === null || this.total == 0) {
        return 'first,  prev, pager, next, last, sizes'
      } else {
        return 'total, first,  prev, pager, next, last, sizes'
      }
    }
  },
  methods: {
    updateData() {
      this.$emit('update',this.currentPage, this.pageSize, this.params)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.$emit('update',this.currentPage, this.pageSize, this.params)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.$emit('update',this.currentPage, this.pageSize, this.params)
    },
    resetPageAndSize(currentPage, pageSize) {
      if (isPositiveNum(currentPage)) {
        this.currentPage = currentPage
      }
      if (isPositiveNum(pageSize)) {
        this.pageSize = pageSize
      }
    }
  }
}
</script>

<style scoped>

</style>