<template>
  <el-dialog v-model="dialogVisible" :title="$t('common.authDialog.login')" width="30%" :close-on-click-modal="false" :before-close="handleClose">
    <el-form  :model="form" ref="form" :rules="rules" label-width="100px">
      <el-form-item :label="$t('common.authDialog.loginMethod')" prop="loginType">
        <el-radio-group v-model="form.loginType">
          <el-radio label="password">{{$t('common.authDialog.passwordMethod')}}</el-radio>
          <el-radio label="code">{{$t('common.authDialog.codeMethod')}}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="$t('common.authDialog.username')" prop="username" class="send-code">
        <el-input v-model="form.username" :placeholder="$t('common.authDialog.pleaseEnterUsername')">
          <template #append>
            <el-button v-if="form.loginType === 'code'" :disabled="!canClick" @click="sendCode">
              {{ canClick ? $t('common.authDialog.sendVerificationCode') : $t('common.authDialog.afterResend', { num:countdown }) }}
            </el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item v-if="form.loginType === 'password'" :label="$t('common.authDialog.password')" prop="password">
        <el-input v-model="form.password" type="password" :placeholder="$t('common.authDialog.pleaseEnterPassword')"></el-input>
      </el-form-item>

      <el-form-item v-else :label="$t('common.authDialog.verificationCode')" prop="code">
        <el-input v-model="form.code" :placeholder="$t('common.authDialog.pleaseEnterVerificationCode')"></el-input>
      </el-form-item>

      <el-form-item style="text-align: right;">
        <el-button  type="primary"  @click="handleSubmit">{{$t('common.authDialog.loginButton')}}</el-button>
        <el-button  plain @click="handleCancel">{{$t('common.authDialog.cancelButton')}}</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import Security from "@/utils/security";
import $ from "jquery"
export default {
  data() {
    return {
      form: {
        loginType: 'password',
        username: '',
        password: '',
        code: ''
      },
      dialogVisible: false,
      countdown: 60, // 倒计时时间
      canClick: true, // 控制按钮是否可点击
      double_token: "",
      operationId: "",
      rules: {
        loginType: [
          { required: true, message: this.$t('common.validate.required'), trigger: 'change' }
        ],
        username: [
          { required: true, message: this.$t('common.validate.required'), trigger: 'blur' }
        ],
        password: [
          { required: true, message: this.$t('common.validate.required'), trigger: 'change' }
        ],
        code: [
          { required: true, message: this.$t('common.validate.required'), trigger: 'change' }
        ]
      }
    }
  },
  /* Started by AICoder, pid:p6e4e665361734f14b47094fc1be57165cf37f15 */
  methods: {
    open(operationId, doubleOperation, callback) {
      this.operationId = operationId;
      if (`${doubleOperation}` === "true" && Security.doubleAuthCheck(this.operationId)) {
        this.dialogVisible = true;
        this.callback = callback;
      } else {
        callback && callback();
      }
    },
    handleClose(done) {
      this.handleCancel(true);
      done();
    },
    handleCancel(cancelAuth) {
      cancelAuth && this.$message({
        type: 'warning',
        message: this.$t('common.authDialog.cancelAuthentication')
      });
      this.$refs["form"].resetFields();
      cancelAuth && (this.dialogVisible = false);
    },
    sendCode() {
      if (!this.canClick) return; // 如果按钮不可点击，则直接返回
      if (!this.form.username) {
        this.$message({
          type: 'error',
          message: this.$t('common.authDialog.pleaseEnterUsername')
        });
        return;
      }

      this.canClick = false; // 设置按钮为不可点击
      let timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(timer); // 清除定时器
          this.countdown = 60; // 重置倒计时
          this.canClick = true; // 允许再次点击
        }
      }, 1000);
      const that = this;
      $.ajax({
        url: '/api/oauth2/v1/users/double/auth/send',
        type: 'POST',
        data: JSON.stringify({ "operation": this.operationId, "authType": 1, "approvalUserName": this.form.username }),
        dataType: 'json',
        contentType: 'application/json',
        success: function (response) {
          that.double_token = response.double_token;
          that.$message({
            type: 'success',
            message: that.$t('common.authDialog.sendVerificationCodeSuccess')
          });
        },
        error: function (response) {
          that.$message({
            type: 'error',
            message: response && response.responseJSON && response.responseJSON.message || that.$t('common.authDialog.sendVerificationCodeFailed')
          });
        }
      });
    },
    handleSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.dialogVisible = false;
          this.doubleAuth();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    doubleAuth() {
      const passwordChecked = this.form.loginType === 'password';
      const that = this;
      $.ajax({
        url: passwordChecked ? "/api/oauth2/v1/users/double/auth/send" : "/api/oauth2/v1/users/double/auth/token/check",
        type: "POST",
        data: JSON.stringify(
          passwordChecked
            ? {
              authType: 0,
              operation: this.operationId,
              approvalUserName: this.form.username,
              password: typeof ict_framework_func1 != 'undefined' ? ict_framework_func1(this.form.password) : this.form.password
            }
            : { double_token: this.double_token, code: this.form.code }
        ),
        dataType: "json",
        contentType: "application/json",
        success: function (response) {
          if (passwordChecked) {
            that.double_token = response.double_token;
          }
          that.$refs["form"].resetFields();
          that.callback && that.callback(that.double_token);
        },
        error: function (response) {
          that.$refs["form"].resetFields();
          that.$message({
            type: 'error',
            message: response && response.responseJSON && response.responseJSON.message || that.$t('common.authDialog.authenticationFailed')
          });
        }
      });
    }
  }
  /* Ended by AICoder, pid:p6e4e665361734f14b47094fc1be57165cf37f15 */
};
</script>

<style lang="scss">
.send-code.el-form-item .el-input-group__append {
  padding: 0px;
  .is-disabled {
    background-color: '#f5f5f5';
    border-color: '#e5e5e5';
    color: '#bfbfbf';
  }
}
</style>