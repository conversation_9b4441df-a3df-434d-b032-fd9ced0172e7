const URL = {
  tcf: {
    cluster: {
      getClusters: '/api/daip-deployer-svr/v1/handler/cluster',
    },
    service: {
      getServiceInstanceInfoByClusterId: '/api/daip-deployer-svr/v1/handler/service/instance/query',
      queryVersionByServiceNameList: '/api/daip-deployer-svr/v1/handler/product/version/queryByServiceNameList'
    },
    upgrade: {
      queryServiceModelVersion: '/api/daip-patcher-handler/v1/patch/serviceInstance/query',
      // queryServiceModelVersion: '/api/daip-deployer-svr/v1/handler/version/upgrade/manager/version/query',
      createTask: '/api/daip-patcher-handler/v1/patch/task/create',
      modifyTask: '/api/daip-patcher-handler/v1/patch/task/modify',
      queryTask: '/api/daip-patcher-handler/v1/patch/tasks',
      queryNeedRestartService: '/api/daip-patcher-handler/v1/patch/restartService/query',
      batchDeleteTask: '/api/daip-patcher-handler/v1/patch/task/remove',
      copyTask: '/api/daip-patcher-handler/v1/patch/task/copy',
      triggerTask: '/api/daip-patcher-handler/v1/patch/task/trigger',
      rollbackTask: '/api/daip-patcher-handler/v1/patch/task/rollback',
      retryTask: '/api/daip-patcher-handler/v1/patch/task/retry',
      pauseTask: '/api/daip-patcher-handler/v1/patch/task/pause',
      resumeTask: '/api/daip-patcher-handler/v1/patch/task/resume',
      queryByTaskId: '/api/daip-patcher-handler/v1/patch/task/${taskId}',
      queryTaskDetail: '/api/daip-patcher-handler/v1/patch/task/detail/${taskId}',
      getDetailUrl: '/api/daip-patcher-handler/v1/patch/task/get/detail/url',
      checkRollback: '/api/daip-patcher-handler/v1/patch/task/checkRollback',
      checkDuplicate: '/api/daip-patcher-handler/v1/patch/task/checkDuplicate',
      queryUnpatchedClusterAndServiceInstance: '/api/daip-patcher-handler/v1/patches/unpatched/cluster/serviceinstance',
    }
  },
  test: {
    cluster: {
      getClusters: '/mock/handler/cluster',
    },
    service: {
      getServiceInstanceInfoByClusterId: '/mock/service/query',
      queryVersionByServiceNameList: '/mock/service/queryVersionByServiceNameList',
    },
    upgrade: {
      queryServiceModelVersion: '/mock/upgrade/queryServiceModelVersion',
      createTask: '/mock/upgrade/create',
      modifyTask: '/mock/upgrade/modify',
      queryTask: '/mock/upgrade/queryTask',
      queryNeedRestartService: '/mock/upgrade/queryNeedRestartService',
      batchDeleteTask: '/mock/upgrade/batchDeleteTask',
      triggerTask: '/mock/upgrade/triggerTask',
      retryTask: '/mock/upgrade/retry',
      pauseTask: '/mock/upgrade/pause',
      resumeTask: '/mock/upgrade/resume'
    }
  },
};
export { URL };
