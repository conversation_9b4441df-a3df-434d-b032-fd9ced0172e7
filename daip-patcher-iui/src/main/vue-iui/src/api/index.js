// 使用 Vite 的 import.meta.glob 动态导入模块
const files = import.meta.glob('./modules/*.js', { eager: true });

const api = {};

// 遍历所有导入的模块
for (const path in files) {
  if (path.endsWith('index.js')) {
    continue; // 跳过 index.js 文件
  }

  // 处理文件路径，去掉 './modules/' 前缀和 '.js' 后缀
  const mk = path.replace(/\.\/modules\/|\.js/g, '');
  const m = files[path];

  // 将模块的导出添加到 api 对象中
  api[mk] = Object.keys(m).reduce((s, e) => {
    if (e !== 'default') {
      s[e] = m[e];
    }
    return s;
  }, m.default || {});
}

// 导出插件
export default {
  install: (app, options) => {
    // 注入一个全局可用的 $api 方法
    app.config.globalProperties.$api = api;
  }
};