import { URL } from '../urlConfig'
import {
  httpGet,
  httpGetWithoutLoading,
  httpPost
} from '@/axios/http'

const env = import.meta.env.MODE;

export function queryServiceModelVersion(versionQueryRequestInfo) {
  let url = URL[env].upgrade.queryServiceModelVersion
  return httpPost(url, {}, versionQueryRequestInfo)
}

export function batchDeleteTask(taskIds) {
  let url = URL[env].upgrade.batchDeleteTask
  return httpPost(url, {}, taskIds)
}

export function copyTask(taskId,taskName) {
  let url = URL[env].upgrade.copyTask
  return httpPost(url, {taskId, taskName})
}

export function triggerTask(taskId, taskName, doubleToken) {
  let url = URL[env].upgrade.triggerTask
  return httpPost(url, {taskId, taskName, doubleToken})
}

export function pauseTask(taskId, taskName, doubleToken) {
  let url = URL[env].upgrade.pauseTask
  return httpPost(url, {taskId, taskName, doubleToken})
}

export function resumeTask(taskId, taskName, doubleToken) {
  let url = URL[env].upgrade.resumeTask
  return httpPost(url, {taskId, taskName, doubleToken})
}

export function retryTask(taskId, taskName, doubleToken) {
  let url = URL[env].upgrade.retryTask
  return httpPost(url, {taskId, taskName, doubleToken})
}
export function queryTask(currentPage, pageSize, taskName) {
  let url = URL[env].upgrade.queryTask
  return httpGet(url, {currentPage, pageSize, taskName})
}

export function timerRefreshTask(currentPage, pageSize) {
  let url = URL[env].upgrade.queryTask
  return httpGetWithoutLoading(url, {currentPage, pageSize})
}

// export function createRollbackTask(wfId, wfName, target) {
//   let url = URL[env].upgrade.createRollbackTask
//   return httpPostWithoutLoading(url, {wfId, wfName, target})
// }

export function logout() {
  let url = '/api/oauth2/v1/logout';
  return httpGetWithoutLoading(url)
}

export function createTask(patchTask ) {
  let url = URL[env].upgrade.createTask
  return httpPost(url, {}, patchTask)
}

export function queryNeedRestartService(patchTask ) {
  let url = URL[env].upgrade.queryNeedRestartService
  return httpPost(url, {}, patchTask)
}

export function modifyTask(patchTask ) {
  let url = URL[env].upgrade.modifyTask
  return httpPost(url, {}, patchTask)
}

export function rollbackTask(patchTask,doubleToken) {
  let url = URL[env].upgrade.rollbackTask
  return httpPost(url, {doubleToken}, patchTask)
}

export function queryByTaskId(taskId) {
  let url = URL[env].upgrade.queryByTaskId
  return httpGet(url.replace(/\${taskId}/, taskId), {})
}

export function queryUnpatchedClusterAndServiceInstance() {
  let url = URL[env].upgrade.queryUnpatchedClusterAndServiceInstance
  return httpGet(url, {})
}

export function queryTaskDetail(taskId) {
  let url = URL[env].upgrade.queryTaskDetail
  return httpGet(url.replace(/\${taskId}/, taskId), {})
}

export function checkRollback(taskId) {
  let url = URL[env].upgrade.checkRollback
  return httpPost(url, {taskId})
}

export function checkDuplicate(taskId) {
  let url = URL[env].upgrade.checkDuplicate
  return httpPost(url, {taskId})
}

export function getDetailUrl(doubleToken) {
  let url = URL[env].upgrade.getDetailUrl
  return httpGet(url,{},doubleToken)
}