import { URL } from '../urlConfig'
import {httpGet, httpPost} from '@/axios/http'
const env = import.meta.env.MODE;

export function getServiceInstanceInfoByClusterId(clusterId) {
  let url = URL[env].service.getServiceInstanceInfoByClusterId
  return httpGet(url, {clusterId})
}

export function queryServiceAllVersion(serviceNameList) {
  let url = URL[env].service.queryVersionByServiceNameList
  return httpPost(url, {}, serviceNameList)
}