import {createRouter, createWebHashHistory} from 'vue-router';

import OkuTaskManager from "@/view/okuTask/OkuTaskManager.vue";
import home from "@/view/home.vue";

const routes = [
  {
    path: '/',
    name: 'home',
    component: home,
    children: [
      {
        path: '/upgrade',
        name: 'upgrade',
        component: OkuTaskManager
      }
    ]
  }
]

let popstateListener = null;

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL), routes,
});

router.beforeEach((to, from, next) => {
  if (!popstateListener) {
    popstateListener = () => {
      // 在这里处理浏览器回退事件
      if (to.name === "home" && from.path === "/") {
        window.removeEventListener("popstate", popstateListener);
        popstateListener = null;
        typeof IctUtil !== 'undefined' && IctUtil.toOtherMenuPage("daip-patcher-update");
        return;
      }
    };
    window.addEventListener("popstate", popstateListener);
  }
  next();
});


export default router
