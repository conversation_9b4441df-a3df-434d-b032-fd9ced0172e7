/* eslint-disable no-unused-vars */
import axios from 'axios'

// axios.defaults.timeout = 5000
const username = window.top && window.top.getUserName && window.top.getUserName() || 'admin';
const lang = window.top && window.top.getLanguage && window.top.getLanguage() || 'zh-CN'
axios.defaults.headers['Content-Type'] = 'application/json;charset=UTF-8'
axios.defaults.headers['daip-locale'] = lang
axios.defaults.headers['Accept-Language'] = lang
// axios.defaults.headers['username'] = username
axios.defaults.baseURL = ''
axios.defaults.withCredentials = true
import { showLoading, hideLoading } from '@/utils/loading';

/**
 * 封装get方法
 * @param url
 * @param data
 * @returns {Promise}
 */

const errorHandle = (status, other) => {
  // 状态码判断
  switch (status) {
    case 404:
      console.log('The requested resource does not exist.');
      break;
    default:
      console.log(other);
  }
}

axios.interceptors.request.use(config => {
  if (!config.headers['forgerydefense']) {
    let csrftoken = window.localStorage.getItem("csrftoken")
    config.headers['forgerydefense'] = csrftoken || "";
  }
  return config
}, error => Promise.reject(error));


axios.interceptors.response.use(
  res => res.status === 200 ? Promise.resolve(res) : Promise.reject(res),
  error => {
    const { response } = error;
    if (response) {
      // 请求已发出，但是不在2xx的范围
      errorHandle(response.status, response.data.message);
      return Promise.reject(response);
    }
  });

function getCookie(name) {
  var strcookie = document.cookie;//获取cookie字符串
  var arrcookie = strcookie.split("; ");//分割
  //遍历匹配
  for (var i = 0; i < arrcookie.length; i++) {
    var arr = arrcookie[i].split("=");
    if (arr[0] == name) {
      return arr[1];
    }
  }
  return "";
}

// 打印所有cookie
function print() {
  var strcookie = document.cookie;//获取cookie字符串
  var arrcookie = strcookie.split(";");//分割

  //遍历匹配
  for (var i = 0; i < arrcookie.length; i++) {
    var arr = arrcookie[i].split("=");
    console.log(arr[0] + "：" + arr[1]);
  }
}

function httpGet(url, params = {}) {
  return new Promise((resolve, reject) => {
    axios
      .get(url, {
        params: params
      })
      .then(
        response => {
          resolve(response.data)
        }).catch(err => {
      reject(err.data)
    })
  })
}

/**
 * 封装post请求application/json
 * @param url
 * @param data
 * @returns {Promise}
 */

function httpPost(url, params = {}, data = {}) {
  showLoading()//显示加载中
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url,
      params,
      data: JSON.stringify(data)
    }).then(
      response => {
        hideLoading()//关闭加载
        resolve(response.data)
      }).catch(err => {
      hideLoading()//关闭加载
      reject(err.data)
    })
  })
}

function httpGetWithoutLoading(url, params = {}) {
  return new Promise((resolve, reject) => {
    axios
      .get(url, {
        params: params
      })
      .then(
        response => {
          resolve(response.data)
        }).catch(err => {
      reject(err.data)
    })
  })
}

function httpPostWithoutLoading(url, params = {}, data = {}) {
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url,
      params,
      data: JSON.stringify(data)
    }).then(
      response => {
        resolve(response.data)
      }).catch(err => {
      reject(err.data)
    })
  })
}


export { httpGet, httpPost, lang, httpGetWithoutLoading, httpPostWithoutLoading }