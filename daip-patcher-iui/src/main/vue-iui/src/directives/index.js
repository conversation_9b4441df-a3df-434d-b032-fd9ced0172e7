/* Started by AICoder, pid:25144009737e54b1488709c8002fa04dc871ace9 */
import Security from "@/utils/security";
let cachedDoubleOperationTree = null
function checkPermission(el, binding) {
    const { value } = binding
    if (value && value instanceof Array) {
        if (value.length > 0) {
            let hasPermission = Security.hasOperRights(value[0])
            if (!hasPermission) {
                el.parentNode && el.parentNode.removeChild(el)
            }else {
                el.setAttribute("operation", value[0])
                if (cachedDoubleOperationTree === null) {
                  cachedDoubleOperationTree = Security.queryDoubleOperationTree()
                }
                (cachedDoubleOperationTree || []).indexOf(value[0]) > -1 && el.setAttribute("doubleOperation", "true")
              }
        }
    } else {
        throw new Error(`use like: v-permission="['add','editor']"`)
    }
}

export default {
    "permission": {
        mounted(el, binding) {
            checkPermission(el, binding)
        },
        updated(el, binding) {
            // 如果新旧值不同，则重新检查权限
            if (binding.value !== binding.oldValue) {
                checkPermission(el, binding);
            }
        }
    }
}
/* Ended by AICoder, pid:25144009737e54b1488709c8002fa04dc871ace9 */