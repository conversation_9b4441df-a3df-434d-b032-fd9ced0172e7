export default {
  okuCreate: {
    taskName: 'Task Name',
    cluster: 'Cluster',
    host: 'Host',
    service: 'Service',
    serviceInstance: 'Service instance',
    upgradeTarget: 'Upgrade Target',
    upgradeMethod: 'Upgrade method',
    rollingUpgrade: 'Rolling Upgrade',
    offlineUpgrade: 'Offline Upgrade',
    selectHost: 'Select Host',
    selectHostTargetVersion: 'Select Host Target Version',
    ipAddress: 'Ip Address',
    status: 'Status',
    selectService: 'Select service instance',
    prVersion: 'Previous Version',
    currentVersion: 'Current Version',
    targetVersion: 'Target Version',
    selectServiceTargetVersion: 'Select Service Target Version',
    instanceCount: 'Instance Count',
    create: 'Create Task',
    bigDataComponent: 'big data components',
    nonBigDataComponent: 'non-big data components',
    hostStatus: {
      good: 'Good',
      abnormal: 'Abnormal',
      disconnect: 'Offline',
      error: 'Bad'
    },
    message: {
      failed2ClusterInfo: 'Failed to obtain cluster information',
      fail2getVersionInfo: 'Failed to get version information',
      fail2getServiceInfo: 'Failed to get service information',
      fail2getPatchInfo: 'Failed to get patch information',
      fail2getHostInfo: 'Failed to get host information',
      selectAtLeastOne: 'Host and service select at least one',
      clusterEmpty: 'Cluster cannot be empty',
      hostEmpty: 'Host cannot be empty',
      serviceEmpty: 'Service cannot be empty',
      serviceVersionEmpty: 'Service target version cannot be empty',
      hostInfoEmpty: 'Host information is empty',
      serviceInfoEmpty: 'Service information is empty',
      targetVersionEmpty: 'target version is empty',
      clusterNotEmpty: 'Cluster cannot be empty',
      taskNameNotEmpty: 'Task name cannot be empty',
      taskIdNotEmpty: 'Task id cannot be empty',
      patchInfoEmpty: 'Patch information is empty',
      taskNameReg: 'The task name can only be English, numbers or underscores',
      taskNameLength: 'The length of the task name cannot exceed 20 characters',   
      taskConfirmServiceMsg: 'Services to Be Upgraded:{services}',
      taskConfirmRelationServicesMsg: 'Relation start or stop services:{relationServices}',
    },
    preCheckMsg: 'Verification overview: A total of {total} hosts were verified, {pass} hosts passed, and {error} hosts failed verification',
    preCheckResult: 'Unable to connect or wrong username and password'
  }
}