import common from './common_zh'
import create from "@/i18n/create_zh";
import task from "@/i18n/task_zh";
import model from "@/i18n/model_zh";
import modelDetail from "@/i18n/modelDetail_zh";
import okuCreate from "@/i18n/okuCreate_zh";
import okuTask from "@/i18n/okuTask_zh";
export default {
  ...common,
  ...task,
  ...create,
  ...model,
  ...modelDetail,
  ...okuCreate,
  ...okuTask,
  cluster: {
    current_cluster: '当前集群'
  },
  serviceStatus: {
    deploy: '部署中',
    success: '成功',
    fail: '失败',
    running: '运行中',
    waiting: '等待中',
    error: '失败',
    service_delete_confirm_message: '此操作将删除所选服务, 是否继续?',
    serviceTag: '服务',
    serviceAddTag: '新增服务',
    service_status_good: '服务可用，serviceStatus=1',
    service_status_lesswell: '服务可用，serviceStatus=1',
    service_status_bad: '服务不可用，serviceStatus!=1',
    service_status_stopped: '全部角色stopped状态',
    service_status_starting: '服务启动中',
    service_status_stopping: '服务停止中',
    service_status_installing: '服务安装中',
    service_status_uninstalling: '服务卸载中',
    service_status_stop_failed: '有一个或多个角色处于stop failed状态'
  },
  service: {
    serviceInstanceName: '服务实例名',
    deployerStatus: '部署状态',
    serviceStatus: '服务状态',
    roleNum: '角色计数',
    dependServices: '依赖服务',
    versionNum: '版本号',
    operation: '操作',
    confirm_message: '确定执行此操作吗？',
    confirm_tip: '提示',
    confirm_sure: '确认',
    confirm_cancel: '取消',
    delete_confirm_message: '是否确认卸载该服务？',
    delete_success: '卸载成功！'
  }
}
