export default {
  model: {
    name: '模型名称',
    desc: '描述',
    service: '服务',
    createTime: '创建时间',
    version: '版本',
    default: "该任务模型为系统内置模型无法被删除",
    operation: '操作',
    query: {
      failed: '获取任务模型失败',
    },
    import: {
      success: '导入成功',
      failure: '导入失败',
      process: '导入中',
      sureUpdate: '文件已存在，是否更新',
      updateSuccess: '更新成功',
      updateFail: '更新失败',
      updateCancel: '取消更新',
      fileFormatError: '文件格式不正确，只支持json格式的文件'
    },
    delete: {
      failed: '默认模型文件不能被删除'
    }
  }
}