export default {
  okuTask: {
    taskName: '升级任务名',
    upgradeModel: '升级模式',
    createTime: '创建时间',
    failure: '失败',
    operation: '操作',
    upgradeObject: '升级对象',
    executionTime: '上一次执行时间',
    progress: '进度',
    message: {
      fail2getTask: '获取任务失败',
      fail2getServiceInfo: '获取服务信息失败',
      fail2getHostInfo: '获取主机信息失败',
      failedToCreateRollbackTask: '创建回滚任务失败',
      fail2viewDetail: '查看详情失败'
    },
    ROLLING: '滚动',
    OFFLINE: '离线',
    dialog: {
      hostName: '主机名',
      hostIp: '主机IP',
      patchName: '补丁名',
      targetVersion: '升级后版本',
      serviceName: '服务名',
      operateName: '操作',
      originVersion: '升级前版本',
      version: '版本',
      rollBackPatchPoints: '补丁回退记录',
      historyPatchPoints:'历史补丁版本',
      currentPatchPoints:'当前补丁版本',
      targetPatchPoint:'目标补丁版本'
    },
    disable: '禁用',
    upgrade: '升级',
    rollback: '回滚',
    execute: {
      success: '执行成功',
      failure: '执行失败',
      retryStep: '是否执行重试',
      cancel: '取消重试',
    },
    popconfirm: {
      title: '请确认回退对象！',
      agentAndService: '主机+服务',
      service: '服务'
    },
    detail: '详情',
    startOrStop: '启停'
  }
}