import common from './common_en'
import create from "@/i18n/create_en";
import task from "@/i18n/task_en";
import model from "@/i18n/model_en";
import modelDetail from "@/i18n/modelDetail_en";
import okuCreate from "@/i18n/okuCreate_en";
import okuTask from "@/i18n/okuTask_en";
export default {
  ...common,
  ...task,
  ...create,
  ...model,
  ...modelDetail,
  ...okuCreate,
  ...okuTask,
  cluster: {
    current_cluster: 'Current Cluster'
  },
  serviceStatus: {
    deploy: 'Deploying',
    success: 'Success',
    fail: 'Fail',
    running: 'Running',
    waiting: 'Waiting',
    error: 'Error',
    serviceTag: 'serviceTag',
    serviceAddTag: 'serviceAddTag',
    service_status_good: 'The status of all the role instances is "Started"',
    service_status_lesswell: 'The status of the Ctrl-Node role instance is "Started",and the status of other role instances is not "Started"',
    service_status_bad: 'Other status',
    service_status_stopped: 'The status of all the role instances in this service is "Stopped"',
    service_status_starting: 'The status of all the role instances in this service is "Starting"',
    service_status_stopping: 'The status of all the role instances in this service is "Stopping"',
    service_status_installing: 'The status of all the role instances in this service is "Installing"',
    service_status_uninstalling: 'Service is uninstalling...',
    service_status_stop_failed: 'Service has stopped failed'
  },
  service: {
    serviceInstanceName: 'serviceInstanceName',
    deployerStatus: 'deployerStatus',
    serviceStatus: 'serviceStatus',
    roleNum: 'roleNum',
    dependServices: 'dependServices',
    versionNum: 'versionNum',
    operation: 'operation',
    confirm_message: 'Confirm this action?',
    confirm_tip: 'Tips',
    confirm_sure: 'Sure',
    confirm_cancel: 'Cancel',
    delete_confirm_message: 'Are you sure you want to uninstall the service?',
    delete_success: 'Uninstalled success！'
  }
}
