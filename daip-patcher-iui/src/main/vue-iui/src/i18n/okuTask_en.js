export default {
  okuTask: {
    taskName: 'Upgrade Task Name',
    upgradeModel: 'Upgrade mode',
    createTime: 'Create Time',
    failure: 'Failure',
    operation: 'Operation',
    upgradeObject: 'Upgrade Object',
    executionTime: 'Last execution time',
    progress: 'Progress rate',
    message: {
      fail2getTask: 'Failed to get task',
      fail2getServiceInfo: 'Failed to get service info',
      fail2getHostInfo: 'Failed to get host info',
      failedToCreateRollbackTask: 'Failed to create rollback task',
      fail2viewDetail: 'View details failed'
    },
    ROLLING: 'rolling',
    OFFLINE: 'offline',
    dialog: {
      hostName: 'Host Name',
      hostIp: 'Host IP',
      patchName: 'Patch Name',
      targetVersion: 'Upgraded Version',
      serviceName: 'Service Name',
      operateName: 'Operate Name',
      version: 'Version',
      originVersion: 'Version Before Upgrade',
      rollBackPatchPoints: 'Rollback Patch Record',
      historyPatchPoints:'History version',
      currentPatchPoints:'Current version',
      targetPatchPoint:'Target version'
    },
    disable: 'disable',
    upgrade: 'upgrade',
    rollback: 'rollback',
    execute: {
      success: 'execute success',
      failure: 'execute failure',
      retryStep: 'Retry or not',
      cancel: 'cancel retry',
    },
    popconfirm: {
      title: 'Please confirm the rollback target!',
      agentAndService: 'Host+Service',
      service: 'Service'
    },
    detail: 'Detail',
    startOrStop: 'startAndStop'
  }
}