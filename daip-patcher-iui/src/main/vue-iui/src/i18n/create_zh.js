export default {
  create: {
    createTask: '创建任务',
    host: '主机',
    service: '服务',
    instance: '实例',
    cluster: '集群',
    taskModel: '任务模型',
    name: '任务名称',
    desc: '描述',
    fail_create: '创建失败，主机列表为空',
    host_list_is_empty: '主机列表为空',
    fail_get_cluster_info: '获取集群信息失败',
    fail_get_host_info: '获取主机信息失败',
    fail_add_operator: '已达到可选集群上限',
    fail_get_task_model: '获取任务模型失败',
    replication: '的副本',
    saveSuccess: '保存成功！',
    saveFailure: '保存失败！',
    select_host: '请选择主机',
    unselect_host: '未选择的主机',
    selected_host: '已选择的主机',
    select_service: '请选择服务',
    unselect_service: '未选择的服务',
    selected_service: '已选择的服务',
    select_instance: '请选择实例',
    unselect_instance: '未选择的实例',
    selected_instance: '已选择的实例',
    edit_task: '编辑任务',
    copy_task: '复制任务',
    preVersion: '原始版本',
    fail_get_pre_version: '获取原始版本失败',
    fail_get_service: '获取服务失败'
  }
}