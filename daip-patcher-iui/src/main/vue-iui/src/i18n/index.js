/* Started by AICoder, pid:4d336t809eeec8914721093830da21113857e740 */
import { createI18n } from "vue-i18n";
import enLocale from './en';
import zhLocale from './zh';

const lang = window.top && window.top.getLanguage && window.top.getLanguage() || 'zh-CN';

const messages = {
  "zh-CN": zhLocale,
  "en-US": enLocale,
};

const i18n = createI18n({
  legacy: false,
  locale: lang,
  fallbackLocale: "en-US",
  messages,
});

export default i18n

/* Ended by AICoder, pid:4d336t809eeec8914721093830da21113857e740 */
