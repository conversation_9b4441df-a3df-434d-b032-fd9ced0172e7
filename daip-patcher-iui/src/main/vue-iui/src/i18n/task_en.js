export default {
  task: {
    create: 'create',
    modelName: 'Model Name',
    detail: 'Detail',
    query: {
      failed: 'Failed to get task data',
    },
    task: {
      selection: 'The current task is executing and cannot be deleted',
      okuTask: 'The current task has associated tasks and cannot be deleted',
      name: 'Task Name',
      desc: 'Description',
      operateObject: 'Operational clusters and hosts',
      cluster: 'Clusters',
      host: 'Host Number',
      createTime: 'Create Time',
      executeTime: 'Last Execute Time',
      startTime: 'Start Time',
      endTime: 'End Time',
      status: 'Status',
      costTime: 'Time Spent',
      process: 'Process',
      operation: 'Operation',
      allHost: 'All hosts in the cluster',
    },
    condition: {
      inputTaskName: 'Please input task name',
      selectCluster: 'Please select cluster',
      selectModel: 'Please select model',
      selectStatus: 'Please select status',
      startDate: 'Start date',
      endDate: 'End date',
      to: 'to',
      query: 'query',
      cleanCondition: 'Clean',
      lastWeek: 'Last Week',
      lastMonth: 'Last Month',
      lastThreeMonths: 'Last Three Months',
      lastSixMonths: 'Last Six Months',
      lastYear: 'Last Year',
    },
    status: {
      Initial: 'Initial',
      Running: 'Running',
      Success: 'Success',
      Failure: 'Failure',
      "Partial Failure": 'Partial Failure',
      Terminate: 'Terminate',
      TIMEOUT: 'Timeout'
    }
  }
}