export default {
  task: {
    create: '创建',
    modelName: '模型名',
    detail: '详情',
    query: {
      failed: '获取任务数据失败',
    },
    task: {
      selection: '当前任务正在执行，不能被删除',
      okuTask: '当前任务存在关联任务，不能被删除',
      name: '任务名称',
      desc: '任务描述',
      cluster: '集群',
      host: '主机数',
      createTime: '创建时间',
      executeTime: '上一次执行时间',
      startTime: '开始时间',
      endTime: '结束时间',
      status: '状态',
      costTime: '执行历时',
      process: '进度',
      operation: '操作',
      allHost: '集群所有主机',
    },
    condition: {
      inputTaskName: '请输入任务名',
      selectCluster: '请选择集群',
      selectModel: '请选择模型',
      selectStatus: '请选择状态',
      startDate: '开始日期',
      endDate: '结束日期',
      to: '至',
      query: '查询',
      cleanCondition: '清空条件',
      lastWeek: '最近一周',
      lastMonth: '最近一个月',
      lastThreeMonths: '最近三个月',
      lastSixMonths: '最近六个月',
      lastYear: '最近一年',
    },
    status: {
      Initial: '初始化',
      Running: '执行中',
      Success: '成功',
      Failure: '失败',
      "Partial Failure": '部分失败',
      Terminate: '暂停',
      TIMEOUT: '超时'
    }
  }
}