export default {
  create: {
    createTask: 'Create Task',
    host: 'Host',
    service: 'Service',
    instance: 'Instance',
    cluster: 'Cluster',
    taskModel: 'Task Model',
    name: 'Task Name',
    desc: 'Task Description',
    fail_create: 'Failed to create, host list is empty',
    host_list_is_empty: 'Host list is empty',
    fail_get_cluster_info: 'Failed to get cluster info',
    fail_get_host_info: 'Failed to get host info',
    fail_add_operator: 'Reached the limit of optional clusters',
    fail_get_task_model: 'Failed to get task model',
    replication: "'s replication",
    saveSuccess: 'Saved successfully!',
    saveFailure: 'Saved failed!',
    select_host: 'Please select host',
    unselect_host: 'Unselected host',
    selected_host: 'selected host',
    select_service: 'Please select service',
    unselect_service: 'Unselected service',
    selected_service: 'selected service',
    select_instance: 'Please select instance',
    unselect_instance: 'Unselected instance',
    selected_instance: 'selected instance',
    edit_task: 'Edit task',
    copy_task: 'Copy task',
    preVersion: 'Original version',
    fail_get_pre_version: 'Failed to get original version',
    fail_get_service: 'Failed to get service'
  }
}