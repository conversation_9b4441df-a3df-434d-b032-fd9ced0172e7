export default {
  okuCreate: {
    taskName: '任务名',
    cluster: '集群',
    host: '主机',
    service: '服务',
    serviceInstance: '服务实例',
    upgradeTarget: '升级目标',
    upgradeMethod: '升级方式',
    rollingUpgrade: '滚动升级',
    offlineUpgrade: '离线升级',
    selectHost: '选择主机',
    selectHostTargetVersion: '选择主机目标版本',
    ipAddress: 'IP地址',
    status: '状态',
    selectService: '选择服务实例',
    prVersion: '前一版本',
    currentVersion: '当前版本',
    targetVersion: '目标版本',
    selectServiceTargetVersion: '选择服务目标版本',
    instanceCount: '实例数',
    create: '创建任务',
    bigDataComponent: '大数据组件',
    nonBigDataComponent: '非大数据组件',
    hostStatus: {
      good: '良好',
      abnormal: '异常',
      disconnect: '离线',
      error: '不良'
    },
    message: {
      failed2ClusterInfo: '获取集群信息失败',
      fail2getVersionInfo: '获取版本信息失败',
      fail2getServiceInfo: '获取服务信息失败',
      fail2getPatchInfo: '获取补丁信息失败',
      fail2getHostInfo: '获取主机信息失败',
      selectAtLeastOne: '主机和服务至少选择一个',
      clusterEmpty: '集群不能为空',
      hostEmpty: '主机不能为空',
      serviceEmpty: '服务不能为空',
      serviceVersionEmpty: '服务目标版本不能为空',
      targetVersionEmpty: '目标版本为空',
      clusterNotEmpty: '集群不能为空',
      hostInfoEmpty: '主机信息为空',
      serviceInfoEmpty: '服务信息为空',
      taskNameNotEmpty: '任务名不能为空',
      taskIdNotEmpty: '任务ID不能为空',
      patchInfoEmpty: '补丁信息为空',
      taskNameReg: '任务名只能为英文、数字或下划线',
      taskConfirmServiceMsg: '待升级服务:{services}',
      taskConfirmRelationServicesMsg: '关联启停服务:{relationServices}',
    },
    preCheckMsg: '验证概览：共验证{total}台主机，{pass}台主机通过，{error}台主机验证错误',
    preCheckResult: '无法连接或用户名密码错误'
  }
}