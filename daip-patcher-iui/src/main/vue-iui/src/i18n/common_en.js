export default {
  common: {
    oku: 'One-key upgrade',
    service: 'ServiceInstance',
    host: 'Host',
    hostNum: 'Host Count',
    cluster: 'Cluster',
    hostName: 'Host Name',
    hostIp: 'Host IP',
    ipAddress: 'IP address',
    username: '<PERSON>rna<PERSON>',
    password: 'Password',
    port: 'Port',
    reason: 'Reason',
    operate: {
      success: 'Operation succeeded',
      confirm: 'Confirm operation?',
      fail: 'Operation failed'
    },
    input: {
      search: 'Search',
      selectPlaceholder: 'please choose',
      inputPlaceholder: 'please enter',
      input2searchPlaceholder: 'Please enter to keyword'
    },
    success: 'Success',
    failure: 'Failure',
    message: {
      success: 'success',
      tip: 'Tip',
      delete: {
        success: "Delete succeeded",
        failure: 'Delete failed',
        confirm: 'Confirm to delete?',
        cancel: 'Cancel delete'
      },
      add_cancel: "Cancel Add",
      add_name_success: "Add {name} Success"
    },
    authDialog:{
      login: 'Secondary auth',
      loginMethod: 'Login Method',
      passwordMethod: 'Password Method',
      codeMethod: 'Code Method',
      username: '<PERSON>rna<PERSON>',
      password: 'Password',
      verificationCode: 'Verification Code',
      sendVerificationCode: 'Send Verification Code',
      loginButton: 'Login',
      cancelButton: 'Cancel',
      pleaseEnterUsername: 'Please enter your username',
      pleaseEnterPassword: 'Please enter your password',
      pleaseEnterVerificationCode: 'Please enter the verification code',
      cancelAuthentication: 'Cancel authentication and end operation',
      sendVerificationCodeSuccess: 'Send verification code successfully',
      sendVerificationCodeFailed: 'Failed to send verification code',
      authenticationFailed: 'Authentication failed, end operation',
      afterResend: 'After {num}s retry'
    },
    taskMessage: {
      createSuccess: 'Create Successfully',
      createFail: 'Create Failed',
      modifySuccess: 'Modify Successfully',
      modifyFail: 'Modify Failed',
      rollbackSuccess: 'Rollback Successfully',
      rollbackFail: 'Rollback Failed',
      rollbackDuplicateFail: 'Rollback Failed:Duplicate Rollback Task',
      rollbackDisorderFail: 'Rollback Failed:Disorder Rollback Task',
      usernameEmpty: 'Username can not be empty',
      pwdEmpty: 'Password can not be blank',
      portEmpty: 'Port number cannot be empty',
      xssError: 'There may be xss vulnerability in the input, please modify it',
      deleteFail: 'Delete Failed',
      copyFail: 'Copy Failed',
      executeFail: 'Execute Failed',
      checkMsg: 'Execute Failed, please check the log!'
    },
    button: {
      disable: 'Disable',
      enable: 'Enable',
      create: 'Create',
      delete: 'Delete',
      add: 'Add',
      edit: 'Edit',
      view: 'View',
      search: 'Search',
      confirm: 'Confirm',
      cancel: 'Cancel',
      export: 'Export',
      copy: 'Copy',
      execute: 'Execute',
      retry: 'Retry',
      load: 'Load',
      save: 'Save',
      refresh: 'Refresh',
      query: 'Query',
      close: 'Close',
      import: 'Import',
      reverseElection: 'Reverse Election',
      allSelect: 'Select All',
      start: 'Start',
      stop: 'Stop',
      continue: 'Continue'
    },
    confirmBox: {
      suspendTask: 'Confirm suspend task: ',
      stopDescribe: 'The task can continue to execute after it is terminated!',
      deleteText: 'Are you sure you want to delete this data?',
      deleteDescribe: 'Deleted data cannot be recovered',
      copyText: 'Are you sure you want to copy this task?',
      confirm: 'Sure',
      cancel: 'Cancel',
      successMessage: 'Successfully Deleted',
      failureMessage: 'Failed to delete',
      cancelMessage: 'Undeleted',
      executeText: 'OK to execute the task: {taskName}?',
      resumeText: 'OK to continue execute the task: {taskName}?',
      retryText: 'OK to retry the task：{taskName}？',
      rollbackText: 'Are you sure to rollback: {taskName}?',
      executeDescribe: 'Once a task starts executing, it cannot be rolled back'
    },
    fail: {
      query: 'Failed to get data',
      execute: 'Execution failed'
    },
    auth: {
      user: 'Authenticate user:{userName}',
      fail: 'Authentication failed',
      password: 'Password',
      verify: 'Verify',
      verifyResult: 'Validation results',
      verifyPass: 'Verification successful',
      verifyFail: 'Verification failed',
      verifyError: 'Validation exception',
    },
    tab: {
      current_task: 'Operate Task',
      history: 'History',
      model_management: 'Model Management'
    },
    label: {
      goBack: 'GoBack',
      name: 'Name',
      file_name: 'FileName',
      file_size: 'FileSame',
      operation: 'Operation',
      "404_title": 'Sorry, the page you visited does not exist',
      "404_subTitle": 'The page you requested was not found. Please refresh the page or return to the previous page',
      "404_refresh": 'Refresh Page',
      status: 'Status',
      email: 'Email',
      operate: 'Operation',
      download: 'Download',
      detail: 'Detail',
      start_time: 'Start',
      end_time: 'End',
      plan_name: "Name",
      day: 'Day',
      month: 'Month',
      filter: 'Filter',
      all_checked: 'All',
      no_data: 'No Data',
      setting: 'Setting',
      template: 'Template',
      default: 'Default',
      true: 'TRUE',
      false: 'FALSE'
    },
    placeholder: {
      search: 'Please enter keyword search inspection plan',
      suite_search: 'Please enter keyword search inspection suite'
    },
    validate: {
      required: 'Required',
      file_exceed_num_limit: "The number of uploaded files exceeds the upper limit {num}.",
      file_exceed_size_limit: "The size of the uploaded file exceeds the upper limit ({fileSize} bytes).",
      file_exceed_size_lower_limit: "The file size is lower than the lower limit ({fileSize} bytes).",
      file_exceed_name_lengthandzh_limit: "The file name cannot contain any Chinese characters and must contain more than {num} characters.",
      please_complete_correct: 'Please enter completely and correctly',
      not_null: 'Can not be empty',
      start_in_en_and_zh: 'Can only start with Chinese and English, and can only contain Chinese, English, numbers, - and _',
      task_name_exist: 'TaskName has existed',
      task_name_check_fail: 'TaskName query fail',
      email_msg_info: 'Incorrect email address',
      character_len_info: 'String length must between {min} - {max}',
      integer_required: 'Integer required',
      invalid_ip_address_format: 'Invalid ip address fomat.',
      char_len_msg: "Characters cannot exceed {max} characters (Chinese is counted as two characters)",
      max_len_msg: 'Description limit {max} characters',
      query_wf_name_pattern: 'Can only contain Chinese, English, numbers, - and _'
    }
  }
}
