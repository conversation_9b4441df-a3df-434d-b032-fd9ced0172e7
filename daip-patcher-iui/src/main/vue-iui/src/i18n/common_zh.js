export default {
  common: {
    oku: '一键升级',
    service: '服务实例',
    host: '主机',
    hostNum: '主机数',
    cluster: '集群',
    hostName: '主机名',
    hostIp: '主机IP',
    ipAddress: 'IP地址',
    username: '用户名',
    password: '密码',
    port: '端口号',
    reason: '原因',
    operate: {
      success: '操作成功',
      confirm: '确认操作',
      fail: '操作失败'
    },
    message: {
      success: '成功',
      tip: '提示',
      delete: {
        success: "删除成功",
        failure: '删除失败',
        confirm: '确认删除？',
        cancel: "取消删除"
      },
      add_cancel: "取消新增",
      add_name_success: "新增{name}成功"
    },
    auth: {
      user: '验证用户:{userName}',
      fail: '认证失败',
      password: '密码',
      verify: '验证',
      verifyResult: '验证结果',
      verifyPass: '验证成功',
      verifyFail: '验证失败',
      verifyError: '验证出现异常',
    },
    success: '成功',
    failure: '失败',
    input: {
      search: '搜索',
      selectPlaceholder: '请选择',
      inputPlaceholder: '请输入',
      input2searchPlaceholder: '请输入关键字'
    },
    taskMessage: {
      createSuccess: '创建成功',
      createFail: '创建失败',
      modifySuccess: '修改成功',
      modifyFail: '修改失败',
      rollbackSuccess: '回退成功',
      rollbackFail: '回退失败',
      rollbackDuplicateFail: '回退失败:回退任务重复',
      rollbackDisorderFail: '回退失败:回退任务乱序',
      usernameEmpty: '用户名不能为空',
      pwdEmpty: '密码不能为空',
      portEmpty: '端口号不能为空',
      xssError: '输入可能存在xss漏洞，请修改',
      deleteFail: '删除失败',
      copyFail: '拷贝失败',
      copyNoPreStepsFail: '拷贝失败:无回退任务不能拷贝',
      executeFail: '操作失败',
      checkMsg: '操作失败，请查看日志！'
    },
    button: {
      disable: '禁用',
      enable: '启用',
      create: '创建',
      delete: '删除',
      add: '新增',
      edit: '编辑',
      view: '查看',
      search: '搜索',
      confirm: '确认',
      cancel: '取消',
      export: '导出',
      copy: '复制',
      execute: '执行',
      retry: '重试',
      load: '加载',
      save: '保存',
      refresh: '刷新',
      query: '查询',
      close: '关闭',
      import: '导入',
      reverseElection: '反选',
      allSelect: '全选',
      start: '启动',
      stop: '暂停',
      continue: '继续'
    },
    confirmBox: {
      suspendTask: '确定暂停任务：',
      stopDescribe: '任务暂停后可以继续执行！',
      deleteText: '确定删除数据？',
      deleteDescribe: '删除后的数据将无法恢复',
      copyText: '确定拷贝任务？',
      confirm: '确定',
      cancel: '取消',
      successMessage: '操作成功',
      failureMessage: '操作失败',
      cancelMessage: '已取消操作',
      executeText: '确定执行任务：{taskName}？',
      resumeText: '确定继续执行任务：{taskName}？',
      retryText: '确定重试任务：{taskName}？',
      rollbackText: '确定回退任务：{taskName}？',
      executeDescribe: '任务一旦开始执行则无法回退'
    },
    authDialog:{
      login: '二次授权',
      loginMethod: '登录方式',
      passwordMethod: '密码方式',
      codeMethod: '认证码方式',
      username: '用户名',
      password: '密码',
      verificationCode: '认证码',
      sendVerificationCode: '发送认证码',
      loginButton: '登录',
      cancelButton: '取消',
      pleaseEnterUsername: '请填写用户名',
      pleaseEnterPassword: '请输入密码',
      pleaseEnterVerificationCode: '请输入认证码',
      cancelAuthentication: '取消认证，结束操作',
      sendVerificationCodeSuccess: '发送验证码成功',
      sendVerificationCodeFailed: '发送验证码失败',
      authenticationFailed: '认证失败，结束操作',
      afterResend: '{num}s后重试'
    },
    fail: {
      query: '获取数据失败',
      execute: '执行失败'
    },
    tab: {
      current_task: '操作任务',
      history: '历史任务',
      model_management: '模型管理',
    },
    label: {
      goBack: '回退',
      name: '名称',
      file_name: '文件名',
      file_size: '文件大小',
      operation: '操作',
      "404_title": '抱歉，您访问的页面不存在',
      "404_subTitle": '没有找到您要的页面，请刷新页面或返回上一页',
      "404_refresh": '刷新页面',
      status: '状态',
      email: '邮箱',
      operate: '操作',
      download: '下载',
      detail: '查看详情',
      start_time: '开始时间',
      end_time: '结束时间',
      plan_name: "计划名称",
      day: '天',
      month: '月',
      filter: '筛选器',
      all_checked: '全选',
      no_data: '无数据',
      setting: '设置',
      template: '模板',
      default: '默认',
      true: '是',
      false: '否'
    },
    placeholder: {
      search: '输入关键字搜索计划名'
    },
    validate: {
      required: '必填项',
      file_exceed_num_limit: "上传文件数量超过上限{num} 个",
      file_exceed_size_limit: "上传文件大小超过上限{fileSize} 字节",
      file_exceed_size_lower_limit: "文件大小低于下限{fileSize} 字节",
      file_exceed_name_lengthandzh_limit: "文件名称不能含有中文，且长度超过上限{num} 字符",
      please_complete_correct: '请填写完整正确',
      not_null: '不能为空',
      start_in_en_and_zh: '只能以中文、英文开头，且只能包含中文、英文、数字、-和_',
      task_name_exist: '任务名已存在',
      task_name_check_fail: '查询任务名失败',
      email_msg_info: '请输入正确的邮箱地址',
      character_len_info: '长度在 {min} 到 {max} 个字符',
      integer_required: '请输入一个整数',
      invalid_ip_address_format: 'IP地址的格式不正确',
      char_len_msg: "字符不能超过{max}个字符（中文算两个字符）",
      max_len_msg: '描述限制{max}个字符',
      query_wf_name_pattern: '只能包含中文、英文、数字、-和_'
    }
  }
}
