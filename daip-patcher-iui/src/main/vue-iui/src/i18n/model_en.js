export default {
  model: {
    name: 'Model Name',
    desc: 'Description',
    service: 'Service',
    createTime: 'Create Time',
    version: 'Version',
    operation: 'Operation',
    default: "The task model is a system built-in model and cannot be deleted!",
    query: {
      failed: 'Failed to get model data',
    },
    import: {
      success: 'Import Success',
      failure: 'Failed To Import',
      process: 'Importing',
      sureUpdate: 'The file already exists, do you want to update it?',
      updateSuccess: 'update completed',
      updateFail: 'update failed',
      updateCancel: 'Cancel update',
      fileFormatError: 'The file format is incorrect, only files in json format are supported'
    },
    delete: {
      failed: 'Default model files cannot be deleted'
    }
  }
}