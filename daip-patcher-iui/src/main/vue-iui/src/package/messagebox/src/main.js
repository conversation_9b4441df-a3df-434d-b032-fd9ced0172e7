import { ElMessageBox } from 'vein-plus';

const CustomMsgBox = {}

CustomMsgBox.confirmBox = function (text = vm.$t('service.confirm_message'), type = 'warning', vm, dangerouslyUseHTMLString) {
  return ElMessageBox.confirm(vm.$t(text), vm.$t('common.message.tip'), {
    confirmButtonText: vm.$t('common.button.confirm'),
    cancelButtonText: vm.$t('common.button.cancel'),
    type: type,
    dangerouslyUseHTMLString: dangerouslyUseHTMLString === undefined || dangerouslyUseHTMLString == null ? false : dangerouslyUseHTMLString
  })
}

/* Started by AICoder, pid:d1c4f70ae98e4653a057a2cf340976b1 */
CustomMsgBox.stopOrDeleteBox = function (deletePost, vm, title, describe, errMes, then = () => {}, error) {
/* Ended by AICoder, pid:d1c4f70ae98e4653a057a2cf340976b1 */
    return ElMessageBox.confirm(title, vm.$t('service.confirm_tip'), {
        confirmButtonText: vm.$t('service.confirm_sure'),
        cancelButtonText: vm.$t('service.confirm_cancel'),
        confirmButtonClass:'confirmButton',
        cancelButtonClass: 'cancelButton',
        type: 'warning',
        describe: describe
        }
    ).then(() => {
        if (typeof deletePost == 'function') {
            deletePost().then((res => {
                if (res.status == 0) {
                    vm.$message({
                        type: 'success',
                        message: vm.$t('common.confirmBox.successMessage')
                    });
                } else {
                    vm.$ctConfirmBox(errMes, 'danger', vm, true)
                }
                if (then !== undefined && then !== null && typeof then == "function") {
                    then();
                }
            })).catch(err => {
                vm.$ctConfirmBox(errMes, 'danger', vm, true)
                if (error !== undefined && error !== null && typeof error === "function") {
                    error();
                }
            })
        } else {
            vm.$message({
                type: 'success',
                message: vm.$t('common.confirmBox.successMessage')
            });
        }
    }).catch(err => {
        vm.$message({
            type: 'info',
            message: vm.$t('common.confirmBox.cancelMessage')
        });
        throw err
    });
}

CustomMsgBox.deleteBox = function (deleteOpetate , loadData, vm, message = vm.$t('common.message.delete.confirm')) {
  CustomMsgBox.confirmBox(message, 'error', vm)
  .then(() => {
    if (typeof deleteOpetate == 'function') {
      deleteOpetate()
            .then(res => {
                if (res.status == '0') {
                  if (typeof loadData == 'function') {
                    loadData()
                  }
                    vm.$message({
                        type: 'success',
                        message: vm.$t('common.message.delete.success')
                    })
                } else {
                    vm.$message({
                        type: 'error',
                        message: vm.$t('common.message.delete.failure')
                    })
                    CustomMsgBox.confirmBox(res.data, 'error', vm)
                }
            }).catch(err => {
              CustomMsgBox.confirmBox(err, 'error', vm)
            })
    } 
  }).catch (() => {
    vm.$message({
      type: 'warning',
      message: vm.$t('common.message.delete.cancel')
  })
  })
}

/* Started by AICoder, pid:m2ed9vb2c9zab26147c60985d0ef1b0a17d2559e */
CustomMsgBox.stopOrDeleteBoxWithAuth = function (deletePost, { vm, oid, dop }, title, describe, errMes, then = () => {}, error) {
  const operationId = (event && event.currentTarget && event.currentTarget.getAttribute("operation")) || (event && event.target && event.target.getAttribute("operation")) || oid
  const doubleOperation = (event && event.currentTarget && event.currentTarget.getAttribute("doubleOperation")) || (event && event.target && event.target.getAttribute("doubleOperation")) || dop
  return ElMessageBox.confirm(title, vm.$t("service.confirm_tip"), {
    confirmButtonText: vm.$t("service.confirm_sure"),
    cancelButtonText: vm.$t("service.confirm_cancel"),
    confirmButtonClass: "confirmButton",
    cancelButtonClass: "cancelButton",
    type: "warning",
    describe: describe,
  })
    .then(() => {
      // 打开二次授权对话框
      return vm.$refs.authDialog.open(operationId, doubleOperation, (doubleToken) => {
        if (typeof deletePost === "function") {
          deletePost(doubleToken)
            .then((res) => {
              if (res.status == 0) {
                vm.$message({
                  type: "success",
                  message: vm.$t("common.confirmBox.successMessage"),
                })
              } else {
                vm.$ctConfirmBox(errMes, "danger", vm, true)
              }
              if (then !== undefined && then !== null && typeof then == "function") {
                then()
              }
            })
            .catch((err) => {
              vm.$ctConfirmBox(errMes, "danger", vm, true)
              if (error !== undefined && error !== null && typeof error === "function") {
                error()
              }
            })
        } else {
          vm.$message({
            type: "success",
            message: vm.$t("common.confirmBox.successMessage"),
          })
        }
      })
    })
    .catch((err) => {
      vm.$message({
        type: "info",
        message: vm.$t("common.confirmBox.cancelMessage"),
      })
      throw err
    })
}
/* Ended by AICoder, pid:m2ed9vb2c9zab26147c60985d0ef1b0a17d2559e */
export default CustomMsgBox
