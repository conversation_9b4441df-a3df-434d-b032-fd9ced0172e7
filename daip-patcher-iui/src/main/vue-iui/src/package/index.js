import CustomMsgBox from './messagebox'


const install = function (Vue, _opts = {}) {

  Vue.config.globalProperties.$ctConfirmBox = CustomMsgBox.confirmBox

  Vue.config.globalProperties.$ctStopOrDeleteBox = CustomMsgBox.stopOrDeleteBox
  Vue.config.globalProperties.$ctStopOrDeleteBoxWithAuth = CustomMsgBox.stopOrDeleteBoxWithAuth

  if (install.installed) return
  install.installed = true
}

// 判断是否是直接引入文件
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

export default {
  install
}