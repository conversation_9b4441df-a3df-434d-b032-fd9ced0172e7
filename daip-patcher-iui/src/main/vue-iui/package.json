{"name": "patcher-iui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode tcf", "build": "vite build --mode tcf", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-basic-ssl": "^1.1.0", "axios": "^1.6.7", "echarts": "^5.5.0", "es6-promise": "^4.2.0", "jquery": "^3.7.1", "uglifyjs-webpack-plugin": "^2.2.0", "vein-plus": "^1.1.3", "vue": "^3.4.21", "vue-dompurify-html": "^5.1.0", "vue-echarts": "^6.6.9", "vue-i18n": "^9.10.1", "vue-router": "^4.2.5", "vuex": "^4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "sass": "^1.26.7", "sass-embedded": "^1.85.1", "sass-loader": "^8.0.2", "vite": "^5.2.0"}}