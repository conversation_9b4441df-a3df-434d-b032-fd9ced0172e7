{"name": "patcher-iui", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "patcher-iui", "version": "0.0.0", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-basic-ssl": "^1.1.0", "axios": "^1.6.7", "echarts": "^5.5.0", "es6-promise": "^4.2.0", "jquery": "^3.7.1", "uglifyjs-webpack-plugin": "^2.2.0", "vein-plus": "^1.1.3", "vue": "^3.4.21", "vue-dompurify-html": "^5.1.0", "vue-echarts": "^6.6.9", "vue-i18n": "^9.10.1", "vue-router": "^4.2.5", "vuex": "^4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "sass": "^1.26.7", "sass-embedded": "^1.85.1", "sass-loader": "^8.0.2", "vite": "^5.2.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.25.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz", "integrity": "sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.25.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz", "integrity": "sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.26.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@babel/parser/-/parser-7.26.9.tgz", "integrity": "sha1-2eeL7m3ID579jyNJ3Pu82s4oD9U=", "license": "MIT", "dependencies": {"@babel/types": "^7.26.9"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/types": {"version": "7.26.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@babel/types/-/types-7.26.9.tgz", "integrity": "sha1-CLQ97HnujmgsKsYxwBC9ysVKIc4=", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bufbuild/protobuf": {"version": "2.2.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@bufbuild/protobuf/-/protobuf-2.2.3.tgz", "integrity": "sha1-nNE29raH5j6bUXs6VCEezpQol+4=", "devOptional": true, "license": "(Apache-2.0 AND BSD-3-<PERSON><PERSON>)"}, "node_modules/@ctrl/tinycolor": {"version": "3.6.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz", "integrity": "sha1-tsdaVqGUfMkW6gWHctZmosiTLzE=", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@element-plus/icons-vue": {"version": "2.3.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@element-plus/icons-vue/-/icons-vue-2.3.1.tgz", "integrity": "sha1-H2Na1f3VyF7ZNkgVJVcOgrWoMHo=", "license": "MIT", "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/@esbuild/aix-ppc64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz", "integrity": "sha1-xxhKMmUz/N8bjuBzPiHHE7l1V18=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-arm": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/android-arm/-/android-arm-0.21.5.tgz", "integrity": "sha1-mwQ4T7dxkm36bXrQQyTssqubLig=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-arm64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz", "integrity": "sha1-Cdm0NXeA2p6jp9+4M6Hx/0ObQFI=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-x64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/android-x64/-/android-x64-0.21.5.tgz", "integrity": "sha1-KZGOwtt1TO3LbBsE3ozWVHr2Rh4=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz", "integrity": "sha1-5JW1OWYOUWkPOSivUKdvsKbM/yo=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz", "integrity": "sha1-wTg4+lc3KDmr3dyR1xVCzuouHiI=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz", "integrity": "sha1-ZGuYmqIL+J/Qcd1dv61po1QuVQ4=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz", "integrity": "sha1-qmFc/ICvlU00WJBuOMoiwYz1wmE=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz", "integrity": "sha1-/G/RGorKVsH284lPK+oEefj2Jrk=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz", "integrity": "sha1-cKxvoU9ct+H3+Ie8/7aArQmSK1s=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz", "integrity": "sha1-MnH1Oz+T49CT1RjRZJ1taNNG7eI=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz", "integrity": "sha1-7WLgQjjFcCauqDHFoTC3PA+fJt8=", "cpu": ["loong64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz", "integrity": "sha1-55uOtIvzsQb63sGsgkD7l7TmTL4=", "cpu": ["mips64el"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz", "integrity": "sha1-XyIDhgoUO5kZ04PvdXNSH7FUw+Q=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz", "integrity": "sha1-B7yv2ZMi1a9i9hjLnmqbf0u4Jdw=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz", "integrity": "sha1-t8z2hnUdaj5EuGJ6uryL4+9i2N4=", "cpu": ["s390x"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-x64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz", "integrity": "sha1-bY8Mdo4HDmQwmvgAS7lOaKsrs7A=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz", "integrity": "sha1-u+Qw9g03jsuI3sshnGAmZzh6YEc=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz", "integrity": "sha1-mdHPKTcnlWDSEEgh9czOIgyyr3A=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz", "integrity": "sha1-CHQVEsENUpVmurqDe0/gUsjzSHs=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz", "integrity": "sha1-Z1tzhTmEESQHNQFhRKsumaYPx10=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz", "integrity": "sha1-G/w86YqmypoJaeTSr3IUTFnBGTs=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-x64": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz", "integrity": "sha1-rK01HVgtFXuxRVNdsqb/U91RS1w=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@floating-ui/core": {"version": "1.6.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@floating-ui/core/-/core-1.6.9.tgz", "integrity": "sha1-ZNHaJRQzAZ2voJHemyiG/zXsFOY=", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/dom": {"version": "1.6.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@floating-ui/dom/-/dom-1.6.13.tgz", "integrity": "sha1-qKk4UyrqJ6lRIewW5meny+jFnjQ=", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.6.0", "@floating-ui/utils": "^0.2.9"}}, "node_modules/@floating-ui/utils": {"version": "0.2.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@floating-ui/utils/-/utils-0.2.9.tgz", "integrity": "sha1-UN6jYWvIGR+44RIoO0nq/wPnhCk=", "license": "MIT"}, "node_modules/@intlify/core-base": {"version": "9.14.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@intlify/core-base/-/core-base-9.14.3.tgz", "integrity": "sha1-BdjGu5u/rJuL8bFifQlv0jNCcXs=", "license": "MIT", "dependencies": {"@intlify/message-compiler": "9.14.3", "@intlify/shared": "9.14.3"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/message-compiler": {"version": "9.14.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@intlify/message-compiler/-/message-compiler-9.14.3.tgz", "integrity": "sha1-+iTrgNZ0uzv7RDfH6keXjfc/Y8s=", "license": "MIT", "dependencies": {"@intlify/shared": "9.14.3", "source-map-js": "^1.0.2"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/shared": {"version": "9.14.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@intlify/shared/-/shared-9.14.3.tgz", "integrity": "sha1-7Q56GjSJckxOwpbdSQuc+EJAu7s=", "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "license": "MIT", "optional": true, "peer": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=", "license": "MIT", "optional": true, "peer": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@jridgewell/source-map/-/source-map-0.3.6.tgz", "integrity": "sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@parcel/watcher": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher/-/watcher-2.5.1.tgz", "integrity": "sha1-NCUHqc+q8XJHmogjCd7x6ZH7EgA=", "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1"}}, "node_modules/@parcel/watcher-android-arm64": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-android-arm64/-/watcher-android-arm64-2.5.1.tgz", "integrity": "sha1-UH+DbX4gQveYx9B60Zw1RvmEisE=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-darwin-arm64": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-darwin-arm64/-/watcher-darwin-arm64-2.5.1.tgz", "integrity": "sha1-PSbc443mWQ73nEfsLFV5PAatT2c=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-darwin-x64": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-darwin-x64/-/watcher-darwin-x64-2.5.1.tgz", "integrity": "sha1-mfOvOGkGnM93Tk3fzPfmT9IxHvg=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-freebsd-x64": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.5.1.tgz", "integrity": "sha1-FNaFd0Gp9R3+UdWwi3yK/bxzrZs=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-arm-glibc": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.5.1.tgz", "integrity": "sha1-Q8MkbWiSOB20c7tPZjIprSC2CaE=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-arm-musl": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-linux-arm-musl/-/watcher-linux-arm-musl-2.5.1.tgz", "integrity": "sha1-ZjdQ9wkLtieNIhDeZD64o/eA0I4=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-arm64-glibc": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.5.1.tgz", "integrity": "sha1-umDh9Wl39+R81+Ma1l0V/cvQfjA=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-arm64-musl": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.5.1.tgz", "integrity": "sha1-9/vN/y8ExSb5bqwB+XQZpqmYVdI=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-x64-glibc": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.5.1.tgz", "integrity": "sha1-TS6g9jPrGRfYPUgzks5hgbapLk4=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-linux-x64-musl": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.5.1.tgz", "integrity": "sha1-J3s0awXbVPVWVzAd13vfmdY2Bu4=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-win32-arm64": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.5.1.tgz", "integrity": "sha1-fp4ComeE1HUD3h0Q6Oq2zOtSQkM=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-win32-ia32": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.5.1.tgz", "integrity": "sha1-LQ+U+lmoc83FhL9/ax3GKN35duY=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher-win32-x64": {"version": "2.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.5.1.tgz", "integrity": "sha1-rlJpMllmS6byIo+mHX7kS2TqCUc=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@parcel/watcher/node_modules/braces": {"version": "3.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/braces/-/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "license": "MIT", "optional": true, "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/@parcel/watcher/node_modules/fill-range": {"version": "7.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "license": "MIT", "optional": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@parcel/watcher/node_modules/is-number": {"version": "7.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "license": "MIT", "optional": true, "engines": {"node": ">=0.12.0"}}, "node_modules/@parcel/watcher/node_modules/micromatch": {"version": "4.0.8", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "license": "MIT", "optional": true, "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/@parcel/watcher/node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "license": "MIT", "optional": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/@popperjs/core": {"name": "@sxzz/popperjs-es", "version": "2.11.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@sxzz/popperjs-es/-/popperjs-es-2.11.7.tgz", "integrity": "sha1-p/aeNmXT2psRX55xZx2uG5fhNnE=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.9.tgz", "integrity": "sha1-ZhpFpHCccOWeWW7HjaqcuLjSdgQ=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.9.tgz", "integrity": "sha1-Eo/o3VENiAz5i0y2x63TJoFaDEs=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.34.9.tgz", "integrity": "sha1-NjRnvEn9Cx4XB1eYrI6a0eHilTU=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.34.9.tgz", "integrity": "sha1-wv49hf/+R/DtDwdrNWOtoiyK8Zw=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.34.9.tgz", "integrity": "sha1-2VvY9uqvgpeBFE/IvS1dcdn2qfU=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.34.9.tgz", "integrity": "sha1-w1dsYBFlbklm3tKfBR7exja0RWQ=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.34.9.tgz", "integrity": "sha1-SMh9De5PjclZGkFnF/kbSonXfj0=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.34.9.tgz", "integrity": "sha1-9MTnwDp3Z/Llqp0MXPv1wPWfLUE=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.34.9.tgz", "integrity": "sha1-EBXJ0HqZAFAl0TuGIrdgACnQtS8=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.34.9.tgz", "integrity": "sha1-j4letVd3SPx1ryG+rjJDlibgoUw=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.34.9.tgz", "integrity": "sha1-yc1du9xrPKTb7rAzdJjPMZSQBKA=", "cpu": ["loong64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.34.9.tgz", "integrity": "sha1-frtbREH6oXhDohD30Fg6IMk7QOQ=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.34.9.tgz", "integrity": "sha1-EPXXNJ+9L+ePnjbsyQqrMVRDXI0=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.34.9.tgz", "integrity": "sha1-GWNH0vogWTqwnQt+JYn7ab33QsY=", "cpu": ["s390x"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.34.9.tgz", "integrity": "sha1-cZPL2NEoISuKzaN+AbOdnpYlnvg=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.34.9.tgz", "integrity": "sha1-KaaGcnjKBCC4kVdM+rmOytcMWdE=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.34.9.tgz", "integrity": "sha1-iUJ9ysDI46bTKxOgOilqJ10N6ak=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.34.9.tgz", "integrity": "sha1-7LlxG6K20r9u5RJlq+BXq5CRPes=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.34.9.tgz", "integrity": "sha1-GXOHGFCFaucrxniusGarlSMw6SM=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@types/estree": {"version": "1.0.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@types/estree/-/estree-1.0.6.tgz", "integrity": "sha1-Yo7/7q4gZKG055946B2Ht+X8e1A=", "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "dev": true, "license": "MIT"}, "node_modules/@types/lodash": {"version": "4.17.16", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@types/lodash/-/lodash-4.17.16.tgz", "integrity": "sha1-lK54+rSjjXMIbpYtC2XDDYFr+wo=", "license": "MIT"}, "node_modules/@types/lodash-es": {"version": "4.17.12", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@types/lodash-es/-/lodash-es-4.17.12.tgz", "integrity": "sha1-ZfbR5fgFOap8+/yWLeXe8M9PNBs=", "license": "MIT", "dependencies": {"@types/lodash": "*"}}, "node_modules/@types/trusted-types": {"version": "2.0.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@types/trusted-types/-/trusted-types-2.0.7.tgz", "integrity": "sha1-usywepcLkXB986PoumiWxX6tLRE=", "license": "MIT", "optional": true}, "node_modules/@types/web-bluetooth": {"version": "0.0.16", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz", "integrity": "sha1-HRKHOo5JVnNx8qdf4+f37cpmYtg=", "license": "MIT"}, "node_modules/@vein-plus/icons-vue": {"version": "1.1.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vein-plus/icons-vue/-/@vein-plus/icons-vue-1.1.3.tgz", "integrity": "sha1-fnFZuNS2dgEDKZcOCWEw01vh7Fc=", "license": "MIT", "peerDependencies": {"vue": ">=3.2.0"}}, "node_modules/@vitejs/plugin-basic-ssl": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vitejs/plugin-basic-ssl/-/plugin-basic-ssl-1.2.0.tgz", "integrity": "sha1-lJD+FbiDM1GYL74JY5h/afQPUBk=", "license": "MIT", "engines": {"node": ">=14.21.3"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0"}}, "node_modules/@vitejs/plugin-vue": {"version": "5.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vitejs/plugin-vue/-/plugin-vue-5.2.1.tgz", "integrity": "sha1-0UkfZ47jr4mfeuV9nCHcUqZccTM=", "dev": true, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0", "vue": "^3.2.25"}}, "node_modules/@vue/compiler-core": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/compiler-core/-/compiler-core-3.5.13.tgz", "integrity": "sha1-sK5sQ0f2DAPoSaBdNOW/dHyb2gU=", "license": "MIT", "dependencies": {"@babel/parser": "^7.25.3", "@vue/shared": "3.5.13", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.0"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/compiler-dom/-/compiler-dom-3.5.13.tgz", "integrity": "sha1-uxuHWNvFQrNljdqXO5ihyTEailg=", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.13", "@vue/shared": "3.5.13"}}, "node_modules/@vue/compiler-sfc": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/compiler-sfc/-/compiler-sfc-3.5.13.tgz", "integrity": "sha1-Rh+L00O1wG+sQYnE/vivMt6oK0Y=", "license": "MIT", "dependencies": {"@babel/parser": "^7.25.3", "@vue/compiler-core": "3.5.13", "@vue/compiler-dom": "3.5.13", "@vue/compiler-ssr": "3.5.13", "@vue/shared": "3.5.13", "estree-walker": "^2.0.2", "magic-string": "^0.30.11", "postcss": "^8.4.48", "source-map-js": "^1.2.0"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/compiler-ssr/-/compiler-ssr-3.5.13.tgz", "integrity": "sha1-53GtzKbT0AD5GkJ3yXKpltB/Q7o=", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.13", "@vue/shared": "3.5.13"}}, "node_modules/@vue/devtools-api": {"version": "6.6.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/devtools-api/-/devtools-api-6.6.4.tgz", "integrity": "sha1-y+l/4BYrNl7cHbqA4XP5BJJTU0M=", "license": "MIT"}, "node_modules/@vue/reactivity": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/reactivity/-/reactivity-3.5.13.tgz", "integrity": "sha1-tB/yu4ZeCTiZoiIZ9bJfl7b+FV8=", "license": "MIT", "dependencies": {"@vue/shared": "3.5.13"}}, "node_modules/@vue/runtime-core": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/runtime-core/-/runtime-core-3.5.13.tgz", "integrity": "sha1-H6+kvwuXrw692dv+mM1jDaNjpFU=", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.13", "@vue/shared": "3.5.13"}}, "node_modules/@vue/runtime-dom": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/runtime-dom/-/runtime-dom-3.5.13.tgz", "integrity": "sha1-YQ/Hld6SRjAOiuiGWTDVNOEkYhU=", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.13", "@vue/runtime-core": "3.5.13", "@vue/shared": "3.5.13", "csstype": "^3.1.3"}}, "node_modules/@vue/server-renderer": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/server-renderer/-/server-renderer-3.5.13.tgz", "integrity": "sha1-Qp6tYu5R3niWRsIu/pCOSJqtRvc=", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.5.13", "@vue/shared": "3.5.13"}, "peerDependencies": {"vue": "3.5.13"}}, "node_modules/@vue/shared": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vue/shared/-/shared-3.5.13.tgz", "integrity": "sha1-h7MJpjecIrkm5paJMjeCb2Qzm28=", "license": "MIT"}, "node_modules/@vueuse/core": {"version": "9.13.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vueuse/core/-/core-9.13.0.tgz", "integrity": "sha1-L2nmbRkFweTuvCSaAXWc+I6gDPQ=", "license": "MIT", "dependencies": {"@types/web-bluetooth": "^0.0.16", "@vueuse/metadata": "9.13.0", "@vueuse/shared": "9.13.0", "vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/core/node_modules/vue-demi": {"version": "0.14.10", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vue-demi/-/vue-demi-0.14.10.tgz", "integrity": "sha1-r8eN49b54Rv3jFXoUQ7hKBRSLwQ=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vueuse/metadata": {"version": "9.13.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vueuse/metadata/-/metadata-9.13.0.tgz", "integrity": "sha1-vCWmza0bGpPDbOMBkRJNplIFOf8=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared": {"version": "9.13.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@vueuse/shared/-/shared-9.13.0.tgz", "integrity": "sha1-CJ/0zE4uekAV5XqPMuSznQljU7k=", "license": "MIT", "dependencies": {"vue-demi": "*"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/@vueuse/shared/node_modules/vue-demi": {"version": "0.14.10", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vue-demi/-/vue-demi-0.14.10.tgz", "integrity": "sha1-r8eN49b54Rv3jFXoUQ7hKBRSLwQ=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@webassemblyjs/ast": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/ast/-/ast-1.9.0.tgz", "integrity": "sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.9.0.tgz", "integrity": "sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=", "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.0.tgz", "integrity": "sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=", "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/helper-buffer/-/helper-buffer-1.9.0.tgz", "integrity": "sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=", "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-code-frame": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.9.0.tgz", "integrity": "sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/wast-printer": "1.9.0"}}, "node_modules/@webassemblyjs/helper-fsm": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz", "integrity": "sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=", "license": "ISC", "peer": true}, "node_modules/@webassemblyjs/helper-module-context": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/helper-module-context/-/helper-module-context-1.9.0.tgz", "integrity": "sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.9.0"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.9.0.tgz", "integrity": "sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=", "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.0.tgz", "integrity": "sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/ieee754/-/ieee754-1.9.0.tgz", "integrity": "sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=", "license": "MIT", "peer": true, "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/leb128/-/leb128-1.9.0.tgz", "integrity": "sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=", "license": "MIT", "peer": true, "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/utf8/-/utf8-1.9.0.tgz", "integrity": "sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=", "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz", "integrity": "sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/helper-wasm-section": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-opt": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "@webassemblyjs/wast-printer": "1.9.0"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.0.tgz", "integrity": "sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.0.tgz", "integrity": "sha1-IhEYHlsxMmRDzIES658LkChyGmE=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz", "integrity": "sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "node_modules/@webassemblyjs/wast-parser": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/wast-parser/-/wast-parser-1.9.0.tgz", "integrity": "sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/floating-point-hex-parser": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-code-frame": "1.9.0", "@webassemblyjs/helper-fsm": "1.9.0", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.9.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz", "integrity": "sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0", "@xtuc/long": "4.2.2"}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "integrity": "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@xtuc/long": {"version": "4.2.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/@xtuc/long/-/long-4.2.2.tgz", "integrity": "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=", "license": "Apache-2.0", "peer": true}, "node_modules/acorn": {"version": "6.4.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/acorn/-/acorn-6.4.2.tgz", "integrity": "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=", "license": "MIT", "peer": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-errors": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/ajv-errors/-/ajv-errors-1.0.1.tgz", "integrity": "sha1-81mGrOuRr63sQQL72FAUlQzvpk0=", "license": "MIT", "peerDependencies": {"ajv": ">=5.0.0"}}, "node_modules/ajv-keywords": {"version": "3.5.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "integrity": "sha1-MfKdpatuANHC0yms97WSlhTVAU0=", "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "license": "ISC", "optional": true, "peer": true, "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/aproba": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/aproba/-/aproba-1.2.0.tgz", "integrity": "sha1-aALmJk79GMeQobDVF/DyYnvyyUo=", "license": "ISC"}, "node_modules/arr-diff": {"version": "4.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/arr-diff/-/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/arr-flatten": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/arr-union": {"version": "3.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/arr-union/-/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-unique": {"version": "0.3.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/array-unique/-/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/asn1.js": {"version": "4.10.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/asn1.js/-/asn1.js-4.10.1.tgz", "integrity": "sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=", "peer": true, "dependencies": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "node_modules/asn1.js/node_modules/bn.js": {"version": "4.12.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/bn.js/-/bn.js-4.12.1.tgz", "integrity": "sha1-IVdB/jydui1+EsAB0M/brkOXW6c=", "license": "MIT", "peer": true}, "node_modules/assert": {"version": "1.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/assert/-/assert-1.5.1.tgz", "integrity": "sha1-A4qySOT/B457wkhbpuY4hGbHj3Y=", "license": "MIT", "peer": true, "dependencies": {"object.assign": "^4.1.4", "util": "^0.10.4"}}, "node_modules/assert/node_modules/inherits": {"version": "2.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "license": "ISC", "peer": true}, "node_modules/assert/node_modules/util": {"version": "0.10.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/util/-/util-0.10.4.tgz", "integrity": "sha1-OqASW/5mikZy3liFfTrOJ+y3aQE=", "license": "MIT", "peer": true, "dependencies": {"inherits": "2.0.3"}}, "node_modules/assign-symbols": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/assign-symbols/-/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/async-each": {"version": "1.0.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/async-each/-/async-each-1.0.6.tgz", "integrity": "sha1-UvHZQDgYwXm3Vh4RpdG3frIWDnc=", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "optional": true, "peer": true}, "node_modules/async-validator": {"version": "4.2.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/async-validator/-/async-validator-4.2.5.tgz", "integrity": "sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk=", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "license": "MIT"}, "node_modules/atob": {"version": "2.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/atob/-/atob-2.1.2.tgz", "integrity": "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=", "license": "(MIT OR Apache-2.0)", "peer": true, "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/axios": {"version": "1.8.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/axios/-/axios-1.8.2.tgz", "integrity": "sha1-+r4G4kHf6DBx1O37yqexw6QPeXk=", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "license": "MIT"}, "node_modules/base": {"version": "0.11.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/base/-/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "license": "MIT", "peer": true, "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/define-property": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "license": "MIT", "peer": true, "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "peer": true}, "node_modules/big.js": {"version": "5.2.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/big.js/-/big.js-5.2.2.tgz", "integrity": "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/binary-extensions/-/binary-extensions-2.3.0.tgz", "integrity": "sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=", "license": "MIT", "optional": true, "peer": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bindings": {"version": "1.5.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/bindings/-/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"file-uri-to-path": "1.0.0"}}, "node_modules/bluebird": {"version": "3.7.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/bluebird/-/bluebird-3.7.2.tgz", "integrity": "sha1-nyKcFb4nJFT/qXOs4NvueaGww28=", "license": "MIT"}, "node_modules/bn.js": {"version": "5.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/bn.js/-/bn.js-5.2.1.tgz", "integrity": "sha1-C8UnpqDRjQqo1bBTjOSnfcz6e3A=", "license": "MIT", "peer": true}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "2.3.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/braces/-/braces-2.3.2.tgz", "integrity": "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=", "license": "MIT", "peer": true, "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/braces/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "license": "MIT", "peer": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/braces/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/brorand": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/brorand/-/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "license": "MIT", "peer": true}, "node_modules/browserify-aes": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/browserify-aes/-/browserify-aes-1.2.0.tgz", "integrity": "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=", "license": "MIT", "peer": true, "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/browserify-cipher": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "integrity": "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=", "license": "MIT", "peer": true, "dependencies": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "node_modules/browserify-des": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/browserify-des/-/browserify-des-1.0.2.tgz", "integrity": "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=", "license": "MIT", "peer": true, "dependencies": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/browserify-rsa": {"version": "4.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/browserify-rsa/-/browserify-rsa-4.1.1.tgz", "integrity": "sha1-BuUwkH/ilJ3CH8PC4jAuELFDcjg=", "license": "MIT", "peer": true, "dependencies": {"bn.js": "^5.2.1", "randombytes": "^2.1.0", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/browserify-rsa/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "peer": true}, "node_modules/browserify-sign": {"version": "4.2.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/browserify-sign/-/browserify-sign-4.2.3.tgz", "integrity": "sha1-ev5MAex+5ZqJpVikt1vYWuYtQgg=", "license": "ISC", "peer": true, "dependencies": {"bn.js": "^5.2.1", "browserify-rsa": "^4.1.0", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "elliptic": "^6.5.5", "hash-base": "~3.0", "inherits": "^2.0.4", "parse-asn1": "^5.1.7", "readable-stream": "^2.3.8", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.12"}}, "node_modules/browserify-sign/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "peer": true}, "node_modules/browserify-zlib": {"version": "0.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "integrity": "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=", "license": "MIT", "peer": true, "dependencies": {"pako": "~1.0.5"}}, "node_modules/buffer": {"version": "4.9.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/buffer/-/buffer-4.9.2.tgz", "integrity": "sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=", "license": "MIT", "peer": true, "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "node_modules/buffer-builder": {"version": "0.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/buffer-builder/-/buffer-builder-0.2.0.tgz", "integrity": "sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8=", "devOptional": true, "license": "MIT/X11"}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=", "license": "MIT"}, "node_modules/buffer-xor": {"version": "1.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/buffer-xor/-/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "license": "MIT", "peer": true}, "node_modules/builtin-status-codes": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "license": "MIT", "peer": true}, "node_modules/cacache": {"version": "12.0.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/cacache/-/cacache-12.0.4.tgz", "integrity": "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=", "license": "ISC", "dependencies": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}}, "node_modules/cache-base": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/cache-base/-/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "license": "MIT", "peer": true, "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/call-bind/-/call-bind-1.0.8.tgz", "integrity": "sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=", "license": "MIT", "peer": true, "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "license": "MIT", "peer": true, "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/chokidar": {"version": "3.6.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/chokidar/-/chokidar-3.6.0.tgz", "integrity": "sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/braces": {"version": "3.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/braces/-/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/chokidar/node_modules/fill-range": {"version": "7.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/chokidar/node_modules/is-number": {"version": "7.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "license": "MIT", "optional": true, "peer": true, "engines": {"node": ">=0.12.0"}}, "node_modules/chokidar/node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/chownr": {"version": "1.1.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/chownr/-/chownr-1.1.4.tgz", "integrity": "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=", "license": "ISC"}, "node_modules/chrome-trace-event": {"version": "1.0.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz", "integrity": "sha1-Bb/9f/koRlCTMUcIyTvfqb0fD1s=", "license": "MIT", "peer": true, "engines": {"node": ">=6.0"}}, "node_modules/cipher-base": {"version": "1.0.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/cipher-base/-/cipher-base-1.0.6.tgz", "integrity": "sha1-j+ZyQ30BzWxFYa9TNODMUP8ZVfc=", "license": "MIT", "peer": true, "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/cipher-base/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "peer": true}, "node_modules/class-utils": {"version": "0.3.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/class-utils/-/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "license": "MIT", "peer": true, "dependencies": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/define-property": {"version": "0.2.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "license": "MIT", "peer": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-descriptor": {"version": "0.1.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-descriptor/-/is-descriptor-0.1.7.tgz", "integrity": "sha1-JyfrYf14nc1b3w7UVp9VHS/jvjM=", "license": "MIT", "peer": true, "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/clone-deep": {"version": "4.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/clone-deep/-/clone-deep-4.0.1.tgz", "integrity": "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/collection-visit": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/collection-visit/-/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "license": "MIT", "peer": true, "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/colorjs.io": {"version": "0.5.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/colorjs.io/-/colorjs.io-0.5.2.tgz", "integrity": "sha1-Y7IBObAHWR68M1mTK++EYo6z/O8=", "devOptional": true, "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/commander/-/commander-2.20.3.tgz", "integrity": "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=", "license": "MIT", "peer": true}, "node_modules/commondir": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/commondir/-/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "license": "MIT"}, "node_modules/component-emitter": {"version": "1.3.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/component-emitter/-/component-emitter-1.3.1.tgz", "integrity": "sha1-7x1XlvfZPxNe5vtoQ0CyZAPJfRc=", "license": "MIT", "peer": true, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "license": "MIT"}, "node_modules/concat-stream": {"version": "1.6.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/console-browserify": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/console-browserify/-/console-browserify-1.2.0.tgz", "integrity": "sha1-ZwY871fOts9Jk6KrOlWECujEkzY=", "peer": true}, "node_modules/constants-browserify": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/constants-browserify/-/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "license": "MIT", "peer": true}, "node_modules/copy-concurrently": {"version": "1.0.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/copy-concurrently/-/copy-concurrently-1.0.5.tgz", "integrity": "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "node_modules/copy-descriptor": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=", "license": "MIT"}, "node_modules/create-ecdh": {"version": "4.0.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/create-ecdh/-/create-ecdh-4.0.4.tgz", "integrity": "sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=", "license": "MIT", "peer": true, "dependencies": {"bn.js": "^4.1.0", "elliptic": "^6.5.3"}}, "node_modules/create-ecdh/node_modules/bn.js": {"version": "4.12.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/bn.js/-/bn.js-4.12.1.tgz", "integrity": "sha1-IVdB/jydui1+EsAB0M/brkOXW6c=", "license": "MIT", "peer": true}, "node_modules/create-hash": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/create-hash/-/create-hash-1.2.0.tgz", "integrity": "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=", "license": "MIT", "peer": true, "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "node_modules/create-hmac": {"version": "1.1.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/create-hmac/-/create-hmac-1.1.7.tgz", "integrity": "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=", "license": "MIT", "peer": true, "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "node_modules/crypto-browserify": {"version": "3.12.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/crypto-browserify/-/crypto-browserify-3.12.1.tgz", "integrity": "sha1-u4khvsmsyBYzN5qo9S1psLaeDaw=", "license": "MIT", "peer": true, "dependencies": {"browserify-cipher": "^1.0.1", "browserify-sign": "^4.2.3", "create-ecdh": "^4.0.4", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "diffie-hellman": "^5.0.3", "hash-base": "~3.0.4", "inherits": "^2.0.4", "pbkdf2": "^3.1.2", "public-encrypt": "^4.0.3", "randombytes": "^2.1.0", "randomfill": "^1.0.4"}, "engines": {"node": ">= 0.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/csstype/-/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=", "license": "MIT"}, "node_modules/cyclist": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/cyclist/-/cyclist-1.0.2.tgz", "integrity": "sha1-ZztfIzvzTY5gK5SUKfgXHZEhvqM=", "license": "MIT"}, "node_modules/dayjs": {"version": "1.11.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/dayjs/-/dayjs-1.11.13.tgz", "integrity": "sha1-kkMLATkFXD67YBUKoT6GCktaNmw=", "license": "MIT"}, "node_modules/debug": {"version": "2.6.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "peer": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/decode-uri-component": {"version": "0.2.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/decode-uri-component/-/decode-uri-component-0.2.2.tgz", "integrity": "sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "license": "MIT", "peer": true, "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-properties/-/define-properties-1.2.1.tgz", "integrity": "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=", "license": "MIT", "peer": true, "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-property": {"version": "2.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-property/-/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "license": "MIT", "peer": true, "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/des.js": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/des.js/-/des.js-1.1.0.tgz", "integrity": "sha1-HTf1dm87v/Tuljjocah2jBc7gdo=", "license": "MIT", "peer": true, "dependencies": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "node_modules/detect-libc": {"version": "1.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/detect-libc/-/detect-libc-1.0.3.tgz", "integrity": "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=", "license": "Apache-2.0", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/diffie-hellman": {"version": "5.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "integrity": "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=", "license": "MIT", "peer": true, "dependencies": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}}, "node_modules/diffie-hellman/node_modules/bn.js": {"version": "4.12.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/bn.js/-/bn.js-4.12.1.tgz", "integrity": "sha1-IVdB/jydui1+EsAB0M/brkOXW6c=", "license": "MIT", "peer": true}, "node_modules/domain-browser": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/domain-browser/-/domain-browser-1.2.0.tgz", "integrity": "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=", "license": "MIT", "peer": true, "engines": {"node": ">=0.4", "npm": ">=1.2"}}, "node_modules/dompurify": {"version": "3.2.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/dompurify/-/dompurify-3.2.4.tgz", "integrity": "sha1-r1paEUB1JEMUVs8Yg2xV0TRBzY4=", "license": "(MPL-2.0 OR Apache-2.0)", "optionalDependencies": {"@types/trusted-types": "^2.0.7"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/duplexify": {"version": "3.7.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/duplexify/-/duplexify-3.7.1.tgz", "integrity": "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=", "license": "MIT", "dependencies": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "node_modules/echarts": {"version": "5.6.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/echarts/-/echarts-5.6.0.tgz", "integrity": "sha1-I3eHTcqftQ8QQFHDVTVEdS2jydY=", "license": "Apache-2.0", "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}}, "node_modules/elliptic": {"version": "6.6.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/elliptic/-/elliptic-6.6.1.tgz", "integrity": "sha1-O4/7AmcL9p44LH9lv1JMl8VAXAY=", "license": "MIT", "peer": true, "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/elliptic/node_modules/bn.js": {"version": "4.12.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/bn.js/-/bn.js-4.12.1.tgz", "integrity": "sha1-IVdB/jydui1+EsAB0M/brkOXW6c=", "license": "MIT", "peer": true}, "node_modules/emojis-list": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/emojis-list/-/emojis-list-3.0.0.tgz", "integrity": "sha1-VXBmIEatKeLpFucariYKvf9Pang=", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/enhanced-resolve": {"version": "4.5.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz", "integrity": "sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=", "peer": true, "dependencies": {"graceful-fs": "^4.1.2", "memory-fs": "^0.5.0", "tapable": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/enhanced-resolve/node_modules/memory-fs": {"version": "0.5.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/memory-fs/-/memory-fs-0.5.0.tgz", "integrity": "sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=", "license": "MIT", "peer": true, "dependencies": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "node_modules/entities": {"version": "4.5.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/entities/-/entities-4.5.0.tgz", "integrity": "sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/errno": {"version": "0.1.8", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/errno/-/errno-0.1.8.tgz", "integrity": "sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=", "license": "MIT", "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es6-promise": {"version": "4.2.8", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/es6-promise/-/es6-promise-4.2.8.tgz", "integrity": "sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo=", "license": "MIT"}, "node_modules/esbuild": {"version": "0.21.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/esbuild/-/esbuild-0.21.5.tgz", "integrity": "sha1-nKMBsSCSKVm3ZjYNisgw2g0CmX0=", "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.21.5", "@esbuild/android-arm": "0.21.5", "@esbuild/android-arm64": "0.21.5", "@esbuild/android-x64": "0.21.5", "@esbuild/darwin-arm64": "0.21.5", "@esbuild/darwin-x64": "0.21.5", "@esbuild/freebsd-arm64": "0.21.5", "@esbuild/freebsd-x64": "0.21.5", "@esbuild/linux-arm": "0.21.5", "@esbuild/linux-arm64": "0.21.5", "@esbuild/linux-ia32": "0.21.5", "@esbuild/linux-loong64": "0.21.5", "@esbuild/linux-mips64el": "0.21.5", "@esbuild/linux-ppc64": "0.21.5", "@esbuild/linux-riscv64": "0.21.5", "@esbuild/linux-s390x": "0.21.5", "@esbuild/linux-x64": "0.21.5", "@esbuild/netbsd-x64": "0.21.5", "@esbuild/openbsd-x64": "0.21.5", "@esbuild/sunos-x64": "0.21.5", "@esbuild/win32-arm64": "0.21.5", "@esbuild/win32-ia32": "0.21.5", "@esbuild/win32-x64": "0.21.5"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/eslint-scope": {"version": "4.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/eslint-scope/-/eslint-scope-4.0.3.tgz", "integrity": "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=", "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=4.0.0"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "5.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "license": "BSD-2-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/estraverse/-/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=", "license": "BSD-2-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=", "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/events/-/events-3.3.0.tgz", "integrity": "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=", "license": "MIT", "peer": true, "engines": {"node": ">=0.8.x"}}, "node_modules/evp_bytestokey": {"version": "1.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "integrity": "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=", "license": "MIT", "peer": true, "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "node_modules/expand-brackets": {"version": "2.1.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/expand-brackets/-/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "license": "MIT", "peer": true, "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "license": "MIT", "peer": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "license": "MIT", "peer": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-descriptor/-/is-descriptor-0.1.7.tgz", "integrity": "sha1-JyfrYf14nc1b3w7UVp9VHS/jvjM=", "license": "MIT", "peer": true, "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/expand-brackets/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/extend-shallow": {"version": "3.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/extend-shallow/-/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "license": "MIT", "peer": true, "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob": {"version": "2.0.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/extglob/-/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "license": "MIT", "peer": true, "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "license": "MIT", "peer": true, "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "license": "MIT", "peer": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "license": "MIT"}, "node_modules/figgy-pudding": {"version": "3.5.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/figgy-pudding/-/figgy-pudding-3.5.2.tgz", "integrity": "sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=", "deprecated": "This module is no longer supported.", "license": "ISC"}, "node_modules/file-uri-to-path": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=", "license": "MIT", "optional": true, "peer": true}, "node_modules/fill-range": {"version": "4.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fill-range/-/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "license": "MIT", "peer": true, "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fill-range/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "license": "MIT", "peer": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fill-range/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/find-cache-dir": {"version": "2.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/find-cache-dir/-/find-cache-dir-2.1.0.tgz", "integrity": "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=", "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/find-up": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/find-up/-/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/flush-write-stream": {"version": "1.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/flush-write-stream/-/flush-write-stream-1.1.1.tgz", "integrity": "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-in": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/form-data": {"version": "4.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/form-data/-/form-data-4.0.2.tgz", "integrity": "sha1-Ncq73TDDznPessQtPI0+2cpReUw=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/fragment-cache": {"version": "0.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fragment-cache/-/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "license": "MIT", "peer": true, "dependencies": {"map-cache": "^0.2.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/from2": {"version": "2.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/from2/-/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "license": "MIT", "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "node_modules/fs-write-stream-atomic": {"version": "1.0.10", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-value": {"version": "2.0.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/get-value/-/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/glob/-/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "deprecated": "Glob versions prior to v9 are no longer supported", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "license": "ISC", "optional": true, "peer": true, "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/gopd/-/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "license": "ISC"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "devOptional": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "license": "MIT", "peer": true, "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-value": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/has-value/-/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "license": "MIT", "peer": true, "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/has-values/-/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "license": "MIT", "peer": true, "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/kind-of": {"version": "4.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "license": "MIT", "peer": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hash-base": {"version": "3.0.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/hash-base/-/hash-base-3.0.5.tgz", "integrity": "sha1-UkgOKFOVz3+6F9xMnkes3H8kioo=", "license": "MIT", "peer": true, "dependencies": {"inherits": "^2.0.4", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/hash-base/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "peer": true}, "node_modules/hash.js": {"version": "1.1.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/hash.js/-/hash.js-1.1.7.tgz", "integrity": "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=", "license": "MIT", "peer": true, "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/hasown/-/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hmac-drbg": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "license": "MIT", "peer": true, "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/https-browserify": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/https-browserify/-/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "license": "MIT", "peer": true}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/iferr": {"version": "0.1.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/iferr/-/iferr-0.1.5.tgz", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "license": "MIT"}, "node_modules/immutable": {"version": "5.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/immutable/-/immutable-5.0.3.tgz", "integrity": "sha1-qgN+IxPqe11ADNkpj6FOQEyTPbE=", "devOptional": true, "license": "MIT"}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/infer-owner": {"version": "1.0.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/infer-owner/-/infer-owner-1.0.4.tgz", "integrity": "sha1-xM78qo5RBRwqQLos6KPScpWvlGc=", "license": "ISC"}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/is-accessor-descriptor": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-accessor-descriptor/-/is-accessor-descriptor-1.0.1.tgz", "integrity": "sha1-MiOxBig1RkS4YmDbKbPmk/XO7dQ=", "license": "MIT", "peer": true, "dependencies": {"hasown": "^2.0.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-buffer": {"version": "1.1.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "license": "MIT", "peer": true}, "node_modules/is-data-descriptor": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-data-descriptor/-/is-data-descriptor-1.0.1.tgz", "integrity": "sha1-IQkWRCYWbTLqOMQFweCUXZ5qTus=", "license": "MIT", "peer": true, "dependencies": {"hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-descriptor": {"version": "1.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-descriptor/-/is-descriptor-1.0.3.tgz", "integrity": "sha1-ktJ8s80xHEl3pNtH30VyNKE8swY=", "license": "MIT", "peer": true, "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-extendable": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "license": "MIT", "peer": true, "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "license": "MIT", "optional": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "license": "MIT", "peer": true, "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "license": "MIT", "peer": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-object": {"version": "2.0.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-windows": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-wsl": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-wsl/-/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/isarray": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "license": "MIT"}, "node_modules/isobject": {"version": "3.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/jquery": {"version": "3.7.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/jquery/-/jquery-3.7.1.tgz", "integrity": "sha1-CD75iSfJpqdNBaavAoBlZtFidN4=", "license": "MIT"}, "node_modules/json-parse-better-errors": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "integrity": "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=", "license": "MIT", "peer": true}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "license": "MIT"}, "node_modules/json5": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/json5/-/json5-1.0.2.tgz", "integrity": "sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=", "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/kind-of": {"version": "6.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/loader-runner": {"version": "2.4.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/loader-runner/-/loader-runner-2.4.0.tgz", "integrity": "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=", "license": "MIT", "peer": true, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "node_modules/loader-utils": {"version": "1.4.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/loader-utils/-/loader-utils-1.4.2.tgz", "integrity": "sha1-KalX86Y5c4g+toTxD/09FR/sAaM=", "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^1.0.1"}, "engines": {"node": ">=4.0.0"}}, "node_modules/locate-path": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/lodash-es/-/lodash-es-4.17.21.tgz", "integrity": "sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=", "license": "MIT"}, "node_modules/lodash-unified": {"version": "1.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/lodash-unified/-/lodash-unified-1.0.3.tgz", "integrity": "sha1-gLHqwQ7S6wLtGJ8IYUopwn0HyJQ=", "license": "MIT", "peerDependencies": {"@types/lodash-es": "*", "lodash": "*", "lodash-es": "*"}}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/magic-string/-/magic-string-0.30.17.tgz", "integrity": "sha1-RQpElnPSRg5bvPupphkWoXFMdFM=", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/make-dir": {"version": "2.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/make-dir/-/make-dir-2.1.0.tgz", "integrity": "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=", "license": "MIT", "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "engines": {"node": ">=6"}}, "node_modules/map-cache": {"version": "0.2.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/map-visit": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/map-visit/-/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "license": "MIT", "peer": true, "dependencies": {"object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/md5.js": {"version": "1.3.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/md5.js/-/md5.js-1.3.5.tgz", "integrity": "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=", "license": "MIT", "peer": true, "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/memoize-one": {"version": "6.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/memoize-one/-/memoize-one-6.0.0.tgz", "integrity": "sha1-slkbhx7YKUiu5HJ9xqvO7qyMEEU=", "license": "MIT"}, "node_modules/memory-fs": {"version": "0.4.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/memory-fs/-/memory-fs-0.4.1.tgz", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "license": "MIT", "peer": true, "dependencies": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}, "node_modules/micromatch": {"version": "3.1.10", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "license": "MIT", "peer": true, "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/miller-rabin": {"version": "4.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/miller-rabin/-/miller-rabin-4.0.1.tgz", "integrity": "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=", "license": "MIT", "peer": true, "dependencies": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "bin": {"miller-rabin": "bin/miller-rabin"}}, "node_modules/miller-rabin/node_modules/bn.js": {"version": "4.12.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/bn.js/-/bn.js-4.12.1.tgz", "integrity": "sha1-IVdB/jydui1+EsAB0M/brkOXW6c=", "license": "MIT", "peer": true}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=", "license": "ISC", "peer": true}, "node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "license": "MIT", "peer": true}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/minimist/-/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mississippi": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/mississippi/-/mississippi-3.0.0.tgz", "integrity": "sha1-6goykfl+C16HdrNj1fChLZTGcCI=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mixin-deep": {"version": "1.3.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/mixin-deep/-/mixin-deep-1.3.2.tgz", "integrity": "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=", "license": "MIT", "peer": true, "dependencies": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mkdirp": {"version": "0.5.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/move-concurrently": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/move-concurrently/-/move-concurrently-1.0.1.tgz", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "node_modules/ms": {"version": "2.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT", "peer": true}, "node_modules/nan": {"version": "2.22.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/nan/-/nan-2.22.2.tgz", "integrity": "sha1-a1BP0Cn7jzjAmQ5SrVwmdy/az7s=", "license": "MIT", "optional": true, "peer": true}, "node_modules/nanoid": {"version": "3.3.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/nanoid/-/nanoid-3.3.9.tgz", "integrity": "sha1-4Al9jgJrM0P/BT6czUBzYKA/UDo=", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/nanomatch": {"version": "1.2.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/nanomatch/-/nanomatch-1.2.13.tgz", "integrity": "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=", "license": "MIT", "peer": true, "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/neo-async": {"version": "2.6.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/neo-async/-/neo-async-2.6.2.tgz", "integrity": "sha1-tKr7k+OustgXTKU88WOrfXMIMF8=", "license": "MIT"}, "node_modules/node-addon-api": {"version": "7.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/node-addon-api/-/node-addon-api-7.1.1.tgz", "integrity": "sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=", "license": "MIT", "optional": true}, "node_modules/node-libs-browser": {"version": "2.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/node-libs-browser/-/node-libs-browser-2.2.1.tgz", "integrity": "sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=", "license": "MIT", "peer": true, "dependencies": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.1", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "^1.0.1"}}, "node_modules/node-libs-browser/node_modules/punycode": {"version": "1.4.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "license": "MIT", "peer": true}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "license": "MIT", "optional": true, "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-wheel-es": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/normalize-wheel-es/-/normalize-wheel-es-1.2.0.tgz", "integrity": "sha1-D6JZPWGfckWlQWUmGRBasHas8J4=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/object-copy": {"version": "0.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/object-copy/-/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "license": "MIT", "peer": true, "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/define-property": {"version": "0.2.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "license": "MIT", "peer": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-descriptor": {"version": "0.1.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-descriptor/-/is-descriptor-0.1.7.tgz", "integrity": "sha1-JyfrYf14nc1b3w7UVp9VHS/jvjM=", "license": "MIT", "peer": true, "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/object-copy/node_modules/kind-of": {"version": "3.2.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "license": "MIT", "peer": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=", "license": "MIT", "peer": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "license": "MIT", "peer": true, "engines": {"node": ">= 0.4"}}, "node_modules/object-visit": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/object-visit/-/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "license": "MIT", "peer": true, "dependencies": {"isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.assign": {"version": "4.1.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/object.assign/-/object.assign-4.1.7.tgz", "integrity": "sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=", "license": "MIT", "peer": true, "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.pick": {"version": "1.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "license": "MIT", "peer": true, "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/os-browserify": {"version": "0.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/os-browserify/-/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "license": "MIT", "peer": true}, "node_modules/p-limit": {"version": "2.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pako": {"version": "1.0.11", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/pako/-/pako-1.0.11.tgz", "integrity": "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=", "license": "(MIT AND Zlib)", "peer": true}, "node_modules/parallel-transform": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/parallel-transform/-/parallel-transform-1.2.0.tgz", "integrity": "sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=", "license": "MIT", "dependencies": {"cyclist": "^1.0.1", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "node_modules/parse-asn1": {"version": "5.1.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/parse-asn1/-/parse-asn1-5.1.7.tgz", "integrity": "sha1-c82qqCISX5ZHFlYl60X4oFHS3wY=", "license": "ISC", "peer": true, "dependencies": {"asn1.js": "^4.10.1", "browserify-aes": "^1.2.0", "evp_bytestokey": "^1.0.3", "hash-base": "~3.0", "pbkdf2": "^3.1.2", "safe-buffer": "^5.2.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/parse-asn1/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "peer": true}, "node_modules/pascalcase": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/pascalcase/-/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-browserify": {"version": "0.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/path-browserify/-/path-browserify-0.0.1.tgz", "integrity": "sha1-5sTd1+06onxoogzE5Q4aTug7vEo=", "license": "MIT", "peer": true}, "node_modules/path-dirname": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "license": "MIT", "optional": true, "peer": true}, "node_modules/path-exists": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pbkdf2": {"version": "3.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/pbkdf2/-/pbkdf2-3.1.2.tgz", "integrity": "sha1-3YIqoIh1gOUvGgOdw+2hCO+uMHU=", "license": "MIT", "peer": true, "dependencies": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}, "engines": {"node": ">=0.12"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "license": "MIT", "optional": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "4.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/pify/-/pify-4.0.1.tgz", "integrity": "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pkg-dir": {"version": "3.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/pkg-dir/-/pkg-dir-3.0.0.tgz", "integrity": "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=", "license": "MIT", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/posix-character-classes": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/postcss": {"version": "8.5.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/postcss/-/postcss-8.5.3.tgz", "integrity": "sha1-FGO28cf7Fv4lhzbLopot41I36vs=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/process": {"version": "0.11.10", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "license": "MIT", "peer": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "license": "MIT"}, "node_modules/promise-inflight": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "license": "ISC"}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=", "license": "MIT"}, "node_modules/prr": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/prr/-/prr-1.0.1.tgz", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY=", "license": "MIT"}, "node_modules/public-encrypt": {"version": "4.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/public-encrypt/-/public-encrypt-4.0.3.tgz", "integrity": "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=", "license": "MIT", "peer": true, "dependencies": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/public-encrypt/node_modules/bn.js": {"version": "4.12.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/bn.js/-/bn.js-4.12.1.tgz", "integrity": "sha1-IVdB/jydui1+EsAB0M/brkOXW6c=", "license": "MIT", "peer": true}, "node_modules/pump": {"version": "3.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/pump/-/pump-3.0.2.tgz", "integrity": "sha1-g28+3WvC7lmSVskk/+DYhXPdy/g=", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/pumpify": {"version": "1.5.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/pumpify/-/pumpify-1.5.1.tgz", "integrity": "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=", "license": "MIT", "dependencies": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "node_modules/pumpify/node_modules/pump": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/pump/-/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/punycode/-/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/qs/-/qs-6.14.0.tgz", "integrity": "sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=", "license": "BSD-3-<PERSON><PERSON>", "peer": true, "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/querystring-es3": {"version": "0.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/querystring-es3/-/querystring-es3-0.2.1.tgz", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=", "peer": true, "engines": {"node": ">=0.4.x"}}, "node_modules/randombytes": {"version": "2.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/randombytes/-/randombytes-2.1.0.tgz", "integrity": "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=", "license": "MIT", "peer": true, "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/randomfill": {"version": "1.0.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/randomfill/-/randomfill-1.0.4.tgz", "integrity": "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=", "license": "MIT", "peer": true, "dependencies": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "node_modules/readable-stream": {"version": "2.3.8", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/regex-not": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/regex-not/-/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "license": "MIT", "peer": true, "dependencies": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/remove-trailing-separator": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "license": "ISC", "optional": true, "peer": true}, "node_modules/repeat-element": {"version": "1.1.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/repeat-element/-/repeat-element-1.1.4.tgz", "integrity": "sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-string": {"version": "1.6.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10"}}, "node_modules/resize-detector": {"version": "0.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/resize-detector/-/resize-detector-0.3.0.tgz", "integrity": "sha1-/klREuGEaVUAqPUeA4nxV3TLHPw=", "license": "MIT"}, "node_modules/resolve-url": {"version": "0.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "deprecated": "https://github.com/lydell/resolve-url#deprecated", "license": "MIT", "peer": true}, "node_modules/ret": {"version": "0.1.15", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/ret/-/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=", "license": "MIT", "peer": true, "engines": {"node": ">=0.12"}}, "node_modules/rimraf": {"version": "2.7.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/rimraf/-/rimraf-2.7.1.tgz", "integrity": "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=", "deprecated": "Rimraf versions prior to v4 are no longer supported", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/ripemd160": {"version": "2.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/ripemd160/-/ripemd160-2.0.2.tgz", "integrity": "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=", "license": "MIT", "peer": true, "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "node_modules/rollup": {"version": "4.34.9", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/rollup/-/rollup-4.34.9.tgz", "integrity": "sha1-4es5eFZHZ3iutqwqw9CbLOF3pVg=", "license": "MIT", "dependencies": {"@types/estree": "1.0.6"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.34.9", "@rollup/rollup-android-arm64": "4.34.9", "@rollup/rollup-darwin-arm64": "4.34.9", "@rollup/rollup-darwin-x64": "4.34.9", "@rollup/rollup-freebsd-arm64": "4.34.9", "@rollup/rollup-freebsd-x64": "4.34.9", "@rollup/rollup-linux-arm-gnueabihf": "4.34.9", "@rollup/rollup-linux-arm-musleabihf": "4.34.9", "@rollup/rollup-linux-arm64-gnu": "4.34.9", "@rollup/rollup-linux-arm64-musl": "4.34.9", "@rollup/rollup-linux-loongarch64-gnu": "4.34.9", "@rollup/rollup-linux-powerpc64le-gnu": "4.34.9", "@rollup/rollup-linux-riscv64-gnu": "4.34.9", "@rollup/rollup-linux-s390x-gnu": "4.34.9", "@rollup/rollup-linux-x64-gnu": "4.34.9", "@rollup/rollup-linux-x64-musl": "4.34.9", "@rollup/rollup-win32-arm64-msvc": "4.34.9", "@rollup/rollup-win32-ia32-msvc": "4.34.9", "@rollup/rollup-win32-x64-msvc": "4.34.9", "fsevents": "~2.3.2"}}, "node_modules/run-queue": {"version": "1.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/run-queue/-/run-queue-1.0.3.tgz", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "license": "ISC", "dependencies": {"aproba": "^1.1.1"}}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/rxjs/-/rxjs-7.8.2.tgz", "integrity": "sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=", "devOptional": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "license": "MIT"}, "node_modules/safe-regex": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "license": "MIT", "peer": true, "dependencies": {"ret": "~0.1.10"}}, "node_modules/sass": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass/-/sass-1.85.1.tgz", "integrity": "sha1-GKsLtIEQrpkWN3jwZEW0BhSMoNU=", "devOptional": true, "license": "MIT", "dependencies": {"chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"@parcel/watcher": "^2.4.1"}}, "node_modules/sass-embedded": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded/-/sass-embedded-1.85.1.tgz", "integrity": "sha1-hozfBno7UV63LLADnmHnLExWyFY=", "devOptional": true, "license": "MIT", "dependencies": {"@bufbuild/protobuf": "^2.0.0", "buffer-builder": "^0.2.0", "colorjs.io": "^0.5.0", "immutable": "^5.0.2", "rxjs": "^7.4.0", "supports-color": "^8.1.1", "sync-child-process": "^1.0.2", "varint": "^6.0.0"}, "bin": {"sass": "dist/bin/sass.js"}, "engines": {"node": ">=16.0.0"}, "optionalDependencies": {"sass-embedded-android-arm": "1.85.1", "sass-embedded-android-arm64": "1.85.1", "sass-embedded-android-ia32": "1.85.1", "sass-embedded-android-riscv64": "1.85.1", "sass-embedded-android-x64": "1.85.1", "sass-embedded-darwin-arm64": "1.85.1", "sass-embedded-darwin-x64": "1.85.1", "sass-embedded-linux-arm": "1.85.1", "sass-embedded-linux-arm64": "1.85.1", "sass-embedded-linux-ia32": "1.85.1", "sass-embedded-linux-musl-arm": "1.85.1", "sass-embedded-linux-musl-arm64": "1.85.1", "sass-embedded-linux-musl-ia32": "1.85.1", "sass-embedded-linux-musl-riscv64": "1.85.1", "sass-embedded-linux-musl-x64": "1.85.1", "sass-embedded-linux-riscv64": "1.85.1", "sass-embedded-linux-x64": "1.85.1", "sass-embedded-win32-arm64": "1.85.1", "sass-embedded-win32-ia32": "1.85.1", "sass-embedded-win32-x64": "1.85.1"}}, "node_modules/sass-embedded-android-arm": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-android-arm/-/sass-embedded-android-arm-1.85.1.tgz", "integrity": "sha1-87zVn7BcKTGuoSaUlqCYj4C5Nq8=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-android-arm64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-android-arm64/-/sass-embedded-android-arm64-1.85.1.tgz", "integrity": "sha1-HKnF4G6hqOz3T/f76mcXBs/lAyA=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-android-ia32": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-android-ia32/-/sass-embedded-android-ia32-1.85.1.tgz", "integrity": "sha1-C0jPGwoVfAZtjWyPTHz102trIps=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-android-riscv64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-android-riscv64/-/sass-embedded-android-riscv64-1.85.1.tgz", "integrity": "sha1-sgJKrrdUVAEb0qOujuTKwnO15xE=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-android-x64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-android-x64/-/sass-embedded-android-x64-1.85.1.tgz", "integrity": "sha1-xTkYMMuzw3jlF3vA4D6Up4LNHME=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-darwin-arm64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-darwin-arm64/-/sass-embedded-darwin-arm64-1.85.1.tgz", "integrity": "sha1-eay7aGfQFolvhDlxvfoKx24QHfg=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-darwin-x64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-darwin-x64/-/sass-embedded-darwin-x64-1.85.1.tgz", "integrity": "sha1-FH7PSb8tGC295s7zN6WKBbepOrE=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-arm": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-arm/-/sass-embedded-linux-arm-1.85.1.tgz", "integrity": "sha1-iZy6lLc+sRn2Q0aInkZt4xtvXTw=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-arm64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-arm64/-/sass-embedded-linux-arm64-1.85.1.tgz", "integrity": "sha1-XHtcJ0lTKZZjClUS0OG/bYRyrNM=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-ia32": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-ia32/-/sass-embedded-linux-ia32-1.85.1.tgz", "integrity": "sha1-8bpT8DQ4ljWv6cEFm8Nqd5NkIbg=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-arm": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-musl-arm/-/sass-embedded-linux-musl-arm-1.85.1.tgz", "integrity": "sha1-Qo5eKZqf9N/SC1eGHSRAQotfvfY=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-arm64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-musl-arm64/-/sass-embedded-linux-musl-arm64-1.85.1.tgz", "integrity": "sha1-sUziYVx6tGJuiMuiZW+Ro6dI4c4=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-ia32": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-musl-ia32/-/sass-embedded-linux-musl-ia32-1.85.1.tgz", "integrity": "sha1-BwYJr9mc0Pnq5yGGyfSi0M32lE0=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-riscv64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-musl-riscv64/-/sass-embedded-linux-musl-riscv64-1.85.1.tgz", "integrity": "sha1-D0JkvkAnfXzBgUnJ4PA68JVi5vc=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-musl-x64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-musl-x64/-/sass-embedded-linux-musl-x64-1.85.1.tgz", "integrity": "sha1-tUteH2RtHwwJ2d9+Dm1JYNbtf6E=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-riscv64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-riscv64/-/sass-embedded-linux-riscv64-1.85.1.tgz", "integrity": "sha1-Jxh3Mwf5T+uBXk4vu1qnMiao03U=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-linux-x64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-linux-x64/-/sass-embedded-linux-x64-1.85.1.tgz", "integrity": "sha1-BmTMiGuHgYrJ0pv06fKZ/mxj9SQ=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-win32-arm64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-win32-arm64/-/sass-embedded-win32-arm64-1.85.1.tgz", "integrity": "sha1-0Ht1X4QI193huDCNOlf7/NqdkJw=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-win32-ia32": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-win32-ia32/-/sass-embedded-win32-ia32-1.85.1.tgz", "integrity": "sha1-Xl8W4aMPjfMRSKr2WncimNcDfXA=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-embedded-win32-x64": {"version": "1.85.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-embedded-win32-x64/-/sass-embedded-win32-x64-1.85.1.tgz", "integrity": "sha1-dY+5bBbncmWkt/JHSkOWLXHvX/0=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=14.0.0"}}, "node_modules/sass-loader": {"version": "8.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sass-loader/-/sass-loader-8.0.2.tgz", "integrity": "sha1-3r7NjDziQ8dkVPLoKQSCFQOACQ0=", "dev": true, "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "loader-utils": "^1.2.3", "neo-async": "^2.6.1", "schema-utils": "^2.6.1", "semver": "^6.3.0"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"fibers": ">= 3.1.0", "node-sass": "^4.0.0", "sass": "^1.3.0", "webpack": "^4.36.0 || ^5.0.0"}, "peerDependenciesMeta": {"fibers": {"optional": true}, "node-sass": {"optional": true}, "sass": {"optional": true}}}, "node_modules/sass-loader/node_modules/schema-utils": {"version": "2.7.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/schema-utils/-/schema-utils-2.7.1.tgz", "integrity": "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/sass-loader/node_modules/semver": {"version": "6.3.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/sass/node_modules/chokidar": {"version": "4.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/chokidar/-/chokidar-4.0.3.tgz", "integrity": "sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=", "devOptional": true, "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/sass/node_modules/readdirp": {"version": "4.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/readdirp/-/readdirp-4.1.2.tgz", "integrity": "sha1-64WAFDX78qfuWPGeCSGwaPxplI0=", "devOptional": true, "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/schema-utils": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "license": "MIT", "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/semver": {"version": "5.7.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/semver/-/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/serialize-javascript": {"version": "1.9.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/serialize-javascript/-/serialize-javascript-1.9.1.tgz", "integrity": "sha1-z8IArvd7YAxH2pu4FJyUPnmML9s=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=", "license": "MIT", "peer": true, "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-value": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/set-value/-/set-value-2.0.1.tgz", "integrity": "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=", "license": "MIT", "peer": true, "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "license": "MIT", "peer": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "license": "MIT", "peer": true}, "node_modules/sha.js": {"version": "2.4.11", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sha.js/-/sha.js-2.4.11.tgz", "integrity": "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=", "license": "(MIT AND BSD-3-<PERSON><PERSON>)", "peer": true, "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/shallow-clone": {"version": "3.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/shallow-clone/-/shallow-clone-3.0.1.tgz", "integrity": "sha1-jymBrZJTH1UDWwH7IwdppA4C76M=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "license": "MIT", "peer": true, "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "license": "MIT", "peer": true, "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "license": "MIT", "peer": true, "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "license": "MIT", "peer": true, "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/snapdragon": {"version": "0.8.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/snapdragon/-/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "license": "MIT", "peer": true, "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node": {"version": "2.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "license": "MIT", "peer": true, "dependencies": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/define-property": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "license": "MIT", "peer": true, "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util": {"version": "3.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "license": "MIT", "peer": true, "dependencies": {"kind-of": "^3.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util/node_modules/kind-of": {"version": "3.2.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "license": "MIT", "peer": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/define-property": {"version": "0.2.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "license": "MIT", "peer": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "license": "MIT", "peer": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-descriptor": {"version": "0.1.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-descriptor/-/is-descriptor-0.1.7.tgz", "integrity": "sha1-JyfrYf14nc1b3w7UVp9VHS/jvjM=", "license": "MIT", "peer": true, "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/snapdragon/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/source-map": {"version": "0.5.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "license": "BSD-3-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-list-map": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/source-list-map/-/source-list-map-2.0.1.tgz", "integrity": "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=", "license": "MIT"}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha1-HOVlD93YerwJnto33P8CTCZnrkY=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.5.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "integrity": "sha1-GQhmvs51U+H48mei7oLGBrVQmho=", "deprecated": "See https://github.com/lydell/source-map-resolve#deprecated", "license": "MIT", "peer": true, "dependencies": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/source-map-support/-/source-map-support-0.5.21.tgz", "integrity": "sha1-BP58f54e0tZiIzwoyys1ufY/bk8=", "license": "MIT", "peer": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-url": {"version": "0.4.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/source-map-url/-/source-map-url-0.4.1.tgz", "integrity": "sha1-CvZmBadFpaL5HPG7+KevvCg97FY=", "deprecated": "See https://github.com/lydell/source-map-url#deprecated", "license": "MIT", "peer": true}, "node_modules/split-string": {"version": "3.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/split-string/-/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "license": "MIT", "peer": true, "dependencies": {"extend-shallow": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ssri": {"version": "6.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/ssri/-/ssri-6.0.2.tgz", "integrity": "sha1-FXk5E08gRk5zAd26PpD/qPdyisU=", "license": "ISC", "dependencies": {"figgy-pudding": "^3.5.1"}}, "node_modules/static-extend": {"version": "0.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/static-extend/-/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "license": "MIT", "peer": true, "dependencies": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/define-property": {"version": "0.2.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "license": "MIT", "peer": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-descriptor": {"version": "0.1.7", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-descriptor/-/is-descriptor-0.1.7.tgz", "integrity": "sha1-JyfrYf14nc1b3w7UVp9VHS/jvjM=", "license": "MIT", "peer": true, "dependencies": {"is-accessor-descriptor": "^1.0.1", "is-data-descriptor": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/stream-browserify": {"version": "2.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/stream-browserify/-/stream-browserify-2.0.2.tgz", "integrity": "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=", "license": "MIT", "peer": true, "dependencies": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "node_modules/stream-each": {"version": "1.2.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/stream-each/-/stream-each-1.2.3.tgz", "integrity": "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "node_modules/stream-http": {"version": "2.8.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/stream-http/-/stream-http-2.8.3.tgz", "integrity": "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=", "license": "MIT", "peer": true, "dependencies": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}}, "node_modules/stream-shift": {"version": "1.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/stream-shift/-/stream-shift-1.0.3.tgz", "integrity": "sha1-hbj6tNcQEPw7qHcugEbMSbijhks=", "license": "MIT"}, "node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/supports-color": {"version": "8.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=", "devOptional": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/sync-child-process": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sync-child-process/-/sync-child-process-1.0.2.tgz", "integrity": "sha1-RefHLnVtEkPoC1R+ouF5V6ueNn8=", "devOptional": true, "license": "MIT", "dependencies": {"sync-message-port": "^1.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/sync-message-port": {"version": "1.1.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/sync-message-port/-/sync-message-port-1.1.3.tgz", "integrity": "sha1-YFXFZe6MgdL57lqufbdX5tkIjAw=", "devOptional": true, "license": "MIT", "engines": {"node": ">=16.0.0"}}, "node_modules/tapable": {"version": "1.1.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/tapable/-/tapable-1.1.3.tgz", "integrity": "sha1-ofzMBrWNth/XpF2i2kT186Pme6I=", "license": "MIT", "peer": true, "engines": {"node": ">=6"}}, "node_modules/terser": {"version": "5.39.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/terser/-/terser-5.39.0.tgz", "integrity": "sha1-DoIDPtV7Pd8flnCNEjzKcX2Gyjo=", "license": "BSD-2-<PERSON><PERSON>", "optional": true, "peer": true, "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser-webpack-plugin": {"version": "1.4.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/terser-webpack-plugin/-/terser-webpack-plugin-1.4.6.tgz", "integrity": "sha1-h/y2WT/RyXfNCeVhQ+zTFARgB1U=", "license": "MIT", "peer": true, "dependencies": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^4.0.0", "source-map": "^0.6.1", "terser": "^4.1.2", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "engines": {"node": ">= 6.9.0"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/terser-webpack-plugin/node_modules/serialize-javascript": {"version": "4.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/serialize-javascript/-/serialize-javascript-4.0.0.tgz", "integrity": "sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=", "license": "BSD-3-<PERSON><PERSON>", "peer": true, "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/terser-webpack-plugin/node_modules/terser": {"version": "4.8.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/terser/-/terser-4.8.1.tgz", "integrity": "sha1-oA5WNFYt4iOf1ATGSQUb9vwhFE8=", "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=6.0.0"}}, "node_modules/terser/node_modules/acorn": {"version": "8.14.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/acorn/-/acorn-8.14.1.tgz", "integrity": "sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=", "license": "MIT", "optional": true, "peer": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/through2": {"version": "2.0.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/through2/-/through2-2.0.5.tgz", "integrity": "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=", "license": "MIT", "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "node_modules/timers-browserify": {"version": "2.0.12", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/timers-browserify/-/timers-browserify-2.0.12.tgz", "integrity": "sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=", "license": "MIT", "peer": true, "dependencies": {"setimmediate": "^1.0.4"}, "engines": {"node": ">=0.6.0"}}, "node_modules/to-arraybuffer": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=", "license": "MIT", "peer": true}, "node_modules/to-object-path": {"version": "0.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/to-object-path/-/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "license": "MIT", "peer": true, "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-object-path/node_modules/kind-of": {"version": "3.2.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "license": "MIT", "peer": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex": {"version": "3.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/to-regex/-/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "license": "MIT", "peer": true, "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range": {"version": "2.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "license": "MIT", "peer": true, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/tslib": {"version": "2.3.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/tslib/-/tslib-2.3.0.tgz", "integrity": "sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=", "license": "0BSD"}, "node_modules/tty-browserify": {"version": "0.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/tty-browserify/-/tty-browserify-0.0.0.tgz", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=", "license": "MIT", "peer": true}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "license": "MIT"}, "node_modules/uglify-js": {"version": "3.19.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/uglify-js/-/uglify-js-3.19.3.tgz", "integrity": "sha1-gjFem7xvKyWIiFis0f/4RBA1t38=", "license": "BSD-2-<PERSON><PERSON>", "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/uglifyjs-webpack-plugin": {"version": "2.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/uglifyjs-webpack-plugin/-/uglifyjs-webpack-plugin-2.2.0.tgz", "integrity": "sha1-51vIDn8ZN/cllUybTFoeln6p0Nc=", "license": "MIT", "dependencies": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^1.7.0", "source-map": "^0.6.1", "uglify-js": "^3.6.0", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "engines": {"node": ">= 6.9.0"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/union-value": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/union-value/-/union-value-1.0.1.tgz", "integrity": "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=", "license": "MIT", "peer": true, "dependencies": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/union-value/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/unique-filename": {"version": "1.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/unique-filename/-/unique-filename-1.1.1.tgz", "integrity": "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=", "license": "ISC", "dependencies": {"unique-slug": "^2.0.0"}}, "node_modules/unique-slug": {"version": "2.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/unique-slug/-/unique-slug-2.0.2.tgz", "integrity": "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}}, "node_modules/unset-value": {"version": "1.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/unset-value/-/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "license": "MIT", "peer": true, "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value": {"version": "0.3.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "license": "MIT", "peer": true, "dependencies": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value/node_modules/isobject": {"version": "2.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "license": "MIT", "peer": true, "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-values": {"version": "0.1.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/upath": {"version": "1.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/upath/-/upath-1.2.0.tgz", "integrity": "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=", "license": "MIT", "optional": true, "peer": true, "engines": {"node": ">=4", "yarn": "*"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/urix": {"version": "0.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/urix/-/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "deprecated": "Please see https://github.com/lydell/urix#deprecated", "license": "MIT", "peer": true}, "node_modules/url": {"version": "0.11.4", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/url/-/url-0.11.4.tgz", "integrity": "sha1-rcp3s1YtVrcnRudrMwt/J7ZyHzw=", "license": "MIT", "peer": true, "dependencies": {"punycode": "^1.4.1", "qs": "^6.12.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/url/node_modules/punycode": {"version": "1.4.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "license": "MIT", "peer": true}, "node_modules/use": {"version": "3.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/use/-/use-3.1.1.tgz", "integrity": "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=", "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/util": {"version": "0.11.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/util/-/util-0.11.1.tgz", "integrity": "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=", "license": "MIT", "peer": true, "dependencies": {"inherits": "2.0.3"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "license": "MIT"}, "node_modules/util/node_modules/inherits": {"version": "2.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "license": "ISC", "peer": true}, "node_modules/varint": {"version": "6.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/varint/-/varint-6.0.0.tgz", "integrity": "sha1-mIHrDOj+rqZRJDnRnd+Ev1UWYdA=", "devOptional": true, "license": "MIT"}, "node_modules/vein-plus": {"version": "1.1.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vein-plus/-/vein-plus-1.1.3.tgz", "integrity": "sha1-3i9Ny9RrfCYgn8epR1iliwT7YY0=", "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.4.1", "@floating-ui/dom": "^1.0.1", "@popperjs/core": "npm:@sxzz/popperjs-es@^2.11.7", "@types/lodash": "^4.14.182", "@types/lodash-es": "^4.17.6", "@vein-plus/icons-vue": "1.1.3", "@vueuse/core": "^9.1.0", "async-validator": "^4.2.5", "dayjs": "^1.11.3", "escape-html": "^1.0.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "memoize-one": "^6.0.0", "normalize-wheel-es": "^1.2.0"}, "peerDependencies": {"vue": ">=3.2.0"}}, "node_modules/vite": {"version": "5.4.14", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vite/-/vite-5.4.14.tgz", "integrity": "sha1-/4JV7bAhNN8YDc/KGRbDemq+hAg=", "license": "MIT", "dependencies": {"esbuild": "^0.21.3", "postcss": "^8.4.43", "rollup": "^4.20.0"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || >=20.0.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/vm-browserify": {"version": "1.1.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vm-browserify/-/vm-browserify-1.1.2.tgz", "integrity": "sha1-eGQcSIuObKkadfUR56OzKobl3aA=", "license": "MIT", "peer": true}, "node_modules/vue": {"version": "3.5.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vue/-/vue-3.5.13.tgz", "integrity": "sha1-n3YKGpgrCcDASoZ5A/wznJ8p7Ao=", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.13", "@vue/compiler-sfc": "3.5.13", "@vue/runtime-dom": "3.5.13", "@vue/server-renderer": "3.5.13", "@vue/shared": "3.5.13"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vue-dompurify-html": {"version": "5.2.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vue-dompurify-html/-/vue-dompurify-html-5.2.0.tgz", "integrity": "sha1-qLtsvulPboWB/ULFcJa95RhdyvI=", "license": "MIT", "dependencies": {"dompurify": "^3.2.1"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/vue-echarts": {"version": "6.7.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vue-echarts/-/vue-echarts-6.7.3.tgz", "integrity": "sha1-MO+vxRpKneG4EX07Y+dLDHYf87o=", "hasInstallScript": true, "license": "MIT", "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}}, "node_modules/vue-echarts/node_modules/vue-demi": {"version": "0.13.11", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vue-demi/-/vue-demi-0.13.11.tgz", "integrity": "sha1-fZA2m9rol02HsZc1ZK05AYJBDZk=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/vue-i18n": {"version": "9.14.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vue-i18n/-/vue-i18n-9.14.3.tgz", "integrity": "sha1-yhyVagwOgkFavS2mNypDrmJKAY8=", "license": "MIT", "dependencies": {"@intlify/core-base": "9.14.3", "@intlify/shared": "9.14.3", "@vue/devtools-api": "^6.5.0"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/vue-router": {"version": "4.5.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vue-router/-/vue-router-4.5.0.tgz", "integrity": "sha1-WPxf43ThC2AY+RAyj3VsPa4IHxQ=", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.4"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/vuex": {"version": "4.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/vuex/-/vuex-4.1.0.tgz", "integrity": "sha1-qhs+pcc4WBKwdMhvru7CIXhy42w=", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.0.0-beta.11"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/watchpack": {"version": "1.7.5", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/watchpack/-/watchpack-1.7.5.tgz", "integrity": "sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=", "license": "MIT", "peer": true, "dependencies": {"graceful-fs": "^4.1.2", "neo-async": "^2.5.0"}, "optionalDependencies": {"chokidar": "^3.4.1", "watchpack-chokidar2": "^2.0.1"}}, "node_modules/watchpack-chokidar2": {"version": "2.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz", "integrity": "sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"chokidar": "^2.1.8"}}, "node_modules/watchpack-chokidar2/node_modules/anymatch": {"version": "2.0.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "license": "ISC", "optional": true, "peer": true, "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "node_modules/watchpack-chokidar2/node_modules/anymatch/node_modules/normalize-path": {"version": "2.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/watchpack-chokidar2/node_modules/binary-extensions": {"version": "1.13.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/binary-extensions/-/binary-extensions-1.13.1.tgz", "integrity": "sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=", "license": "MIT", "optional": true, "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/watchpack-chokidar2/node_modules/chokidar": {"version": "2.1.8", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/chokidar/-/chokidar-2.1.8.tgz", "integrity": "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}, "optionalDependencies": {"fsevents": "^1.2.7"}}, "node_modules/watchpack-chokidar2/node_modules/fsevents": {"version": "1.2.13", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/fsevents/-/fsevents-1.2.13.tgz", "integrity": "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=", "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues", "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "peer": true, "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1"}, "engines": {"node": ">= 4.0"}}, "node_modules/watchpack-chokidar2/node_modules/glob-parent": {"version": "3.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "license": "ISC", "optional": true, "peer": true, "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/watchpack-chokidar2/node_modules/glob-parent/node_modules/is-glob": {"version": "3.1.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/watchpack-chokidar2/node_modules/is-binary-path": {"version": "1.0.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"binary-extensions": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/watchpack-chokidar2/node_modules/readdirp": {"version": "2.2.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/readdirp/-/readdirp-2.2.1.tgz", "integrity": "sha1-DodiKjMlqjPokihcr4tOhGUppSU=", "license": "MIT", "optional": true, "peer": true, "dependencies": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}, "engines": {"node": ">=0.10"}}, "node_modules/webpack": {"version": "4.47.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/webpack/-/webpack-4.47.0.tgz", "integrity": "sha1-i4oCFS1wdq6wO2G0fa0u7tmBDrw=", "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/wasm-edit": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "acorn": "^6.4.1", "ajv": "^6.10.2", "ajv-keywords": "^3.4.1", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^4.5.0", "eslint-scope": "^4.0.3", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.4.0", "loader-utils": "^1.2.3", "memory-fs": "^0.4.1", "micromatch": "^3.1.10", "mkdirp": "^0.5.3", "neo-async": "^2.6.1", "node-libs-browser": "^2.2.1", "schema-utils": "^1.0.0", "tapable": "^1.1.3", "terser-webpack-plugin": "^1.4.3", "watchpack": "^1.7.4", "webpack-sources": "^1.4.1"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=6.11.5"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}, "webpack-command": {"optional": true}}}, "node_modules/webpack-sources": {"version": "1.4.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/webpack-sources/-/webpack-sources-1.4.3.tgz", "integrity": "sha1-7t2OwLko+/HL/plOItLYkPMwqTM=", "license": "MIT", "dependencies": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}}, "node_modules/worker-farm": {"version": "1.7.0", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/worker-farm/-/worker-farm-1.7.0.tgz", "integrity": "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=", "license": "MIT", "dependencies": {"errno": "~0.1.7"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "license": "ISC"}, "node_modules/xtend": {"version": "4.0.2", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/xtend/-/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=", "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "4.0.3", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/y18n/-/y18n-4.0.3.tgz", "integrity": "sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=", "license": "ISC"}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "license": "ISC"}, "node_modules/zrender": {"version": "5.6.1", "resolved": "https://artsz.zte.com.cn:443/artifactory/api/npm/it-npm-virtual/zrender/-/zrender-5.6.1.tgz", "integrity": "sha1-4I1X7PSsrHCMT8t0gesgHffxCms=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tslib": "2.3.0"}}}}