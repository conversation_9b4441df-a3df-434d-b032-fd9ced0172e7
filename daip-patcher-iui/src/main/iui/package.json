{"name": "eslint_rules", "dependencies": {}, "devDependencies": {"htmlhint": "^0.14.2", "eslint": "^6.1.0", "eslint-plugin-html": "^6.1.2"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "replace-temp": "node htmlhint/copyFile.js", "lint-o": "./node_modules/.bin/eslint js/ --rulesdir ./rules/js -f html -o report.html", "lint": "./node_modules/.bin/eslint --resolve-plugins-relative-to path js/ --rulesdir ./rules/js", "htmlhint": "./node_modules/.bin/htmlhint page/ -R ./rules/html", "htmlhint-o": "./node_modules/.bin/htmlhint page/ -R ./rules/html -f html >hintReport.html"}, "author": "", "license": "ISC", "engines": {"node": ">=10.0.0", "npm": ">=6.0.0"}}