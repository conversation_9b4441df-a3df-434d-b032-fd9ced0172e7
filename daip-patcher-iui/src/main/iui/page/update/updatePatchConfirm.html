<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7; IE=EmulateIE9"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap-theme.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/css/common/components.css"/>

    <style>
        table td {
            height: 30px;
            border: 1px solid white;
            text-align: center;
        }

        .input_search {
            width: 270px;
            border: 1px solid #D8D8D8;
            border-radius: 3px;
        }

        .fontShow {
            font-size: 13px;
            font-family: 'Arial Normal', 'Arial';
            font-weight: 400;
            font-style: normal;
            color: #333333;
            text-align: left;
            line-height: normal;
        }

        .patchBlock {
            display: inline-block;
            margin-left: 10px;
        }

        a {
            text-decoration: none;
        }

        #hostContain li {
            text-align: center;
            display: inline-block;
            width: 131px;
            height: 30px;
            line-height: 20px;
            margin-right: 1px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            padding: 5px;
        }

        #hostContain li:nth-child(12n+1),
        #hostContain li:nth-child(12n+2),
        #hostContain li:nth-child(12n+3),
        #hostContain li:nth-child(12n+4),
        #hostContain li:nth-child(12n+5),
        #hostContain li:nth-child(12n+6) {
            background: #f0f0f0;
        }

    </style>


</head>

<body>
<div style="display:inline-block;">
    <span style="font-size:18px;padding-left:5px"
          id="com_zte_ums_ict_framework_ui_update_patch_choose_confirm"
          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
    <!--<span style="padding-left:10px;color:red;" id="com_zte_ums_ict_framework_ui_update_show_red" name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>-->
</div>
<hr style="margin-top:5px;"/>
<div style="display:inline-block;margin-left: 5px;" id="confirmTab" class="fontShow">
</div>


<div class="modal fade" id="patchHostDialog">
    <div class="modal-dialog" style="width: 846px;">
        <div class="modal-content" style="width: 846px;">
            <div class="modal-header">
                <h4 class="modal-title" id="title">
                    <span id="com_zte_ums_ict_framework_ui_button_more"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="height: 360px;overflow-y: scroll">
                <div>
                    <span id="com_zte_ums_ict_framework_ui_federal_search"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span><input
                        type="text" id="searchHost" placeholder="Search IP/HostName"
                        class="input_search"/>
                </div>
                <div style="padding: 15px 0">
                    <span id="com_zte_ums_ict_framework_ui_control_host_table"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    (<span id="hostCount"> </span>) :
                    <div id="hostSwitch" style="float:right">
                        <input type="radio" name="hostRadio" value="hostName"/>
                    <span id="com_zte_ums_ict_framework_ui_alarm_hostname"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span> /
                        <input type="radio" name="hostRadio" value="hostIp" checked="true"/>
                    <span id="com_zte_ums_ict_framework_ui_host_ip_address"
                           name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </div>
                </div>
                <div id="hostContain"></div>
            </div>
            <div class="modal-footer">
                <a class="button" id="btn-default" data-bs-dismiss="modal">
                    <span id="com_zte_ums_ict_framework_js_chart_utils_close"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </a>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>

<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/jquery/jquery.min.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/i18n/jquery.i18n.properties-1.0.9.js">
</script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/i18n/loadi18n.js"></script>

<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/http/http.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/security/escapeHtml.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/http/getParamFromLocation.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/utils/commonUtil.js"></script>

<script type="text/javascript"
        src="../../js/update/updatePatchConfirm.js"></script>
</body>
</html>
