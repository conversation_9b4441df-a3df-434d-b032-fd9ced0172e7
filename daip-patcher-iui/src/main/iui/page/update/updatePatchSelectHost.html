<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7; IE=EmulateIE9" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link rel="stylesheet" type="text/css"
        href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap-dialog.css" />
    <link rel="stylesheet" type="text/css"
        href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css"
        href="../../@iui.common.microservice.name@/lib/bootstrap/css//bootstrap-theme.min.css" />
    <link rel="stylesheet" type="text/css" href="../../@iui.common.microservice.name@/css/common/components.css" />
    <link rel="stylesheet" type="text/css" href="../../@iui.common.microservice.name@/css/common/table.css" />

    <style>
        .input_area {
            position: absolute;
            width: 487px;
            height: 55px;
            font-family: 'Arial Normal', 'Arial';
            font-weight: 400;
            font-style: normal;
            font-size: 13px;
            text-decoration: none;
            color: #000000;
            text-align: left;
            border-color: #D8D8D8;
            outline-style: none;
            margin-left: 5px;
            resize: none;
        }

        .ipbgcolor {
            background-color: #0081c2;
        }

        .button-css {
            font-family: 'Microsoft Yahei', 微软雅黑, Arial, Verdana, sans-serif;
            font-size: 12px;
            height: 25px;
            line-height: 25px;
            display: inline-block;
            padding: 0px 25px;
            background: #444;
            color: #fff;
            text-shadow: 1px 1px 1px rgba(255, 255, 255, .22);
            -webkit-border-radius: 5px;
            -moz-border-radius: 5px;
            border-radius: 5px;

        }

        textarea {
            margin: 0px;
            box-sizing: border-box;
            -moz-box-sizing: border-box;
        }

        a {
            text-decoration: none;
        }

        a:link {
            text-decoration: none;
        }

        a:visited {
            text-decoration: none;
        }

        a:hover {
            text-decoration: none;
        }

        a:active {
            text-decoration: none;
        }

        .noShowPanel {
            display: none;
        }

        .panelStyle {
            height: 150px;
            width: 487px;
            overflow-x: hidden;
            background-color: rgb(243, 243, 243);
            resize: none;
        }

        body {
            font-family: 'Microsoft Yahei', 微软雅黑, Arial, Verdana, sans-serif;
        }

        li {
            margin-left: 16px;
        }

        table.dataTable thead .sorting-disabled {
            background-image: none;
        }
    </style>
    <style>
        tr {
            height: 35px;
        }

        table th {
            background: #919699;
        }
    </style>

</head>

<body>
    <div style="display:inline-block;">
        <span style="font-size:18px;padding-left:5px" id="com_zte_ums_ict_framework_ui_update_patch_choose_host"
            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
    </div>
    <hr style="margin-top:5px;" />
    <div>
        <table id="serviceAndRoleAndHost">
            <thead>
                <tr>
                    <th id="com_zte_ums_ict_framework_ui_static_service"
                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu" style="width: 30%"></th>
                    <th id="com_zte_ums_ict_framework_ui_alarm_host"
                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu" style="width: 70%"></th>
                </tr>
            </thead>
            <tbody></tbody>
            <tfoot></tfoot>
        </table>
    </div>
    <div class="modal fade" id="addDestDlg" data-bs-backdrop="static" tabindex="-1" role="dialog"
        aria-labelledby="myModalLabel"
        style="width:100%; font-family: 'Arial Normal', 'Arial';font-weight: 400;font-style: normal;font-size: 13px;">
        <div class="modal-dialog" role="document" style="width:52%">
            <div class="modal-content" style="width:650px;height:450px;">
                <div class="modal-header">
                    <span id="com_zte_ums_ict_framework_ui_update_patch_choose_host"
                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"
                        style="font-size: 18px;font-weight: 400;font-family:'微软雅黑 Regular', '微软雅黑';font-style: normal"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="cancel()">
                    </button>
                </div>
                <div class="modal-body" style="padding: 0 15px;height:300px;">
                    <div id="commandDispatcherContent"
                        style="margin-top:15px;margin-left:10px;height: 300px;width:600px;">
                        <div style="height: 55%">

                            <div
                                style="padding: 0 0px;border: 1px solid #D8D8D8;display:inline-block;width: 585px;">

                                <div style="width:100%;height:215px;margin-top:10px;margin-bottom: 10px">
                                    <div style="float:left;width:100%;height:250px;margin-left:7px;" id="hostList">
                                        <div style="width:100%;">
                                            <input type="text" placeholder="IP search"
                                                style="border: 1px solid #D8D8D8;width:90%;margin-left:15px;height:23px;padding-left:4px;"
                                                id="hostSearch">
                                        </div>
                                        <div
                                            style="float:left;margin-top:10px;width:40%;height:220px;margin-left: 17px">
                                            <span id="chooseHostDesc" style="display: block;"><span
                                                    id="com_zte_ums_ict_framework_ui_fileDispatcher_To_be_selected"
                                                    name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></span>

                                            <div id="chooseHostArea"
                                                style="border:1px solid #ccc;width:220px;height:190px;margin-top:5px;overflow:scroll;overflow-x:hidden;">
                                            </div>
                                        </div>
                                        <div id="AddandRemove" style="float:left;width:72px;margin-top:45px;">
                                            <a href="#" class="button-css export"
                                                style="color:#004FFF;padding:0px 17px;border: 1px solid #CCCCCC;background: linear-gradient(to bottom,#ffffff 0,#ffffff 100%)"
                                                id="addAll"> &gt;&gt; </a>
                                            <a href="#" class="button-css export"
                                                style="color:#004FFF;padding: 0px 21px;border: 1px solid #CCCCCC;margin-top:10px;background: linear-gradient(to bottom,#ffffff 0,#ffffff 100%);"
                                                id="batchAdd"> &gt; </a>
                                            <a href="#" class="button-css export"
                                                style="color:#004FFF;padding: 0px 21px;border: 1px solid #CCCCCC;margin-top:10px;background: linear-gradient(to bottom,#ffffff 0,#ffffff 100%);"
                                                id="batchRemove"> &lt; </a>
                                            <a href="#" class="button-css export"
                                                style="color:#004FFF;padding: 0px 17px;border: 1px solid #CCCCCC;margin-top:10px;background: linear-gradient(to bottom,#ffffff 0,#ffffff 100%);"
                                                id="removeAll"> &lt;&lt; </a>
                                        </div>
                                        <div style="float:left;margin-top:10px;width:40%;height:200px">
                                            <span id="chooseedHostDesc" style="display: block;"><span
                                                    id="com_zte_ums_ict_framework_ui_fileDispatcher_selected"
                                                    name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></span>
                                            <div id="chooseedHostArea"
                                                style="border:1px solid #ccc;width:220px;height:190px;margin-top:5px;overflow:scroll;overflow-x:hidden;">
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer" style="margin-top: 0px;height: 66px">

                    <button id="confirmHostList" href="javaScript:void(0)" class="dap-btn dap-btn-black dap-btn-right"
                        onclick="confirm()">
                        <span id="com_zte_ums_ict_framework_ui_common_confirm"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </button>
                    <button id="commandCancel" href="javaScript:void(0)" class="dap-btn dap-btn-white"
                        data-bs-dismiss="modal" onclick="cancel()">
                        <span id="com_zte_ums_ict_framework_ui_common_cancle"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="text/javascript" src="../../@iui.common.microservice.name@/lib/jquery/jquery.min.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/lib/bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript"
    src="../../@iui.common.microservice.name@/lib/jquery-datatables/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/i18n/jquery.i18n.properties-1.0.9.js">
</script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/i18n/loadi18n.js"></script>

<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/http/http.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/http/getParamFromLocation.js">
</script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/utils/commonUtil.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/plugin/message.js"></script>

<script type="text/javascript" src="../../js/update/updatePatchSelectHost.js"></script>

</html>