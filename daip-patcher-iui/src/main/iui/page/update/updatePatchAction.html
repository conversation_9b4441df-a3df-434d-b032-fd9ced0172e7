<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7; IE=EmulateIE9"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap-theme.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/css/common/components.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/css/common/table.css"/>

</head>
<style>
    li {
        margin-left: 16px;
    }

    .progressDiv {
        position: relative;
        height: 15px;
        margin-top: 10px;
        margin-bottom: 10px;
        border: 1px solid #D8D8D8;
        border-radius: 5px;
    }

    .fill {
        display: block;
        width: 0%;
        height: 100%;
        background: #87D43D;
        border-radius: 5px;
    }

    .label {
        position: absolute;
        top: -1px;
        left: 0;
        width: 100%;
        height: 100%;
        text-align: center;

    }

    #totalProgLabel {
    color:black;
    }




</style>

<body>

<div class="subtitleForTable" style="display:inline-block;">
    <span id="com_zte_ums_ict_framework_ui_update_patch_action"
           name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
</div>
<hr style="margin-top:5px;"/>


<div id="progressPanel" style="margin-top: 10px">
    <table id="patchInfoTab" style=" border: 1px solid #D8D8D8;">
        <thead style="background-color: #919699">
        <tr style="height: 40px;">
            <th width="40%" align="center">
                <span id="com_zte_ums_ict_framework_ui_update_patch_name"
                      name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
            </th>
            <th width="30%" align="center">
                <span id="com_zte_ums_ict_framework_ui_update_patch_progress"
                      name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
            </th>
        </tr>
        </thead>

        <tbody id="patchInfo" style="background-color: #F8F8F8;">

        </tbody>
    </table>
</div>


<div class="modal fade" id="updatePatchErr" data-bs-backdrop="static" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel"
     style="width:100%; font-family: 'Arial Normal', 'Arial';font-weight: 400;font-style: normal;font-size: 13px;">
    <div class="modal-dialog" role="document" style="width:50%">
        <div class="modal-content" style="width: 580px;height: 430px">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle"><span
                        id="com_zte_ums_ict_framework_ui_update_patch_failed_info"
                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                        onclick="cancel()">
                </button>
            </div>
            <div class="modal-body" style="padding: 0 15px;">
                <div style="width:100%;height:300px;margin-top:10px">
                    <div style="float:left;width:100%;height:300px;overflow:auto;overflow-x:hidden;background-color:#F8F8F8;;margin-bottom:10px;border-bottom:1px solid #e5e5e5">
                        <div id="resultList" style="margin-left: 20px"></div>
                    </div>
                </div>
            </div>


            <div class="modal-footer" style="margin-top: 0px">
                <a id="saveBtn" href="javaScript:void(0)" style=" padding-left: 25px;
             padding-right: 25px;" class="button filter"
                   onclick="closeModal()">
                    <span id="com_zte_ums_ict_framework_ui_button_confirm"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </a>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/jquery/jquery.min.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/jquery-datatables/jquery.dataTables.min.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/i18n/jquery.i18n.properties-1.0.9.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/i18n/jquery.i18n.properties-1.0.9.js">
</script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/i18n/loadi18n.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/http/http.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/http/getParamFromLocation.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/security/escapeHtml.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/utils/commonUtil.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/plugin/message.js"></script>

<script type="text/javascript"
        src="../../js/update/updatePatchAction.js"></script>
</body>
</html>
