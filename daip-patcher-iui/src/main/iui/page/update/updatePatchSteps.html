<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7; IE=EmulateIE9"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>

    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/css/common/components.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/css/common/common.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../css/jquery-steps.css"/>

</head>
<body>
<div style="margin-bottom: 10px; padding-left: 5px;">
        <span id="com_zte_ums_ict_framework_ui_update_patch_action"
              name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"
              style="font-size: 20px; font-weight: 600;"></span>
    <div style="float:right;height:25px;margin-top:3px;margin-right:5px">
        <b><span id="com_zte_ums_ict_framework_ui_cluster_current"
                 name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></b>
        <span id="currentClusterSelect"></span>
    </div>
</div>
<div id="steps"></div>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/jquery/jquery.min.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/bootstrap/js/bootstrap.min.js"></script>

<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/i18n/jquery.i18n.properties-1.0.9.js">
</script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/i18n/loadi18n.js"></script>

<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/http/http.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/utils/commonUtil.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/plugin/message.js"></script>
<script type="text/javascript" src="../../js/common/jquery-steps.js"></script>
<script type="text/javascript" src="../../js/update/updatePatchStep.js"></script>
</body>
</html>