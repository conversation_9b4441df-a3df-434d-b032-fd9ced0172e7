<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7; IE=EmulateIE9"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/css/common/table.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/css/common/components.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../css/fileinput.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/jquery-datatables/jquery.dataTables.min.css"/>
    <style>
        .rollbackBtn {
            color: #F42928;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 2px 10px;
        }

        .rollbackBtn:hover {
            color: #F42928;
            background: #e0e0e0;
        }

        .op, .dataTables_filter {
            margin-bottom: 8px;
        }

        .dataTables_filter select {
            height: 28px;
            margin-right: 10px;
            margin-left: 5px
        }

        table td:first-child {
            padding-bottom: 8px;
            padding-top: 8px
        }

        table td a {
            display: inline-block;
            padding: 0;
            margin: 0 5px;
            background: none;
            color: #0082D5;
        }

        table td a:hover {
            background: none;
            color: #0082D5;
        }


    </style>

</head>

<body>
<div>
    <div id="rollbackPage">

        <div class="dataTables_filter">
            <a onclick="rollBack();" href="#" id="rollbackBtn" class="button">
                <span id="com_zte_ums_ict_framework_ui_button_rollback"
                      name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu">
                </span>
            </a>

            <span id="com_zte_ums_ict_framework_ui_alarm_current_keywords"
                  name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
            <input type="text" id="patchSearch3"/>
            <a href="#" id="reset" class="button btns">
                <span id="com_zte_ums_ict_framework_ui_button_reset"
                      name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
            </a>
            <a href="#" id="search" class="button btns" style="margin-right: 5px;">
                <span id="com_zte_ums_ict_framework_ui_button_search"
                      name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
            </a>
        </div>
        <table id="rollbackPatchTable">
            <thead>
            <tr>
                <th><span id="com_zte_ums_ict_framework_ui_update_patch_upgrade_time"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                <th><span id="com_zte_ums_ict_framework_ui_update_patch_id"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                <th><span id="com_zte_ums_ict_framework_ui_update_patch_table_type"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                <th><span id="com_zte_ums_ict_framework_ui_control_host_table"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </th>
            </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</div>

<!-- confirem Modal -->
<div class="modal fade" id="hostDialog">
    <div class="modal-dialog" style="width: 846px;">
        <div class="modal-content" style="width: 846px;">
            <div class="modal-header">
                <h4 class="modal-title" id="title">
                    <span id="com_zte_ums_ict_framework_ui_button_more"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="height: 386px;overflow-y: scroll">
                <div>
                    <span id="com_zte_ums_ict_framework_ui_federal_search"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span><input
                        type="text" id="searchHost" style="width: 270px"/>
                </div>
                <div style="padding: 15px 0">
                    <span id="com_zte_ums_ict_framework_ui_control_host_table"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    (<span id="hostCount"> </span>) :
                    <div id="hostSwitch" style="float:right">
                        <input type="radio" name="hostRadio" value="hostName"/>
                        <span id="com_zte_ums_ict_framework_ui_alarm_hostname"
                              name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span> /
                        <input type="radio" name="hostRadio" value="hostIp" checked="true"/>
                        <span id="com_zte_ums_ict_framework_ui_host_ip_address"
                              name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </div>
                </div>
                <div id="hostContain"></div>
            </div>
            <div class="modal-footer">
                <a class="button" id="btn-default" data-bs-dismiss="modal">
                    <span id="com_zte_ums_ict_framework_js_chart_utils_close"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </a>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
</body>


<script type="text/javascript"
        src="/dapmanager-web/res/web-framework/js/jquery/jquery.min.js"></script>
<script type="text/javascript"
        src="/dapmanager-web/res/web-framework/js/bootstrap-3.3.5-dist/js/bootstrap.min.js"></script>
<script type="text/javascript"
        src="/dapmanager-web/res/web-framework/js/common/fileinput.min.js"></script>
<script type="text/javascript"
        src="/dapmanager-web/res/web-framework/js/common/fileinput_zh.js"></script>
<script type="text/javascript"
        src="/dapmanager-web/res/web-framework/js/jquery-ui/jquery-ui.min.js"></script>
<script type="text/javascript"
        src="/dapmanager-web/res/web-framework/js/jquery.dataTables.min.js"></script>
<script type="text/javascript"
        src="/dapmanager-web/res/web-framework/js/common/loadJsWithVersion.js"></script>
<script type="text/javascript"
        src="/dapmanager-web/res/web-framework/js/common/message.js"></script>
<script type="text/javascript">
        loadJs(
                [
                    "/dapmanager-web/res/web-framework/js/common/commonUtil.js",
                    "/dapmanager-web/res/web-framework/js/common/http.js",
                    "/dapmanager-web/res/web-framework/js/common/security.js",
                    "/dapmanager-web/res/web-framework/js/common/page.js",
                    "/dapmanager-web/res/web-framework/js/getParamFromLocation.js",
                    "/dapmanager-web/res/web-framework/i18n/jquery.i18n.properties-1.0.9.js",
                    "/web/res/web-common/comp/tools.js",
                    "/dapmanager-web/res/web-framework/i18n/loadi18n.js",
                    "/dapmanager-web/res/web-framework/js/update/rollbackPatch.js"
                ]
        );


</script>
</html>
