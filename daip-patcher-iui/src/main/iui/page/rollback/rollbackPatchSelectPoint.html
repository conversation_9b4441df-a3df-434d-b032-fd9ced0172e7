<!DOCTYPE html
        PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7; IE=EmulateIE9"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap-dialog.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/lib/bootstrap/css//bootstrap-theme.min.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/css/common/components.css"/>
    <link rel="stylesheet" type="text/css"
          href="../../@iui.common.microservice.name@/css/common/table.css"/>

    <style>
        .input_area {
            position: absolute;
            width: 487px;
            height: 55px;
            font-family: 'Arial Normal', 'Arial';
            font-weight: 400;
            font-style: normal;
            font-size: 13px;
            text-decoration: none;
            color: #000000;
            text-align: left;
            border-color: #D8D8D8;
            outline-style: none;
            margin-left: 5px;
            resize: none;
        }

        .ipbgcolor {
            background-color: #0081c2;
        }

        .button-css {
            font-family: 'Microsoft Yahei', 微软雅黑, Arial, Verdana, sans-serif;
            font-size: 12px;
            height: 25px;
            line-height: 25px;
            display: inline-block;
            padding: 0px 25px;
            background: #444;
            color: #fff;
            text-shadow: 1px 1px 1px rgba(255, 255, 255, .22);
            -webkit-border-radius: 5px;
            -moz-border-radius: 5px;
            border-radius: 5px;

        }

        textarea {
            margin: 0px;
            box-sizing: border-box;
            -moz-box-sizing: border-box;
        }

        a {
            text-decoration: none;
        }

        a:link {
            text-decoration: none;
        }

        a:visited {
            text-decoration: none;
        }

        a:hover {
            text-decoration: none;
        }

        a:active {
            text-decoration: none;
        }

        .noShowPanel {
            display: none;
        }

        .panelStyle {
            height: 150px;
            width: 487px;
            overflow-x: hidden;
            background-color: rgb(243, 243, 243);
            resize: none;
        }

        body {
            font-family: 'Microsoft Yahei', 微软雅黑, Arial, Verdana, sans-serif;
        }

        li {
            margin-left: 16px;
        }

        table.dataTable thead .sorting-disabled {
            background-image: none;
        }


    </style>
    <style>
        tr {
            height: 35px;
        }

        table th {
            background: #919699;
        }



    </style>

</head>

<body>
<div style="display:inline-block;">
        <span style="font-size:18px;padding-left:5px"
              id="com_zte_ums_ict_framework_ui_update_patch_choose_service"
              name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
</div>
<hr style="margin-top:5px;"/>
<div>
    <table id="serviceAndRollbackPoint">
        <thead>
        <tr>
            <th>
                <input type="checkbox" name="checkAll" id="checkAllService" onchange="selectAllService()"/>
            </th>
            <th id="com_zte_ums_ict_framework_ui_static_service"
                name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu" style="width: 30%"></th>
            <th id="com_zte_ums_ict_framework_ui_update_patch_rollback"
                name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu" style="width: 70%"></th>
        </tr>
        </thead>
        <tbody></tbody>
        <tfoot></tfoot>
    </table>
</div>
</body>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/jquery/jquery.min.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/lib/jquery-datatables/jquery.dataTables.min.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/i18n/jquery.i18n.properties-1.0.9.js">
</script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/i18n/loadi18n.js"></script>

<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/http/http.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/http/getParamFromLocation.js">
</script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/utils/commonUtil.js"></script>
<script type="text/javascript"
        src="../../@iui.common.microservice.name@/js/common/plugin/message.js"></script>

<script type="text/javascript" src="../../js/rollback/rollbackPatchSelectPoint.js"></script>

</html>