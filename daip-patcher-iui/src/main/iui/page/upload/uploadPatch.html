﻿<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7; IE=EmulateIE9" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <link rel="stylesheet" type="text/css" href="../../@iui.common.microservice.name@/lib/layui/css/layui.css"/>
    <link rel="stylesheet" type="text/css" href="../../@iui.common.microservice.name@/lib/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="../../@iui.common.microservice.name@/css/common/components.css" />
    <link rel="stylesheet" type="text/css" href="../../@iui.common.microservice.name@/css/common/table.css" />
    <link rel="stylesheet" type="text/css" href="../../@iui.common.microservice.name@/css/common/common.css" />

    <link rel="stylesheet" type="text/css" href="../../css/fileinput.min.css" />
    <link rel="stylesheet" type="text/css"
        href="../../@iui.common.microservice.name@/lib/jquery-datatables/jquery.dataTables.min.css" />
    <link rel="stylesheet" type="text/css"
        href="../../@iui.common.microservice.name@/css/font-awesome-4.7.0/css/font-awesome.min.css" />

    <style>
        .rollbackBtn {
            color: #F42928;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 2px 10px;
        }

        .rollbackBtn:hover {
            color: #F42928;
            background: #e0e0e0;
        }

        .op,
        .dataTables_filter {
            margin-bottom: 8px;
        }

        .dataTables_filter select {
            height: 28px;
            margin-right: 10px;
            margin-left: 5px
        }

        table td:first-child {
            padding-bottom: 8px;
            padding-top: 8px
        }

        table td a {
            display: inline-block;
            padding: 0;
            margin: 0 5px;
            background: none;
            color: #0082D5;
        }

        table td a:hover {
            color: #0082D5;
        }

        #deletePatch {
            padding-left: 4px;
            color: #3366cc
        }

        #deletePatch.disabled {
            color: #cccccc;
        }

        div.progress {
            position: relative;
            width: 100%;
            height: 20px;
            border: 1px solid #999;
        }

        span.success {

            color: #50be0b
        }

        span.error {
            color: #F42928
        }

        span.initial {
            color: #CCCCCC
        }

        .clickable {
            cursor: pointer;
        }

        .webuploader-element-invisible {
            position: absolute !important;
            clip: rect(1px 1px 1px 1px);
            /* IE6, IE7 */
            clip: rect(1px, 1px, 1px, 1px);
        }

        .layui-table td,
        .layui-table th {
            padding: 10px 3px;
        }
    </style>

</head>

<body>
    <div id="patchPage">
        <div class="op">
            <div class="batch-actions" style="margin-top:3px;display: inline-block;">
                <button id="uploadPrepareBtn"  class="dap-btn dap-btn-black" data-bs-toggle="modal" data-bs-target="#uploadDialog" operation="operation.daip.patcher.update.patcher.upload">
                    <span id="com_zte_ums_ict_framework_ui_button_upload_patch"
                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </button>
                <button href="#" id="dispatcherBtn" class="dap-btn dap-btn-black disabled"
                    operation="operation.daip.patcher.update.patcher.dispatcher">
                    <span id="com_zte_ums_ict_framework_ui_dispatcher"
                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </button>
                <button href="#" id="delBtn" class="dap-btn dap-btn-black disabled">
                    <span id="com_zte_ums_ict_framework_ui_delete"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </button>
                <button style="display:none;" href="#" id="updateBtn" class="dap-btn dap-btn-black disabled">
                    <span id="com_zte_ums_ict_framework_ui_button_update"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </button>
                <button style="display:none;" href="#" id="rollbackBtn" class="dap-btn dap-btn-black">
                    <span id="com_zte_ums_ict_framework_ui_button_rollback"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </button>
                <button href="#" id="onekeyupdateBtn" class="dap-btn dap-btn-black"
                        operation="operation.daip.patcher.update.patcher.update">
                    <span id="com_zte_ums_ict_framework_ui_button_one_key_upgrade"
                          name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                </button>
                <span id="com_zte_ums_ict_framework_ui_non_ms_patch_msg"
                    name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                <span id="com_zte_ums_ict_framework_ui_patch_need_update"
                    name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu" style="color: #FF0000"></span>

            </div>

        </div>

        <div class="dataTables_filter">
            <span id="com_zte_ums_ict_framework_ui_update_patch_type"
                name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu" style="margin-right: 5px"></span>:
            <select id="patchCate"></select>
            <span id="com_zte_ums_ict_framework_ui_alarm_current_keywords"
                name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
            <input type="text" id="patchSearch" />
            <a href="#" id="reset" class="button btns">
                <span id="com_zte_ums_ict_framework_ui_button_reset"
                    name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
            </a>
            <a href="#" id="search" class="button btns" operation="operation.daip.patcher.update.patcher.view" style="margin-right: 5px;">
                <span id="com_zte_ums_ict_framework_ui_button_search"
                    name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
            </a>
        </div>
        <table id="patchTable">
            <thead>
                <tr>
                    <th><input type="checkbox" name="checkAll" id="checkAll" /></th>
                    <th><span id="com_zte_ums_ict_framework_ui_update_patch_id"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                    <th><span id="com_zte_ums_ict_framework_ui_update_patch_table_type"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                    <th><span id="com_zte_ums_ict_framework_ui_update_patch_base_version"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                    <th><span id="com_zte_ums_ict_framework_ui_update_patch_size"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                    <th><span id="com_zte_ums_ict_framework_ui_update_patch_upload_time"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                    <th><span id="com_zte_ums_ict_framework_ui_update_patch_desc_supplement"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                    <th><span id="com_zte_ums_ict_framework_ui_dispatcher_results"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </th>
                    <th><span id="com_zte_ums_ict_framework_ui_update_patch_pending_hosts_number"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                    <th><span id="com_zte_ums_ict_framework_ui_update_patch_desc"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                    <th><span id="com_zte_ums_ict_framework_ui_update_patch_comment"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                    <th><span id="com_zte_ums_ict_framework_ui_button_operation"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>


    <div class="modal fade" id="uploadDialog" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        data-bs-backdrop="static" aria-hidden="false">
        <div class="modal-dialog" style="width: 1066px;">
            <div class="modal-content" style="width: 1066px;">
                <div class="modal-header">

                    <h4 class="modal-title"><span id="com_zte_ums_ict_framework_ui_button_upload_patch"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body" >

                    <button class="dap-btn dap-btn-black" id="filePicker">
                        <span id="com_zte_ums_ict_framework_ui_button_choose_file"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </button>

                    <button class="dap-btn dap-btn-black" id="uploads" data-name="upload">
                        <span id="com_zte_ums_ict_framework_ui_button_batch_upload"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </button>

                    <button class="dap-btn dap-btn-black" id="stops" data-name="uploadStop">
                        <span id="com_zte_ums_ict_framework_ui_button_batch_upload_stop"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </button>

                    <button class="dap-btn dap-btn-black" id="cancels" data-name="uploadCancel">
                        <span id="com_zte_ums_ict_framework_ui_button_batch_upload_cancel"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </button>
                    <form class="layui-form" lay-filter="uploadFormFilter">

                        <table class="layui-table" lay-skin="line" style="min-height: 350px;max-height: 450px; display: block;overflow-y: auto; width: 100%;">
                            <colgroup>
                                <col width="0" />
                                <col width="200" />
                                <col width="90" />
                                <col width="250" />
                                <col width="110" />
                                <col width="160" />
                                <col width="200" />
                            </colgroup>
                            <thead>
                                <tr>
                                    <th>
                                        <input id="checkBoxAllId" type="checkbox" lay-skin="primary"
                                            value="checkBoxAllValue" />
                                    </th>
                                    <th><span id="com_zte_ums_ict_framework_ui_filename"
                                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                                    <th><span id="com_zte_ums_ict_framework_ui_filesize"
                                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                                    <th><span id="com_zte_ums_ict_framework_ui_upload_progress"
                                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                                    <th><span id="com_zte_ums_ict_framework_ui_upload_speed"
                                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                                    <th><span id="com_zte_ums_ict_framework_ui_upload_status"
                                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                                    <th><span id="com_zte_ums_ict_framework_ui_button_operation"
                                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span></th>
                                </tr>
                            </thead>
                            <tbody id="fileLists">
                            </tbody>
                        </table>

                    </form>

                </div>
                <div class="modal-footer">
                    <a class="button" id="btn-default" data-bs-dismiss="modal">
                        <span id="com_zte_ums_ict_framework_js_chart_utils_close"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- 补丁分发主机信息列表 -->
    <div class="modal fade" id="dispatchHostDialog">
        <div class="modal-dialog" style="width: 846px;">
            <div class="modal-content" style="width: 846px;">
                <div class="modal-header">

                    <h4 class="modal-title" id="patchName"></h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height: 350px;max-height: 500px;overflow-y: auto; width: 100%;">
                    <table id="dispatchHostInfoTable">
                        <thead>
                            <tr>
                                <th><span id="com_zte_ums_ict_framework_ui_host_ip_address"
                                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                                </th>
                                <th><span id="com_zte_ums_ict_framework_ui_update_patch_dispatch_time"
                                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                                </th>
                                <th><span id="com_zte_ums_ict_framework_ui_status"
                                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                                </th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <a class="button" id="btn-default" data-bs-dismiss="modal">
                        <span id="com_zte_ums_ict_framework_js_chart_utils_close"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 补丁升级主机信息列表 -->
    <div class="modal fade" id="historyHostDialog">
        <div class="modal-dialog" style="width: 846px;">
            <div class="modal-content" style="width: 846px;">
                <div class="modal-header">
                    <h4 class="modal-title" id="historyPatchName"></h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="min-height: 300px;max-height: 450px;overflow-y: auto; width: 100%;">
                    <table id="historyHostInfoTable">
                        <thead>
                            <tr>
                                <th><span id="com_zte_ums_ict_framework_ui_host_ip_address"
                                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                                </th>
                                <th><span id="com_zte_ums_ict_framework_ui_update_patch_upgrade_time"
                                        name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                                </th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <a class="button" id="btn-default" data-bs-dismiss="modal">
                        <span id="com_zte_ums_ict_framework_js_chart_utils_close"
                            name_i18n="com_zte_ums_ict_framework_ui_i18n_sideMenu"></span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="text/javascript" src="../../@iui.common.microservice.name@/lib/jquery/jquery.min.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/lib/bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/lib/layui/layui.all.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/lib/jquery-datatables/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/lib/webuploader/webuploader.min.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/lib/webuploader/chunkUpload.js"></script>

<script type="text/javascript" src="../../@iui.common.microservice.name@/js/i18n/jquery.i18n.properties-1.0.9.js">
</script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/i18n/loadi18n.js"></script>

<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/http/http.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/http/getParamFromLocation.js">
</script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/security/escapeHtml.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/utils/commonUtil.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/utils/prototype.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/plugin/message.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/utils/domUtil.js"></script>
<script type="text/javascript" src="../../js/upload/dispatchPatch.js"></script>
<script type="text/javascript" src="../../js/upload/uploadPatch.js"></script>
<script type="text/javascript" src="../../@iui.common.microservice.name@/js/common/security/security.js"></script>

</html>