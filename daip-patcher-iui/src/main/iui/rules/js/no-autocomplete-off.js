module.exports = {
    meta: {
        docs: {
            description: "必须关闭密码框自动填充功能",
        }
    },

    create: function (context) {

        function checkLiteral (node) {
            const content =
                (node.type === "Literal" && node.value) ||
                (node.type === "TemplateLiteral" && node.quasis[0].value.cooked);

            if (checkAutocomplete(content)) {
                context.report({
                    node,
                    message: "必须关闭密码框自动填充功能",
                });
            }
        }

        function checkAutocomplete (content) {
            function isString (str) {
                return (typeof str == 'string') && str.constructor == String;
            }
            if (!content || !isString(content)) {
                return false
            }
            return /[\w\s]*\s*type\s*=\s*'password'[\w\s]*/.test(content) && !/[\w\s]*\s*autocomplete\s*=\s*'off'[\w\s]*/.test(content)

        }
        return {
            // 文本
            Literal: checkLiteral,
            // 模板字符串
            TemplateLiteral: checkLiteral
        };
    }
};