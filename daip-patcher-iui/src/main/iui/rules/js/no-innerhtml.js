module.exports = {
    /*
    create ( function ) 返回一个对象，其中包含了 ESLint 在遍历 JavaScript 代码
    的抽象语法树 AST ( ESTree 定义的 AST ) 时，用来访问节点的方法
    */
    create: function (context) {
        return {
            Identifier: function (node) {
                var name = (node.name || "").toLowerCase()
                if (name === "innerhtml") {
                    /*
                     它用来发布警告或错误（取决于你所使用的配置）
                    */
                    context.report({
                        node: node,
                        message: 'innerText代替innerHtml'
                    });
                }
            }
        };
    }
};