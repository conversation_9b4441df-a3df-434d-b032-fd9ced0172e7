require("../utils");

module.exports = {
    schema: [{
        "type": "array",
        "items": {

            "type": "string"
        },
        "minItems": 1
    }],
    create: function (context) {
        return {
            CallExpression: function (node) {
                // 如果函数名为md5,des，则报错

                var functionObj = node.callee || { name: "" }

                var insecureAlg = context.options.length && context.options[0]


                var functionName = (functionObj.name || (functionObj.property && functionObj.property.name) || "").toLowerCase()

                if (insecureAlg  instanceof Array && insecureAlg.some(function (item) {
                    return functionName.endWith(item)
                })) {
                    context.report({
                        node: node,
                        message: '不安全密码算法'
                    });
                }
            }
        };
    }
};