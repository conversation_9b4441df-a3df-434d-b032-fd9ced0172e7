module.exports = {
    create: function (context) {
        return {
            CallExpression: function (node) {

                var functionObj = node.callee || { name: "" }

                var functionName = (functionObj.name || (functionObj.property && functionObj.property.name) || "").toLowerCase()
                if (functionName.indexOf('decrypt') > -1) {
                    context.report({
                        node: node,
                        message: '不能使用解密算法'
                    });
                }

            }
        }
    }
};