module.exports = function (HTMLHint) {
    HTMLHint.addRule({
        id: 'no-autocomplete-off',
        description: 'no-autocomplete-off',
        init: function (parser, reporter) {
            var self = this;
            parser.addListener('tagstart', function (event) {
                var tagName = event.tagName,
                    attrs = event.attrs,
                    attr,
                    col = event.col + tagName.length + 1;
                if (tagName === 'input') {

                    for (var i = 0, l = attrs.length; i < l; i++) {
                        attr = attrs[i];
                        if (attr.name.toLowerCase() === 'type' && attr.value === 'password') {
                            /*
                            在html中找出密码输入框，如果未设置autocomplete或者autocomplete!=off
                            则扫描提示`必须关闭密码框自动填充功能`
                            */
                            var autocompleteAttr = findAttr('autocomplete', attrs)
                            if (!autocompleteAttr || autocompleteAttr.value !== 'off') {
                                reporter.warn(
                                    `必须关闭密码框自动填充功能`,
                                    event.line,
                                    col + attr.index,
                                    self,
                                    attr.raw
                                )
                            }
                        }
                    }

                }
            });

            function findAttr (attrName, attrs) {
                if (!attrName || attrs.length == 0) {
                    return null
                }
                return attrs.find(function (item) {
                    return item.name.toLowerCase() == attrName.toLowerCase()
                });
            }
        }
    });
}
