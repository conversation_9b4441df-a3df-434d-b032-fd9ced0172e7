/**
 * Created by 10204961 on 2018/2/5.
 */
var patch2hosts = {};
$(function () {
    patch2hosts = {};
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', initPatchUpdateInfo);
});

function initPatchUpdateInfo() {
    $Steps.steps("disableNextBtn");

    var sourceData = parent.selectedData;
    var serviceToHosts = [];

    for (var key in sourceData) {
        if (sourceData[key].hosts && sourceData[key].hosts.length > 0) {
            serviceToHosts.push({
                "service": key,
                "unpatchedHosts": sourceData[key].hosts
            })
        }
    }
    var params = {};

    var clusterId = parent.currentClusterId || $('#clusterSelectId', parent.document).val()

    params[clusterId] = serviceToHosts;

    dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/unpatched/patches/host', JSON.stringify({ clusterServiceHostMap: params })).then(function (result) {
        result = HtmlUtil.jsonEncode(result)
        window.parent.confirmInfo = serviceToHosts;
        !result.length && $Steps.steps("disableNextBtn");
        drawConfirmTab(result);
    })

    !parent.listenValues.isAutoMode && $Steps.steps("enableNextBtn");
    parent.autoAction();

}

function drawConfirmTab(data) {
    $(data).each(function (index, item) {

        var patchTotalDiv = $('<div></div>');

        if (index > 0) {
            patchTotalDiv.css("margin-top", "30px");
        }

        var patchInfo = item.patch;
        var patchName = patchInfo.patchName;
        var serviceName = patchInfo.serviceInstanceId || patchInfo.service
        var roleName = patchInfo.roles;

        var patchInfoDiv = $('<div></div>').css({
            "display": "inline-block",
            "margin-bottom": 5
        });

        var patchNameDiv = $('<div class="patchBlock"></div>');
        $('<span style="color: #999999"></span>').text(geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch")).appendTo(
            patchNameDiv);
        $('<span></span>').text(patchName).appendTo(patchNameDiv);

        var patchServiceDiv = $('<div  class="patchBlock"></div>').css("margin-left", "50px");
        $('<span style="color: #999999"></span>').text(geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_service"))
            .appendTo(patchServiceDiv);
        $('<span></span>').text(serviceName).appendTo(patchServiceDiv);

        var roleNameDiv = $('<div class="patchBlock"></div>').css("margin-left", "50px");
        if (roleName) {
            $('<span style="color: #999999"></span>').text(geti18nPropVal(
                "com_zte_ums_ict_framework_ui_update_patch_role")).appendTo(roleNameDiv);
            $('<span></span>').text(roleName).appendTo(roleNameDiv);
        }

        patchInfoDiv.append(patchNameDiv);
        patchInfoDiv.append(patchServiceDiv);
        patchInfoDiv.append(roleNameDiv);

        var hosts = item.hosts;
        var hostInfoDiv = $('<div></div>').css("margin-left", "10px");
        hostInfoDiv.append($('<div style="color: #999999"></div>').text(geti18nPropVal(
            "com_zte_ums_ict_framework_ui_update_patch_hosts") + "(" + hosts.length + "):"));

        patch2hosts[patchName] = hosts;

        for (var i = 0; i < hosts.length; i++) {
            if (i < 3) {
                hostInfoDiv.append($('<span></span>').text(hosts[i].ip + ";"));
            }
            if (i == 3) {
                hostInfoDiv.append($('<span></span>').html('<a onclick="openMoreDialog(\'' + patchName + '\');">' +
                    geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_detail") + '</a>'));
                break;
            }
        }

        patchTotalDiv.append(patchInfoDiv);
        patchTotalDiv.append(hostInfoDiv);

        $('#confirmTab').append(patchTotalDiv);
    });
}

// eslint-disable-next-line no-unused-vars
function openMoreDialog(patchName) {

    var data = patch2hosts[patchName];

    var $hostDialog = $('#patchHostDialog');
    $hostDialog.data("host", data);
    $('input[name="hostRadio"]').off().on("click", allHostDialog);
    $('#searchHost').off().on("keyup", filterHostDialog);
    fill(data);
    $hostDialog.modal('show');
}

function allHostDialog() {
    var hosts = $('#patchHostDialog').data("host");
    fill(hosts);
}

function fill(data) {
    var displayIp = !($('input[name="hostRadio"]:checked').val() == "hostName");
    var hostArr = hostToIpOrName(data, displayIp);
    $('#hostCount').text(data.length);
    var $ul = $("<ul></ul>");
    for (var i = 0; i < hostArr.length; i++) {
        $ul.append("<li title='" + hostArr[i] + "'>" + hostArr[i] + "</li>");
    }
    $('#hostContain').empty().append($ul);
}

function hostToIpOrName(hosts, displayIp) {
    var result = [];
    for (var i = 0; i < hosts.length; i++) {
        if (!displayIp) {
            result.push(hosts[i].hostName);
        } else {
            result.push(hosts[i].ip);
        }
    }
    return result;
}

function filterHostDialog() {
    var hosts = $('#patchHostDialog').data("host");
    var searchValue = $('#searchHost').val();
    var filter = hosts.filter(function (host) {
        return host.hostName.indexOf(searchValue) != -1 || host.ip.indexOf(searchValue) != -1;
    });
    fill(filter);
}