/**
 * Created by 10204961 on 2018/2/5.
 */
var allHosts = {};
var unSelected = {};
var selected = {};
var searchHostIp;
var curService;
$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', init);
});

function init() {
    allHosts = {};
    window.parent.selectedData = [];
    window.parent.unSelectData = [];
    queryService2Host();
}

function queryService2Host() {
    $Steps.steps("disableNextBtn");
    var params = parent.selectClusterServicesMap;

    dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/unpatched/patches/servicehost', JSON.stringify(params)).then(function (result) {
        if (result && result.length > 0) {
            $(result).each(function (count, info) {
                $(info.unpatchedHosts).each(function (count1, info1) {
                    allHosts[info.service + "_" + info1.ip] = info1;
                });
            });
            drawTable(result);
            if (window.parent.currentClusterId !== "100000") {
                $("#serviceAndRoleAndHost tbody").on("click", "td:nth-child(2)", function () { //给tr或者td添加click事件
                    openModal(this);
                });
            }
            extractedDialogMethod("");
        } else {
            var message = result.errMsg || "error";
            MessageModal.showConfirmWarning(message, geti18nPropVal(
                "com_zte_ums_ict_framework_ui_update_patch_action"));
        }
        !parent.listenValues.isAutoMode && $Steps.steps("enableNextBtn");
        parent.autoAction();
    })
}

function updateParentData(data, service) {
    $(data).each(function (index, item) {
        if (item.service == service) {
            var selectedHostInfo = [];
            $(item.unpatchedHosts).each(function (index1, item1) {
                selectedHostInfo.push(item1);
            });

            var selectedServiceAndRoleAndHost = {
                "service": item.service,
                "hosts": selectedHostInfo
            };
            window.parent.selectedData[item.service] = selectedServiceAndRoleAndHost;
            window.parent.unSelectData[item.service] = [];
        }
    });
}

function drawTable(serviceAndRoleAndHost) {
    DataTablesUtil.initDataTablesOpt({
        "bSort": true,
        "bPaginate": false, //分页
        "bFilter": false, //搜索框
        "bInfo": false, //分页信息

    });

    $('#serviceAndRoleAndHost').DataTable({
        data: serviceAndRoleAndHost,
        columnDefs: [{
            "targets": [0],
            "width": "20%",
            "data": "service"
        },
        {
            "targets": [1],
            "width": "60%",
            "render": function (data, type, row, meta) {
                var service = row.service;

                if (parent.isClusterChange || (row.unpatchedHosts && row.unpatchedHosts.length > 0 && !window.parent.selectedData[
                    service])) {
                    updateParentData(serviceAndRoleAndHost, service);
                }

                if (window.parent.selectedData[service] && window.parent.selectedData[service].hosts.length >
                    0) {

                    var curService = row.service;
                    return writeDataToCell(window.parent.selectedData[curService].hosts);
                }

                var div = "<div name='host'><a style='color: #B4B2B1;width: 90px;text-align:center'>" +
                    geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_click_add") + "</a></div>";
                return div;
            }
        }

        ]
    });
}

function openModal(obj) {
    $('#commandDispatch').show();
    $('#commandCancel').show();
    $('#thisPanel').hide();
    $('#dialogClose').hide();
    $('#AddandRemove a').css('pointer-events', 'auto');
    $('#chooseSource').css('pointer-events', 'auto');

    $('#addDestDlg').modal('show');

    var curCol = obj._DT_CellIndex.column;
    var curRow = obj._DT_CellIndex.row;
    cell = $('#serviceAndRoleAndHost').DataTable().cell(curRow, curCol);
    allUnSelectData = $('#serviceAndRoleAndHost').DataTable().row(obj).data();

    curService = allUnSelectData.service;
    var unSelectHostInfo = [];
    var selectedHostInfo = [];

    if (window.parent.selectedData[curService]) {
        unSelectHostInfo = parent.unSelectData[curService] || [];

        $(parent.selectedData[curService].hosts).each(function (index, item) {
            selectedHostInfo.push(item.ip + '(' + item.hostName + ')');
        });

    } else {
        var data = allUnSelectData;
        $(data.hosts).each(function (index, item) {
            unSelectHostInfo.push(item.ip + '(' + item.hostName + ')');
        })
    }

    showHostList(unSelectHostInfo, selectedHostInfo);
}

function showHostList(unSelectHostInfo, selectedHostInfo) {
    $("#hostSearch").val('');
    $("#chooseHostArea").empty();
    $("#chooseedHostArea").empty();
    var ipNumUnselected = 0;
    var ipNumSelected = 0;
    searchHostIp = [];


    $.each(unSelectHostInfo, function (index, item) {
        var span = $("<span id='modalHostIps' ondblclick='modalTextRemove(this)'></span>");
        span.css("display", "block");
        span.css("cursor", "default");
        span.css("word-wrap", "break-word;");

        span.text(item);
        searchHostIp.push(item);
        ipNumUnselected++;
        $("#chooseHostArea").append(span);
        span.click(function () {
            if ($(this).hasClass("ipbgcolor")) {
                $(this).removeClass("ipbgcolor");
            } else {
                $(this).addClass("ipbgcolor");
            }
        });
    })
    if (selectedHostInfo.length != 0) {
        $.each(selectedHostInfo, function (index, item) {
            var span = $("<span id='modalHostIps' ondblclick='modalTextRemove(this)'></span>");
            span.css("display", "block");
            span.css("cursor", "default");
            span.css("word-wrap", "break-word");

            span.text(item);
            ipNumSelected++;
            $("#chooseedHostArea").append(span);
            span.click(function () {
                if ($(this).hasClass("ipbgcolor")) {
                    $(this).removeClass("ipbgcolor");
                } else {
                    $(this).addClass("ipbgcolor");
                }
            });
        })
    }

    $("#chooseHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_select") + "(" +
        ipNumUnselected + ")");
    $("#chooseedHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_selected") + "(" +
        ipNumSelected + ")");
}

// eslint-disable-next-line no-unused-vars
function modalTextRemove(obj) {
    $("#chooseedHostArea").append($(obj));
    $("#chooseHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_select") + "(" + $("span",
        "#chooseHostArea").length + ")");
    $("#chooseedHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_selected") + "(" + $(
        "span", "#chooseedHostArea").length + ")");
}

function extractedDialogMethod() {
    $("#hostSearch").keyup(function () {
        var rightHostIp = [];
        $('#chooseedHostArea').find('span').each(function () {
            rightHostIp.push($(this).text().trim());
        });
        var leftFillArr = searchHostSourceFill(searchHostIp, rightHostIp);

        var dataGrid_HostIps = $("#hostSearch").val();
        if (dataGrid_HostIps.trim().length == 0) {
            $("#chooseHostArea").empty();
            var num = 0;
            for (var i = 0; i < leftFillArr.length; i++) {
                var span = $("<span  id='modalHostIps' ondblclick='modalTextRemove(this)'></span>");
                span.css("display", "block");
                span.css("cursor", "default");
                span.css("word-wrap", "break-word");
                span.text(leftFillArr[i] + ' ');
                num++;
                $("#chooseHostArea").append(span);
                span.click(function () {
                    if ($(this).hasClass("ipbgcolor")) {
                        $(this).removeClass("ipbgcolor");
                    } else {
                        $(this).addClass("ipbgcolor");
                    }
                });
            }
            $("#chooseHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_select") +
                "(" + num + ")");
        } else {
            $("#chooseHostArea").empty();
            var sourceIps = [];
            var dataGrid_AllIPs = leftFillArr;

            if (dataGrid_AllIPs.length != 0) {
                for (var i = 0; i < dataGrid_AllIPs.length; i++)
                    sourceIps.push(dataGrid_AllIPs[i]);
            }
            var filterArrayIPs = getFilterArrayIPs(dataGrid_HostIps, sourceIps);

            var num = 0;
            for (var i = 0; i < filterArrayIPs.length; i++) {
                var span = $("<span  id='modalHostIps' ondblclick='modalTextRemove(this)'></span>");
                span.css("display", "block");
                span.css("cursor", "default");
                span.css("word-wrap", "break-word");
                span.text(filterArrayIPs[i] + ' ');
                num++;
                span.click(function () {
                    if ($(this).hasClass("ipbgcolor")) {
                        $(this).removeClass("ipbgcolor");
                    } else {
                        $(this).addClass("ipbgcolor");
                    }
                });
                $("#chooseHostArea").append(span);
            }
            $("#chooseHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_select") +
                "(" + num + ")");
        }
    });

    function searchHostSourceFill(sourceHost, selectedHost) {
        var leftFillArr = [];
        $(sourceHost).each(function (index, item) {
            if ($.inArray(item, selectedHost) == -1) {
                leftFillArr.push(item);
            }
        });
        return leftFillArr;
    }

    $("#addAll").click(function () {
        if ($("#chooseHostArea").html().length > 0) {
            $("#chooseedHostArea").append($("#chooseHostArea").html());
            $("#chooseHostArea").empty();
            $("#chooseHostDesc").text(geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_select_zero"));
            $("#chooseedHostDesc").text("" + geti18nPropVal(
                "com_zte_ums_ict_framework_ui_fileDispatcher_addDest_selected") + "(" + $("span",
                    "#chooseedHostArea").length + ")");
            bindEvent("chooseedHostArea");
        }
    });

    $("#batchAdd").click(function () {
        $("span", "#chooseHostArea").each(function () {
            if ($(this).hasClass("ipbgcolor")) {
                $(this).removeClass("ipbgcolor");
                $("#chooseedHostArea").append($(this));
            }
        });
        $("#chooseHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_select") + "(" +
            $("span", "#chooseHostArea").length + ")");
        $("#chooseedHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_selected") +
            "(" + $("span", "#chooseedHostArea").length + ")");
    });

    $("#batchRemove").click(function () {
        searchHostIp = [];
        $("span", "#chooseedHostArea").each(function () {
            if ($(this).hasClass("ipbgcolor")) {
                $(this).removeClass("ipbgcolor");
                $("#chooseHostArea").append($(this));
            }
        });
        $("#chooseHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_select") + "(" +
            $("span", "#chooseHostArea").length + ")");
        $("#chooseedHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_selected") +
            "(" + $("span", "#chooseedHostArea").length + ")");
        $('#chooseHostArea').find('span').each(function () {
            searchHostIp.push($(this).text().trim());
        });
    });

    $("#removeAll").click(function () {
        searchHostIp = [];
        if ($("#chooseedHostArea").html().length > 0) {
            $("#chooseHostArea").append($("#chooseedHostArea").html());
            $("#chooseedHostArea").empty();
            $("#chooseedHostDesc").text(geti18nPropVal(
                "com_zte_ums_ict_framework_ui_fileDispatcher_addDest_selected_zero"));
            $("#chooseHostDesc").text("" + geti18nPropVal("com_zte_ums_ict_framework_ui_fileDispatcher_addDest_select") +
                "(" + $("span", "#chooseHostArea").length + ")");
            bindEvent("chooseHostArea");
        }
        $('#chooseHostArea').find('span').each(function () {
            searchHostIp.push($(this).text().trim());
        });
    });
}

function bindEvent(id) {
    $("span", "#" + id).each(function () {
        $(this).unbind();
        $(this).click(function () {
            if ($(this).hasClass("ipbgcolor")) {
                $(this).removeClass("ipbgcolor");
            } else {
                $(this).addClass("ipbgcolor");
            }
        });
    });
}

function cancel() {
    $('#addDestDlg').modal('hide');
}

function confirm() {
    parent.isClusterChange = false;
    var unSelectHostInfo = [];
    var selectedHostInfo = [];
    $('#chooseedHostArea span').each(function () {
        var hostInfoKey = allUnSelectData.service + "_" + $(this).text().split("(")[0];
        var host = allHosts[hostInfoKey];
        selectedHostInfo.push(host);
    });
    $('#chooseHostArea span').each(function () {
        unSelectHostInfo.push($(this).text());
    });
    var selectedServiceAndRoleAndHost = {
        "service": allUnSelectData.service,
        "hosts": selectedHostInfo
    };
    window.parent.selectedData[curService] = selectedServiceAndRoleAndHost;
    window.parent.unSelectData[curService] = unSelectHostInfo;

    cell.data("").draw();
    cancel();
}

function writeDataToCell(data) {
    var span = '';
    var div = '';

    $(data).each(function (index, item) {
        if (index < 3) {
            span += '<span>' + item.ip + '(' + item.hostName + ')' + '</span>';
            span += "&nbsp;&nbsp"
        }
    });
    var hostIpLength = data.length;
    span += '<a style="display: inline-block;">' + geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_total") + '' +
        hostIpLength + '' + geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_num") + '</a>';
    div += '<div>' + span + '</div>';
    return div;
}

function getFilterArrayIPs(dataGrid_HostIps, sourceIps) {
    var tempArray = [];
    var dataGrid_HostIp = [];
    if (dataGrid_HostIps != null) {
        dataGrid_HostIp = dataGrid_HostIps.split(",");
    }
    for (var i = 0; i < sourceIps.length; i++) {
        for (var j = 0; j < dataGrid_HostIp.length; j++) {
            if (dataGrid_HostIp[j].trim() != '') {
                if ((sourceIps[i].trim().indexOf(dataGrid_HostIp[j].trim())) >= 0) {
                    if ($.inArray(sourceIps[i], tempArray) == -1) {
                        tempArray.push(sourceIps[i]);
                    }
                }
            }
        }
    }
    return tempArray;
}

// eslint-disable-next-line no-unused-vars
function toConfirmPatch() {
    if ($.isEmptyObject(parent.selectedData) || !isSelectedHost(parent.selectedData)) {
        var failStr = geti18nPropVal("com_zte_ums_ict_framework_ui_down_rolling_restart_no_host");
        MessageModal.showConfirmNormal(failStr, geti18nPropVal(
            "com_zte_ums_ict_framework_ui_service_rolling_restart_select_host_modal"));
        return;
    }
    $Steps.steps("nextStep");
}

function isSelectedHost(data) {
    for (var serviceKey in data) {
        if (data[serviceKey].hosts && data[serviceKey].hosts.length > 0) {
            return true;
        }
    }
    return false;
}
