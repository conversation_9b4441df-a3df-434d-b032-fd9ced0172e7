/**
 * Created by 10204961 on 2018/2/5.
 */
$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', initMethod);
});
var currentClusterId = $('#clusterSelectId', parent.document).val();

function initMethod () {
    var clusterServicesMap = parent.parent.clusterServicesMap || {}
    $Steps.steps("hidePreBtn");
    $('#clusterSelectId', parent.document).prop('disabled', false);
    queryUnpatchedClusterAndServiceInstance();
    initClusterService(clusterServicesMap[currentClusterId]);
    $('#allService').on('click', 'input[name="checkService"]', chooseService);
    $('#allServiceClick').on('click', chooseAllService);
    //appendAutoBtn();
    $Steps.steps("enableNextBtn");
}

function chooseService () {
    if (isAllServiceSelected()) {
        $('#allServiceClick').prop("checked", true);
    } else {
        $('#allServiceClick').prop("checked", false);
    }
}

function chooseAllService () {
    if ($('#allServiceClick').prop('checked')) {
        $('#allService input[name="checkService"]:not(:disabled)').prop('checked', true);
    } else {
        $('#allService input[name="checkService"]:not(:disabled)').prop('checked', false);
    }
}

function isAllServiceSelected () {
    var flag = true;
    $('#allService').find('input[name="checkService"]').each(function () {
        if (!$(this).prop('checked')) {
            flag = false;
        }
    });
    return flag;
}

function queryUnpatchedClusterAndServiceInstance () {
    // PatchSelectClusterServices
    var clusterAndServiceInstanceMap = { clusterServicesMap: {} }
    window.parent.clusterAndServiceInstanceMap = clusterAndServiceInstanceMap
    dapHttpRequest.getRest('/api/daip-patcher-handler/v1/patches/unpatched/cluster/serviceinstance').then(function (response) {
        clusterAndServiceInstanceMap.clusterServicesMap = response.clusterServiceInstanceMap;
        window.parent.clusterAndServiceInstanceMap = clusterAndServiceInstanceMap
    })

}

function initClusterService (allServices) {

    $(allServices).each(function (index, item) {
        item = HtmlUtil.htmlEncodeByRegExp(item);
        var serviceDiv = $('<div></div>');
        serviceDiv.css({
            "display": "inline-block",
            "padding-left": "30px",
            "padding-top": "5px"
        });
        serviceDiv.append('<input name="checkService" checked value="' + item + '" type="checkbox"/><span>' + item +
            '</span>');
        $('#allService').append(serviceDiv);
    });
    chooseService();
    setDisabled();
}

function setDisabled () {
    $('input[value="agent"]').prop("disabled", true);
}

// eslint-disable-next-line no-unused-vars
function toConfirmPatch () {
    var services = [];
    var clusterIds = [];
    var selectClusterServicesMap = { clusterServicesMap: {} }


    $('#allService').find('input[name="checkService"]').each(function () {
        if ($(this).prop('checked')) {
            services.push($(this).val());
        }
    });

    if ($.isEmptyObject(services)) {
        var failStr = geti18nPropVal("com_zte_ums_ict_framework_ui_update_select_service_cluster_tip");
        MessageModal.showConfirmNormal(failStr);
        return;
    }
    selectClusterServicesMap.clusterServicesMap[currentClusterId] = services

    window.parent.selectClusterServicesMap = selectClusterServicesMap;

    if (window.parent.clusterIds) {
        var lastClusterIds = window.parent.clusterIds;
        window.parent.isClusterChange = (JSON.stringify(lastClusterIds.sort()) != JSON.stringify(clusterIds.sort()));
    }

    window.parent.clusterIds = clusterIds;
    $('#clusterSelectId', parent.document).prop('disabled', true);

    // $Steps.steps("showOrHideCustomBtn", "autoUpdateBtn", false);
    $Steps.steps("showPreBtn");
    $Steps.steps("nextStep");
}
