$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', init);
});

function init () {
    $Steps.steps("setStepBtnStatus", {
        showPreBtn: false,
        showNextBtn: false,
        showContinueBtn: true,
        showFinishBtn: true,
    });
    initReport();
    initStopOrStartServiceInfo();
}

function initStopOrStartServiceInfo () {
    if (parent.isStopFail) {
        $('#stopOrStartServiceInfo').html(geti18nPropVal('com_zte_ums_ict_framework_ui_update_patch_stop_failure'));
        $('#stopOrStartServiceInfo').show();
    }
    if (parent.isStartFail) {
        $('#stopOrStartServiceInfo').html(geti18nPropVal('com_zte_ums_ict_framework_ui_update_patch_start_failure'));
        $('#stopOrStartServiceInfo').show();
    }
}

function initReport () {
    if (!parent.report) {
        return;
    }
    var report = parent.report;
    var totalText = geti18nPropVal('com_zte_ums_ict_framework_ui_update_patch_update_report_total');
    totalText = totalText.replace(new RegExp("\\{0\\}", "g"), report.totalHostCount);
    totalText = totalText.replace(new RegExp("\\{1\\}", "g"), report.totalSuccessfulHostCount);
    totalText = totalText.replace(new RegExp("\\{2\\}", "g"), report.totalFailedHostCount);
    totalText = totalText.replace(new RegExp("\\{3\\}", "g"), report.successRate * 100);
    $('#updateTotal').text(totalText);
    var updateFailedHtml = [];
    var reportInfos = report.reportInfos;
    for (var reportkey in reportInfos) {
        var reportInfo = reportInfos[reportkey];
        if (reportInfo.failedHostIps.length == 0) {
            continue;
        }
        updateFailedHtml.push('<div class="updateFailed"><div class="patchName">');
        if (reportkey == "dap.manager.common.bigdata") {
            reportkey = "zookeeper,hdfs...";
        }
        updateFailedHtml.push(reportkey + '(' + reportInfo.successfulHostCount + '/' + reportInfo.hostCount + ')');
        updateFailedHtml.push('</div><div class="failedHosts">');
        updateFailedHtml.push(geti18nPropVal('com_zte_ums_ict_framework_ui_update_patch_failure') + ': ');
        updateFailedHtml.push('<span>');
        updateFailedHtml.push(reportInfo.failedHostIps.join(', '));
        updateFailedHtml.push('</span></div></div>');
    }
    $('#updateFailed').html(updateFailedHtml.join(''));
}

// eslint-disable-next-line no-unused-vars
function continueFun () {
    queryNotPatchedInfo();
    parent.location.href = "../../page/update/updatePatchSteps.html";
}

// eslint-disable-next-line no-unused-vars
function finished () {
    parent.location.href = "../../page/upload/uploadPatch.html;
}

function queryNotPatchedInfo () {
    parent.clusterServicesMap = {};

    dapHttpRequest.getRestWithOption({
        url: "/api/daip-patcher-handler/v1/patches/unpatched/cluster/service",
        async: false,
        success: function (response) {
            var clusterServicesMap = response.clusterServicesMap;
            parent.parent.clusterServicesMap = clusterServicesMap
        }
    })
}
