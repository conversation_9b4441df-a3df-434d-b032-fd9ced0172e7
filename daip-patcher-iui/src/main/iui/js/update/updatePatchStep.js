/**
 * Created by 10204961 on 2018/2/5.
 */
$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/');
    //addBreadcrumb();
    initPage();
    initCluster();
});

var currentClusterId = "";

var listenValues = {};
var interval = null;
var time = 5;

Object.defineProperty(listenValues, 'isAutoMode', {
    get: function () {
        return this.value;
    },
    set: function (newValue) {
        this.value = newValue
    }
});

// eslint-disable-next-line no-unused-vars
function autoAction() {
    if (listenValues.isAutoMode) {
        time = 5;
        updateNextBtn();
        interval = setInterval(updateNextBtn, 1000);
    }
}

function updateNextBtn() {
    $('#steps').steps("setStepBtnStatus", {
        enablePreBtn: false,
        enableNextBtn: false
    });
    var btnText = geti18nPropVal('com_zte_ums_ict_framework_ui_host_next_step') + ' (' + time + 's)';
    $('#steps').steps("setNextBtnText", btnText);
    if (time <= 0) {
        clearInterval(interval);
        $('#steps').steps("clickNextBtn");
    }
    time--;
}


function initCluster() {
    new ClusterSelect("currentClusterSelect", clusterChange, null, true);
}

function initPage() {
    var stepArray = [];
    if ($("#steps").data('steps')) {
        $("#steps").steps("cleanStep");
    }

    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_choose_service"),
        stepUrl: "../../page/update/updatePatchSelectService.html",
        nextStepFun: "toConfirmPatch",
        showTitle: false
    });

    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_choose_host"),
        stepUrl: "../../page/update/updatePatchSelectHost.html",
        nextStepFun: "toConfirmPatch",
        showTitle: false
    });

    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_choose_confirm"),
        stepUrl: "../../page/update/updatePatchConfirm.html",
        showTitle: false
    });


    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_action"),
        stepUrl: "../../page/update/updatePatchAction.html",
        showTitle: false
    });

    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_upgrade_report"),
        stepUrl: "../../page/update/updatePatchReport.html",
        continueFun: "continueFun",
        finishFun: "finished",
        showTitle: false
    });

    $('#steps').steps({
        width: "100%",
        height: document.documentElement.clientHeight - 80 + "px",
        steps: stepArray
    });
}

function clusterChange() {
    window.patchIsInstall = false
    //window.frames[0].$Steps.steps("toStep",3)
    if (checkClusterStatus()) {

        currentClusterId && $("#clusterSelectId").val(currentClusterId);
        var confirmInfo = $('#currentClusterId option[value="' + currentClusterId + '"]').text() + geti18nPropVal(
            'com_zte_ums_ict_framework_ui_update_patch_cluster_is_install');
        MessageModal.showConfirm(confirmInfo, function () {
            var $Steps = window.frames[0].$Steps
            window.patchIsInstall = true
            if ($Steps) {
                //$Steps.steps("showOrHideCustomBtn", "autoUpdateBtn", false);
                $Steps.steps("showPreBtn");
                $Steps.steps("toStep", 3);
            }
        });

        return;
    }
    currentClusterId = $("#clusterSelectId").val();
    initPage();
}

function checkClusterStatus() {
    var patchIsInstall = true;
    dapHttpRequest.getRestWithOption({
        url: "/api/daip-patcher-handler/v1/patches/update/permit/cluster/" + $("#clusterSelectId").val(),
        async: false,
        success: function (result) {
            result && (patchIsInstall = false)
        }
    });
    return patchIsInstall;
}
