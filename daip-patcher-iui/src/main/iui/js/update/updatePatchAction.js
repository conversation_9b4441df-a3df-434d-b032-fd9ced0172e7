/**
 * Created by 10204961 on 2018/2/5.
 */
//@ sourceURL=/dapmanager-web/res/web-framework/js/update/updatePatchAction.js
$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', doUpdatePatch);
});

var clusterId = parent.currentClusterId || $('#clusterSelectId', parent.document).val()

function doUpdatePatch() {

    $Steps.steps("disableNextBtn");
    $Steps.steps("disablePreBtn");
    $Steps.steps("setAllStepJumpLink", false);
    $('#clusterSelectId', parent.document).prop('disabled', true);

    var selectServiceArray = (parent.selectClusterServicesMap && parent.selectClusterServicesMap.clusterServicesMap[clusterId]) || [];
    var serviceInstanceArray = (parent.clusterAndServiceInstanceMap && parent.clusterAndServiceInstanceMap.clusterServicesMap[clusterId]) || []

    console.log(parent.confirmInfo)
    var params = {
        clusterId: clusterId,
        serviceInstanceInfos: serviceInstanceArray.filter(function (item) {
            return item.serviceName && selectServiceArray.indexOf(item.serviceName) > -1 || (item.serviceInstanceId && selectServiceArray.indexOf(item.serviceInstanceId) > -1)
        })
    }

    let obj = {};
    parent.confirmInfo.forEach(item => {
        let key = item.service;
        obj[key] = item.unpatchedHosts.map(function (item) {
            return item.ip;
        })
    })

    params.serviceInstanceInfos.forEach(function (item) {
        if (item.serviceInstanceId && obj.hasOwnProperty(item.serviceInstanceId)) {
            item.ips = obj[item.serviceInstanceId]
            return false;
        }
        if (item.serviceName && obj.hasOwnProperty(item.serviceName)) {
            item.ips = obj[item.serviceName]
        }
    })

    console.log(params)

    MessageModal.showConfirmDanger(geti18nPropVal("com_zte_ums_ict_framework_ui_patch_update_stop_service_tip"), function () {
        if (!window.parent.patchIsInstall) {
            dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/update', JSON.stringify(params)).then(function () {
                window.t = initUpdateProgress() || setInterval(initUpdateProgress, 5000);
            }, function () {
                MessageModal.showMessage(geti18nPropVal("com_zte_ums_ict_framework_ui_patch_update_error"), geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_action"),
                    backToFirstStep);
            })
        } else {
            window.t = initUpdateProgress() || setInterval(initUpdateProgress, 5000);
        }
    })


}

function backToFirstStep() {
    parent.location.href = "../../page/update/updatePatchSteps.html";
}

//PatchProgressQueryHandler
function initUpdateProgress() {
    dapHttpRequest.getRest('/api/daip-patcher-handler/v1/patches/updating/process/cluster/' + clusterId).then(function (result) {
        searchProgressCallback(HtmlUtil.jsonEncode(result))
    }, function () {
        MessageModal.showConfirmWarning(geti18nPropVal("com_zte_ums_ict_framework_ui_host_find_progress_failed"), geti18nPropVal(
            "com_zte_ums_ict_framework_ui_update_patch_action"));
    })

}

function searchProgressCallback(result) {
    var updatePatchOver = result.allFinished;
    var isUpdateEnd = updatePatchOver;
    var progressInfo = result.patchResults || [];
    var dataArr = organizeTableParam(progressInfo);

    showData(dataArr, isUpdateEnd, updatePatchOver);

    if (isUpdateEnd && dataArr.length != 0) {
        window.t != null && clearInterval(window.t);
        window.isSuccess = result.isSuccess;
        parent.report = getReport(progressInfo);
        parent.autoAction();
        !parent.listenValues.isAutoMode && $Steps.steps("enableNextBtn");
    }
}

function organizeTableParam(progressInfo) {
    var dataArr = []

    progressInfo.forEach(item => {

        var failedHostIps = [], successHostIps = [], totalCount = (item.hosts || []).length,
            updatedCount = [];

        (item.hosts || []).forEach(function (host) {
            if (host.finished) {
                updatedCount.push(host.ipAddress)
                if (!host.success) {
                    failedHostIps.push(host.ipAddress)
                } else {
                    successHostIps.push(host.ipAddress)
                }
            }
        })
        dataArr.push({
            patchName: item.patchName,
            totalCount: totalCount,
            updatedCount: updatedCount.length,
            successCount: successHostIps.length,
            success: totalCount > 0 && failedHostIps.length == 0,
            failedHostIps: failedHostIps
        })
    });

    return dataArr
}

//PatchHandlerInfoQueryHandler
function getReport(progressInfo) {
    var totalHostSet = new Set();
    var totalFailedHostCount = new Set();
    var reportInfos = {};

    var dataArr = organizeTableParam(progressInfo)

    progressInfo.forEach(function (item) {
        (item.hosts || []).forEach(function (host) {
            if (!host.success) {
                totalFailedHostCount.add(host.ipAddress)
            }
            totalHostSet.add(host.ipAddress)
        })
    })

    dataArr.forEach(function (item) {
        reportInfos[item.patchName] = {
            hostCount: item.totalCount,
            successfulHostCount: item.updatedCount,
            failedHostIps: item.failedHostIps
        }
    })

    return {
        "totalHostCount": totalHostSet.size,
        "totalSuccessfulHostCount": totalHostSet.size - totalFailedHostCount.size,
        "totalFailedHostCount": totalFailedHostCount.size,
        "successRate": ((totalHostSet.size - totalFailedHostCount.size) / totalHostSet.size).toFixed(4),
        "reportInfos": reportInfos
    }
}

var dataTables;

function showData(resultList, isUpdateEnd, updatePatchOver) {
    var restartServiceOver = true;
    if (dataTables) {
        dataTables.destroy();
    }
    DataTablesUtil.initDataTablesOpt({
        "bPaginate": false,
        "bInfo": false
    });
    dataTables = $('#patchInfoTab').DataTable({
        "destroy": true,
        "paging": false,
        searching: false,
        bLengthChange: false,
        data: resultList,
        columnDefs: [{
            "targets": [0],
            "searchable": false,
            "width": "40%",
            "render": function (data, type, row) {
                return '<div><span>' + row.patchName + '<span></div>';
            }
        },
        {
            "targets": [1],
            "searchable": false,
            "width": "30%",
            "render": function (data, type, row) {
                var finished = row.updatedCount;
                var total = row.totalCount;
                var patchName = row.patchName.toLowerCase();
                var percent = (total <= 0 ? "0%" : (parseInt(Math.round(finished / total * 10000) / 100.00) +
                    "%"));
                if (finished / total >= 1) {
                    if (!isUpdateEnd && patchName.indexOf("agent") != -1) {
                        percent = parseInt(Math.round((finished - 1) / total * 10000) / 100.00) + "%";
                    } else {
                        if (updatePatchOver && !restartServiceOver) {
                            percent = 95 + "%";
                        } else {
                            percent = 100 + "%";
                        }
                    }
                }
                var spanProgress = '<span class="fill" id="totalProgressBar"  style="width: ' + percent +
                    '"></span><span class="label" id="totalProgLabel">' + finished + "/" + total + '</span> ';
                if (row.success != true) {
                    spanProgress = '<span class="fill" id="totalProgressBar"  style="background:red;width: ' +
                        percent + '"></span><span class="label" id="totalProgLabel">' + finished + "/" +
                        total + '</span> ';
                }

                var progressDiv = '<div class="progressDiv">' + spanProgress + '</div>';

                return progressDiv;
            }
        },
        {
            "bSortable": false,
            "aTargets": [1]
        }
        ]
    });
}


// eslint-disable-next-line no-unused-vars
function closeModal() {
    $("#updatePatchErr").modal('hide');
}
