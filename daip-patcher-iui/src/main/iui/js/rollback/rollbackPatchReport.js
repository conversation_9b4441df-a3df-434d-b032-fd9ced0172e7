//@ sourceURL=/dapmanager-web/res/web-framework/js/rollback/rollbackPatchAction.js
$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', initMethod);
});
var currentClusterId = parent.clusterId;

function initMethod () {
    $Steps.steps("setStepBtnStatus", {
            showPreBtn: false,
            showNextBtn: false,
            showContinueBtn: true,
            showFinishBtn: true
        });
    $('#clusterSelectId', parent.document).prop('disabled', true);
    MessageModal.initMessageModal();
    patchPage.initRollbackReport();
}

(function (global, $) {
    function patchPage () {
        var self = this;

        self.initRollbackReport =  function rollbackPageBoot() {
            dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/rollback/progress?clusterId=' + currentClusterId).then(function (result) {
                 drawTable(result.rollbackServiceProgressDtos);
              }, function () {
                   MessageModal.showConfirmWarning(geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_rollback_report_error"), geti18nPropVal(
                       "com_zte_ums_ict_framework_ui_update_patch_rollback_report"));
              });
        }

        function drawTable(rollbackServiceProgressDtos) {
            DataTablesUtil.initDataTablesOpt({
                "bSort": true,
                "bPaginate": false, //分页
                "bFilter": false, //搜索框
                "bInfo": false, //分页信息

            });

            var table = $('#patchInfoTab').DataTable({
                   "destroy": true,
                   "paging": false,
                   searching: false,
                   bLengthChange: false,
                   data: rollbackServiceProgressDtos,

                   columnDefs: [

                       {
                           "targets": [0],
                           "searchable": false,
                           "width": "40%",
                           "render": function (data, type, row, meta) {
                               return '<div><span>' + row.service + '<span></div>';
                           }
                       },
                       {
                           "targets": [1],
                           "searchable": false,
                           "width": "30%",
                           "render": function (data, type, row, meta) {
                                return '<div><span>' + row.success + '<span></div>';
                           }
                       },
                       {
                           "targets": [2],
                           "searchable": false,
                           "width": "30%",
                           "render": function (data, type, row, meta) {
                                if (row.failed >0) {
                                   return '<div><a  id="detail_failed" href="#"">' + geti18nPropVal("com_zte_ums_ict_framework_ui_rollback_patch_failed_detail")+row.failed + '</a><div>';
                                }
                                return geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_failure") + ":0";
                           }
                       },
                       {
                           "bSortable": false, "aTargets": [1]
                       }
                   ]
               });
               registerIncoOpsEven(table);
        }

        function registerIncoOpsEven ($table) {
            $('#patchInfoTab tbody').on('click', 'a#detail_failed', function () {
                var data = $table.row($(this).parents('tr')).data();
                detailReport(data.rollbackHostProgressDtos);
            });
        }



        function detailReport(rollbackHostProgressDtos){
            var $hostDialog = $('#patchRollbackDialog');
            $hostDialog.modal('show');
            $hostDialog.show('fast',function(){
                $('#failedRollbackTable').DataTable().draw();
            });

            var failedHosts = [];
            rollbackHostProgressDtos.forEach(function (item) {
                if(!item.success){
                    failedHosts.push(item);
                }
            })

            drawDetail(failedHosts);

        }

        function drawDetail(failedDetail){
           DataTablesUtil.initDataTablesOpt({
               "bSort": true,
               "bPaginate": false, //分页
               "bFilter": false, //搜索框
               "bInfo": false, //分页信息
           });
           $('#failedRollbackTable').DataTable({
               data: failedDetail,
               columnDefs: [
                    {
                       "targets": [0],
                       "searchable": false,
                       "orderable": true,
                       "width": "20%",
                       "render": function (data, type, row, meta) {
                           return '<div><span>' + row.ipAddress + '<span></div>';
                       }
                    },
                    {
                         "targets": [1],
                         "searchable": false,
                         "orderable": false,
                         "width": "50%",
                         "render": function (data, type, row, meta) {
                            return '<div><span>' + row.message + '<span></div>';
                         }
                    }

               ]
           });
        }


    }

    global.patchPage = new patchPage();
})(this, jQuery);

function continueFun () {
    parent.location.href = "../../page/rollback/rollbackPatchSteps.html";
}

function finished () {
    parent.location.href = "../../page/upload/uploadPatch.html";
}




