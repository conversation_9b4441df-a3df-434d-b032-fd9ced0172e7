/**
 * Created by 10204961 on 2018/2/5.
 */
$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/');
    initPage();
    initCluster();
});

var currentClusterId = "";
var listenValues = {};
var interval = null;
var time = 5;

Object.defineProperty(listenValues, 'isAutoMode', {
    get: function () {
        return this.value;
    },
    set: function (newValue) {
        this.value = newValue
    }
});

function autoAction() {
    if (listenValues.isAutoMode) {
        time = 5;
        rollbackNextBtn();
        interval = setInterval(rollbackNextBtn, 1000);
    }
}

function rollbackNextBtn() {
    $('#steps').steps("setStepBtnStatus", {
        enablePreBtn: false,
        enableNextBtn: false
    });
    var btnText = geti18nPropVal('com_zte_ums_ict_framework_ui_host_next_step') + ' (' + time + 's)';
    $('#steps').steps("setNextBtnText", btnText);
    if (time <= 0) {
        clearInterval(interval);
        $('#steps').steps("clickNextBtn");
    }
    time--;
}


function initCluster() {
    new ClusterSelect("currentClusterSelect", clusterChange, null, true);
}

function initPage() {
    var stepArray = [];
    if ($("#steps").data('steps')) {
        $("#steps").steps("cleanStep");
    }
    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_choose_service"),
        stepUrl: "../../page/rollback/rollbackPatchSelectPoint.html",
        nextStepFun: "toConfirmPatch",
        showTitle: false
    });
    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_choose_host"),
        stepUrl: "../../page/rollback/rollbackPatchSelectHost.html",
        nextStepFun: "toConfirmPatch",
        showTitle: false
    });
    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_service_command_stop_service"),
        stepUrl: "../../page/common/PatchStartAndStopService.html",
        nextStepFun: "toConfirmPatch",
        showTitle: false
    });
    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_rollback_patch_action"),
        stepUrl: "../../page/rollback/rollbackPatchAction.html",
        nextStepFun: "toConfirmPatch",
        showTitle: false
    });
    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_service_command_start_service"),
        stepUrl: "../../page/common/PatchStartAndStopService.html",
        nextStepFun: "toConfirmPatch",
        showTitle: false
    });
    stepArray.push({
        stepName: geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_rollback_report"),
        stepUrl: "../../page/rollback/rollbackPatchResult.html",
        continueFun: "continueFun",
        finishFun: "finished",
        showTitle: false
    });
    $('#steps').steps({
        width: "100%",
        height: document.documentElement.clientHeight - 80 + "px",
        steps: stepArray
    });
}

function clusterChange() {
    window.patchIsInstall = false
    currentClusterId = $("#clusterSelectId").val();
    initPage();
}

