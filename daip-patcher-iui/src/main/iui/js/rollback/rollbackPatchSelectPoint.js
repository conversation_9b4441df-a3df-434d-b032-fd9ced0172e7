//@ sourceURL=/dapmanager-web/res/web-framework/js/rollback/rollbackPatchSelectPoint.js
$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', initMethod);
});
var currentClusterId = $('#clusterSelectId', parent.document).val();
var currentClusterName = $('#clusterSelectId', parent.document).text();

function initMethod () {
   $Steps.steps("setStepBtnStatus", {
        showPreBtn: false,
        showNextBtn: true,
        showContinueBtn: false,
        showFinishBtn: false
    });
    $('#clusterSelectId', parent.document).prop('disabled', false);
    initServiceAndRollbackPoint();
}

function initServiceAndRollbackPoint(){
    dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/rollback/services?clusterId='+currentClusterId).then(function (result) {
         drawTable(result);
    })
}

function drawTable(serviceAndRoleAndHost) {
    DataTablesUtil.initDataTablesOpt({
        "bSort": true,
        "bPaginate": false, //分页
        "bFilter": false, //搜索框
        "bInfo": false, //分页信息

    });

    $('#serviceAndRollbackPoint').DataTable({
        data: serviceAndRoleAndHost,
        columnDefs: [{
             "targets": [0],
             "searchable": false,
             "className": "sorting-disabled",
             "width": "5%",
             "render": function (data, type, row, meta) {
                 return '<input name="selectService" type="checkbox" onchange="isSelectAll()"/>';
             }
         },
          {
              "targets": [1],
              "searchable": false,
              "className": "sorting-disabled",
              "width": "30%",
              "render": function (data, type, row, meta) {
                  var span = "";
                  if(row.serviceInstanceId != null && row.serviceInstanceId != "") {
                      span = '<span>' + row.serviceInstanceId + '</span>'
                  }
                  else if(row.roleName != null && row.roleName != "") {
                      span = '<span>' + row.serviceName + '(' + row.roleName + ')</span>'
                  }
                  else {
                      span = '<span>' + row.serviceName + '</span>'
                  }
                  return span;
              }
          },
          {
              "targets": [2],
              "searchable": false,
              "className": "sorting-disabled",
              "width": "55%",
              "render": function (data, type, row, meta) {
                  var patchSelect = '<select class="rollback-position-css">';
                  var patchList = row.patchNames;
                  $.each(patchList, function (key, value) {
                      patchSelect += '<option value="' + value + '">' + value + '</option>';
                  });

                  patchSelect = patchSelect + '</select>'
                  return patchSelect;
              }
          }
        ]
    });
}

function toConfirmPatch(){
     var service2Patch=[];
     var operateServicesParam=[];
     $('#serviceAndRollbackPoint').find('input[name="selectService"]').each(function () {
        if ($(this).prop('checked')) {
            var nodes = $('#serviceAndRollbackPoint').DataTable().row($(this).parents('tr')).node();
            var patchName = $(nodes).find('select option:selected').text();
            var data = $('#serviceAndRollbackPoint').DataTable().row($(this).parents('tr')).data();
            service2Patch.push({
                "serviceName": data.serviceName,
                "roleName":data.roleName,
                "serviceInstanceId":data.serviceInstanceId,
                "patchName":patchName
            });
            operateServicesParam.push({
                "serviceName": data.serviceName,
                "serviceInstanceId":data.serviceInstanceId
            })
        }
     });

    if ($.isEmptyObject(currentClusterId) || $.isEmptyObject(service2Patch)) {
         var failStr = geti18nPropVal("com_zte_ums_ict_framework_ui_update_select_service_cluster_tip");
         MessageModal.showConfirmNormal(failStr, geti18nPropVal("com_zte_ums_ict_framework_ui_service_rolling_restart_select_host_modal"));
         return;
     }

    window.parent.service2Patch = service2Patch;
    window.parent.clusterId = currentClusterId;
    window.parent.clusterName = currentClusterName;
    window.parent.operateServicesParam = operateServicesParam;
    $Steps.steps("nextStep");
}

function isSelectAll() {
    var flag = true;
    $("input[name='selectService']").each(function () {
        var isCheck = $(this).prop("checked");
        if (!isCheck) {
            flag = false;
        }
    });
    if (flag) {
        $("input[name='checkAll']").prop("checked", true);
    } else {
        $("input[name='checkAll']").prop("checked", false);
    }
}

function selectAllService () {
    var isChecked = $('input[name="checkAll"]').prop("checked");
    if (isChecked) {
        $('input[name="selectService"]').each(function () {
            var isCheck = $(this).prop("checked");
            if (!isCheck) {
                $(this).prop("checked", true).trigger('change');
            }
        });
    } else {
        $('input[name="selectService"]').each(function () {
            var isCheck = $(this).prop("checked");
            if (isCheck) {
                $(this).prop("checked", false).trigger('change');
            }
        });
    }
}