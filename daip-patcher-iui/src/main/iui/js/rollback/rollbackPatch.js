//@ sourceURL=/dapmanager-web/res/web-framework/js/update/updatePatch.js

$(function () {
    loadPropertiesSideMenu(lang, ['dap-manager-i18n', 'dap-manager-system-i18n'], '/dapmanager-web/res/web-framework/i18n/', initMethod);

    function initMethod () {
        MessageModal.initMessageModal();
        patchPage.rollbackPageBoot();
    }
});

(function (global, $) {
    function patchPage () {
        var self = this;

        function bindKeySearchEvent (table) {
            $("#search").click(function () {
                table.search("").draw();
            })

            $("#reset").click(function () {
                $('#patchSearch3').val("");
                table.search("").draw();
            })
        }

        function registerIncoOpsEven ($table) {
            $('#rollbackPatchTable tbody').on('click', 'a.moreBtn', function () {
                var data = $table.row($(this).parents('tr')).data();
                self.moreHost(data);
            });
        }

        function getPatchType (service, role) {
            var type = !role ? service : service + "(" + role + ")";
            return type;
        }

        function openMoreDialog (data) {
            var $hostDialog = $('#hostDialog');
            $hostDialog.data("host", data);
            $('input[name="hostRadio"]').off().on("click", allHostDialog);
            $('#searchHost').off().on("keyup", filterHostDialog);
            fill(data);
            $hostDialog.modal('show');
        }

        function hostToIpOrName (hosts, displayIp) {
            var result = [];
            for (var i = 0; i < hosts.length; i++) {
                if (!displayIp) {
                    result.push(hosts[i].hostName);
                } else {
                    result.push(hosts[i].ipAddress);
                }
            }
            return result;
        }

        function allHostDialog () {
            var hosts = $('#hostDialog').data("host");
            fill(hosts);
        }

        function filterHostDialog () {
            var hosts = $('#hostDialog').data("host");
            var searchValue = $('#searchHost').val();
            var filter = hosts.filter(function (host) {
                return host.hostName.indexOf(searchValue) != -1 || host.ipAddress.indexOf(searchValue) != -1;
            });
            fill(filter);
        }

        function fill (data) {
            var displayIp = !($('input[name="hostRadio"]:checked').val() == "hostName");
            var hostArr = hostToIpOrName(data, displayIp);
            $('#hostCount').text(data.length);
            var $ul = $("<ul></ul>");
            for (var i = 0; i < hostArr.length; i++) {
                $ul.append("<li title='" + hostArr[i] + "'>" + hostArr[i] + "</li>");
            }
            $('#hostContain').empty().append($ul);
        }

        function showRollbackTable () {
            initDataTablesOpt({
                "dom": '<"clear">rt<"infobottom"ipl><"clear">',
                "aaSorting": [
                    [0, "desc"]
                ]
            });

            var table = $('#rollbackPatchTable').DataTable({
                "searching": false,
                "destroy": true,
                "bServerSide": true,
                "bStateSave": true,
                "sAjaxSource": "/api/daip-deployer-handler/v1/patch/rollback/query",
                "fnServerData": queryRollbackPatch,
                columnDefs: [{
                    "targets": [0],
                    "width": "16%",
                    "data": "patchUpTime",
                    "render": function (data, type, row) {
                        var uploadTime = new Date(parseInt(row.patchUpTime));
                        var uploadTimeStr = uploadTime.format("yyyy-MM-dd hh:mm:ss");
                        return uploadTimeStr;
                    }
                }, {
                    "targets": [1],
                    "data": "patchName",
                    "render": function (data, type, row) {
                        return row.patchName
                    }
                }, {
                    "targets": [2],
                    "width": "12%",
                    "render": function (data, type, row) {
                        var div = "<div><span>" + getPatchType(row.serviceName, row.roleName) + "</span></div>"
                        return div;
                    }
                }, {
                    "targets": [3],
                    "orderable": false,
                    "width": "24%",
                    "render": function (data, type, row) {
                        return row.applyHostNum +
                            ' <a class="moreBtn">' + geti18nPropVal(
                                "com_zte_ums_ict_framework_ui_detail_information") + '</a>';
                    }
                }
                ]
            });
            bindKeySearchEvent(table);
            registerIncoOpsEven(table)
        }

        function queryRollbackPatch (sSource, aoData, fnCallback) {
            var params = {
                filterCondition: $("#patchSearch3").val() || ""
            };

            dapHttpRequest.postRest(sSource, JSON.stringify($.extend(params, DataTablesUtil.parseAoData(aoData)))).then(function (result) {
                if (result.status == 0) {
                    fnCallback(dataTableConverterPagingAttr(result.data));
                }
            });
        }

        self.moreHost = function (rowData) {
            dapHttpRequest.postRest("/api/daip-patcher-handler/v1/patches/history/rollback", JSON.stringify(rowData)).then(function (result) {
                if (result.status === '0') {
                    var data = result.data;
                    openMoreDialog(data);
                }
            })
        };
        self.rollbackPageBoot = function () {
            showRollbackTable();
        };

    }

    global.patchPage = new patchPage();
})(this, jQuery);


// eslint-disable-next-line no-unused-vars
function rollBack () {
    if ($('#rollbackBtn').attr("class").indexOf("disabled") > -1) {
        return;
    }

    location.href = '/dapmanager-web/res/web-framework/page/update/rollbackStepPatch.html';
}