//@ sourceURL=/dapmanager-web/res/web-framework/js/rollback/rollbackPatchAction.js
$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', initMethod);
});
var currentClusterId = parent.clusterId;
var rollbackParams = parent.patchRollbackHostDtos;

function initMethod () {
    $Steps.steps("setStepBtnStatus", {
            showPreBtn: true,
            showNextBtn: true,
            showContinueBtn: false,
            showFinishBtn: false,
            enableNextBtn:false,
            enablePreBtn:false
        });
    $('#clusterSelectId', parent.document).prop('disabled', true);
    doRollBackPatch();
}


 function doRollBackPatch(){
   dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/rollback?clusterId='+currentClusterId, JSON.stringify(rollbackParams)).then(function () {
          window.t = initRollbackProgress() || setInterval(initRollbackProgress, 5000);
     }, function () {
          MessageModal.showMessage(geti18nPropVal("com_zte_ums_ict_framework_ui_rollback_patch_failed"), geti18nPropVal("com_zte_ums_ict_framework_ui_rollback_patch_action"),
              backToFirstStep);
     })
 }


function initRollbackProgress() {
    dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/rollback/progress?clusterId=' + currentClusterId).then(function (result) {
             drawTable(result.rollbackServiceProgressDtos);
             if (result.finish) {
                window.t != null && clearInterval(window.t);
                 $Steps.steps("setStepBtnStatus", {
                    enableNextBtn:true
                 });
             }
      }, function () {
           MessageModal.showConfirmWarning(geti18nPropVal("com_zte_ums_ict_framework_ui_host_find_progress_failed"), geti18nPropVal(
               "com_zte_ums_ict_framework_ui_rollback_patch_action"));
      });
}


function continueFun () {
    parent.location.href = "../../page/rollback/rollbackPatchSteps.html";
}

// eslint-disable-next-line no-unused-vars
function finished () {
    parent.location.href = "../..page/upload/uploadPatch.html";
}

function toConfirmPatch() {
    window.parent.isStart=true;
    $Steps.steps("nextStep");
}


function drawTable(rollbackServiceProgressDtos) {
    DataTablesUtil.initDataTablesOpt({
        "bSort": true,
        "bPaginate": false, //分页
        "bFilter": false, //搜索框
        "bInfo": false, //分页信息

    });

    $('#patchInfoTab').DataTable({
           "destroy": true,
           "paging": false,
           searching: false,
           bLengthChange: false,
           data: rollbackServiceProgressDtos,

           columnDefs: [

               {
                   "targets": [0],
                   "searchable": false,
                   "width": "40%",
                   "render": function (data, type, row, meta) {
                       return '<div><span>' + row.service + '<span></div>';
                   }
               },

               {
                   "targets": [1],
                   "searchable": false,
                   "width": "30%",
                   "render": function (data, type, row, meta) {
                       var finished = row.failed+row.success;
                       var total = row.total;
                       var percent = ( total <= 0 ? "0%" : (parseInt(Math.round(finished / total * 10000) / 100.00) + "%"));
                       if (finished / total > 1) {
                           percent = 100 + "%";
                       }
                       var spanProgress = '<span class="fill" id="totalProgressBar"  style="width: ' + percent + '"></span><span class="label" id="totalProgLabel">' + finished + "/" + total + '</span> ';
                       if (row.failed != 0) {
                           spanProgress = '<span class="fill" id="totalProgressBar"  style="background:red;width: ' + percent + '"></span><span class="label" id="totalProgLabel">' + finished + "/" + total + '</span> ';
                       }

                       var progressDiv = '<div class="progressDiv">' + spanProgress + '</div>';

                       return progressDiv;
                   }
               },
               {
                   "bSortable": false, "aTargets": [1]
               }
           ]
       });
}