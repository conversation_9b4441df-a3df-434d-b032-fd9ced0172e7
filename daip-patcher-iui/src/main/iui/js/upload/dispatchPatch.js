//@ sourceURL=/dapmanager-web/res/web-framework/js/update/updatedPatch.js
$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', initMethod);

    function initMethod () {
        MessageModal.initMessageModal();
        dispatchPatchPage.initDispatchHostInfoTable();
    }
});

(function (global, $) {
    function dispatchPatchPage () {
        var self = this;

        self.moreHost = function (patchName, resultType) {
            $('#patchName').html(patchName);
            $('#dispatchHostDialog').modal('show');

            dapHttpRequest.getRest('/api/daip-patcher-handler/v1/patches/dispatch/history?patchName=' + patchName + '&resultType=' + resultType).then(function (response) {
                reloadTable(HtmlUtil.jsonEncode(response));
            });
        };

        // eslint-disable-next-line max-params
        self.moreHistoryBtn = function (patchName, serviceName, baseVersion, updateType) {
            $('#historyPatchName').html(patchName);
            $('#historyHostDialog').modal('show');

            dapHttpRequest.getRest('/api/daip-patcher-handler/v1/patches/update/history?patchName=' + patchName + '&serviceName=' + serviceName + '&version=' + baseVersion + '&updateType=' + updateType).then(function (response) {
                reloadTable2(HtmlUtil.jsonEncode(response));
            });
        };

        self.initDispatchHostInfoTable = function () {
            createHostTable();
            $('#dispatchHostDialog').on('shown.bs.modal', function () {
                $('#dispatchHostInfoTable').DataTable().draw();
            });
            $('#historyHostDialog').on('shown.bs.modal', function () {
                $('#historyHostInfoTable').DataTable().draw();
            });

        }

        var dispatchHostInfoTable = null, historyHostInfoTable = null;

        function createHostTable () {
            DataTablesUtil.initDataTablesOpt({
                aaSorting: [
                    [0, "asc"]
                ],
                searching: true,
                dom: 'f<"clear">rt<"infobottom"ipl><"clear">'
            });
            dispatchHostInfoTable = $('#dispatchHostInfoTable').DataTable({
                autoWidth: false,
                columnDefs: [
                    {
                        "targets": [0],
                        "data": "ip",
                        "width": "33%"
                    },
                    {
                        "targets": [1],
                        "data": "patchDispatchUptime",
                        "width": "34%",
                        render: function (data) {
                            return new Date(data).format("yyyy-MM-dd HH:mm:ss");
                        }
                    },
                    {
                        "targets": [2],
                        "data": "success",
                        "width": "34%",
                        render: function (data) {
                            return geti18nPropVal(data ? "com_zte_ums_ict_framework_ui_successful" : "com_zte_ums_ict_framework_ui_failure")
                        }
                    }
                ],
                searching: true
            });

            historyHostInfoTable = $('#historyHostInfoTable').DataTable({
                autoWidth: false,
                columnDefs: [
                    {
                        "targets": [0],
                        "data": "ip",
                        "width": "33%"
                    },
                    {
                        "targets": [1],
                        "data": "patchUptime",
                        "width": "34%",
                        render: function (data) {
                            if (!data || data <= 0) { return "" }
                            return new Date(data).format("yyyy-MM-dd HH:mm:ss");
                        }
                    }
                ],
                searching: true
            });
        }

        function reloadTable (data) {
            if (dispatchHostInfoTable) {
                DataTablesUtil.refreshTable('#dispatchHostInfoTable', data);
            }
        }

        function reloadTable2 (data) {
            if (historyHostInfoTable) {
                DataTablesUtil.refreshTable('#historyHostInfoTable', data);
            }
        }
    }

    global.dispatchPatchPage = new dispatchPatchPage();
})(this, jQuery);

