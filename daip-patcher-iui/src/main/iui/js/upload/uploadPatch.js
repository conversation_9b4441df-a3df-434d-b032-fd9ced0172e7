//@ sourceURL=/dapmanager-web/res/web-framework/js/update/updatePatch.js
var isDependPatchSuccess = true;
$(function () {
    loadPropertiesSideMenu(lang, ['dap-manager-i18n', 'dap-manager-system-i18n'], '../../daip-common-iui/i18n/', initMethod);

    function initMethod() {
        MessageModal.initMessageModal();
        patchPage.patchPageBoot();

        ChunkUpload.loadUploader({
            closeDialogCallback: function () {
                patchPage.patchPageBoot();
            }
        })
    }
});

(function (global, $) {

    var localStorageData = localStorage.getItem("DataTables_patchTable_/page/upload/uploadPatch.html");
    if (localStorageData != null) {
        localStorageData = JSON.parse(localStorageData);
        localStorageData.order = [5, "desc"];
        localStorage.setItem("DataTables_patchTable_/page/upload/uploadPatch.html", JSON.stringify(localStorageData));
    }

    function patchPage() {
        var self = this;

        //初始化表
        function showPatchTable() {
            DataTablesUtil.initDataTablesOpt({
                "dom": '<"clear">rt<"infobottom"ipl><"clear">',
                "aaSorting": [
                    [5, "desc"]
                ],
                "bSort": true,
                "bPaginate": true,//分页
                "bFilter": false,//搜索框
                "bInfo": true,//分页信息
            });
            var hasDeleteRights = OperSecurity.hasOperRights("operation.daip.patcher.update.patcher.delete");

            var table =$('#patchTable').DataTable({
                "searching": false,
                "destroy": true,
                "bServerSide": true,
                "bStateSave": true,
                "sAjaxSource": "/api/daip-patcher-handler/v1/patches/paging",
                "fnServerData": queryPatchInfo,
                columnDefs: [
                    {
                        "targets": [0],
                        "orderable": false,
                        "width": "2%",
                        "render": function (data, type, row, meta) {
                            if (row.canDel) {
                               return '<input type="checkbox" name="checkbox" value=""/>';
                            }else{
                                return '<input type="checkbox" name="checkbox" value="" disabled/>';
                            }
                        }
                    },
                    {
                        "targets": [1],
                        "data": "patchName",
                        "width": "18%"
                    },
                    {
                        "targets": [2],
                        "width": "8%",
                        "data": "realPatchType"

                    },
                    {
                        "targets": [3],
                        "data": "baseVersion",
                        "width": "8%"
                    },
                    {
                        "targets": [4],
                        "width": "6%",
                        "render": function (data, type, row) {
                            var div = "<div class='patchInfo'>";

                            if (row.patchSize >= 1024 * 1024) {
                                div = div + "<span>" + (row.patchSize / 1024 / 1024).toFixed(2) + " M</span></div>";
                            } else {
                                div = div + "<span>" + (row.patchSize / 1024).toFixed(2) + " KB</span></div>";
                            }

                            return div;
                        }
                    },
                    {
                        "targets": [5],
                        "width": "10%",
                        "data": "patchUploadTimeStr"
                    },
                    {
                        "targets": [6],
                        "data": "supplementDesc",
                        "orderable": true,
                        "visible": false,
                        "width": "0%",
                    },
                    {
                        "targets": [7],
                        "orderable": false,
                        "width": "8%",
                        "render": function (data, type, row) {
                            var successNum = row.patchDispatchResultDto.successNum,
                                errorNum = row.patchDispatchResultDto.errorNum;
                            return '<span class="' + (successNum <= 0 ? "initial" : "success") + ' dispatch clickable">' + successNum + '(' + geti18nPropVal("com_zte_ums_ict_framework_ui_successful") + ')</span>  <span class="' + (errorNum <= 0 ? "initial" : "error") + ' dispatch clickable">' + errorNum +
                                '(' + geti18nPropVal("com_zte_ums_ict_framework_ui_failure") + ')</span>';
                        }
                    },
                    {
                        "targets": [8],
                        "orderable": false,
                        "width": "8%",
                        "render": function (data, type, row) {
                            var updateNum = row.patchUpdateResultDto.updateNum,
                                pendingNum = row.patchUpdateResultDto.pendingNum;
                            return '<span class="' + (updateNum <= 0 ? "initial" : "success") + ' history clickable">' + updateNum + '(' + geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_updated_num") + ')</span> '+ (/-RepositoryVersion-|-schema-/.test(row.patchName) ? '' : (' <span class="' + (pendingNum <= 0 ? "initial" : "error") + ' history clickable">' + pendingNum +
                                '(' + geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_pending_num") + ')</span>'));
                        }
                    },
                    {
                        "targets": [9],
                        "orderable": false,
                        "width": "10%",
                        "render": function (data, type, row) {
                            var descText = row.descriptionZh;
                            var lang = getLanguage();
                            if (lang.indexOf("en") > -1) {
                                descText = row.descriptionEn;
                            }
                            return "<div>" + descText + "</div>";
                        }
                    },
                    {
                        "targets": [10],
                        "orderable": false,
                        "width": "10%",
                        "render": function (data, type, row) {
                            var commentText = "";
                            if (!row.dependPatchIsOkFlg) {
                                commentText = '<span style="color:red">' + "  (" + row.supplementDesc + ")" + '</span>'
                            }
                            return "<div>" + commentText + "</div>";
                        }
                    },
                    {
                        "targets": [11],
                        "orderable": false,
                        "width": "5%",
                        "render": function (data, type, row) {
                            if (hasDeleteRights) {
                                return '<div id="deletePatch" class="' + (row.canDel ? "clickable" : "disabled clickable") + '" operation="operation.daip.patcher.update.patcher.delete" >' + geti18nPropVal("com_zte_ums_ict_framework_ui_button_delete") + '</div>';
                            } else {
                                return "<div></div>";
                            }
                        }
                    }
                ]
            });
            table.on('length.dt', function (e, settings, len) {
                $('#checkAll').prop('checked', false);
                $('#checkAll').prop('indeterminate', false);
            });
            table.on('draw', function () {
                $('#checkAll').prop('checked', false);
                $('#checkAll').prop('indeterminate', false);
            });
            DataTablesUtil.bindCheckAllEvent("patchTable", function (_checkAll, _hasRows) {
                $("#delBtn")[!_checkAll || !_hasRows ? "addClass" : "removeClass"]("disabled");
            }, function (_checkAll, _indeterminate, _singleChecked) {
                var method = (!_checkAll && !_indeterminate) ? "addClass" : "removeClass";
                $("#delBtn")[method]("disabled");
            });
            $('#patchTable').DataTable().search("").draw();
            bindButtonEvent();
            registerIncoOpsEven();
        }

        function queryPatchInfo(sSource, aoData, fnCallback) {
            var params = {
                patchType: $('#patchCate').val() || "",
                searchKeyWord: $("#patchSearch").val() || ""
            };

            dapHttpRequest.postRest(sSource, JSON.stringify($.extend(params, DataTablesUtil.parseAoData(aoData)))).then(function (result) {
                typeof (HtmlUtil) != "undefined" && HtmlUtil.jsonEncode && (result = HtmlUtil.jsonEncode(result))
                if (result.status == 0) {
                    //initPatchCategory(result.data.patchType)
                    isDependPatchSuccess = result.data.dependPatchIsOkFlg || true;
                    fnCallback(DataTablesUtil.dataTableConverterPagingAttr(result.data));
                    try {
                        $("#dispatcherBtn")[result.data && result.data.data.length ? "removeClass" : "addClass"]("disabled")
                    } catch (err) {
                        console.log(err)
                    }

                }
            });
        }

        function queryNotPatchedInfo() {
            parent.clusterServicesMap = {};

            dapHttpRequest.getRest('/api/daip-patcher-handler/v1/patches/unpatched/cluster/service').then(function (response) {
                var clusterServicesMap = response.clusterServicesMap;
                parent.clusterServicesMap = clusterServicesMap
                if (clusterServicesMap && Object.values(clusterServicesMap).length > 0) {
                    $('#updateBtn').removeClass('disabled');
                    $("#com_zte_ums_ict_framework_ui_non_ms_patch_msg").show();
                    $('#com_zte_ums_ict_framework_ui_patch_need_update').show();
                } else {
                    $('#updateBtn').addClass('disabled');
                }
            })

        }

        function queryPatchType() {
            dapHttpRequest.getRest('/api/daip-patcher-handler/v1/patches/type').then(function (response) {
                response = HtmlUtil.jsonEncode(response)
                if (response.status == 0) {
                    initPatchCategory(response.data)
                }
            })
        }

        function initPatchCategory(data) {
            var $patchCate = $('#patchCate'),
                optionHtml = '<option value="-1">' + geti18nPropVal("com_zte_ums_ict_framework_ui_button_all") + '</option>';
            if (!data || data.length == 0) {
                return;
            }

            optionHtml += data.reduce(function (total, currentValue) {
                return total + '<option value=' + currentValue + '>' + currentValue + '</option>'
            }, "")

            $patchCate.empty().append(optionHtml);
        }

        function initPage() {
            $('#com_zte_ums_ict_framework_ui_non_ms_patch_msg').hide();
            $('#com_zte_ums_ict_framework_ui_patch_need_update').hide();

            $("#updateBtn").off("click").click(function () {
                if ($(this).attr("class").indexOf("disabled") > -1) {
                    return;
                }

                if (!isDependPatchSuccess) {
                    MessageModal.showConfirmDanger(geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_depend_patch_not_ok"), function () {
                        toUpdate()
                    });
                } else {
                    toUpdate();
                }
            });

            $("#delBtn").off("click").click(function () {
                if ($(this).attr("class").indexOf("disabled") > -1) {
                    return;
                }
                deletePatches(getSelectedPatchNames());
            });


            $("#rollbackBtn").off("click").click(function () {
                if ($(this).attr("class").indexOf("disabled") > -1) {
                    return;
                }
                $("#page-mainIframe", parent.document).attr("src", "/iui/daip-patcher-iui/page/rollback/rollbackPatchSteps.html");
            });

            $("#onekeyupdateBtn").off("click").click(function () {
                location.href = '../../dist/index.html';
            });

            $("#dispatcherBtn").off("click").click(function () {
                if ($(this).hasClass("disabled")) {
                    return;
                }

                dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/dispatch/precheck').then(function (res) {
                    if (res && res.status == "0") {
                        if (!res.data.status) {
                            MessageModal.showConfirmWarning(geti18nPropVal("com_zte_ums_ict_framework_ui_patch_dispatch_precheck_tip").formati18n(obtaindispatchPrecheckSummary(res.data.message)), function () {
                                execDispatch()
                            })
                        } else {
                            execDispatch()
                        }
                    } else {
                        MessageModal.showMessageFailure(geti18nPropVal("com_zte_ums_ict_framework_ui_patch_dispatch_precheck_error"));
                    }
                }, function () {
                    MessageModal.showMessageFailure(geti18nPropVal("com_zte_ums_ict_framework_ui_patch_dispatch_precheck_error"));
                })
            })
            $("#uploadDialog").off("click").click(function () {
                var checkBoxAll = $("#fileLists input[type='checkbox']");
                var on = 0;
                checkBoxAll.each(function () {
                    if ($(this).prop('checked')) on++; // 选中
                });
                layui.use(['form'], function(){
                  var form = layui.form;
                  var checkBox = document.getElementById("checkBoxAllId");
                  if(checkBoxAll.length > 0 && on == checkBoxAll.length) {
                      checkBox.checked = true;
                  }else {
                      checkBox.checked = false;
                  }
                  form.render('checkbox');
                });
            })
        }
        self.patchPageBoot = function () {
            initPage();
            queryPatchType();
            showPatchTable();
            queryNotPatchedInfo();

        };

        /* Started by AICoder, pid:j80f434c7a0b2ee14ae80a7bc0eea6211028123f */
        function getSelectedPatchNames() {
            var table = $('#patchTable').DataTable();
            var selectedPatchNames = [];

            // 获取当前页所有行
            var rows = table.rows({ page: 'current', search: 'applied' }).nodes();

            // 遍历当前页的所有行
            $(rows).each(function () {
                var row = this;
                var checkbox = $(row).find('input[type="checkbox"]');

                // 检查复选框是否被选中
                if (checkbox.prop('checked')) {
                    // 获取该行的数据
                    var data = table.row(row).data();
                    // 将 patchName 添加到列表中
                    if (data && data.patchName) {
                        selectedPatchNames.push(data.patchName);
                    }
                }
            });

            return selectedPatchNames;
        }

        function toUpdate() {
            $("#page-mainIframe", parent.document).attr("src", "/iui/daip-patcher-iui/page/update/updatePatchSteps.html");
        }

        function execDispatch() {
            dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/dispatch').then(function () {
                MessageModal.showMessageSuccess(geti18nPropVal("com_zte_ums_ict_framework_ui_patch_dispatch_success"));
            }, function () {
                MessageModal.showMessageFailure(geti18nPropVal("com_zte_ums_ict_framework_ui_patch_dispatch_error"));
            })
        }

        function obtaindispatchPrecheckSummary(message) {
            if (!message) {
                return ""
            }
            var patchArr = message.split(",");
            if (patchArr.length <= 3) {
                return message
            } else {
                return patchArr.slice(0, 3).join(",") + "..."
            }
        }

        function registerIncoOpsEven() {
            $('#patchTable tbody').on('click', 'span.dispatch', function (_event) {
                _event.stopPropagation(), _event.preventDefault()
                var data = $('#patchTable').DataTable().row($(this).parents('tr')).data();
                dispatchPatchPage.moreHost(data.patchName, $(this).hasClass("success") ? "success" : "error")
            })

            $('#patchTable tbody').on('click', 'span.history', function (_event) {
                _event.stopPropagation(), _event.preventDefault()
                var data = $('#patchTable').DataTable().row($(this).parents('tr')).data();
                dispatchPatchPage.moreHistoryBtn(data.patchName, data.service, data.baseVersion, $(this).hasClass("success") ? "updated" : "pending")
            })

            $('#patchTable tbody').on('click', 'div#deletePatch', function (_event) {
                _event.stopPropagation(), _event.preventDefault()
                if ($(this).hasClass('disabled')) {
                    return;
                }
                var data = $('#patchTable').DataTable().row($(this).parents('tr')).data();
                deletePatch(data.patchName)
            })
        }

        function bindButtonEvent() {
            $("#search").off("click").click(function () {
                $('#patchTable').DataTable().search("").draw();
            })

            $("#reset").off("click").click(function () {
                $('#patchSearch').val("");
                $("#patchCate").val("-1");
                $('#patchTable').DataTable().search("").draw();
            })
        }
    }

    global.patchPage = new patchPage();
})(this, jQuery);

function deletePatch(name) {
    MessageModal.showConfirmNormal(geti18nPropVal("com_zte_ums_ict_framework_ui_command_dispatcher_task_delete"), deletePatchAction, name);
}

function deletePatchAction(patchName) {
    dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/deletion?patchName=' + patchName).then(function (res) {
        if (res.status == 0) {
            MessageModal.showMessageSuccess(geti18nPropVal("com_zte_ums_ict_framework_ui_del_success"));
            patchPage.patchPageBoot();
            var fileId = ChunkUpload.fileNameMap.get(patchName);
            fileId && ChunkUpload.utils.uploadCancel(fileId, true);
        } else {
            MessageModal.showMessageFailure(geti18nPropVal("com_zte_ums_ict_framework_ui_del_failed"));
        }
    }, function () {
        MessageModal.showMessageFailure(geti18nPropVal("com_zte_ums_ict_framework_ui_del_failed"));
    });
}

function deletePatches(patchNames) {
    MessageModal.showConfirmNormal(geti18nPropVal("com_zte_ums_ict_framework_ui_command_dispatcher_task_delete"), deletePatchesAction, patchNames);
}

function deletePatchesAction(patchNames) {
    dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/batch/deletion' ,JSON.stringify(patchNames)).then(function (res) {
        if (res.status == 0) {
            MessageModal.showMessageSuccess(geti18nPropVal("com_zte_ums_ict_framework_ui_del_success"));
            patchPage.patchPageBoot();
            $("#delBtn")["addClass"]("disabled");
            patchNames.forEach(function(patchName, index) {
                var fileId = ChunkUpload.fileNameMap.get(patchName);
                fileId && ChunkUpload.utils.uploadCancel(fileId, true);
            });
        } else {
            MessageModal.showMessageFailure(geti18nPropVal("com_zte_ums_ict_framework_ui_del_failed"));
        }
    }, function () {
        MessageModal.showMessageFailure(geti18nPropVal("com_zte_ums_ict_framework_ui_del_failed"));
    });
}
