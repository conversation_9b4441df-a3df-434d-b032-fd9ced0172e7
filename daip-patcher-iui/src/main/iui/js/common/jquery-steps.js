(function($) {
    var defaultStepStatus = {
        showPreBtn: false,
        enablePreBtn: true,
        showNextBtn: true,
        enableNextBtn: true,
        showContinueBtn: false,
        enableContinueBtn: true,
        showFinishBtn: false,
        enableFinishBtn: true,
        showCustomBtn: [],
        enableCustomBtn: [],
    };
    var clickTime = new Date().getTime();

    var Steps = function($step, option) {
        this.curStep = 1;
        this.option = option;
        this.stepArray = option.steps;
        this.stepStatus = $.extend(true, {}, defaultStepStatus);
        this.$step = $step;
        this.setDefaultOpt();
        this.customBtn = [];

        this.$step.addClass('dap_steps');
        this.$step.css('width', option.width || '100%');
        this.$step.css('height', option.height || '600px');
        this.$step.html('');

        this.$step.append('<div class="stepBars"></div>');
        this.$step.append('<div class="stepBody"></div>');
        initStepBar.apply(this);
        initStepBody.apply(this);
    };

    function initStepBar() {
        this.$stepBar = this.$step.find('.stepBars');
        this.$stepBar.html('');
        for (var i = 1; i <= this.stepArray.length; i++) {
            var stepItem = this.stepArray[i - 1];
            var $stepItem = $('<div id="step_' + i + '" class="stepItem"></div>');
            $stepItem.append('<div class="stepNum">' + i + '</div>');
            $stepItem.append('<div class="stepName">' + stepItem.stepName + '</div>');
            if (i < this.curStep) {
                $stepItem.addClass('passStep');
                stepItem.jumpLink && $stepItem.bind("click", i, $.proxy(jumpStep, this));
                !stepItem.jumpLink && $stepItem.addClass('disabled');
            }
            if (i == this.curStep) {
                $stepItem.addClass('curStep');
            }
            this.$stepBar.append($stepItem);
        }
    }

    function initStepBody() {
        this.$stepBody = this.$step.find('.stepBody');
        this.$stepBody.html('');
        if (this.stepArray[this.curStep - 1].showTitle) {
            this.$stepBody.append('<div class="stepTitle">' + this.stepArray[this.curStep - 1].stepName + '</div>');
            this.$stepBody.append('<hr />');
        }
        this.$iframe = $('<iframe class="stepIframe"></iframe>');
        this.$iframe.attr({
            id: "stepIframe",
            name: "stepIframe",
            src: this.stepArray[this.curStep - 1].stepUrl,
            frameborder: 0,
            frameIndex: -1,
            scrolling: "auto",
            width: '100%',
            height: this.$step.height() - 80 + 'px'
        });
        this.$stepBody.append(this.$iframe);
        initStepButton.apply(this);
        this.$stepBody.append(this.$stepButton);
        this.$iframe[0].contentWindow.$Steps = this.$step;
    }

    function initStepButton() {
        this.$stepButton = $('<div class="stepButtons"></div>');

        this.$preBtn = $('<button class="dap-btn dap-btn-black dap-btn-left"></button>');
        this.$preBtn.text(geti18nPropVal('com_zte_ums_ict_framework_ui_host_back_step'));
        this.$preBtn.click($.proxy(clickPreBtn, this));
        this.$preBtn.css("order", 10);

        this.$nextBtn = $('<button class="dap-btn dap-btn-black dap-btn-left"></button>');
        this.$nextBtn.text(geti18nPropVal('com_zte_ums_ict_framework_ui_host_next_step'));
        this.$nextBtn.click($.proxy(clickNextBtn, this));
        this.$nextBtn.css("order", 20);

        this.$continueBtn = $('<button class="dap-btn dap-btn-black dap-btn-left"></button>');
        this.$continueBtn.text(geti18nPropVal('com_zte_ums_ict_framework_ui_host_continue'));
        this.$continueBtn.click($.proxy(clickContinueBtn, this));
        this.$continueBtn.css("order", 10);

        this.$finishBtn = $('<button class="dap-btn dap-btn-black dap-btn-left"></button>');
        this.$finishBtn.text(geti18nPropVal('com_zte_ums_ict_framework_ui_host_finish'));
        this.$finishBtn.click($.proxy(clickFinishBtn, this));
        this.$finishBtn.css("order", 20);

        this.$stepButton.append(this.$preBtn);
        this.$stepButton.append(this.$nextBtn);
        this.$stepButton.append(this.$continueBtn);
        this.$stepButton.append(this.$finishBtn);
        initCustomBtn.apply(this);
        this.setStepStyle();
    }

    function initCustomBtn() {
        var that = this;
        this.customBtn.forEach(function(item) {
            appendCustomBtn.apply(that, [item]);
        });
    }

    function appendCustomBtn(btnOpt) {
        var customBtnName = btnOpt.name;
        this[customBtnName] = $('<button class="dap-btn dap-btn-black dap-btn-left"></button>');
        this[customBtnName].text(btnOpt.textName);
        this[customBtnName].click($.proxy(clickCustomBtn, this, btnOpt));
        this[customBtnName].css("order", btnOpt.order);
        this.$stepButton.append(this[customBtnName]);
    }

    function clickCustomBtn(btnOpt) {
        if (this[btnOpt.name].hasClass("disabled") && this[btnOpt.name][0] == document.activeElement) {
            return;
        }
        var clickFun = btnOpt.clickFun;
        if (typeof clickFun === "string") {
            clickFun = this.$iframe[0].contentWindow[clickFun];
        }
        typeof clickFun === "function" && clickFun.apply(this);
    }

    function clickPreBtn() {
        if (this.$preBtn.hasClass("disabled") && this.$preBtn[0] == document.activeElement) {
            return;
        }

        if (new Date().getTime() - clickTime < 1000) {
            return;
        }
        clickTime = new Date().getTime();

        var curStepOpt = this.getStepOption(this.curStep);
        var preStepFun = curStepOpt.preStepFun;
        if (typeof preStepFun === "string") {
            preStepFun = this.$iframe[0].contentWindow[preStepFun];
        }
        var beforePre = curStepOpt.beforePre;
        if (typeof beforePre === "string") {
            beforePre = this.$iframe[0].contentWindow[curStepOpt.beforePre];
        }
        if (typeof preStepFun === 'function') {
            preStepFun.apply(this);
        } else {
            this.stepStatus.showNextBtn = true;
            typeof beforePre === 'function' && beforePre.apply(this);
            this.preStep();
        }
    }

    function clickNextBtn() {
        if (this.$nextBtn.hasClass("disabled") && this.$nextBtn[0] == document.activeElement) {
            return;
        }

        if (new Date().getTime() - clickTime < 1000) {
            return;
        }
        clickTime = new Date().getTime();

        nextBtnEvent.apply(this);
    }

    function nextBtnEvent() {
        var curStepOpt = this.getStepOption(this.curStep);
        var nextStepFun = curStepOpt.nextStepFun;
        if (typeof nextStepFun === "string") {
            nextStepFun = this.$iframe[0].contentWindow[nextStepFun];
        }
        var beforeNext = curStepOpt.beforeNext;
        if (typeof beforeNext === "string") {
            beforeNext = this.$iframe[0].contentWindow[beforeNext];
        }
        if (typeof nextStepFun === 'function') {
            nextStepFun.apply(this);
        } else {
            this.stepStatus.showPreBtn = true;
            typeof beforeNext === 'function' && beforeNext.apply(this);
            this.nextStep();
        }
    }

    function clickContinueBtn() {
        if (this.$continueBtn.hasClass("disabled") && this.$continueBtn[0] == document.activeElement) {
            return;
        }
        var curStepOpt = this.getStepOption(this.curStep);
        var continueFun = curStepOpt.continueFun;
        if (typeof continueFun === "string") {
            continueFun = this.$iframe[0].contentWindow[continueFun];
        }
        typeof continueFun === 'function' && continueFun.apply(this);
    }

    function clickFinishBtn() {
        if (this.$finishBtn.hasClass("disabled") && this.$finishBtn[0] == document.activeElement) {
            return;
        }
        var curStepOpt = this.getStepOption(this.curStep);
        var finishFun = curStepOpt.finishFun;
        if (typeof finishFun === "string") {
            finishFun = this.$iframe[0].contentWindow[finishFun];
        }
        typeof finishFun === 'function' && finishFun.apply(this);
    }

    function jumpStep(event) {
        this.toStep(event.data);
    }

    Steps.prototype.appendCustomBtn = function(btnOpt) {
        var customBtnName = btnOpt.name;
        if (this[customBtnName]) {
            return;
        }
        this.customBtn.push(btnOpt);
        appendCustomBtn.apply(this, [btnOpt]);
        this.setStepStyle();
    }

    Steps.prototype.showOrHideCustomBtn = function(customBtnName, status) {
        this.stepStatus.showCustomBtn[customBtnName] = status;
        this.setStepStyle();
    }

    Steps.prototype.enOrDisCustomBtn = function(customBtnName, status) {
        this.stepStatus.enableCustomBtn[customBtnName] = status;
        this.setStepStyle();
    }

    Steps.prototype.toStep = function(step) {
        this.curStep = step || this.curStep;
        initStepBar.apply(this);
        initStepBody.apply(this);
    }

    Steps.prototype.preStep = function() {
        this.curStep--;
        if (this.curStep == 1) {
            this.stepStatus.showPreBtn = false;
        }
        if (this.curStep >= 1) {
            this.toStep();
        }
    }

    Steps.prototype.nextStep = function() {
        this.curStep++;
        if (this.curStep == this.stepArray.length) {
            this.stepStatus.showNextBtn = false;
        }
        if (this.curStep <= this.stepArray.length) {
            this.toStep();
        }
    }

    Steps.prototype.setDefaultOpt = function() {
        this.stepArray.forEach(function(item) {
            if (typeof item.jumpLink === 'undefined') {
                item.jumpLink = true;
            }
            if (typeof item.showTitle === 'undefined') {
                item.showTitle = true;
            }
        });
    }

    Steps.prototype.appendStep = function(stepOption) {
        if (!this.hasStep(stepOption.stepName)) {
            this.stepArray.push(stepOption);
            initStepBar.apply(this);
            initStepBody.apply(this);
        }
    }

    Steps.prototype.updateStep = function(stepOption) {
        if (this.hasStep(stepOption.stepName)) {
            this.stepArray.forEach(function(item) {
                if (item.stepName == stepOption.stepName) {
                    item = stepOption;
                }
            });
            initStepBar.apply(this);
            this.stepStatus.showNextBtn = true;
            this.setStepStyle();
        }
    }

    Steps.prototype.hasStep = function(stepName) {
        var arr = this.stepArray.filter(function(item) {
            return item.stepName === stepName;
        });
        if (arr.length > 0) {
            return true;
        }
        return false;
    }

    Steps.prototype.getStepOption = function(stepNum) {
        return this.stepArray[stepNum - 1];
    }

    Steps.prototype.setStepStyle = function() {
        this.stepStatus.showPreBtn ? this.$preBtn.show() : this.$preBtn.hide();
        this.stepStatus.showNextBtn ? this.$nextBtn.show() : this.$nextBtn.hide();
        this.stepStatus.showContinueBtn ? this.$continueBtn.show() : this.$continueBtn.hide();
        this.stepStatus.showFinishBtn ? this.$finishBtn.show() : this.$finishBtn.hide();

        this.stepStatus.enablePreBtn ? this.$preBtn.removeClass('disabled') : this.$preBtn.addClass('disabled');
        this.stepStatus.enableNextBtn ? this.$nextBtn.removeClass('disabled') : this.$nextBtn.addClass(
            'disabled');
        this.stepStatus.enableContinueBtn ? this.$continueBtn.removeClass('disabled') :
            this.$continueBtn.addClass('disabled');
        this.stepStatus.enableFinishBtn ? this.$finishBtn.removeClass('disabled') : this.$finishBtn.addClass(
            'disabled');

        for (var customBtnName in this.stepStatus.showCustomBtn) {
            this.stepStatus.showCustomBtn[customBtnName] ? this[customBtnName].show() : this[customBtnName].hide();
        }

        for (var customBtnName1 in this.stepStatus.enableCustomBtn) {
            this.stepStatus.enableCustomBtn[customBtnName1] ? this[customBtnName1].removeClass('disabled') :
                this[customBtnName1].addClass('disabled');
        }
    }

    Steps.prototype.setStepJumpLink = function(stepNum, status) {
        this.getStepOption(stepNum).jumpLink = status;
        initStepBar.apply(this);
    }

    Steps.prototype.setAllStepJumpLink = function(status) {
        this.stepArray.forEach(function(item) {
            item.jumpLink = status;
        });
        initStepBar.apply(this);
    }

    Steps.prototype.setStepBtnStatus = function(opt) {
        this.stepStatus = $.extend(true, this.stepStatus, opt);
        this.setStepStyle();
    }

    Steps.prototype.setNextBtnText = function(btnText) {
        this.$nextBtn.text(btnText);
    }

    Steps.prototype.clickNextBtn = function() {
        nextBtnEvent.apply(this);
    }

    Steps.prototype.enablePreBtn = function() {
        this.setStepBtnStatus({
            enablePreBtn: true
        });
    }

    Steps.prototype.disablePreBtn = function() {
        this.setStepBtnStatus({
            enablePreBtn: false
        });
    }

    Steps.prototype.enableNextBtn = function() {
        this.setStepBtnStatus({
            enableNextBtn: true
        });
    }

    Steps.prototype.disableNextBtn = function() {
        this.setStepBtnStatus({
            enableNextBtn: false
        });
    }

    Steps.prototype.showPreBtn = function() {
        this.setStepBtnStatus({
            showPreBtn: true
        });
    }

    Steps.prototype.hidePreBtn = function() {
        this.setStepBtnStatus({
            showPreBtn: false
        });
    }

    Steps.prototype.showNextBtn = function() {
        this.setStepBtnStatus({
            showNextBtn: true
        });
    }

    Steps.prototype.hideNextBtn = function() {
        this.setStepBtnStatus({
            showNextBtn: false
        });
    }

    Steps.prototype.cleanStep = function() {
        this.$step.html("");
        this.$step.removeData('steps');
    }

    $.fn.steps = function(option) {
        var args = Array.prototype.slice.call(arguments, 1);
        return this.each(function(index, el) {
            var $el = $(el);
            var data = $el.data('steps');
            if (!data) {
                data = new Steps($el, option);
                $el.data('steps', data);
            }
            if (typeof option == 'string') {
                data[option].apply(data, args);
            }
        });
    }

})(jQuery);
