$(function () {
    loadPropertiesSideMenu(lang, 'dap-manager-i18n', '../../@iui.common.microservice.name@/i18n/', initMethod);
});
var currentClusterId = parent.clusterId;
var currentClusterName = parent.clusterName;
var isStart = parent.isStart;
var operateServicesParam = parent.operateServicesParam;
var stopServicesByPatch = parent.stopServicesByPatch;

function initMethod () {
    $('#operateServiceBtn').hide();
    $Steps.steps("setStepBtnStatus", {
        showPreBtn: true,
        showNextBtn: true,
        showContinueBtn: false,
        showFinishBtn: false
    });
    if(isStart){
        $("#operateServiceBtn").append('<span>' + geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_start_service") + '</span>');
    }else{
        $("#operateServiceBtn").append('<span>' + geti18nPropVal("com_zte_ums_ict_framework_ui_update_patch_stop_service") + '</span>');
    }
    $('#allService').on('click', 'input[name="checkService"]', chooseService);
    $('#allServiceClick').on('click', chooseAllService);
    $('#clusterSelectId', parent.document).prop('disabled', true);
    initCanOperateService();
}

function chooseService () {
    if (isAllServiceSelected()) {
        $('#allServiceClick').prop("checked", true);
    } else {
        $('#allServiceClick').prop("checked", false);
    }
}

function chooseAllService () {
    if ($('#allServiceClick').prop('checked')) {
        $('#allService input[name="checkService"]:not(:disabled)').prop('checked', true);
    } else {
        $('#allService input[name="checkService"]:not(:disabled)').prop('checked', false);
    }
}

function isAllServiceSelected () {
    var flag = true;
    $('#allService').find('input[name="checkService"]').each(function () {
        if (!$(this).prop('checked')) {
            flag = false;
        }
    });
    return flag;
}

function initCanOperateService(){
    dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/cluster/services?clusterId='+currentClusterId+'&isStart='+isStart, JSON.stringify(operateServicesParam)).then(function (result) {
        if (result) {
            if(result.length>0){
                $('#operateServiceBtn').show();
                $(result).each(function (index, service) {
                    item = HtmlUtil.htmlEncodeByRegExp(service);
                    var serviceDiv = $('<div></div>');
                    serviceDiv.css({
                        "display": "inline-block",
                        "padding-left": "30px",
                        "padding-top": "5px"
                    });
                    if(!isStart){
                        serviceDiv.append('<input name="checkService" checked value="' + item + '" type="checkbox"/><span>' + item +'</span>');
                    }else{
                        if(stopServicesByPatch.indexOf(item) > -1){
                            serviceDiv.append('<input name="checkService" checked value="' + item + '" type="checkbox"/><span>' + item +'</span>');
                        }else{
                            serviceDiv.append('<input name="checkService" value="' + item + '" type="checkbox"/><span>' + item +'</span>');
                        }
                    }
                    $('#allService').append(serviceDiv);
                });
            }else{
                $('#operateServiceBtn').hide();
                var serviceDiv = $('<div></div>');
                serviceDiv.css({
                    "display": "inline-block",
                    "padding-left": "30px",
                    "padding-top": "5px"
                });
                serviceDiv.append(geti18nPropVal("com_zte_ums_ict_framework_ui_no_service_available"));
                $('#allService').append(serviceDiv);
            }
        } else {
            var message = result.errMsg || "error";
            MessageModal.showConfirmWarning(message, geti18nPropVal("com_zte_ums_ict_framework_ui_rollback_patch_action"));
        }
    })
    chooseService();
    if (!isStart){
        $('#allService input[name="checkService"]:not(:disabled)').prop('checked', true);
    }

}

function operateService () {
    dapHttpRequest.getRest("/api/daip-patcher-handler/v1/patches/cluster/progress?clusterId=" + currentClusterId).then(function (res) {
        if (res && res.status != "-1") {
            var instanceStepKeys = res.instanceStepKeys,
            startOrStop = res.startOrStop
            if (!(instanceStepKeys instanceof Array && instanceStepKeys.length === 0 || res.progress >=100)) {
                isStart = !startOrStop;
                var failStr = geti18nPropVal('com_zte_ums_ict_framework_ui_update_patch_can_not_stop_service');
                MessageModal.showConfirmNormal(failStr, geti18nPropVal("com_zte_ums_ict_framework_ui_rollback_patch_action"));
            } else {
                startOrStopServiceAction();
            }
        } else {
            MessageModal.showMessage(res.data);
        }
    });
}

function startOrStopServiceAction(){

    var operateServices = [];
     $('#allService').find('input[name="checkService"]').each(function () {
         if ($(this).prop('checked')) {
             operateServices.push($(this).val());
         }
     });
     if(!isStart){
         window.parent.stopServicesByPatch = operateServices;
     }
     $Steps.steps("disableNextBtn");
     $Steps.steps("disablePreBtn");
     var url = '/api/daip-patcher-handler/v1/patches/cluster/'+ (isStart ? 'start' : 'stop')+'?clusterId=' +currentClusterId
     window.parent.ProgressParams = {
        clusterId: currentClusterId,
        clusterName: currentClusterName,
        isShowClusterSelect: false,
        taskName: geti18nPropVal('com_zte_ums_ict_framework_ui_host_service'),
        pageTitle:  geti18nPropVal(isStart ? "com_zte_ums_ict_framework_ui_update_patch_start_service" : "com_zte_ums_ict_framework_ui_update_patch_stop_service"),
        progressDesc: geti18nPropVal('com_zte_ums_ict_framework_ui_cluster_start_used_time'),
        taskOperate: {
            url: url,
            data: JSON.stringify(operateServices),
            option: { contentType: 'application/json' }
        },
        taskProgress: {
            url: '/api/daip-patcher-handler/v1/patches/cluster/progress?clusterId=' + currentClusterId,
            serviceInstanceIds: operateServices,
            data: {},
            callback: function (_window) {
                if(!isStart){
                    $Steps.steps("enablePreBtn");
                }
                $Steps.steps("enableNextBtn");
            }
        }
    }
    window.location.replace('../../@iui.common.microservice.name@/template/progress.html');
}

function toConfirmPatch() {
    dapHttpRequest.postRest('/api/daip-patcher-handler/v1/patches/cluster/services?clusterId='+currentClusterId+'&isStart='+isStart, JSON.stringify(operateServicesParam)).then(function (result) {
        if (result) {
            if(result.length>0){
                if(isStart){
                    var mess = geti18nPropVal('com_zte_ums_ict_framework_ui_patch_update_start_service');
                }else{
                    var mess = geti18nPropVal('com_zte_ums_ict_framework_ui_patch_update_stop_service');
                }
                 MessageModal.showConfirmWarning(mess + geti18nPropVal('com_zte_ums_ict_framework_ui_danger_operation'), function () {
                    $Steps.steps("nextStep");
                });
            }else{
                $Steps.steps("nextStep");
            }
        } else {
            var message = result.errMsg || "error";
            MessageModal.showConfirmWarning(message, geti18nPropVal("com_zte_ums_ict_framework_ui_rollback_patch_action"));
        }
    })
}