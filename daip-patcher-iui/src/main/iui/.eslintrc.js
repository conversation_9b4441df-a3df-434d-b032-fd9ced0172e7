module.exports = {
    "env": {
        "browser": true,
        "es6": true
    },
    "parserOptions": {
        "ecmaVersion": 6,
        "sourceType": "module"
    },
    //plugins: ['html'],
    globals: { // 允许在代码中使用全局变量
        location: true,
        setTimeout: true,
        console: true,
        window: true,
        geti18nPropVal: true,
        CipherUtil: true,
        UrlParam: true,
        Tabs: true,
        loadPropertiesSideMenu: true,
        lang: true,
        MessageModal: true
    },
    extends: 'eslint:recommended',
    "rules": {
        "no-autocomplete-off": 2,
        "no-innerhtml": 2,
        "no-decrypt": 2,
        "no-undef": 0,
        "insecure-password-algorithm": [2, ["md5", "des"]]
    }
};