.dap_steps {
    width: 100%;
    display: flex;
    align-items: flex-start;
}

.dap_steps .stepBars {
    min-width: 200px;
    margin-right: 20px;
    border: 1px solid rgba(221, 221, 221, 1);
    background-color: rgba(245, 245, 245, 1);
}

.dap_steps .stepBody {
    width: 100%;
    height: 100%;
}

.dap_steps .stepBars .stepItem {
    height: 32px;
    padding-left: 38px;
    display: flex;
    align-items: center;
    font-size: 13px;
    border-left: 2px solid transparent;
}

.dap_steps .stepBars .stepItem.curStep {
    border-left: 2px solid #3366cc;
}

.dap_steps .stepBars .stepNum {
    width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 10px;
    background-color: #919699;
    text-align: center;
    color: #FFFFFF;
    margin-right: 16px;
    font-weight: 600;
    cursor: default;
}

.dap_steps .stepBars .stepName {
    cursor: default;
    height: 22px;
    line-height: 22px;
    color: #666666;
}

.dap_steps .stepItem.curStep .stepNum {
    background-color: #3366cc;
}

.dap_steps .stepItem.curStep .stepName {
    color: #3366cc;
}

.dap_steps .stepItem.passStep .stepNum {
    background-color: #7ba7ff;
    cursor: pointer;
}

.dap_steps .stepItem.passStep .stepName {
    color: #558eff;
    cursor: pointer;
}

.dap_steps .stepItem.disabled .stepNum {
    background-color: #dddddd;
}

.dap_steps .stepItem.disabled .stepName {
    color: #bbbbbb;
    cursor: default;
}

.dap_steps .stepBody .stepTitle {
    font-size: 16px;
    color: #333333;
    height: 24px;
    line-height: 24px;
    margin-top: 5px;
    font-size: 18px;
}

.dap_steps .stepBody hr {
    border: 0px;
    border-top: 1px solid rgba(221, 221, 221, 1);
    margin: 10px 0;
}

.dap_steps .stepBody .stepButtons {
    padding: 5px 65px;
    display: flex;
    justify-content: flex-end;
}