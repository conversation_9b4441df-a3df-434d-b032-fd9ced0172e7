<assembly>
    <id>product-iui-inner-tar</id>
    <formats>
        <!-- zip,tar,tar.gz,tar.bz2,jar,dir,war -->
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>

    <fileSets>
        <fileSet>
            <directory>
                ${iui.compile.dir}
            </directory>
            <includes>
                <include></include>
            </includes>
            <outputDirectory>./iui</outputDirectory>
            <directoryMode>0750</directoryMode>
        </fileSet>
        <fileSet>
            <directory>
                ${iui.compile.dir}
            </directory>
            <includes>
                <include></include>
            </includes>
            <outputDirectory>./iui/${iui.microservice.name}</outputDirectory>
            <directoryMode>0750</directoryMode>
        </fileSet>
        <fileSet>
            <directory>
                ${iui.compile.dir}
            </directory>
            <includes>
                <include>dist/**</include>
            </includes>
            <outputDirectory>./iui/${iui.microservice.name}/</outputDirectory>
            <filtered>false</filtered>
            <directoryMode>0750</directoryMode>
            <fileMode>0640</fileMode>
        </fileSet>
        <fileSet>
            <directory>
                ${project.basedir}/src/main/iui
            </directory>
            <includes>
                <include>page/**</include>
                <include>js/**</include>
                <include>css/**</include>
            </includes>
            <outputDirectory>./iui/${iui.microservice.name}/</outputDirectory>
            <filtered>true</filtered>
            <directoryMode>0750</directoryMode>
            <fileMode>0640</fileMode>
        </fileSet>
    </fileSets>

</assembly>
