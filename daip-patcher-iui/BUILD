load("@rules_pkg//:pkg.bzl", "pkg_tar")
#load("@npm//@vue/cli-service:index.bzl", "vue_cli_service")
#
#vue_cli_service(
#    name = "install",
#    args = ["install"],
#)

# daip-patcher-iui.tar.gz/iui/daip-deployer-iui/
filegroup(
    name = "daip-patcher-iui-inner",
    srcs = glob([
        "src/main/iui/css/**",
        "src/main/iui/page/**",
        "src/main/iui/js/**",
        "src/main/vue-iui/dist/**",
    ]),
    visibility = [
        "//visibility:public",
    ],
)

# daip-patcher-iui.tar.gz
pkg_tar(
    name = "daip-patcher-iui",
    srcs = [
        ":daip-patcher-iui-inner",
    ],
    extension = "tar.gz",
    package_dir = "/iui/daip-patcher-iui",
    strip_prefix = "src/main/iui",
    visibility = [
        "//:__pkg__",
    ],
)
