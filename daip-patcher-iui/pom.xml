<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.zte.daip.manager.patcher</groupId>
        <artifactId>daip-patcher</artifactId>
        <version>deletePatch</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>daip-patcher-iui</artifactId>
    <groupId>com.zte.daip.manager.patcher</groupId>
    <version>deletePatch</version>
    <packaging>pom</packaging>

    <properties>
        <iui.compile.dir>${basedir}/target/iui</iui.compile.dir>
    </properties>

    <profiles>
        <profile>
            <id>paas</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-resources-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-resources</id>
                                <!-- here the phase you need -->
                                <phase>validate</phase>
                                <goals>
                                    <goal>copy-resources</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${iui.compile.dir}</outputDirectory>
                                    <resources>
                                        <resource>
                                            <directory>${basedir}/src/main/vue-iui</directory>
                                            <filtering>true</filtering>
                                            <excludes>
                                                <exclude>node_modules/**/*</exclude>
                                                <exclude>tmp/**/*</exclude>
                                                <exclude>dist/**/*</exclude>
                                            </excludes>
                                        </resource>
                                    </resources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <version>1.9.1</version>
                        <executions>
                            <execution>
                                <id>install node and npm</id>
                                <goals>
                                    <goal>install-node-and-npm</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>npm install</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <configuration>
                                    <npmRegistryURL>https://artsz.zte.com.cn/artifactory/api/npm/it-npm-virtual/</npmRegistryURL>
                                    <environmentVariables>
                                        <HUSKY_SKIP_INSTALL>true</HUSKY_SKIP_INSTALL>
                                    </environmentVariables>
                                </configuration>
                            </execution>
                            <execution>
                                <id>npm run build</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <configuration>
                                    <arguments>run build</arguments>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <nodeVersion>v20.11.1</nodeVersion>
                            <nodeDownloadRoot>https://artnj.zte.com.cn/artifactory/daip-thirdparty-release-maven/node/</nodeDownloadRoot>
                            <serverId>public</serverId>
                            <workingDirectory>${iui.compile.dir}</workingDirectory>
                        </configuration>
                    </plugin>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <finalName>${iui.microservice.name}</finalName>
                            <appendAssemblyId>false</appendAssemblyId>
                            <ignoreDirFormatExtensions>true</ignoreDirFormatExtensions>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                            <descriptors>
                                <descriptor>src/assembly/assembly_iui_inner_tar.xml</descriptor>
                            </descriptors>
                            <overrideUid>${non.root.uid}</overrideUid>
                            <overrideGid>${non.root.uid}</overrideGid>
                        </configuration>
                        <executions>
                            <execution>
                                <id>assembly</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>