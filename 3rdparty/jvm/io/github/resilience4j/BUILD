java_library(
    name = "resilience4j_circuitbreaker",
    exports = [
        "//3rdparty/jvm/io/vavr:vavr",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/github/resilience4j/resilience4j_circuitbreaker",
        ":resilience4j_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "resilience4j_circularbuffer",
    exports = [
        "//3rdparty/jvm/io/vavr:vavr",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/github/resilience4j/resilience4j_circularbuffer"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "resilience4j_consumer",
    exports = [
        "//3rdparty/jvm/io/vavr:vavr",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/github/resilience4j/resilience4j_consumer",
        ":resilience4j_circularbuffer",
        ":resilience4j_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "resilience4j_core",
    exports = [
        "//3rdparty/jvm/io/vavr:vavr",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/github/resilience4j/resilience4j_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "resilience4j_rxjava2",
    exports = [
        "//3rdparty/jvm/io/reactivex/rxjava2:rxjava",
        "//3rdparty/jvm/io/vavr:vavr",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/github/resilience4j/resilience4j_rxjava2"
    ],
    visibility = [
        "//visibility:public"
    ]
)


