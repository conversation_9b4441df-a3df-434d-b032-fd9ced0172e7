java_library(
    name = "feign_form",
    exports = [
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/github/openfeign/form/feign_form"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)



java_library(
    name = "feign_form_spring",
    exports = [
        "//3rdparty/jvm/commons_fileupload:commons_fileupload",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//external:jar/io/github/openfeign/form/feign_form_spring",
        ":feign_form"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)


