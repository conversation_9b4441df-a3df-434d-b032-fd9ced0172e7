java_library(
    name = "feign_core",
    exports = [
        "//external:jar/io/github/openfeign/feign_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "feign_hystrix",
    exports = [
        "//3rdparty/jvm/com/netflix/archaius:archaius_core",
        "//3rdparty/jvm/com/netflix/hystrix:hystrix_core",
        "//external:jar/io/github/openfeign/feign_hystrix",
        ":feign_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "feign_slf4j",
    exports = [
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/github/openfeign/feign_slf4j",
        ":feign_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)


