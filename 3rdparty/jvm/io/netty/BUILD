java_library(
    name = "netty_buffer",
    exports = [
        "//external:jar/io/netty/netty_buffer",
        ":netty_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_codec",
    exports = [
        "//external:jar/io/netty/netty_codec",
        ":netty_buffer",
        ":netty_common",
        ":netty_transport"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_codec_dns",
    exports = [
        "//external:jar/io/netty/netty_codec_dns",
        ":netty_buffer",
        ":netty_codec",
        ":netty_common",
        ":netty_transport"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_common",
    exports = [
        "//external:jar/io/netty/netty_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_handler",
    exports = [
        "//external:jar/io/netty/netty_handler",
        ":netty_buffer",
        ":netty_codec",
        ":netty_common",
        ":netty_resolver",
        ":netty_transport",
        ":netty_transport_native_unix_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_resolver",
    exports = [
        "//external:jar/io/netty/netty_resolver",
        ":netty_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_resolver_dns",
    exports = [
        "//external:jar/io/netty/netty_resolver_dns",
        ":netty_buffer",
        ":netty_codec",
        ":netty_codec_dns",
        ":netty_common",
        ":netty_handler",
        ":netty_resolver",
        ":netty_transport"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_transport",
    exports = [
        "//external:jar/io/netty/netty_transport",
        ":netty_buffer",
        ":netty_common",
        ":netty_resolver"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_transport_classes_epoll",
    exports = [
        "//external:jar/io/netty/netty_transport_classes_epoll",
        ":netty_buffer",
        ":netty_common",
        ":netty_transport",
        ":netty_transport_native_unix_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_transport_native_epoll",
    exports = [
        "//external:jar/io/netty/netty_transport_native_epoll",
        ":netty_buffer",
        ":netty_common",
        ":netty_transport",
        ":netty_transport_classes_epoll",
        ":netty_transport_native_unix_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "netty_transport_native_unix_common",
    exports = [
        "//external:jar/io/netty/netty_transport_native_unix_common",
        ":netty_buffer",
        ":netty_common",
        ":netty_transport"
    ],
    visibility = [
        "//visibility:public"
    ]
)


