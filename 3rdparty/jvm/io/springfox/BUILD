java_library(
    name = "springfox_core",
    exports = [
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//external:jar/io/springfox/springfox_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "springfox_schema",
    exports = [
        "//external:jar/io/springfox/springfox_schema",
        ":springfox_core",
        ":springfox_spi"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "springfox_spi",
    exports = [
        "//external:jar/io/springfox/springfox_spi",
        ":springfox_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "springfox_spring_web",
    exports = [
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//external:jar/io/springfox/springfox_spring_web",
        ":springfox_spi"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "springfox_swagger2",
    exports = [
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/io/swagger:swagger_annotations",
        "//3rdparty/jvm/io/swagger:swagger_models",
        "//3rdparty/jvm/org/mapstruct:mapstruct",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//external:jar/io/springfox/springfox_swagger2",
        ":springfox_schema",
        ":springfox_spi",
        ":springfox_spring_web",
        ":springfox_swagger_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "springfox_swagger_common",
    exports = [
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/io/swagger:swagger_annotations",
        "//3rdparty/jvm/io/swagger:swagger_models",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_core",
        "//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata",
        "//external:jar/io/springfox/springfox_swagger_common",
        ":springfox_schema",
        ":springfox_spi",
        ":springfox_spring_web"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "springfox_swagger_ui",
    exports = [
        "//external:jar/io/springfox/springfox_swagger_ui",
        ":springfox_spring_web"
    ],
    visibility = [
        "//visibility:public"
    ]
)


