java_library(
    name = "kubernetes_client",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/github/mifmif:generex",
        "//3rdparty/jvm/com/squareup/okhttp3:logging_interceptor",
        "//3rdparty/jvm/com/squareup/okhttp3:okhttp",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/fabric8/kubernetes_client",
        ":kubernetes_model",
        ":zjsonpatch"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)



java_library(
    name = "kubernetes_model",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations",
        "//external:jar/io/fabric8/kubernetes_model"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)



java_library(
    name = "zjsonpatch",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//external:jar/io/fabric8/zjsonpatch"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)


