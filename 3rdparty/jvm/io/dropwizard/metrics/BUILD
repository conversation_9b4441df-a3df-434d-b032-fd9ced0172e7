java_library(
    name = "metrics_annotation",
    exports = [
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_annotation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "metrics_core",
    exports = [
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "metrics_healthchecks",
    exports = [
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_healthchecks"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "metrics_jersey2",
    exports = [
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_server",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_jersey2",
        ":metrics_annotation",
        ":metrics_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "metrics_jetty9",
    exports = [
        "//3rdparty/jvm/org/eclipse/jetty:jetty_server",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_jetty9",
        ":metrics_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "metrics_jmx",
    exports = [
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_jmx",
        ":metrics_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "metrics_json",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_json",
        ":metrics_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "metrics_jvm",
    exports = [
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_jvm",
        ":metrics_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "metrics_logback",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_logback",
        ":metrics_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "metrics_servlets",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/helger:profiler",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/metrics/metrics_servlets",
        ":metrics_core",
        ":metrics_healthchecks",
        ":metrics_json",
        ":metrics_jvm"
    ],
    visibility = [
        "//visibility:public"
    ]
)


