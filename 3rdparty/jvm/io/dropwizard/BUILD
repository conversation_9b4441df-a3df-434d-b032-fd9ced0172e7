java_library(
    name = "dropwizard_configuration",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/com/github/ben_manes/caffeine:caffeine",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/org/apache/commons:commons_text",
        "//external:jar/io/dropwizard/dropwizard_configuration",
        ":dropwizard_jackson",
        ":dropwizard_util",
        ":dropwizard_validation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_core",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_healthchecks",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jetty9",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jmx",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jvm",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_servlets",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/net/sourceforge/argparse4j:argparse4j",
        "//3rdparty/jvm/org/eclipse/jetty/toolchain/setuid:jetty_setuid_java",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_security",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlet",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_core",
        ":dropwizard_configuration",
        ":dropwizard_jackson",
        ":dropwizard_jersey",
        ":dropwizard_jetty",
        ":dropwizard_lifecycle",
        ":dropwizard_logging",
        ":dropwizard_metrics",
        ":dropwizard_request_logging",
        ":dropwizard_servlets",
        ":dropwizard_util",
        ":dropwizard_validation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_jackson",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_joda",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_afterburner",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names",
        "//3rdparty/jvm/com/github/ben_manes/caffeine:caffeine",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_jackson",
        ":dropwizard_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_jersey",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/github/ben_manes/caffeine:caffeine",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jersey2",
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/joda_time:joda_time",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_io",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_api",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_client",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_server",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_metainf_services",
        "//3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/javassist:javassist",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_jersey",
        ":dropwizard_jackson",
        ":dropwizard_logging",
        ":dropwizard_util",
        ":dropwizard_validation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_jetty",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_http",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_io",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_security",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlet",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlets",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_jetty",
        ":dropwizard_jackson",
        ":dropwizard_logging",
        ":dropwizard_util",
        ":dropwizard_validation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_lifecycle",
    exports = [
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_lifecycle",
        ":dropwizard_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_logging",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/io/dropwizard/logback:logback_throttling_appender",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_logback",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//3rdparty/jvm/org/slf4j:jcl_over_slf4j",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//3rdparty/jvm/org/slf4j:log4j_over_slf4j",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_logging",
        ":dropwizard_jackson",
        ":dropwizard_util",
        ":dropwizard_validation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_metrics",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/github/ben_manes/caffeine:caffeine",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_metrics",
        ":dropwizard_jackson",
        ":dropwizard_lifecycle",
        ":dropwizard_util",
        ":dropwizard_validation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_request_logging",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_access",
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_http",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_request_logging",
        ":dropwizard_jackson",
        ":dropwizard_logging",
        ":dropwizard_util",
        ":dropwizard_validation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_servlets",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_annotation",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_servlets",
        ":dropwizard_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_util",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/guava:guava",
        "//external:jar/io/dropwizard/dropwizard_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dropwizard_validation",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/io/dropwizard/dropwizard_validation",
        ":dropwizard_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)


