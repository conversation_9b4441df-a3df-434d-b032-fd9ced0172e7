java_library(
    name = "zipkin_reporter",
    exports = [
        "//3rdparty/jvm/io/zipkin/zipkin2:zipkin",
        "//external:jar/io/zipkin/reporter2/zipkin_reporter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zipkin_reporter_brave",
    exports = [
        "//3rdparty/jvm/io/zipkin/zipkin2:zipkin",
        "//external:jar/io/zipkin/reporter2/zipkin_reporter_brave",
        ":zipkin_reporter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zipkin_reporter_metrics_micrometer",
    exports = [
        "//3rdparty/jvm/io/zipkin/zipkin2:zipkin",
        "//external:jar/io/zipkin/reporter2/zipkin_reporter_metrics_micrometer",
        ":zipkin_reporter"
    ],
    visibility = [
        "//visibility:public"
    ]
)


