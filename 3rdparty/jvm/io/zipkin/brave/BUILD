java_library(
    name = "brave",
    exports = [
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_brave",
        "//external:jar/io/zipkin/brave/brave"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_context_slf4j",
    exports = [
        "//external:jar/io/zipkin/brave/brave_context_slf4j",
        ":brave"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_http",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_http",
        ":brave"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_httpasyncclient",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_httpasyncclient",
        ":brave",
        ":brave_instrumentation_http"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_httpclient",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_httpclient",
        ":brave",
        ":brave_instrumentation_http"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_jms",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_jms",
        ":brave",
        ":brave_instrumentation_messaging"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_kafka_clients",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_kafka_clients",
        ":brave",
        ":brave_instrumentation_messaging"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_kafka_streams",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_kafka_streams",
        ":brave",
        ":brave_instrumentation_kafka_clients",
        ":brave_instrumentation_messaging"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_messaging",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_messaging",
        ":brave"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_rpc",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_rpc",
        ":brave"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_servlet",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_servlet",
        ":brave",
        ":brave_instrumentation_http"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_spring_rabbit",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_spring_rabbit",
        ":brave",
        ":brave_instrumentation_messaging"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_spring_web",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_spring_web",
        ":brave",
        ":brave_instrumentation_http"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "brave_instrumentation_spring_webmvc",
    exports = [
        "//external:jar/io/zipkin/brave/brave_instrumentation_spring_webmvc",
        ":brave",
        ":brave_instrumentation_servlet"
    ],
    visibility = [
        "//visibility:public"
    ]
)


