java_library(
    name = "archaius_core",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/google/code/findbugs:jsr305",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_configuration:commons_configuration",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/com/netflix/archaius/archaius_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)


