java_library(
    name = "dexcloud_springboot_common_utils",
    exports = [
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_common_utils",
        ":dexcloud_springboot_starter_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_commons_impl",
    exports = [
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/javax/validation:validation_api",
        "//3rdparty/jvm/org/reflections:reflections",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_actuator",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_commons_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_configcenter_commons",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_configcenter_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_configcenter_configclient",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/i18n:zenap_i18n_core",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_config",
        "//3rdparty/jvm/org/springframework/retry:spring_retry",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_configcenter_configclient",
        ":dexcloud_springboot_configcenter_commons",
        ":dexcloud_springboot_starter_kafka",
        ":dexcloud_springboot_starter_msb"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_datetime_utils",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_datetime_utils",
        ":dexcloud_springboot_starter_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_db_jdbc_impl",
    exports = [
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/com/zaxxer:HikariCP",
        "//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jdbc",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_db_jdbc_impl",
        ":dexcloud_springboot_starter_commons",
        ":dexcloud_springboot_starter_internalcontrol_agent",
        ":dexcloud_springboot_starter_utils_service"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_gr_impl",
    exports = [
        "//3rdparty/jvm/joda_time:joda_time",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_gr_impl",
        ":dexcloud_springboot_commons_impl",
        ":dexcloud_springboot_starter_kafka"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_initckeck_utils",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_initckeck_utils",
        ":dexcloud_springboot_starter_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_internalcontrol_agent_impl",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_center_cipher_springboot_agent",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_internalcontrol_agent_impl",
        ":dexcloud_springboot_commons_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_jersey_impl",
    exports = [
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/io/swagger:swagger_jersey2_jaxrs",
        "//3rdparty/jvm/org/reflections:reflections",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jersey",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_jersey_impl",
        ":dexcloud_springboot_starter_gr"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_jersey_swagger_impl",
    exports = [
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/io/dropwizard:dropwizard_servlets",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_jersey_swagger_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_jetty_impl",
    exports = [
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/org/eclipse/jetty/http2:http2_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_alpn_conscrypt_server",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jetty",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_jetty_impl",
        ":dexcloud_springboot_starter_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_kafka_impl",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/kafka:zenap_kafka_rule",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/org/apache/kafka:kafka_2_12",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_kafka_impl",
        ":dexcloud_springboot_starter_msb",
        ":dexcloud_springboot_starter_utils_service"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_msb_impl",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_msb_impl",
        ":dexcloud_springboot_starter_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_redis_redisson_impl",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_redis_redisson_impl",
        ":dexcloud_springboot_starter_commons",
        ":dexcloud_springboot_starter_internalcontrol_agent",
        ":dexcloud_springboot_starter_utils_service",
        ":redisson"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_redis_redisson_mult_impl",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_redis_redisson_mult_impl",
        ":dexcloud_springboot_starter_commons",
        ":dexcloud_springboot_starter_internalcontrol_agent",
        ":dexcloud_springboot_starter_utils_service",
        ":redisson"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_rpc_retrofit_impl",
    exports = [
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core",
        "//3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core",
        "//3rdparty/jvm/org/reflections:reflections",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_rpc_retrofit_impl",
        ":dexcloud_springboot_starter_commons",
        ":dexcloud_springboot_starter_msb"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_sm_impl",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/org/glassfish/jersey/bundles/repackaged:jersey_guava",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_sm_impl",
        ":dexcloud_springboot_starter_commons",
        ":dexcloud_springboot_starter_kafka",
        ":dexcloud_springboot_starter_rpc_retrofit"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter",
        ":dexcloud_springboot_starter_commons",
        ":dexcloud_springboot_starter_configcenter",
        ":dexcloud_springboot_starter_msb"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_commons",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_commons",
        ":dexcloud_springboot_commons_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_configcenter",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/i18n:zenap_i18n_core",
        "//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_actuator",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_config",
        "//3rdparty/jvm/org/springframework/retry:spring_retry",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_configcenter",
        ":dexcloud_springboot_configcenter_commons",
        ":dexcloud_springboot_configcenter_configclient",
        ":dexcloud_springboot_starter_kafka"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_db_jdbc",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_db_jdbc",
        ":dexcloud_springboot_db_jdbc_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_gr",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_gr",
        ":dexcloud_springboot_gr_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_internalcontrol_agent",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_internalcontrol_agent",
        ":dexcloud_springboot_internalcontrol_agent_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_jersey",
    exports = [
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_web",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_jersey",
        ":dexcloud_springboot_jersey_impl",
        ":dexcloud_springboot_jersey_swagger_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_jetty",
    exports = [
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_web",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_jetty",
        ":dexcloud_springboot_jetty_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_kafka",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_kafka",
        ":dexcloud_springboot_kafka_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_msb",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_msb",
        ":dexcloud_springboot_msb_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_redis_redisson",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_redis_redisson",
        ":dexcloud_springboot_redis_redisson_impl",
        ":dexcloud_springboot_redis_redisson_mult_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_rpc_retrofit",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_rpc_retrofit",
        ":dexcloud_springboot_rpc_retrofit_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_sm",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_sm",
        ":dexcloud_springboot_sm_impl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_utils_service",
    exports = [
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_utils_service",
        ":dexcloud_springboot_common_utils",
        ":dexcloud_springboot_datetime_utils",
        ":dexcloud_springboot_initckeck_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_starter_web",
    exports = [
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_actuator",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_web",
        ":dexcloud_springboot_starter_jersey",
        ":dexcloud_springboot_starter_jetty"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "dexcloud_springboot_uiframe_agent_impl",
    exports = [
        "//3rdparty/jvm/commons_io:commons_io",
        "//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_uiframe_agent_impl",
        ":dexcloud_springboot_starter_commons",
        ":dexcloud_springboot_starter_configcenter",
        ":dexcloud_springboot_starter_kafka",
        ":dexcloud_springboot_starter_rpc_retrofit"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "redisson",
    exports = [
        "//3rdparty/jvm/com/googlecode/java_ipv6:java_ipv6",
        "//3rdparty/jvm/de/ruedigermoeller:fst",
        "//3rdparty/jvm/io/projectreactor:reactor_core",
        "//3rdparty/jvm/io/reactivex/rxjava3:rxjava",
        "//3rdparty/jvm/javax/cache:cache_api",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river",
        "//3rdparty/jvm/org/jodd:jodd_bean",
        "//external:jar/com/zte/oes/dexcloud/redisson"
    ],
    visibility = [
        "//visibility:public"
    ]
)


