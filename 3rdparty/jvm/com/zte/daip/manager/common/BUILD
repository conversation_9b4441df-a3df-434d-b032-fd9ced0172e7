java_library(
    name = "daip_alarm_api",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_alarm_api",
        ":daip_patcher_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_alarm_client_paas",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_alarm_client_paas",
        ":daip_alarm_api",
        ":daip_common_client_paas"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_cache_common",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/redisson:redisson",
        "//external:jar/com/zte/daip/manager/common/daip_cache_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_cache_paas",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/redisson:redisson",
        "//3rdparty/jvm/org/springframework/session:spring_session_core",
        "//external:jar/com/zte/daip/manager/common/daip_cache_paas",
        ":daip_cache_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_common_client_paas",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit",
        "//external:jar/com/zte/daip/manager/common/daip_common_client_paas"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_common_i18n",
    exports = [
        "//3rdparty/jvm/com/squareup/okhttp3:okhttp",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core",
        "//3rdparty/jvm/org/jdom:jdom2",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_common_i18n",
        ":daip_common_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_common_utils",
    exports = [
        "//3rdparty/jvm/com/alibaba:cooma",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/com/networknt:json_schema_validator",
        "//3rdparty/jvm/com/zte/zdh:zdh_commons",
        "//3rdparty/jvm/commons_codec:commons_codec",
        "//3rdparty/jvm/commons_configuration:commons_configuration",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//3rdparty/jvm/org/apache/commons:commons_collections4",
        "//3rdparty/jvm/org/apache/sshd:sshd_common",
        "//3rdparty/jvm/org/apache/sshd:sshd_core",
        "//3rdparty/jvm/org/apache/sshd:sshd_scp",
        "//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/redisson:redisson",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//3rdparty/jvm/org/springframework:spring_webmvc",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//external:jar/com/zte/daip/manager/common/daip_common_utils",
        ":daip_logback_appender",
        ":daip_sensitive_log"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_communication",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth",
        "//external:jar/com/zte/daip/manager/common/daip_communication",
        ":daip_communication_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_communication_common",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth",
        "//external:jar/com/zte/daip/manager/common/daip_communication_common",
        ":daip_cache_common",
        ":daip_communication_kafka"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_communication_kafka",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth",
        "//external:jar/com/zte/daip/manager/common/daip_communication_kafka"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_communication_replyproducer",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth",
        "//external:jar/com/zte/daip/manager/common/daip_communication_replyproducer",
        ":daip_communication"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_configcenter_api",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_configcenter_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_configcenter_client_paas",
    exports = [
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_configcenter_client_paas",
        ":daip_common_client_paas",
        ":daip_configcenter_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_deployer_api",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_deployer_api",
        ":daip_common_utils",
        ":daip_patcher_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_deployer_client_paas",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_deployer_client_paas",
        ":daip_common_client_paas",
        ":daip_common_utils",
        ":daip_deployer_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_event_api",
    exports = [
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_event_api",
        ":daip_common_utils",
        ":daip_event_beans"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_event_beans",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_event_beans"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_event_reporter_api",
    exports = [
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth",
        "//external:jar/com/zte/daip/manager/common/daip_event_reporter_api",
        ":daip_common_utils",
        ":daip_event_beans"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_event_reporter_client",
    exports = [
        "//3rdparty/jvm/com/lmax:disruptor",
        "//3rdparty/jvm/org/springframework:spring_tx",
        "//external:jar/com/zte/daip/manager/common/daip_event_reporter_client",
        ":daip_communication",
        ":daip_event_api",
        ":daip_event_beans",
        ":daip_event_reporter_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_filemanagement_api",
    exports = [
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_filemanagement_api",
        ":daip_common_utils",
        ":daip_filemanagement_common_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_filemanagement_client_paas",
    exports = [
        "//external:jar/com/zte/daip/manager/common/daip_filemanagement_client_paas",
        ":daip_common_client_paas",
        ":daip_filemanagement_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_filemanagement_common_util",
    exports = [
        "//external:jar/com/zte/daip/manager/common/daip_filemanagement_common_util",
        ":daip_common_utils",
        ":daip_http_auth"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_filemanagement_download_provider",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/springframework/retry:spring_retry",
        "//external:jar/com/zte/daip/manager/common/daip_filemanagement_download_provider",
        ":daip_common_utils",
        ":daip_filemanagement_common_util",
        ":daip_http_auth",
        ":daip_security"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_http_auth",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_codec:commons_codec",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/io/jsonwebtoken:jjwt",
        "//3rdparty/jvm/javax/servlet:servlet_api",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//external:jar/com/zte/daip/manager/common/daip_http_auth",
        ":daip_common_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_httpclient",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/auth0:java_jwt",
        "//3rdparty/jvm/commons_codec:commons_codec",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/javax/servlet:servlet_api",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_openfeign",
        "//external:jar/com/zte/daip/manager/common/daip_httpclient",
        ":daip_common_utils",
        ":daip_event_reporter_api",
        ":daip_http_auth"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_init_paas",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_init_paas",
        ":daip_common_utils",
        ":daip_utils_paas"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_inspection_api",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/okhttp:okhttp",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_inspection_api",
        ":daip_inspection_common",
        ":daip_patcher_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_inspection_client_paas",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//external:jar/com/zte/daip/manager/common/daip_inspection_client_paas",
        ":daip_common_client_paas",
        ":daip_inspection_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_inspection_common",
    exports = [
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_inspection_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_logback_appender",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//external:jar/com/zte/daip/manager/common/daip_logback_appender"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_patcher_api",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_patcher_api",
        ":daip_common_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_patcher_impl_client_paas",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_patcher_impl_client_paas",
        ":daip_common_client_paas",
        ":daip_common_utils",
        ":daip_patcher_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_response",
    exports = [
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_response",
        ":daip_common_i18n",
        ":spring_message_resource_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_security",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_fileupload:commons_fileupload",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/apache/xmlgraphics:batik_css",
        "//3rdparty/jvm/org/owasp/esapi:esapi",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/xerces:xercesImpl",
        "//external:jar/com/zte/daip/manager/common/daip_security"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_sensitive_log",
    exports = [
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_sensitive_log"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_task_api",
    exports = [
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_task_api",
        ":daip_patcher_api",
        ":daip_response",
        ":daip_task_common_bean"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_task_client",
    exports = [
        "//external:jar/com/zte/daip/manager/common/daip_task_client",
        ":daip_common_client_paas",
        ":daip_task_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_task_common_bean",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/org/apache/commons:commons_collections4",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//external:jar/com/zte/daip/manager/common/daip_task_common_bean",
        ":daip_common_utils",
        ":daip_security",
        ":spring_message_resource_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_task_message",
    exports = [
        "//external:jar/com/zte/daip/manager/common/daip_task_message",
        ":daip_communication",
        ":daip_task_common_bean"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_task_worker",
    exports = [
        "//3rdparty/jvm/org/springframework/statemachine:spring_statemachine_core",
        "//external:jar/com/zte/daip/manager/common/daip_task_worker",
        ":daip_common_utils",
        ":daip_event_reporter_client",
        ":daip_task_api",
        ":daip_task_common_bean",
        ":daip_task_message"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_utils_msb",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service",
        "//3rdparty/jvm/commons_codec:commons_codec",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//external:jar/com/zte/daip/manager/common/daip_utils_msb",
        ":daip_common_utils",
        ":daip_httpclient"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_utils_paas",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_center_cipher_springboot_agent",
        "//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_utils_paas",
        ":daip_common_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_whale_api",
    exports = [
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_whale_api",
        ":daip_common_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_whale_client_paas",
    exports = [
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/daip_whale_client_paas",
        ":daip_common_client_paas",
        ":daip_common_utils",
        ":daip_whale_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_message_resource_starter",
    exports = [
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/common/spring_message_resource_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)


