java_library(
    name = "daip_patcher_application",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/patcher/daip_patcher_application",
        ":daip_patcher_domain",
        ":daip_patcher_inner_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_patcher_domain",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_replyproducer",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_client",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_client_paas",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework/retry:spring_retry",
        "//3rdparty/jvm/org/springframework:spring_context_support",
        "//external:jar/com/zte/daip/manager/patcher/daip_patcher_domain",
        ":daip_patcher_infrastructure_api",
        ":daip_patcher_inner_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_patcher_handler_api",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/patcher/daip_patcher_handler_api",
        ":daip_patcher_inner_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_patcher_impl_inner_client_paas",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/patcher/daip_patcher_impl_inner_client_paas",
        ":daip_patcher_inner_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_patcher_infrastructure_api",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_client",
        "//3rdparty/jvm/com/zte/daip/manager/miniagent:daip_miniagent_seed",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_data_jpa",
        "//external:jar/com/zte/daip/manager/patcher/daip_patcher_infrastructure_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_patcher_inner_api",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/io/springfox:springfox_swagger2",
        "//3rdparty/jvm/io/springfox:springfox_swagger_ui",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/patcher/daip_patcher_inner_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_patcher_interfaces",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//external:jar/com/zte/daip/manager/patcher/daip_patcher_interfaces",
        ":daip_patcher_application",
        ":daip_patcher_domain",
        ":daip_patcher_inner_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "daip_patcher_task_worker",
    exports = [
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_client_paas",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_client",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_task_worker",
        "//external:jar/com/zte/daip/manager/patcher/daip_patcher_task_worker",
        ":daip_patcher_domain"
    ],
    visibility = [
        "//visibility:public"
    ]
)


