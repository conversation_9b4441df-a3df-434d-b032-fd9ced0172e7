java_library(
    name = "daip_miniagent_seed",
    exports = [
        "//3rdparty/jvm/com/alibaba:fastjson",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_communication",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_httpclient",
        "//3rdparty/jvm/com/zte/daip/manager/common:daip_security",
        "//3rdparty/jvm/commons_configuration:commons_configuration",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_lang:commons_lang",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/projectlombok:lombok",
        "//3rdparty/jvm/org/zeroturnaround:zt_zip",
        "//external:jar/com/zte/daip/manager/miniagent/daip_miniagent_seed"
    ],
    visibility = [
        "//visibility:public"
    ]
)


