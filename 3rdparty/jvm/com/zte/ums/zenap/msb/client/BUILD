java_library(
    name = "zenap_msb_client_bundle",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2",
        "//3rdparty/jvm/io/dropwizard:dropwizard_core",
        "//external:jar/com/zte/ums/zenap/msb/client/zenap_msb_client_bundle",
        ":zenap_msb_client_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zenap_msb_client_core",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service",
        "//3rdparty/jvm/io/dropwizard:dropwizard_jackson",
        "//external:jar/com/zte/ums/zenap/msb/client/zenap_msb_client_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)


