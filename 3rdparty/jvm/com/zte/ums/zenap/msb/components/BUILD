java_library(
    name = "msb_service",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base",
        "//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/com/mikesamuel:json_sanitizer",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/org/apache/commons:commons_lang3",
        "//3rdparty/jvm/org/apache/httpcomponents:httpasyncclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpclient",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_http",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_client",
        "//3rdparty/jvm/org/immutables:value",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/com/zte/ums/zenap/msb/components/msb_service"
    ],
    visibility = [
        "//visibility:public"
    ]
)


