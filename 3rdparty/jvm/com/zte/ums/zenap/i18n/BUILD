java_library(
    name = "zenap_i18n_core",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/com/mikesamuel:json_sanitizer",
        "//3rdparty/jvm/org/reflections:reflections",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/com/zte/ums/zenap/i18n/zenap_i18n_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)


