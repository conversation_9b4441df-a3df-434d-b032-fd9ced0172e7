java_library(
    name = "zenap_center_cipher_springboot_agent",
    exports = [
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_configcenter",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_rpc_retrofit",
        "//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_sm",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/com/zte/ums/zenap/util/cipher/zenap_center_cipher_springboot_agent",
        ":zenap_kms_agent_sdk"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zenap_cipher",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit",
        "//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on",
        "//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on",
        "//external:jar/com/zte/ums/zenap/util/cipher/zenap_cipher"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zenap_kms_agent_sdk",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/commons_beanutils:commons_beanutils",
        "//3rdparty/jvm/commons_collections:commons_collections",
        "//3rdparty/jvm/net/sf/json_lib:json_lib_jar_jdk15",
        "//3rdparty/jvm/org/apache/httpcomponents:httpclient",
        "//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/com/zte/ums/zenap/util/cipher/zenap_kms_agent_sdk",
        ":zenap_cipher"
    ],
    visibility = [
        "//visibility:public"
    ]
)


