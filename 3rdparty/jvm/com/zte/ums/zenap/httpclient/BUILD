java_library(
    name = "zenap_httpclient_retrofit",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_bundle",
        "//3rdparty/jvm/io/dropwizard:dropwizard_core",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_api",
        "//external:jar/com/zte/ums/zenap/httpclient/zenap_httpclient_retrofit",
        ":zenap_httpclient_retrofit_core",
        ":zenap_httpclient_retrofit_istio"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zenap_httpclient_retrofit_circuitbreaker",
    exports = [
        "//3rdparty/jvm/com/squareup/retrofit2:retrofit",
        "//3rdparty/jvm/com/zte/ums/zenap/okhttp:okhttp",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_circuitbreaker",
        "//external:jar/com/zte/ums/zenap/httpclient/zenap_httpclient_retrofit_circuitbreaker"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zenap_httpclient_retrofit_core",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_jmx",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_consumer",
        "//3rdparty/jvm/io/github/resilience4j:resilience4j_rxjava2",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//external:jar/com/zte/ums/zenap/httpclient/zenap_httpclient_retrofit_core",
        ":zenap_httpclient_retrofit_circuitbreaker"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zenap_httpclient_retrofit_istio",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2",
        "//3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core",
        "//external:jar/com/zte/ums/zenap/httpclient/zenap_httpclient_retrofit_istio",
        ":zenap_httpclient_retrofit_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)


