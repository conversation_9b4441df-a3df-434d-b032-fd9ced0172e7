java_library(
    name = "zenap_hk2",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/zte/ums/zenap/dropwizard/ext:zenap_dropwizard_ext",
        "//3rdparty/jvm/io/dropwizard:dropwizard_core",
        "//3rdparty/jvm/org/reflections:reflections",
        "//external:jar/com/zte/ums/zenap/hk2/zenap_hk2",
        ":zenap_hk2_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zenap_hk2_core",
    exports = [
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_api",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_locator",
        "//3rdparty/jvm/org/reflections:reflections",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/com/zte/ums/zenap/hk2/zenap_hk2_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zenap_hk2_core_pom",
    exports = [
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_api",
        "//3rdparty/jvm/org/glassfish/hk2:hk2_locator",
        "//3rdparty/jvm/org/reflections:reflections",
        "//3rdparty/jvm/org/slf4j:slf4j_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)


