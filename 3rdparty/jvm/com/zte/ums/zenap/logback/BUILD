java_library(
    name = "zenap_logback_core",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_access",
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/ch/qos/logback:logback_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/github/loki4j:loki_logback_appender_jdk8",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//external:jar/com/zte/ums/zenap/logback/zenap_logback_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)


