java_library(
    name = "zenap_kafka_rule",
    exports = [
        "//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core_pom",
        "//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core",
        "//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_core",
        "//3rdparty/jvm/io/dropwizard/metrics:metrics_core",
        "//3rdparty/jvm/org/apache/kafka:kafka_clients",
        "//external:jar/com/zte/ums/zenap/kafka/zenap_kafka_rule"
    ],
    visibility = [
        "//visibility:public"
    ]
)


