java_library(
    name = "zdh_commons",
    exports = [
        "//3rdparty/jvm/com/alibaba:cooma",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/com/google/guava:guava",
        "//3rdparty/jvm/commons_codec:commons_codec",
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_logging:commons_logging",
        "//3rdparty/jvm/io/fabric8:kubernetes_client",
        "//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem",
        "//3rdparty/jvm/junit:junit",
        "//3rdparty/jvm/org/apache/httpcomponents:httpclient",
        "//3rdparty/jvm/org/apache/httpcomponents:httpcore",
        "//3rdparty/jvm/org/reflections:reflections",
        "//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons",
        "//3rdparty/jvm/org/springframework/security:spring_security_crypto",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//external:jar/com/zte/zdh/zdh_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)


