java_library(
    name = "jackson_module_afterburner",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//external:jar/com/fasterxml/jackson/module/jackson_module_afterburner"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jackson_module_jaxb_annotations",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/jakarta/activation:jakarta_activation_api",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//external:jar/com/fasterxml/jackson/module/jackson_module_jaxb_annotations"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jackson_module_parameter_names",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//external:jar/com/fasterxml/jackson/module/jackson_module_parameter_names"
    ],
    visibility = [
        "//visibility:public"
    ]
)


