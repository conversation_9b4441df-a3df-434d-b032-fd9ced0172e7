java_library(
    name = "jackson_jaxrs_base",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//external:jar/com/fasterxml/jackson/jaxrs/jackson_jaxrs_base"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jackson_jaxrs_json_provider",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations",
        "//external:jar/com/fasterxml/jackson/jaxrs/jackson_jaxrs_json_provider",
        ":jackson_jaxrs_base"
    ],
    visibility = [
        "//visibility:public"
    ]
)


