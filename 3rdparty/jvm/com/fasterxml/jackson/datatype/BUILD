java_library(
    name = "jackson_datatype_guava",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/google/guava:guava",
        "//external:jar/com/fasterxml/jackson/datatype/jackson_datatype_guava"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jackson_datatype_jdk8",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//external:jar/com/fasterxml/jackson/datatype/jackson_datatype_jdk8"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jackson_datatype_joda",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/joda_time:joda_time",
        "//external:jar/com/fasterxml/jackson/datatype/jackson_datatype_joda"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jackson_datatype_jsr310",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//external:jar/com/fasterxml/jackson/datatype/jackson_datatype_jsr310"
    ],
    visibility = [
        "//visibility:public"
    ]
)


