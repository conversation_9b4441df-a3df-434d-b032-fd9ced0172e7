java_library(
    name = "class_model",
    exports = [
        "//3rdparty/jvm/org/ow2/asm:asm_analysis",
        "//external:jar/org/glassfish/hk2/class_model"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "hk2",
    exports = [
        "//external:jar/org/glassfish/hk2/hk2",
        ":class_model",
        ":hk2_api",
        ":hk2_core",
        ":hk2_locator",
        ":hk2_runlevel",
        ":hk2_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "hk2_api",
    exports = [
        "//3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//external:jar/org/glassfish/hk2/hk2_api",
        ":hk2_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "hk2_core",
    exports = [
        "//external:jar/org/glassfish/hk2/hk2_core",
        ":hk2_locator",
        ":hk2_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "hk2_locator",
    exports = [
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/javassist:javassist",
        "//external:jar/org/glassfish/hk2/hk2_locator",
        ":hk2_api",
        ":hk2_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "hk2_runlevel",
    exports = [
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//external:jar/org/glassfish/hk2/hk2_runlevel",
        ":hk2_api",
        ":hk2_locator"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "hk2_utils",
    exports = [
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//external:jar/org/glassfish/hk2/hk2_utils"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "osgi_resource_locator",
    exports = [
        "//external:jar/org/glassfish/hk2/osgi_resource_locator"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_bridge",
    exports = [
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//external:jar/org/glassfish/hk2/spring_bridge",
        ":hk2_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)


