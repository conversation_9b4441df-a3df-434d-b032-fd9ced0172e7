java_library(
    name = "jaxb_runtime",
    exports = [
        "//3rdparty/jvm/com/sun/activation:jakarta_activation",
        "//3rdparty/jvm/com/sun/istack:istack_commons_runtime",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//external:jar/org/glassfish/jaxb/jaxb_runtime",
        ":txw2"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "txw2",
    exports = [
        "//external:jar/org/glassfish/jaxb/txw2"
    ],
    visibility = [
        "//visibility:public"
    ]
)


