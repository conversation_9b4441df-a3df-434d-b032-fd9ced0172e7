java_library(
    name = "jersey_client",
    exports = [
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//external:jar/org/glassfish/jersey/core/jersey_client",
        ":jersey_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jersey_common",
    exports = [
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator",
        "//external:jar/org/glassfish/jersey/core/jersey_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jersey_server",
    exports = [
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_jaxb",
        "//external:jar/org/glassfish/jersey/core/jersey_server",
        ":jersey_client",
        ":jersey_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)


