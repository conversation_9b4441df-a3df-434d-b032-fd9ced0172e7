java_library(
    name = "jersey_media_jaxb",
    exports = [
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//external:jar/org/glassfish/jersey/media/jersey_media_jaxb"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jersey_media_json_jackson",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_entity_filtering",
        "//external:jar/org/glassfish/jersey/media/jersey_media_json_jackson"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jersey_media_multipart",
    exports = [
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//3rdparty/jvm/org/jvnet/mimepull:mimepull",
        "//external:jar/org/glassfish/jersey/media/jersey_media_multipart"
    ],
    visibility = [
        "//visibility:public"
    ]
)


