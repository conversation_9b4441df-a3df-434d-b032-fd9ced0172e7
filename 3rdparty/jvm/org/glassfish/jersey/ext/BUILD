java_library(
    name = "jersey_bean_validation",
    exports = [
        "//3rdparty/jvm/jakarta/el:jakarta_el_api",
        "//3rdparty/jvm/jakarta/validation:jakarta_validation_api",
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_server",
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//external:jar/org/glassfish/jersey/ext/jersey_bean_validation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jersey_entity_filtering",
    exports = [
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//external:jar/org/glassfish/jersey/ext/jersey_entity_filtering"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jersey_metainf_services",
    exports = [
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_common",
        "//external:jar/org/glassfish/jersey/ext/jersey_metainf_services"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jersey_spring5",
    exports = [
        "//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api",
        "//3rdparty/jvm/org/glassfish/hk2:hk2",
        "//3rdparty/jvm/org/glassfish/hk2:spring_bridge",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_server",
        "//3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//3rdparty/jvm/org/springframework:spring_beans",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//external:jar/org/glassfish/jersey/ext/jersey_spring5"
    ],
    visibility = [
        "//visibility:public"
    ]
)


