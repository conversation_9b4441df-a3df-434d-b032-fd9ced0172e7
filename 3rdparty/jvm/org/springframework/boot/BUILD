java_library(
    name = "spring_boot",
    exports = [
        "//3rdparty/jvm/org/springframework:spring_context",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//external:jar/org/springframework/boot/spring_boot"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_actuator",
    exports = [
        "//external:jar/org/springframework/boot/spring_boot_actuator",
        ":spring_boot"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_actuator_autoconfigure",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310",
        "//external:jar/org/springframework/boot/spring_boot_actuator_autoconfigure",
        ":spring_boot",
        ":spring_boot_actuator",
        ":spring_boot_autoconfigure"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_autoconfigure",
    exports = [
        "//external:jar/org/springframework/boot/spring_boot_autoconfigure",
        ":spring_boot"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter",
    exports = [
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//external:jar/org/springframework/boot/spring_boot_starter",
        ":spring_boot",
        ":spring_boot_autoconfigure",
        ":spring_boot_starter_logging"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_actuator",
    exports = [
        "//3rdparty/jvm/io/micrometer:micrometer_core",
        "//external:jar/org/springframework/boot/spring_boot_starter_actuator",
        ":spring_boot_actuator_autoconfigure",
        ":spring_boot_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_aop",
    exports = [
        "//3rdparty/jvm/org/aspectj:aspectjweaver",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//external:jar/org/springframework/boot/spring_boot_starter_aop",
        ":spring_boot_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_data_jpa",
    exports = [
        "//3rdparty/jvm/jakarta/persistence:jakarta_persistence_api",
        "//3rdparty/jvm/jakarta/transaction:jakarta_transaction_api",
        "//3rdparty/jvm/org/hibernate:hibernate_core",
        "//3rdparty/jvm/org/springframework/data:spring_data_jpa",
        "//3rdparty/jvm/org/springframework:spring_aspects",
        "//external:jar/org/springframework/boot/spring_boot_starter_data_jpa",
        ":spring_boot_starter_aop",
        ":spring_boot_starter_jdbc"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_jdbc",
    exports = [
        "//3rdparty/jvm/com/zaxxer:HikariCP",
        "//3rdparty/jvm/org/springframework:spring_jdbc",
        "//external:jar/org/springframework/boot/spring_boot_starter_jdbc",
        ":spring_boot_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_jersey",
    exports = [
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet",
        "//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core",
        "//3rdparty/jvm/org/glassfish/jersey/core:jersey_server",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation",
        "//3rdparty/jvm/org/glassfish/jersey/ext:jersey_spring5",
        "//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_json_jackson",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//external:jar/org/springframework/boot/spring_boot_starter_jersey",
        ":spring_boot_starter_json",
        ":spring_boot_starter_tomcat",
        ":spring_boot_starter_validation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_jetty",
    exports = [
        "//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api",
        "//3rdparty/jvm/jakarta/websocket:jakarta_websocket_api",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:javax_websocket_server_impl",
        "//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_server",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlets",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_webapp",
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//external:jar/org/springframework/boot/spring_boot_starter_jetty"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_json",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8",
        "//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310",
        "//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//external:jar/org/springframework/boot/spring_boot_starter_json",
        ":spring_boot_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_logging",
    exports = [
        "//3rdparty/jvm/ch/qos/logback:logback_classic",
        "//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j",
        "//3rdparty/jvm/org/slf4j:jul_to_slf4j",
        "//external:jar/org/springframework/boot/spring_boot_starter_logging"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_test",
    exports = [
        "//3rdparty/jvm/com/jayway/jsonpath:json_path",
        "//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api",
        "//3rdparty/jvm/org/assertj:assertj_core",
        "//3rdparty/jvm/org/hamcrest:hamcrest",
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter",
        "//3rdparty/jvm/org/junit/vintage:junit_vintage_engine",
        "//3rdparty/jvm/org/mockito:mockito_core",
        "//3rdparty/jvm/org/mockito:mockito_junit_jupiter",
        "//3rdparty/jvm/org/skyscreamer:jsonassert",
        "//3rdparty/jvm/org/springframework:spring_core",
        "//3rdparty/jvm/org/springframework:spring_test",
        "//3rdparty/jvm/org/xmlunit:xmlunit_core",
        "//external:jar/org/springframework/boot/spring_boot_starter_test",
        ":spring_boot_starter",
        ":spring_boot_test",
        ":spring_boot_test_autoconfigure"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_tomcat",
    exports = [
        "//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api",
        "//3rdparty/jvm/org/apache/tomcat/embed:tomcat_embed_core",
        "//3rdparty/jvm/org/apache/tomcat/embed:tomcat_embed_websocket",
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//external:jar/org/springframework/boot/spring_boot_starter_tomcat"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_validation",
    exports = [
        "//3rdparty/jvm/org/glassfish:jakarta_el",
        "//3rdparty/jvm/org/hibernate/validator:hibernate_validator",
        "//external:jar/org/springframework/boot/spring_boot_starter_validation",
        ":spring_boot_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_starter_web",
    exports = [
        "//3rdparty/jvm/org/springframework:spring_web",
        "//3rdparty/jvm/org/springframework:spring_webmvc",
        "//external:jar/org/springframework/boot/spring_boot_starter_web",
        ":spring_boot_starter",
        ":spring_boot_starter_json",
        ":spring_boot_starter_tomcat"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_test",
    exports = [
        "//external:jar/org/springframework/boot/spring_boot_test",
        ":spring_boot"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_boot_test_autoconfigure",
    exports = [
        "//external:jar/org/springframework/boot/spring_boot_test_autoconfigure",
        ":spring_boot",
        ":spring_boot_autoconfigure",
        ":spring_boot_test"
    ],
    visibility = [
        "//visibility:public"
    ]
)


