java_library(
    name = "spring_cloud_commons",
    exports = [
        "//3rdparty/jvm/org/springframework/security:spring_security_crypto",
        "//external:jar/org/springframework/cloud/spring_cloud_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_cloud_config_client",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/org/apache/httpcomponents:httpclient",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//external:jar/org/springframework/cloud/spring_cloud_config_client",
        ":spring_cloud_commons",
        ":spring_cloud_context"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_cloud_context",
    exports = [
        "//3rdparty/jvm/org/springframework/security:spring_security_crypto",
        "//external:jar/org/springframework/cloud/spring_cloud_context"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_cloud_netflix_archaius",
    exports = [
        "//external:jar/org/springframework/cloud/spring_cloud_netflix_archaius"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)



java_library(
    name = "spring_cloud_netflix_ribbon",
    exports = [
        "//external:jar/org/springframework/cloud/spring_cloud_netflix_ribbon",
        ":spring_cloud_netflix_archaius"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)



java_library(
    name = "spring_cloud_openfeign_core",
    exports = [
        "//3rdparty/jvm/io/github/openfeign/form:feign_form_spring",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure",
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop",
        "//external:jar/org/springframework/cloud/spring_cloud_openfeign_core",
        ":spring_cloud_netflix_ribbon"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)



java_library(
    name = "spring_cloud_sleuth_core",
    exports = [
        "//3rdparty/jvm/io/zipkin/brave:brave",
        "//3rdparty/jvm/io/zipkin/brave:brave_context_slf4j",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpasyncclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpclient",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_jms",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_clients",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_streams",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_rpc",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_rabbit",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_web",
        "//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_webmvc",
        "//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_metrics_micrometer",
        "//3rdparty/jvm/org/aspectj:aspectjrt",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//external:jar/org/springframework/cloud/spring_cloud_sleuth_core",
        ":spring_cloud_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_cloud_starter",
    exports = [
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter",
        "//3rdparty/jvm/org/springframework/security:spring_security_rsa",
        "//external:jar/org/springframework/cloud/spring_cloud_starter",
        ":spring_cloud_commons",
        ":spring_cloud_context"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_cloud_starter_config",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//external:jar/org/springframework/cloud/spring_cloud_starter_config",
        ":spring_cloud_config_client",
        ":spring_cloud_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_cloud_starter_openfeign",
    exports = [
        "//3rdparty/jvm/io/github/openfeign:feign_core",
        "//3rdparty/jvm/io/github/openfeign:feign_hystrix",
        "//3rdparty/jvm/io/github/openfeign:feign_slf4j",
        "//3rdparty/jvm/org/springframework:spring_web",
        "//external:jar/org/springframework/cloud/spring_cloud_starter_openfeign",
        ":spring_cloud_commons",
        ":spring_cloud_openfeign_core",
        ":spring_cloud_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_cloud_starter_sleuth",
    exports = [
        "//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop",
        "//external:jar/org/springframework/cloud/spring_cloud_starter_sleuth",
        ":spring_cloud_sleuth_core",
        ":spring_cloud_starter"
    ],
    visibility = [
        "//visibility:public"
    ]
)


