java_library(
    name = "spring_plugin_core",
    exports = [
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/springframework:spring_aop",
        "//3rdparty/jvm/org/springframework:spring_beans",
        "//3rdparty/jvm/org/springframework:spring_context",
        "//external:jar/org/springframework/plugin/spring_plugin_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_plugin_metadata",
    exports = [
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/org/springframework/plugin/spring_plugin_metadata",
        ":spring_plugin_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)


