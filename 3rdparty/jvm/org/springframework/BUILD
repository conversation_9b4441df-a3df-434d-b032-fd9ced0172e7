java_library(
    name = "spring_aop",
    exports = [
        "//external:jar/org/springframework/spring_aop",
        ":spring_beans",
        ":spring_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_aspects",
    exports = [
        "//3rdparty/jvm/org/aspectj:aspectjweaver",
        "//external:jar/org/springframework/spring_aspects"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_beans",
    exports = [
        "//external:jar/org/springframework/spring_beans",
        ":spring_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_context",
    exports = [
        "//external:jar/org/springframework/spring_context",
        ":spring_aop",
        ":spring_beans",
        ":spring_core",
        ":spring_expression"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_context_support",
    exports = [
        "//external:jar/org/springframework/spring_context_support",
        ":spring_beans",
        ":spring_context",
        ":spring_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_core",
    exports = [
        "//external:jar/org/springframework/spring_core",
        ":spring_jcl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_expression",
    exports = [
        "//external:jar/org/springframework/spring_expression",
        ":spring_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_jcl",
    exports = [
        "//external:jar/org/springframework/spring_jcl"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_jdbc",
    exports = [
        "//external:jar/org/springframework/spring_jdbc",
        ":spring_beans",
        ":spring_core",
        ":spring_tx"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_messaging",
    exports = [
        "//external:jar/org/springframework/spring_messaging",
        ":spring_beans",
        ":spring_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_orm",
    exports = [
        "//external:jar/org/springframework/spring_orm",
        ":spring_beans",
        ":spring_core",
        ":spring_jdbc",
        ":spring_tx"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_test",
    exports = [
        "//external:jar/org/springframework/spring_test",
        ":spring_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_tx",
    exports = [
        "//external:jar/org/springframework/spring_tx",
        ":spring_beans",
        ":spring_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_web",
    exports = [
        "//external:jar/org/springframework/spring_web",
        ":spring_beans",
        ":spring_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "spring_webmvc",
    exports = [
        "//external:jar/org/springframework/spring_webmvc",
        ":spring_aop",
        ":spring_beans",
        ":spring_context",
        ":spring_core",
        ":spring_expression",
        ":spring_web"
    ],
    visibility = [
        "//visibility:public"
    ]
)


