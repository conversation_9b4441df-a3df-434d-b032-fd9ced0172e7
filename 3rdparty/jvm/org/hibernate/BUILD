java_library(
    name = "hibernate_core",
    exports = [
        "//3rdparty/jvm/antlr:antlr",
        "//3rdparty/jvm/com/fasterxml:classmate",
        "//3rdparty/jvm/javax/activation:javax_activation_api",
        "//3rdparty/jvm/javax/persistence:javax_persistence_api",
        "//3rdparty/jvm/javax/xml/bind:jaxb_api",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/org/dom4j:dom4j",
        "//3rdparty/jvm/org/glassfish/jaxb:jaxb_runtime",
        "//3rdparty/jvm/org/hibernate/common:hibernate_commons_annotations",
        "//3rdparty/jvm/org/javassist:javassist",
        "//3rdparty/jvm/org/jboss/logging:jboss_logging",
        "//3rdparty/jvm/org/jboss/spec/javax/transaction:jboss_transaction_api_1_2_spec",
        "//3rdparty/jvm/org/jboss:jandex",
        "//external:jar/org/hibernate/hibernate_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)


