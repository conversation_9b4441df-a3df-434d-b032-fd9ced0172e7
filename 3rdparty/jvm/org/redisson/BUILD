java_library(
    name = "redisson",
    exports = [
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml",
        "//3rdparty/jvm/io/netty:netty_buffer",
        "//3rdparty/jvm/io/netty:netty_codec",
        "//3rdparty/jvm/io/netty:netty_common",
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_resolver_dns",
        "//3rdparty/jvm/io/netty:netty_transport",
        "//3rdparty/jvm/io/projectreactor:reactor_core",
        "//3rdparty/jvm/io/reactivex/rxjava3:rxjava",
        "//3rdparty/jvm/javax/cache:cache_api",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river",
        "//3rdparty/jvm/org/jodd:jodd_bean",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/yaml:snakeyaml",
        "//external:jar/org/redisson/redisson"
    ],
    visibility = [
        "//visibility:public"
    ]
)


