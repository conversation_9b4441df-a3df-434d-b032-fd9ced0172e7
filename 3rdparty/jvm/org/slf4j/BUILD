java_library(
    name = "jcl_over_slf4j",
    exports = [
        "//external:jar/org/slf4j/jcl_over_slf4j",
        ":slf4j_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jul_to_slf4j",
    exports = [
        "//external:jar/org/slf4j/jul_to_slf4j",
        ":slf4j_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "log4j_over_slf4j",
    exports = [
        "//external:jar/org/slf4j/log4j_over_slf4j",
        ":slf4j_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "slf4j_api",
    exports = [
        "//external:jar/org/slf4j/slf4j_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "slf4j_log4j12",
    exports = [
        "//external:jar/org/slf4j/slf4j_log4j12"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)


