java_library(
    name = "jetty_alpn_conscrypt_server",
    exports = [
        "//3rdparty/jvm/org/conscrypt:conscrypt_openjdk_uber",
        "//external:jar/org/eclipse/jetty/jetty_alpn_conscrypt_server",
        ":jetty_alpn_server",
        ":jetty_io"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_alpn_server",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_alpn_server",
        ":jetty_server"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_annotations",
    exports = [
        "//3rdparty/jvm/javax/annotation:javax_annotation_api",
        "//3rdparty/jvm/org/ow2/asm:asm",
        "//3rdparty/jvm/org/ow2/asm:asm_commons",
        "//external:jar/org/eclipse/jetty/jetty_annotations",
        ":jetty_plus",
        ":jetty_webapp"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_client",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_client",
        ":jetty_http",
        ":jetty_io"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_continuation",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_continuation"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_http",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_http",
        ":jetty_io",
        ":jetty_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_io",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_io",
        ":jetty_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_jndi",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_jndi",
        ":jetty_util"
    ],
    visibility = [
        "//3rdparty/jvm:__subpackages__"
    ]
)



java_library(
    name = "jetty_plus",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_plus",
        ":jetty_jndi",
        ":jetty_webapp"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_security",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_security",
        ":jetty_server"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_server",
    exports = [
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//external:jar/org/eclipse/jetty/jetty_server",
        ":jetty_http",
        ":jetty_io"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_servlet",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_servlet",
        ":jetty_security",
        ":jetty_util_ajax"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_servlets",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_servlets",
        ":jetty_continuation",
        ":jetty_http",
        ":jetty_io",
        ":jetty_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_util",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_util_ajax",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_util_ajax",
        ":jetty_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_webapp",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_webapp",
        ":jetty_servlet",
        ":jetty_xml"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "jetty_xml",
    exports = [
        "//external:jar/org/eclipse/jetty/jetty_xml",
        ":jetty_util"
    ],
    visibility = [
        "//visibility:public"
    ]
)


