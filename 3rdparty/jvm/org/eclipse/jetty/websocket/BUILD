java_library(
    name = "javax_websocket_client_impl",
    exports = [
        "//3rdparty/jvm/javax/websocket:javax_websocket_client_api",
        "//external:jar/org/eclipse/jetty/websocket/javax_websocket_client_impl",
        ":websocket_client"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "javax_websocket_server_impl",
    exports = [
        "//3rdparty/jvm/javax/websocket:javax_websocket_api",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_annotations",
        "//external:jar/org/eclipse/jetty/websocket/javax_websocket_server_impl",
        ":javax_websocket_client_impl",
        ":websocket_server"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "websocket_api",
    exports = [
        "//external:jar/org/eclipse/jetty/websocket/websocket_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "websocket_client",
    exports = [
        "//3rdparty/jvm/org/eclipse/jetty:jetty_client",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_io",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//external:jar/org/eclipse/jetty/websocket/websocket_client",
        ":websocket_common"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "websocket_common",
    exports = [
        "//3rdparty/jvm/org/eclipse/jetty:jetty_io",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_util",
        "//external:jar/org/eclipse/jetty/websocket/websocket_common",
        ":websocket_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "websocket_server",
    exports = [
        "//3rdparty/jvm/org/eclipse/jetty:jetty_http",
        "//3rdparty/jvm/org/eclipse/jetty:jetty_servlet",
        "//external:jar/org/eclipse/jetty/websocket/websocket_server",
        ":websocket_client",
        ":websocket_common",
        ":websocket_servlet"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "websocket_servlet",
    exports = [
        "//3rdparty/jvm/javax/servlet:javax_servlet_api",
        "//external:jar/org/eclipse/jetty/websocket/websocket_servlet",
        ":websocket_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)


