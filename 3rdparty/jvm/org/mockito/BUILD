java_library(
    name = "mockito_core",
    exports = [
        "//3rdparty/jvm/net/bytebuddy:byte_buddy",
        "//3rdparty/jvm/net/bytebuddy:byte_buddy_agent",
        "//3rdparty/jvm/org/objenesis:objenesis",
        "//external:jar/org/mockito/mockito_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "mockito_junit_jupiter",
    exports = [
        "//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api",
        "//external:jar/org/mockito/mockito_junit_jupiter",
        ":mockito_core"
    ],
    visibility = [
        "//visibility:public"
    ]
)


