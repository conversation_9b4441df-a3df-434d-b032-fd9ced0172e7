java_library(
    name = "junit_platform_commons",
    exports = [
        "//3rdparty/jvm/org/apiguardian:apiguardian_api",
        "//external:jar/org/junit/platform/junit_platform_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "junit_platform_engine",
    exports = [
        "//3rdparty/jvm/org/apiguardian:apiguardian_api",
        "//3rdparty/jvm/org/opentest4j:opentest4j",
        "//external:jar/org/junit/platform/junit_platform_engine",
        ":junit_platform_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)


