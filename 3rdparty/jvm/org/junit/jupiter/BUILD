java_library(
    name = "junit_jupiter",
    exports = [
        "//external:jar/org/junit/jupiter/junit_jupiter",
        ":junit_jupiter_api",
        ":junit_jupiter_engine",
        ":junit_jupiter_params"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "junit_jupiter_api",
    exports = [
        "//3rdparty/jvm/org/apiguardian:apiguardian_api",
        "//3rdparty/jvm/org/junit/platform:junit_platform_commons",
        "//3rdparty/jvm/org/opentest4j:opentest4j",
        "//external:jar/org/junit/jupiter/junit_jupiter_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "junit_jupiter_engine",
    exports = [
        "//3rdparty/jvm/org/apiguardian:apiguardian_api",
        "//3rdparty/jvm/org/junit/platform:junit_platform_engine",
        "//external:jar/org/junit/jupiter/junit_jupiter_engine",
        ":junit_jupiter_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "junit_jupiter_params",
    exports = [
        "//3rdparty/jvm/org/apiguardian:apiguardian_api",
        "//external:jar/org/junit/jupiter/junit_jupiter_params",
        ":junit_jupiter_api"
    ],
    visibility = [
        "//visibility:public"
    ]
)


