java_library(
    name = "lombok",
    exported_plugins = [
        ":lombok_plugin",
    ],
    visibility = ["//visibility:public"],
    exports = [
        ":lombok_jar",
    ],
)

java_plugin(
    name = "lombok_plugin",
    generates_api = True,
    processor_class = "lombok.launch.AnnotationProcessorHider$AnnotationProcessor",
    deps = [
        ":lombok_jar",
    ],
)

java_library(
    name = "lombok_jar",
    visibility = [
        "//visibility:public",
    ],
    exports = [
        "//external:jar/org/projectlombok/lombok",
    ],
)