java_library(
    name = "httpasyncclient",
    exports = [
        "//3rdparty/jvm/commons_logging:commons_logging",
        "//external:jar/org/apache/httpcomponents/httpasyncclient",
        ":httpclient",
        ":httpcore",
        ":httpcore_nio"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "httpclient",
    exports = [
        "//3rdparty/jvm/commons_codec:commons_codec",
        "//3rdparty/jvm/commons_logging:commons_logging",
        "//external:jar/org/apache/httpcomponents/httpclient",
        ":httpcore"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "httpcore",
    exports = [
        "//external:jar/org/apache/httpcomponents/httpcore"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "httpcore_nio",
    exports = [
        "//external:jar/org/apache/httpcomponents/httpcore_nio",
        ":httpcore"
    ],
    visibility = [
        "//visibility:public"
    ]
)


