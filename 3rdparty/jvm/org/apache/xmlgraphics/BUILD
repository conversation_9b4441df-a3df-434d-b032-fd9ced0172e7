java_library(
    name = "batik_constants",
    exports = [
        "//external:jar/org/apache/xmlgraphics/batik_constants",
        ":batik_shared_resources"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "batik_css",
    exports = [
        "//3rdparty/jvm/xml_apis:xml_apis_ext",
        "//external:jar/org/apache/xmlgraphics/batik_css",
        ":batik_shared_resources",
        ":batik_util",
        ":xmlgraphics_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "batik_i18n",
    exports = [
        "//external:jar/org/apache/xmlgraphics/batik_i18n",
        ":batik_shared_resources"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "batik_shared_resources",
    exports = [
        "//external:jar/org/apache/xmlgraphics/batik_shared_resources"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "batik_util",
    exports = [
        "//external:jar/org/apache/xmlgraphics/batik_util",
        ":batik_constants",
        ":batik_i18n",
        ":batik_shared_resources"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "xmlgraphics_commons",
    exports = [
        "//3rdparty/jvm/commons_io:commons_io",
        "//3rdparty/jvm/commons_logging:commons_logging",
        "//external:jar/org/apache/xmlgraphics/xmlgraphics_commons"
    ],
    visibility = [
        "//visibility:public"
    ]
)


