java_library(
    name = "zookeeper",
    exports = [
        "//3rdparty/jvm/io/netty:netty_handler",
        "//3rdparty/jvm/io/netty:netty_transport_native_epoll",
        "//3rdparty/jvm/log4j:log4j",
        "//3rdparty/jvm/org/apache/yetus:audience_annotations",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/slf4j:slf4j_log4j12",
        "//external:jar/org/apache/zookeeper/zookeeper",
        ":zookeeper_jute"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "zookeeper_jute",
    exports = [
        "//3rdparty/jvm/org/apache/yetus:audience_annotations",
        "//external:jar/org/apache/zookeeper/zookeeper_jute"
    ],
    visibility = [
        "//visibility:public"
    ]
)


