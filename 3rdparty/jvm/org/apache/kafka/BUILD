java_library(
    name = "kafka_2_12",
    exports = [
        "//3rdparty/jvm/com/101tec:zkclient",
        "//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind",
        "//3rdparty/jvm/com/typesafe/scala_logging:scala_logging_2_12",
        "//3rdparty/jvm/com/yammer/metrics:metrics_core",
        "//3rdparty/jvm/net/sf/jopt_simple:jopt_simple",
        "//3rdparty/jvm/org/apache/zookeeper:zookeeper",
        "//3rdparty/jvm/org/scala_lang:scala_library",
        "//3rdparty/jvm/org/scala_lang:scala_reflect",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//external:jar/org/apache/kafka/kafka_2_12",
        ":kafka_clients"
    ],
    visibility = [
        "//visibility:public"
    ]
)



java_library(
    name = "kafka_clients",
    exports = [
        "//3rdparty/jvm/com/github/luben:zstd_jni",
        "//3rdparty/jvm/org/lz4:lz4_java",
        "//3rdparty/jvm/org/slf4j:slf4j_api",
        "//3rdparty/jvm/org/xerial/snappy:snappy_java",
        "//external:jar/org/apache/kafka/kafka_clients"
    ],
    visibility = [
        "//visibility:public"
    ]
)


