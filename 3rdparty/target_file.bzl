# Do not edit. bazel-deps autogenerates this file from.
_JAVA_LIBRARY_TEMPLATE = """
java_library(
  name = "{name}",
  exports = [
      {exports}
  ],
  runtime_deps = [
    {runtime_deps}
  ],
  visibility = [
      "{visibility}"
  ]
)\n"""

_SCALA_IMPORT_TEMPLATE = """
scala_import(
    name = "{name}",
    exports = [
        {exports}
    ],
    jars = [
        {jars}
    ],
    runtime_deps = [
        {runtime_deps}
    ],
    visibility = [
        "{visibility}"
    ]
)
"""

_SCALA_LIBRARY_TEMPLATE = """
scala_library(
    name = "{name}",
    exports = [
        {exports}
    ],
    runtime_deps = [
        {runtime_deps}
    ],
    visibility = [
        "{visibility}"
    ]
)
"""


def _build_external_workspace_from_opts_impl(ctx):
    build_header = ctx.attr.build_header
    separator = ctx.attr.separator
    target_configs = ctx.attr.target_configs

    result_dict = {}
    for key, cfg in target_configs.items():
      build_file_to_target_name = key.split(":")
      build_file = build_file_to_target_name[0]
      target_name = build_file_to_target_name[1]
      if build_file not in result_dict:
        result_dict[build_file] = []
      result_dict[build_file].append(cfg)

    for key, file_entries in result_dict.items():
      build_file_contents = build_header + '\n\n'
      for build_target in file_entries:
        entry_map = {}
        for entry in build_target:
          elements = entry.split(separator)
          build_entry_key = elements[0]
          if elements[1] == "L":
            entry_map[build_entry_key] = [e for e in elements[2::] if len(e) > 0]
          elif elements[1] == "B":
            entry_map[build_entry_key] = (elements[2] == "true" or elements[2] == "True")
          else:
            entry_map[build_entry_key] = elements[2]

        exports_str = ""
        for e in entry_map.get("exports", []):
          exports_str += "\"" + e + "\",\n"

        jars_str = ""
        for e in entry_map.get("jars", []):
          jars_str += "\"" + e + "\",\n"

        runtime_deps_str = ""
        for e in entry_map.get("runtimeDeps", []):
          runtime_deps_str += "\"" + e + "\",\n"

        name = entry_map["name"].split(":")[1]
        if entry_map["lang"] == "java":
            build_file_contents += _JAVA_LIBRARY_TEMPLATE.format(name = name, exports=exports_str, runtime_deps=runtime_deps_str, visibility=entry_map["visibility"])
        elif entry_map["lang"].startswith("scala") and entry_map["kind"] == "import":
            build_file_contents += _SCALA_IMPORT_TEMPLATE.format(name = name, exports=exports_str, jars=jars_str, runtime_deps=runtime_deps_str, visibility=entry_map["visibility"])
        elif entry_map["lang"].startswith("scala") and entry_map["kind"] == "library":
            build_file_contents += _SCALA_LIBRARY_TEMPLATE.format(name = name, exports=exports_str, runtime_deps=runtime_deps_str, visibility=entry_map["visibility"])
        else:
            print(entry_map)

      ctx.file(ctx.path(key + "/BUILD"), build_file_contents, False)
    return None

build_external_workspace_from_opts = repository_rule(
    attrs = {
        "target_configs": attr.string_list_dict(mandatory = True),
        "separator": attr.string(mandatory = True),
        "build_header": attr.string(mandatory = True),
    },
    implementation = _build_external_workspace_from_opts_impl
)




def build_header():
 return """"""

def list_target_data_separator():
 return "|||"

def list_target_data():
    return {
"3rdparty/jvm/antlr:antlr": ["lang||||||java","name||||||//3rdparty/jvm/antlr:antlr","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/antlr/antlr","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/ch/qos/logback:logback_access": ["lang||||||java","name||||||//3rdparty/jvm/ch/qos/logback:logback_access","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/ch/qos/logback:logback_core|||//external:jar/ch/qos/logback/logback_access","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/ch/qos/logback:logback_classic": ["lang||||||java","name||||||//3rdparty/jvm/ch/qos/logback:logback_classic","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/ch/qos/logback:logback_core|||//external:jar/ch/qos/logback/logback_classic","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/ch/qos/logback:logback_core": ["lang||||||java","name||||||//3rdparty/jvm/ch/qos/logback:logback_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/ch/qos/logback/logback_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/101tec:zkclient": ["lang||||||java","name||||||//3rdparty/jvm/com/101tec:zkclient","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/apache/zookeeper:zookeeper|||//external:jar/com/101tec/zkclient","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/alibaba:cooma": ["lang||||||java","name||||||//3rdparty/jvm/com/alibaba:cooma","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/alibaba/cooma","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/alibaba:fastjson": ["lang||||||java","name||||||//3rdparty/jvm/com/alibaba:fastjson","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/alibaba/fastjson","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/auth0:java_jwt": ["lang||||||java","name||||||//3rdparty/jvm/com/auth0:java_jwt","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/auth0/java_jwt","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml:classmate": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml:classmate","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/fasterxml/classmate","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/fasterxml/jackson/core/jackson_annotations","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/core:jackson_core": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/fasterxml/jackson/core/jackson_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//external:jar/com/fasterxml/jackson/core/jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/yaml:snakeyaml|||//external:jar/com/fasterxml/jackson/dataformat/jackson_dataformat_yaml","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/fasterxml/jackson/datatype/jackson_datatype_guava","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/fasterxml/jackson/datatype/jackson_datatype_jdk8","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_joda": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_joda","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//external:jar/com/fasterxml/jackson/datatype/jackson_datatype_joda|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/joda_time:joda_time|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//external:jar/com/fasterxml/jackson/datatype/jackson_datatype_jsr310","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/fasterxml/jackson/jaxrs/jackson_jaxrs_base","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base|||//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations|||//external:jar/com/fasterxml/jackson/jaxrs/jackson_jaxrs_json_provider","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_afterburner": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_afterburner","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/fasterxml/jackson/module/jackson_module_afterburner","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api|||//external:jar/com/fasterxml/jackson/module/jackson_module_jaxb_annotations|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/jakarta/activation:jakarta_activation_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names": ["lang||||||java","name||||||//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/fasterxml/jackson/module/jackson_module_parameter_names","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/github/ben_manes/caffeine:caffeine": ["lang||||||java","name||||||//3rdparty/jvm/com/github/ben_manes/caffeine:caffeine","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/checkerframework:checker_qual|||//3rdparty/jvm/com/google/errorprone:error_prone_annotations|||//external:jar/com/github/ben_manes/caffeine/caffeine","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/github/loki4j:loki_logback_appender_jdk8": ["lang||||||java","name||||||//3rdparty/jvm/com/github/loki4j:loki_logback_appender_jdk8","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//external:jar/com/github/loki4j/loki_logback_appender_jdk8","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/github/luben:zstd_jni": ["lang||||||java","name||||||//3rdparty/jvm/com/github/luben:zstd_jni","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/github/luben/zstd_jni","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/github/mifmif:generex": ["lang||||||java","name||||||//3rdparty/jvm/com/github/mifmif:generex","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/dk/brics/automaton:automaton|||//external:jar/com/github/mifmif/generex","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/google/code/findbugs:jsr305": ["lang||||||java","name||||||//3rdparty/jvm/com/google/code/findbugs:jsr305","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/google/code/findbugs/jsr305","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/google/errorprone:error_prone_annotations": ["lang||||||java","name||||||//3rdparty/jvm/com/google/errorprone:error_prone_annotations","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/google/errorprone/error_prone_annotations","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/google/guava:failureaccess": ["lang||||||java","name||||||//3rdparty/jvm/com/google/guava:failureaccess","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/google/guava/failureaccess","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/google/guava:guava": ["lang||||||java","name||||||//3rdparty/jvm/com/google/guava:guava","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/errorprone:error_prone_annotations|||//3rdparty/jvm/com/google/guava:failureaccess|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/com/google/guava:listenablefuture|||//3rdparty/jvm/com/google/j2objc:j2objc_annotations|||//3rdparty/jvm/org/checkerframework:checker_qual|||//external:jar/com/google/guava/guava","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/google/guava:listenablefuture": ["lang||||||java","name||||||//3rdparty/jvm/com/google/guava:listenablefuture","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/google/guava/listenablefuture","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/google/j2objc:j2objc_annotations": ["lang||||||java","name||||||//3rdparty/jvm/com/google/j2objc:j2objc_annotations","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/google/j2objc/j2objc_annotations","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/googlecode/java_ipv6:java_ipv6": ["lang||||||java","name||||||//3rdparty/jvm/com/googlecode/java_ipv6:java_ipv6","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/googlecode/java_ipv6/java_ipv6","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/helger:profiler": ["lang||||||java","name||||||//3rdparty/jvm/com/helger:profiler","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/helger/profiler","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/io7m/xom:xom": ["lang||||||java","name||||||//3rdparty/jvm/com/io7m/xom:xom","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/xml_apis:xml_apis|||//3rdparty/jvm/xerces:xercesImpl|||//3rdparty/jvm/xalan:xalan|||//external:jar/com/io7m/xom/xom","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/jayway/jsonpath:json_path": ["lang||||||java","name||||||//3rdparty/jvm/com/jayway/jsonpath:json_path","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/net/minidev:json_smart|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/com/jayway/jsonpath/json_path","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/lmax:disruptor": ["lang||||||java","name||||||//3rdparty/jvm/com/lmax:disruptor","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/lmax/disruptor","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/mikesamuel:json_sanitizer": ["lang||||||java","name||||||//3rdparty/jvm/com/mikesamuel:json_sanitizer","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/mikesamuel/json_sanitizer","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/netflix/archaius:archaius_core": ["lang||||||java","name||||||//3rdparty/jvm/com/netflix/archaius:archaius_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/commons_configuration:commons_configuration|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//external:jar/com/netflix/archaius/archaius_core|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/netflix/hystrix:hystrix_core": ["lang||||||java","name||||||//3rdparty/jvm/com/netflix/hystrix:hystrix_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/hdrhistogram:HdrHistogram|||//3rdparty/jvm/io/reactivex:rxjava|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/com/netflix/hystrix/hystrix_core|||//3rdparty/jvm/com/netflix/archaius:archaius_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/networknt:json_schema_validator": ["lang||||||java","name||||||//3rdparty/jvm/com/networknt:json_schema_validator","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//external:jar/com/networknt/json_schema_validator","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/squareup/okhttp3:logging_interceptor": ["lang||||||java","name||||||//3rdparty/jvm/com/squareup/okhttp3:logging_interceptor","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/squareup/okhttp3:okhttp|||//external:jar/com/squareup/okhttp3/logging_interceptor","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/squareup/okhttp3:okhttp": ["lang||||||java","name||||||//3rdparty/jvm/com/squareup/okhttp3:okhttp","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/squareup/okio:okio|||//external:jar/com/squareup/okhttp3/okhttp","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/squareup/okio:okio": ["lang||||||java","name||||||//3rdparty/jvm/com/squareup/okio:okio","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/squareup/okio/okio","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/squareup/retrofit2:retrofit": ["lang||||||java","name||||||//3rdparty/jvm/com/squareup/retrofit2:retrofit","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/squareup/okhttp3:okhttp|||//external:jar/com/squareup/retrofit2/retrofit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/sun/activation:jakarta_activation": ["lang||||||java","name||||||//3rdparty/jvm/com/sun/activation:jakarta_activation","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/sun/activation/jakarta_activation","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/sun/istack:istack_commons_runtime": ["lang||||||java","name||||||//3rdparty/jvm/com/sun/istack:istack_commons_runtime","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/sun/istack/istack_commons_runtime","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/typesafe/scala_logging:scala_logging_2_12": ["lang||||||java","name||||||//3rdparty/jvm/com/typesafe/scala_logging:scala_logging_2_12","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/scala_lang:scala_library|||//3rdparty/jvm/org/scala_lang:scala_reflect|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/com/typesafe/scala_logging/scala_logging_2_12","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/vaadin/external/google:android_json": ["lang||||||java","name||||||//3rdparty/jvm/com/vaadin/external/google:android_json","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/vaadin/external/google/android_json","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/yammer/metrics:metrics_core": ["lang||||||java","name||||||//3rdparty/jvm/com/yammer/metrics:metrics_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/com/yammer/metrics/metrics_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zaxxer:HikariCP": ["lang||||||java","name||||||//3rdparty/jvm/com/zaxxer:HikariCP","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/com/zaxxer/HikariCP","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_swagger2|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_alarm_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_client_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_client_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_alarm_client_paas|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas|||//3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_cache_common": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_cache_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/org/redisson:redisson|||//external:jar/com/zte/daip/manager/common/daip_cache_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_cache_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_cache_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/session:spring_session_core|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/com/zte/daip/manager/common:daip_cache_common|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_cache_paas|||//3rdparty/jvm/org/redisson:redisson","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit|||//external:jar/com/zte/daip/manager/common/daip_common_client_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_common_i18n": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_i18n","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_common_i18n|||//3rdparty/jvm/com/squareup/okhttp3:okhttp|||//3rdparty/jvm/org/jdom:jdom2","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem|||//3rdparty/jvm/com/zte/zdh:zdh_commons|||//3rdparty/jvm/com/alibaba:cooma|||//3rdparty/jvm/javax/servlet:javax_servlet_api|||//3rdparty/jvm/org/springframework:spring_context|||//3rdparty/jvm/io/springfox:springfox_swagger2|||//3rdparty/jvm/commons_configuration:commons_configuration|||//3rdparty/jvm/commons_codec:commons_codec|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/springframework:spring_webmvc|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/org/apache/commons:commons_collections4|||//3rdparty/jvm/org/apache/sshd:sshd_scp|||//3rdparty/jvm/org/apache/sshd:sshd_core|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/io/springfox:springfox_swagger_ui|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/org/yaml:snakeyaml|||//3rdparty/jvm/org/apache/sshd:sshd_common|||//external:jar/com/zte/daip/manager/common/daip_common_utils|||//3rdparty/jvm/org/redisson:redisson|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/org/mockito:mockito_core|||//3rdparty/jvm/com/networknt:json_schema_validator|||//3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender|||//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2|||//3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_communication": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_communication|||//3rdparty/jvm/org/apache/kafka:kafka_clients|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth|||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_communication_common": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/com/zte/daip/manager/common:daip_cache_common|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_communication_common|||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_kafka|||//3rdparty/jvm/org/apache/kafka:kafka_clients|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_communication_kafka": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_kafka","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/org/apache/kafka:kafka_clients|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth|||//external:jar/com/zte/daip/manager/common/daip_communication_kafka","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_communication_replyproducer": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_replyproducer","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//external:jar/com/zte/daip/manager/common/daip_communication_replyproducer|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/org/apache/kafka:kafka_clients|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth|||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_swagger2|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_configcenter_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_client_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_client_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_configcenter_client_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_swagger2|||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/io/springfox:springfox_swagger_ui|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/zte/daip/manager/common/daip_deployer_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_client_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_client_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas|||//external:jar/com/zte/daip/manager/common/daip_deployer_client_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_event_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_beans|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//external:jar/com/zte/daip/manager/common/daip_event_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_event_beans": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_beans","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/zte/daip/manager/common/daip_event_beans|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth|||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_beans|||//external:jar/com/zte/daip/manager/common/daip_event_reporter_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_client": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_client","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_tx|||//3rdparty/jvm/com/lmax:disruptor|||//external:jar/com/zte/daip/manager/common/daip_event_reporter_client|||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_beans|||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//3rdparty/jvm/io/springfox:springfox_swagger2|||//external:jar/com/zte/daip/manager/common/daip_filemanagement_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/io/springfox:springfox_swagger_ui|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_common_util","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_client_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_client_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas|||//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_api|||//external:jar/com/zte/daip/manager/common/daip_filemanagement_client_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_common_util": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_common_util","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth|||//external:jar/com/zte/daip/manager/common/daip_filemanagement_common_util","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_download_provider": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_download_provider","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth|||//3rdparty/jvm/com/zte/daip/manager/common:daip_security|||//3rdparty/jvm/org/springframework/retry:spring_retry|||//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_common_util|||//external:jar/com/zte/daip/manager/common/daip_filemanagement_download_provider","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/javax/servlet:servlet_api|||//3rdparty/jvm/commons_codec:commons_codec|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//external:jar/com/zte/daip/manager/common/daip_http_auth|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/io/jsonwebtoken:jjwt","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_httpclient": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_httpclient","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/httpcomponents:httpcore|||//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem|||//3rdparty/jvm/javax/servlet:servlet_api|||//3rdparty/jvm/commons_codec:commons_codec|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_openfeign|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/com/auth0:java_jwt|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth|||//external:jar/com/zte/daip/manager/common/daip_httpclient","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_init_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_init_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/zte/daip/manager/common/daip_init_paas|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_utils_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_swagger2|||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_inspection_api|||//3rdparty/jvm/com/zte/ums/zenap/okhttp:okhttp|||//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_client_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_client_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_api|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas|||//external:jar/com/zte/daip/manager/common/daip_inspection_client_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_common": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/commons_lang:commons_lang|||//external:jar/com/zte/daip/manager/common/daip_inspection_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_logback_appender","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//3rdparty/jvm/ch/qos/logback:logback_core|||//3rdparty/jvm/com/google/guava:guava|||//external:jar/com/zte/daip/manager/common/daip_logback_appender|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/org/apache/kafka:kafka_clients|||//3rdparty/jvm/org/mockito:mockito_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_swagger2|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/io/springfox:springfox_swagger_ui|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/zte/daip/manager/common/daip_patcher_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_impl_client_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_impl_client_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//external:jar/com/zte/daip/manager/common/daip_patcher_impl_client_paas|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_response": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_response","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_i18n|||//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter|||//external:jar/com/zte/daip/manager/common/daip_response","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_security": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_security","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/xmlgraphics:batik_css|||//external:jar/com/zte/daip/manager/common/daip_security|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/xerces:xercesImpl|||//3rdparty/jvm/commons_fileupload:commons_fileupload|||//3rdparty/jvm/org/owasp/esapi:esapi","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_sensitive_log","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_sensitive_log","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_task_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean|||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_response|||//external:jar/com/zte/daip/manager/common/daip_task_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_task_client": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_client","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas|||//external:jar/com/zte/daip/manager/common/daip_task_client","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//3rdparty/jvm/org/springframework:spring_context|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/apache/commons:commons_collections4|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/daip_task_common_bean|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/com/zte/daip/manager/common:daip_security","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_task_message": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_message","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean|||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication|||//external:jar/com/zte/daip/manager/common/daip_task_message","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_task_worker": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_worker","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/statemachine:spring_statemachine_core|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean|||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_client|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//external:jar/com/zte/daip/manager/common/daip_task_worker|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_message","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_utils_msb": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_utils_msb","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_context|||//3rdparty/jvm/commons_codec:commons_codec|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_httpclient|||//3rdparty/jvm/org/springframework:spring_web|||//external:jar/com/zte/daip/manager/common/daip_utils_msb|||//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_utils_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_utils_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_center_cipher_springboot_agent|||//external:jar/com/zte/daip/manager/common/daip_utils_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_whale_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/io/springfox:springfox_swagger2|||//external:jar/com/zte/daip/manager/common/daip_whale_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:daip_whale_client_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_client_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/zte/daip/manager/common/daip_whale_client_paas|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_api|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/common:spring_message_resource_starter","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/common/spring_message_resource_starter","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/miniagent:daip_miniagent_seed": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/miniagent:daip_miniagent_seed","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api|||//external:jar/com/zte/daip/manager/miniagent/daip_miniagent_seed|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/commons_configuration:commons_configuration|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_httpclient|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/zeroturnaround:zt_zip|||//3rdparty/jvm/org/mockito:mockito_core|||//3rdparty/jvm/com/zte/daip/manager/common:daip_security|||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_application": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_application","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/patcher/daip_patcher_application|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_domain","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_domain": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_domain","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_client|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_configcenter_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_communication_replyproducer|||//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_client_paas|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/zte/daip/manager/common:daip_filemanagement_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/zte/daip/manager/common:daip_whale_api|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/common:daip_inspection_api|||//3rdparty/jvm/org/springframework:spring_context_support|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/com/zte/daip/manager/common:daip_alarm_api|||//3rdparty/jvm/org/springframework/retry:spring_retry|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_infrastructure_api|||//external:jar/com/zte/daip/manager/patcher/daip_patcher_domain","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_handler_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_handler_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/patcher/daip_patcher_handler_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_impl_inner_client_paas": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_impl_inner_client_paas","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/zte/daip/manager/patcher/daip_patcher_impl_inner_client_paas|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_client_paas","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_infrastructure_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_infrastructure_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/zte/daip/manager/common:daip_event_reporter_client|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_data_jpa|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//3rdparty/jvm/com/zte/daip/manager/miniagent:daip_miniagent_seed|||//external:jar/com/zte/daip/manager/patcher/daip_patcher_infrastructure_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_swagger2|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_common_bean|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/io/springfox:springfox_swagger_ui|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/patcher/daip_patcher_inner_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_interfaces": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_interfaces","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_application|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/zte/daip/manager/common:daip_patcher_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_inner_api|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/com/zte/daip/manager/common:daip_common_utils|||//3rdparty/jvm/com/alibaba:fastjson|||//3rdparty/jvm/org/projectlombok:lombok|||//external:jar/com/zte/daip/manager/patcher/daip_patcher_interfaces|||//3rdparty/jvm/com/zte/daip/manager/common:daip_http_auth|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_domain","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_task_worker": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_task_worker","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_api|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_client|||//3rdparty/jvm/com/zte/daip/manager/common:daip_task_worker|||//3rdparty/jvm/com/zte/daip/manager/common:daip_deployer_client_paas|||//external:jar/com/zte/daip/manager/patcher/daip_patcher_task_worker|||//3rdparty/jvm/com/zte/daip/manager/patcher:daip_patcher_domain","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_common_utils": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_common_utils","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_common_utils","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_commons_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_commons_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_commons_impl|||//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/javax/validation:validation_api|||//3rdparty/jvm/org/reflections:reflections|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_actuator","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_configcenter_commons": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_configcenter_commons","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_configcenter_commons","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_configcenter_configclient": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_configcenter_configclient","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_config|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_configcenter_configclient|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_msb|||//3rdparty/jvm/org/springframework/retry:spring_retry|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_configcenter_commons|||//3rdparty/jvm/com/zte/ums/zenap/i18n:zenap_i18n_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_datetime_utils": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_datetime_utils","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_datetime_utils","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_db_jdbc_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_db_jdbc_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_utils_service|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jdbc|||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_db_jdbc_impl|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_internalcontrol_agent|||//3rdparty/jvm/com/zaxxer:HikariCP","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_gr_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_gr_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_commons_impl|||//3rdparty/jvm/joda_time:joda_time|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_gr_impl","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_initckeck_utils": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_initckeck_utils","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_initckeck_utils","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_internalcontrol_agent_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_internalcontrol_agent_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_commons_impl|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_center_cipher_springboot_agent|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_internalcontrol_agent_impl","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jersey_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jersey_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_gr|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/reflections:reflections|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_jersey_impl|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/io/swagger:swagger_jersey2_jaxrs|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jersey","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jersey_swagger_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jersey_swagger_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/io/dropwizard:dropwizard_servlets|||//3rdparty/jvm/com/google/guava:guava|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_jersey_swagger_impl","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jetty_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jetty_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api|||//3rdparty/jvm/org/eclipse/jetty/http2:http2_server|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/org/eclipse/jetty:jetty_alpn_conscrypt_server|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jetty|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_jetty_impl","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_kafka_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_kafka_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_utils_service|||//3rdparty/jvm/org/apache/kafka:kafka_2_12|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_kafka_impl|||//3rdparty/jvm/com/zte/ums/zenap/kafka:zenap_kafka_rule|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_msb|||//3rdparty/jvm/org/apache/kafka:kafka_clients|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_msb_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_msb_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_msb_impl|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava|||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base|||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_redis_redisson_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_redis_redisson_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_utils_service|||//3rdparty/jvm/io/netty:netty_buffer|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//3rdparty/jvm/io/netty:netty_handler|||//3rdparty/jvm/io/netty:netty_common|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher|||//3rdparty/jvm/io/netty:netty_codec|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_redis_redisson_impl|||//3rdparty/jvm/com/zte/oes/dexcloud:redisson|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_internalcontrol_agent|||//3rdparty/jvm/io/netty:netty_transport|||//3rdparty/jvm/io/netty:netty_resolver_dns","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_redis_redisson_mult_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_redis_redisson_mult_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_utils_service|||//3rdparty/jvm/io/netty:netty_buffer|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//3rdparty/jvm/io/netty:netty_handler|||//3rdparty/jvm/io/netty:netty_common|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher|||//3rdparty/jvm/io/netty:netty_codec|||//3rdparty/jvm/com/zte/oes/dexcloud:redisson|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_redis_redisson_mult_impl|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_internalcontrol_agent|||//3rdparty/jvm/io/netty:netty_transport|||//3rdparty/jvm/io/netty:netty_resolver_dns","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_rpc_retrofit_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_rpc_retrofit_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core|||//3rdparty/jvm/org/reflections:reflections|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_msb|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_rpc_retrofit_impl|||//3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_sm_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_sm_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_sm_impl|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava|||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base|||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka|||//3rdparty/jvm/org/glassfish/jersey/bundles/repackaged:jersey_guava|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_rpc_retrofit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_msb|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_configcenter|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_commons_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_commons","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_configcenter": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_configcenter","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_configcenter|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_config|||//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_actuator|||//3rdparty/jvm/org/springframework/retry:spring_retry|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_configcenter_commons|||//3rdparty/jvm/com/zte/ums/zenap/i18n:zenap_i18n_core|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_configcenter_configclient","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_db_jdbc": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_db_jdbc","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_db_jdbc_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_db_jdbc","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_gr": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_gr","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_gr_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_gr","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_internalcontrol_agent": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_internalcontrol_agent","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_internalcontrol_agent_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_internalcontrol_agent","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_jersey": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_jersey","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_web|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jersey_impl|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jersey_swagger_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_jersey","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_jetty": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_jetty","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_web|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_jetty_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_jetty","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_kafka_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_kafka","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_msb": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_msb","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_msb_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_msb","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_redis_redisson": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_redis_redisson","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_redis_redisson_impl|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_redis_redisson_mult_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_redis_redisson","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_rpc_retrofit": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_rpc_retrofit","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_rpc_retrofit_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_rpc_retrofit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_sm": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_sm","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_sm_impl|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_sm","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_utils_service": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_utils_service","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_datetime_utils|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_initckeck_utils|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_common_utils|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_utils_service","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_web": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_web","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_jersey|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_jetty|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_actuator|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_starter_web","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_uiframe_agent_impl": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_uiframe_agent_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_commons|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka|||//external:jar/com/zte/oes/dexcloud/dexcloud_springboot_uiframe_agent_impl|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_configcenter|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_rpc_retrofit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/oes/dexcloud:redisson": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/oes/dexcloud:redisson","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/zte/oes/dexcloud/redisson|||//3rdparty/jvm/de/ruedigermoeller:fst|||//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river|||//3rdparty/jvm/io/projectreactor:reactor_core|||//3rdparty/jvm/io/reactivex/rxjava3:rxjava|||//3rdparty/jvm/javax/cache:cache_api|||//3rdparty/jvm/org/jodd:jodd_bean|||//3rdparty/jvm/net/bytebuddy:byte_buddy|||//3rdparty/jvm/com/googlecode/java_ipv6:java_ipv6","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/dropwizard/ext:zenap_dropwizard_ext": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/dropwizard/ext:zenap_dropwizard_ext","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/com/zte/ums/zenap/dropwizard/ext/zenap_dropwizard_ext|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/io/dropwizard:dropwizard_core|||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core|||//3rdparty/jvm/ch/qos/logback:logback_classic","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/ums/zenap/dropwizard/ext:zenap_dropwizard_ext|||//external:jar/com/zte/ums/zenap/hk2/zenap_hk2|||//3rdparty/jvm/io/dropwizard:dropwizard_core|||//3rdparty/jvm/org/reflections:reflections|||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/glassfish/hk2:hk2_api|||//3rdparty/jvm/org/glassfish/hk2:hk2_locator|||//3rdparty/jvm/org/reflections:reflections|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/com/zte/ums/zenap/hk2/zenap_hk2_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core_pom": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core_pom","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/glassfish/hk2:hk2_api|||//3rdparty/jvm/org/glassfish/hk2:hk2_locator|||//3rdparty/jvm/org/reflections:reflections|||//3rdparty/jvm/org/slf4j:slf4j_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/hk2:hk2_api|||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core|||//3rdparty/jvm/io/dropwizard:dropwizard_core|||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_istio|||//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_bundle|||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2|||//external:jar/com/zte/ums/zenap/httpclient/zenap_httpclient_retrofit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_circuitbreaker": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_circuitbreaker","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/github/resilience4j:resilience4j_circuitbreaker|||//3rdparty/jvm/com/squareup/retrofit2:retrofit|||//3rdparty/jvm/com/zte/ums/zenap/okhttp:okhttp|||//external:jar/com/zte/ums/zenap/httpclient/zenap_httpclient_retrofit_circuitbreaker","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/io/dropwizard/metrics:metrics_jmx|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/io/github/resilience4j:resilience4j_consumer|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_circuitbreaker|||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/com/zte/ums/zenap/httpclient/zenap_httpclient_retrofit_core|||//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/io/github/resilience4j:resilience4j_rxjava2","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_istio": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_istio","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2|||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core|||//3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core|||//external:jar/com/zte/ums/zenap/httpclient/zenap_httpclient_retrofit_istio","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/i18n:zenap_i18n_core": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/i18n:zenap_i18n_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/mikesamuel:json_sanitizer|||//3rdparty/jvm/com/google/guava:guava|||//external:jar/com/zte/ums/zenap/i18n/zenap_i18n_core|||//3rdparty/jvm/org/reflections:reflections|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/kafka:zenap_kafka_rule": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/kafka:zenap_kafka_rule","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_core|||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core_pom|||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit_core|||//external:jar/com/zte/ums/zenap/kafka/zenap_kafka_rule|||//3rdparty/jvm/org/apache/kafka:kafka_clients|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/logback:zenap_logback_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/ch/qos/logback:logback_access|||//3rdparty/jvm/ch/qos/logback:logback_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//external:jar/com/zte/ums/zenap/logback/zenap_logback_core|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/apache/kafka:kafka_clients|||//3rdparty/jvm/com/github/loki4j:loki_logback_appender_jdk8","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_bundle": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_bundle","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/dropwizard:dropwizard_core|||//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_core|||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2|||//external:jar/com/zte/ums/zenap/msb/client/zenap_msb_client_bundle","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_core": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/msb/client:zenap_msb_client_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/ums/zenap/hk2:zenap_hk2_core|||//3rdparty/jvm/io/dropwizard:dropwizard_jackson|||//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service|||//external:jar/com/zte/ums/zenap/msb/client/zenap_msb_client_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/msb/components:msb_service","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/mikesamuel:json_sanitizer|||//3rdparty/jvm/org/eclipse/jetty:jetty_http|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava|||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_base|||//3rdparty/jvm/org/immutables:value|||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/com/zte/ums/zenap/msb/components/msb_service|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/apache/httpcomponents:httpclient|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_client|||//3rdparty/jvm/org/apache/httpcomponents:httpasyncclient","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/okhttp:okhttp": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/okhttp:okhttp","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/squareup/okio:okio|||//external:jar/com/zte/ums/zenap/okhttp/okhttp","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_center_cipher_springboot_agent": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_center_cipher_springboot_agent","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_kms_agent_sdk|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_kafka|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_sm|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_configcenter|||//external:jar/com/zte/ums/zenap/util/cipher/zenap_center_cipher_springboot_agent|||//3rdparty/jvm/com/zte/oes/dexcloud:dexcloud_springboot_starter_rpc_retrofit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/zte/ums/zenap/httpclient:zenap_httpclient_retrofit|||//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on|||//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on|||//external:jar/com/zte/ums/zenap/util/cipher/zenap_cipher","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_kms_agent_sdk": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_kms_agent_sdk","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//external:jar/com/zte/ums/zenap/util/cipher/zenap_kms_agent_sdk|||//3rdparty/jvm/com/zte/ums/zenap/util/cipher:zenap_cipher|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/net/sf/json_lib:json_lib_jar_jdk15|||//3rdparty/jvm/org/apache/httpcomponents:httpclient|||//3rdparty/jvm/commons_beanutils:commons_beanutils|||//3rdparty/jvm/commons_collections:commons_collections|||//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/com/zte/zdh:zdh_commons": ["lang||||||java","name||||||//3rdparty/jvm/com/zte/zdh:zdh_commons","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/httpcomponents:httpcore|||//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem|||//3rdparty/jvm/com/alibaba:cooma|||//3rdparty/jvm/commons_codec:commons_codec|||//3rdparty/jvm/commons_logging:commons_logging|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons|||//3rdparty/jvm/io/fabric8:kubernetes_client|||//3rdparty/jvm/org/reflections:reflections|||//3rdparty/jvm/org/yaml:snakeyaml|||//external:jar/com/zte/zdh/zdh_commons|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/apache/httpcomponents:httpclient|||//3rdparty/jvm/org/springframework/security:spring_security_crypto|||//3rdparty/jvm/junit:junit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/commons_beanutils:commons_beanutils": ["lang||||||java","name||||||//3rdparty/jvm/commons_beanutils:commons_beanutils","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_logging:commons_logging|||//3rdparty/jvm/commons_collections:commons_collections|||//external:jar/commons_beanutils/commons_beanutils","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/commons_codec:commons_codec": ["lang||||||java","name||||||//3rdparty/jvm/commons_codec:commons_codec","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/commons_codec/commons_codec","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/commons_collections:commons_collections": ["lang||||||java","name||||||//3rdparty/jvm/commons_collections:commons_collections","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/commons_collections/commons_collections","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/commons_configuration:commons_configuration": ["lang||||||java","name||||||//3rdparty/jvm/commons_configuration:commons_configuration","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_logging:commons_logging|||//3rdparty/jvm/commons_lang:commons_lang|||//external:jar/commons_configuration/commons_configuration","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/commons_fileupload:commons_fileupload": ["lang||||||java","name||||||//3rdparty/jvm/commons_fileupload:commons_fileupload","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_io:commons_io|||//external:jar/commons_fileupload/commons_fileupload","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/commons_io:commons_io": ["lang||||||java","name||||||//3rdparty/jvm/commons_io:commons_io","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/commons_io/commons_io","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/commons_lang:commons_lang": ["lang||||||java","name||||||//3rdparty/jvm/commons_lang:commons_lang","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/commons_lang/commons_lang","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/commons_logging:commons_logging": ["lang||||||java","name||||||//3rdparty/jvm/commons_logging:commons_logging","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/commons_logging/commons_logging","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/de/ruedigermoeller:fst": ["lang||||||java","name||||||//3rdparty/jvm/de/ruedigermoeller:fst","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/org/javassist:javassist|||//3rdparty/jvm/org/objenesis:objenesis|||//external:jar/de/ruedigermoeller/fst","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/dk/brics/automaton:automaton": ["lang||||||java","name||||||//3rdparty/jvm/dk/brics/automaton:automaton","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/dk/brics/automaton/automaton","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/guru/nidi:jdepend": ["lang||||||java","name||||||//3rdparty/jvm/guru/nidi:jdepend","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/guru/nidi/jdepend","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_configuration": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_configuration","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/commons:commons_text|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/io/dropwizard:dropwizard_jackson|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//external:jar/io/dropwizard/dropwizard_configuration|||//3rdparty/jvm/io/dropwizard:dropwizard_validation|||//3rdparty/jvm/com/github/ben_manes/caffeine:caffeine|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_core": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/dropwizard/metrics:metrics_jvm|||//3rdparty/jvm/io/dropwizard:dropwizard_jersey|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/org/hibernate/validator:hibernate_validator|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/net/sourceforge/argparse4j:argparse4j|||//3rdparty/jvm/io/dropwizard:dropwizard_lifecycle|||//3rdparty/jvm/org/eclipse/jetty:jetty_server|||//3rdparty/jvm/io/dropwizard/metrics:metrics_jmx|||//3rdparty/jvm/io/dropwizard/metrics:metrics_servlets|||//3rdparty/jvm/io/dropwizard:dropwizard_jetty|||//3rdparty/jvm/io/dropwizard:dropwizard_metrics|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/io/dropwizard:dropwizard_jackson|||//3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/io/dropwizard/metrics:metrics_jetty9|||//external:jar/io/dropwizard/dropwizard_core|||//3rdparty/jvm/io/dropwizard:dropwizard_logging|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//3rdparty/jvm/io/dropwizard:dropwizard_servlets|||//3rdparty/jvm/io/dropwizard:dropwizard_validation|||//3rdparty/jvm/io/dropwizard/metrics:metrics_healthchecks|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/io/dropwizard:dropwizard_configuration|||//3rdparty/jvm/org/eclipse/jetty:jetty_security|||//3rdparty/jvm/org/eclipse/jetty/toolchain/setuid:jetty_setuid_java|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/org/eclipse/jetty:jetty_servlet|||//3rdparty/jvm/io/dropwizard:dropwizard_request_logging","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_jackson": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_jackson","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_joda|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_afterburner|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_guava|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/github/ben_manes/caffeine:caffeine|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/io/dropwizard/dropwizard_jackson|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310|||//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_jersey": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_jersey","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api|||//3rdparty/jvm/org/hibernate/validator:hibernate_validator|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/io/dropwizard/metrics:metrics_jersey2|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2|||//3rdparty/jvm/org/glassfish/hk2:hk2_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/io/dropwizard:dropwizard_jackson|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//external:jar/io/dropwizard/dropwizard_jersey|||//3rdparty/jvm/org/glassfish/jersey/ext:jersey_metainf_services|||//3rdparty/jvm/joda_time:joda_time|||//3rdparty/jvm/io/dropwizard:dropwizard_logging|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_server|||//3rdparty/jvm/com/fasterxml/jackson/jaxrs:jackson_jaxrs_json_provider|||//3rdparty/jvm/io/dropwizard:dropwizard_validation|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/github/ben_manes/caffeine:caffeine|||//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/javassist:javassist|||//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_client|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/com/fasterxml:classmate","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_jetty": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_jetty","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_http|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/org/eclipse/jetty:jetty_server|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/io/dropwizard:dropwizard_jackson|||//external:jar/io/dropwizard/dropwizard_jetty|||//3rdparty/jvm/io/dropwizard:dropwizard_logging|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//3rdparty/jvm/org/eclipse/jetty:jetty_servlets|||//3rdparty/jvm/io/dropwizard:dropwizard_validation|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/eclipse/jetty:jetty_security|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/org/eclipse/jetty:jetty_servlet","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_lifecycle": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_lifecycle","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/org/eclipse/jetty:jetty_server|||//external:jar/io/dropwizard/dropwizard_lifecycle|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_logging": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_logging","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/ch/qos/logback:logback_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//external:jar/io/dropwizard/dropwizard_logging|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/io/dropwizard/logback:logback_throttling_appender|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/io/dropwizard:dropwizard_jackson|||//3rdparty/jvm/org/slf4j:jul_to_slf4j|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//3rdparty/jvm/io/dropwizard:dropwizard_validation|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/slf4j:log4j_over_slf4j|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/io/dropwizard/metrics:metrics_logback|||//3rdparty/jvm/org/slf4j:jcl_over_slf4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_metrics": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_metrics","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/io/dropwizard:dropwizard_lifecycle|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/io/dropwizard:dropwizard_jackson|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//external:jar/io/dropwizard/dropwizard_metrics|||//3rdparty/jvm/io/dropwizard:dropwizard_validation|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/github/ben_manes/caffeine:caffeine|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_request_logging": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_request_logging","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/ch/qos/logback:logback_access|||//3rdparty/jvm/ch/qos/logback:logback_core|||//3rdparty/jvm/org/eclipse/jetty:jetty_http|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//external:jar/io/dropwizard/dropwizard_request_logging|||//3rdparty/jvm/org/eclipse/jetty:jetty_server|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/io/dropwizard:dropwizard_jackson|||//3rdparty/jvm/io/dropwizard:dropwizard_logging|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//3rdparty/jvm/io/dropwizard:dropwizard_validation|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_servlets": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_servlets","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/io/dropwizard/metrics:metrics_annotation|||//external:jar/io/dropwizard/dropwizard_servlets|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_util": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_util","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/com/google/guava:guava|||//external:jar/io/dropwizard/dropwizard_util","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard:dropwizard_validation": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard:dropwizard_validation","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/hibernate/validator:hibernate_validator|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/io/dropwizard:dropwizard_util|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/com/google/code/findbugs:jsr305|||//3rdparty/jvm/org/glassfish:jakarta_el|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/dropwizard/dropwizard_validation|||//3rdparty/jvm/com/fasterxml:classmate","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/logback:logback_throttling_appender": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/logback:logback_throttling_appender","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/dropwizard/logback/logback_throttling_appender","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_annotation": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_annotation","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/dropwizard/metrics/metrics_annotation","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_core": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/dropwizard/metrics/metrics_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_healthchecks": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_healthchecks","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/dropwizard/metrics/metrics_healthchecks","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_jersey2": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_jersey2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_server|||//external:jar/io/dropwizard/metrics/metrics_jersey2|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/io/dropwizard/metrics:metrics_annotation|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_jetty9": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_jetty9","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/org/eclipse/jetty:jetty_server|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/dropwizard/metrics/metrics_jetty9","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_jmx": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_jmx","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/dropwizard/metrics/metrics_jmx","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_json": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_json","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/dropwizard/metrics/metrics_json","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_jvm": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_jvm","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/dropwizard/metrics/metrics_jvm","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_logback": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_logback","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/dropwizard/metrics/metrics_logback","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/dropwizard/metrics:metrics_servlets": ["lang||||||java","name||||||//3rdparty/jvm/io/dropwizard/metrics:metrics_servlets","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/dropwizard/metrics:metrics_jvm|||//3rdparty/jvm/io/dropwizard/metrics:metrics_json|||//external:jar/io/dropwizard/metrics/metrics_servlets|||//3rdparty/jvm/com/helger:profiler|||//3rdparty/jvm/io/dropwizard/metrics:metrics_healthchecks|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/io/dropwizard/metrics:metrics_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/fabric8:kubernetes_client": ["lang||||||java","name||||||//3rdparty/jvm/io/fabric8:kubernetes_client","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/fabric8:kubernetes_model|||//3rdparty/jvm/io/fabric8:zjsonpatch|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//external:jar/io/fabric8/kubernetes_client|||//3rdparty/jvm/org/slf4j:jul_to_slf4j|||//3rdparty/jvm/com/squareup/okhttp3:logging_interceptor|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/github/mifmif:generex|||//3rdparty/jvm/com/squareup/okhttp3:okhttp","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/fabric8:kubernetes_model": ["lang||||||java","name||||||//3rdparty/jvm/io/fabric8:kubernetes_model","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations|||//external:jar/io/fabric8/kubernetes_model","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/fabric8:zjsonpatch": ["lang||||||java","name||||||//3rdparty/jvm/io/fabric8:zjsonpatch","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//external:jar/io/fabric8/zjsonpatch","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/hakky54:sslcontext_kickstart": ["lang||||||java","name||||||//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/github/hakky54/sslcontext_kickstart","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem": ["lang||||||java","name||||||//3rdparty/jvm/io/github/hakky54:sslcontext_kickstart_for_pem","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/github/hakky54/sslcontext_kickstart_for_pem","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/openfeign:feign_core": ["lang||||||java","name||||||//3rdparty/jvm/io/github/openfeign:feign_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/github/openfeign/feign_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/openfeign:feign_hystrix": ["lang||||||java","name||||||//3rdparty/jvm/io/github/openfeign:feign_hystrix","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/github/openfeign:feign_core|||//3rdparty/jvm/com/netflix/archaius:archaius_core|||//3rdparty/jvm/com/netflix/hystrix:hystrix_core|||//external:jar/io/github/openfeign/feign_hystrix","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/openfeign:feign_slf4j": ["lang||||||java","name||||||//3rdparty/jvm/io/github/openfeign:feign_slf4j","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/github/openfeign:feign_core|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/github/openfeign/feign_slf4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/openfeign/form:feign_form": ["lang||||||java","name||||||//3rdparty/jvm/io/github/openfeign/form:feign_form","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/github/openfeign/form/feign_form","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/openfeign/form:feign_form_spring": ["lang||||||java","name||||||//3rdparty/jvm/io/github/openfeign/form:feign_form_spring","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/github/openfeign/form/feign_form_spring|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/io/github/openfeign/form:feign_form|||//3rdparty/jvm/commons_fileupload:commons_fileupload","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/resilience4j:resilience4j_circuitbreaker": ["lang||||||java","name||||||//3rdparty/jvm/io/github/resilience4j:resilience4j_circuitbreaker","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/vavr:vavr|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/io/github/resilience4j:resilience4j_core|||//external:jar/io/github/resilience4j/resilience4j_circuitbreaker","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/resilience4j:resilience4j_circularbuffer": ["lang||||||java","name||||||//3rdparty/jvm/io/github/resilience4j:resilience4j_circularbuffer","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/vavr:vavr|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/github/resilience4j/resilience4j_circularbuffer","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/resilience4j:resilience4j_consumer": ["lang||||||java","name||||||//3rdparty/jvm/io/github/resilience4j:resilience4j_consumer","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/github/resilience4j/resilience4j_consumer|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/io/github/resilience4j:resilience4j_core|||//3rdparty/jvm/io/github/resilience4j:resilience4j_circularbuffer|||//3rdparty/jvm/io/vavr:vavr","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/resilience4j:resilience4j_core": ["lang||||||java","name||||||//3rdparty/jvm/io/github/resilience4j:resilience4j_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/vavr:vavr|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/github/resilience4j/resilience4j_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/github/resilience4j:resilience4j_rxjava2": ["lang||||||java","name||||||//3rdparty/jvm/io/github/resilience4j:resilience4j_rxjava2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/vavr:vavr|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/io/reactivex/rxjava2:rxjava|||//external:jar/io/github/resilience4j/resilience4j_rxjava2","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/jsonwebtoken:jjwt": ["lang||||||java","name||||||//3rdparty/jvm/io/jsonwebtoken:jjwt","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/io/jsonwebtoken/jjwt","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/micrometer:micrometer_core": ["lang||||||java","name||||||//3rdparty/jvm/io/micrometer:micrometer_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/hdrhistogram:HdrHistogram|||//3rdparty/jvm/org/latencyutils:LatencyUtils|||//external:jar/io/micrometer/micrometer_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_buffer": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_buffer","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_common|||//external:jar/io/netty/netty_buffer","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_codec": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_codec","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_transport|||//3rdparty/jvm/io/netty:netty_common|||//3rdparty/jvm/io/netty:netty_buffer|||//external:jar/io/netty/netty_codec","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_codec_dns": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_codec_dns","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_buffer|||//3rdparty/jvm/io/netty:netty_common|||//external:jar/io/netty/netty_codec_dns|||//3rdparty/jvm/io/netty:netty_codec|||//3rdparty/jvm/io/netty:netty_transport","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_common": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/netty/netty_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_handler": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_handler","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_buffer|||//3rdparty/jvm/io/netty:netty_common|||//3rdparty/jvm/io/netty:netty_codec|||//3rdparty/jvm/io/netty:netty_transport_native_unix_common|||//3rdparty/jvm/io/netty:netty_transport|||//3rdparty/jvm/io/netty:netty_resolver|||//external:jar/io/netty/netty_handler","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_resolver": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_resolver","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_common|||//external:jar/io/netty/netty_resolver","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_resolver_dns": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_resolver_dns","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_buffer|||//3rdparty/jvm/io/netty:netty_handler|||//3rdparty/jvm/io/netty:netty_common|||//external:jar/io/netty/netty_resolver_dns|||//3rdparty/jvm/io/netty:netty_codec_dns|||//3rdparty/jvm/io/netty:netty_codec|||//3rdparty/jvm/io/netty:netty_transport|||//3rdparty/jvm/io/netty:netty_resolver","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_transport": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_transport","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_resolver|||//3rdparty/jvm/io/netty:netty_common|||//3rdparty/jvm/io/netty:netty_buffer|||//external:jar/io/netty/netty_transport","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_transport_classes_epoll": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_transport_classes_epoll","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_buffer|||//3rdparty/jvm/io/netty:netty_common|||//3rdparty/jvm/io/netty:netty_transport_native_unix_common|||//external:jar/io/netty/netty_transport_classes_epoll|||//3rdparty/jvm/io/netty:netty_transport","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_transport_native_epoll": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_transport_native_epoll","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_buffer|||//3rdparty/jvm/io/netty:netty_common|||//3rdparty/jvm/io/netty:netty_transport_classes_epoll|||//external:jar/io/netty/netty_transport_native_epoll|||//3rdparty/jvm/io/netty:netty_transport_native_unix_common|||//3rdparty/jvm/io/netty:netty_transport","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/netty:netty_transport_native_unix_common": ["lang||||||java","name||||||//3rdparty/jvm/io/netty:netty_transport_native_unix_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_transport|||//3rdparty/jvm/io/netty:netty_buffer|||//3rdparty/jvm/io/netty:netty_common|||//external:jar/io/netty/netty_transport_native_unix_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/projectreactor:reactor_core": ["lang||||||java","name||||||//3rdparty/jvm/io/projectreactor:reactor_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/reactivestreams:reactive_streams|||//external:jar/io/projectreactor/reactor_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/reactivex:rxjava": ["lang||||||java","name||||||//3rdparty/jvm/io/reactivex:rxjava","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/reactivex/rxjava","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/reactivex/rxjava2:rxjava": ["lang||||||java","name||||||//3rdparty/jvm/io/reactivex/rxjava2:rxjava","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/reactivestreams:reactive_streams|||//external:jar/io/reactivex/rxjava2/rxjava","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/reactivex/rxjava3:rxjava": ["lang||||||java","name||||||//3rdparty/jvm/io/reactivex/rxjava3:rxjava","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/reactivestreams:reactive_streams|||//external:jar/io/reactivex/rxjava3/rxjava","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/springfox:springfox_core": ["lang||||||java","name||||||//3rdparty/jvm/io/springfox:springfox_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata|||//3rdparty/jvm/net/bytebuddy:byte_buddy|||//3rdparty/jvm/org/springframework/plugin:spring_plugin_core|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/springfox/springfox_core|||//3rdparty/jvm/com/fasterxml:classmate","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/springfox:springfox_schema": ["lang||||||java","name||||||//3rdparty/jvm/io/springfox:springfox_schema","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_core|||//3rdparty/jvm/io/springfox:springfox_spi|||//external:jar/io/springfox/springfox_schema","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/springfox:springfox_spi": ["lang||||||java","name||||||//3rdparty/jvm/io/springfox:springfox_spi","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_core|||//external:jar/io/springfox/springfox_spi","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/springfox:springfox_spring_web": ["lang||||||java","name||||||//3rdparty/jvm/io/springfox:springfox_spring_web","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata|||//3rdparty/jvm/io/springfox:springfox_spi|||//external:jar/io/springfox/springfox_spring_web|||//3rdparty/jvm/org/springframework/plugin:spring_plugin_core|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/fasterxml:classmate","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/springfox:springfox_swagger2": ["lang||||||java","name||||||//3rdparty/jvm/io/springfox:springfox_swagger2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/mapstruct:mapstruct|||//3rdparty/jvm/io/springfox:springfox_schema|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata|||//3rdparty/jvm/io/springfox:springfox_spi|||//3rdparty/jvm/io/swagger:swagger_models|||//external:jar/io/springfox/springfox_swagger2|||//3rdparty/jvm/org/springframework/plugin:spring_plugin_core|||//3rdparty/jvm/io/springfox:springfox_swagger_common|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/io/swagger:swagger_annotations|||//3rdparty/jvm/io/springfox:springfox_spring_web|||//3rdparty/jvm/com/fasterxml:classmate","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/springfox:springfox_swagger_common": ["lang||||||java","name||||||//3rdparty/jvm/io/springfox:springfox_swagger_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_schema|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata|||//3rdparty/jvm/io/springfox:springfox_spi|||//3rdparty/jvm/io/swagger:swagger_models|||//3rdparty/jvm/org/springframework/plugin:spring_plugin_core|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/io/swagger:swagger_annotations|||//3rdparty/jvm/io/springfox:springfox_spring_web|||//3rdparty/jvm/com/fasterxml:classmate|||//external:jar/io/springfox/springfox_swagger_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/springfox:springfox_swagger_ui": ["lang||||||java","name||||||//3rdparty/jvm/io/springfox:springfox_swagger_ui","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/springfox:springfox_spring_web|||//external:jar/io/springfox/springfox_swagger_ui","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/swagger:swagger_annotations": ["lang||||||java","name||||||//3rdparty/jvm/io/swagger:swagger_annotations","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/swagger/swagger_annotations","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/swagger:swagger_core": ["lang||||||java","name||||||//3rdparty/jvm/io/swagger:swagger_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//external:jar/io/swagger/swagger_core|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/javax/validation:validation_api|||//3rdparty/jvm/io/swagger:swagger_models|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/swagger:swagger_jaxrs": ["lang||||||java","name||||||//3rdparty/jvm/io/swagger:swagger_jaxrs","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/google/guava:guava|||//3rdparty/jvm/org/reflections:reflections|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/io/swagger:swagger_core|||//3rdparty/jvm/javax/ws/rs:jsr311_api|||//external:jar/io/swagger/swagger_jaxrs","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/swagger:swagger_jersey2_jaxrs": ["lang||||||java","name||||||//3rdparty/jvm/io/swagger:swagger_jersey2_jaxrs","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/swagger:swagger_jaxrs|||//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core|||//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_multipart|||//external:jar/io/swagger/swagger_jersey2_jaxrs","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/swagger:swagger_models": ["lang||||||java","name||||||//3rdparty/jvm/io/swagger:swagger_models","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/swagger:swagger_annotations|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/io/swagger/swagger_models","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/vavr:vavr": ["lang||||||java","name||||||//3rdparty/jvm/io/vavr:vavr","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/vavr:vavr_match|||//external:jar/io/vavr/vavr","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/vavr:vavr_match": ["lang||||||java","name||||||//3rdparty/jvm/io/vavr:vavr_match","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/vavr/vavr_match","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_brave|||//external:jar/io/zipkin/brave/brave","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_context_slf4j": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_context_slf4j","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_context_slf4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_http","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpasyncclient": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpasyncclient","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_httpasyncclient","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpclient": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpclient","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_httpclient","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_jms": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_jms","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_jms","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_clients": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_clients","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_kafka_clients","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_streams": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_streams","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_clients|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_kafka_streams","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_messaging","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_rpc": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_rpc","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_rpc","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_servlet": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_servlet","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_servlet","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_rabbit": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_rabbit","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_spring_rabbit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_web": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_web","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_http|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_spring_web","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_webmvc": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_webmvc","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_servlet|||//3rdparty/jvm/io/zipkin/brave:brave|||//external:jar/io/zipkin/brave/brave_instrumentation_spring_webmvc","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/zipkin2:zipkin|||//external:jar/io/zipkin/reporter2/zipkin_reporter","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_brave": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_brave","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter|||//3rdparty/jvm/io/zipkin/zipkin2:zipkin|||//external:jar/io/zipkin/reporter2/zipkin_reporter_brave","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_metrics_micrometer": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_metrics_micrometer","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter|||//3rdparty/jvm/io/zipkin/zipkin2:zipkin|||//external:jar/io/zipkin/reporter2/zipkin_reporter_metrics_micrometer","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/io/zipkin/zipkin2:zipkin": ["lang||||||java","name||||||//3rdparty/jvm/io/zipkin/zipkin2:zipkin","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/io/zipkin/zipkin2/zipkin","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/activation:jakarta_activation_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/activation:jakarta_activation_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/jakarta/activation/jakarta_activation_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/annotation:jakarta_annotation_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/jakarta/annotation/jakarta_annotation_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/el:jakarta_el_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/el:jakarta_el_api","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/jakarta/el/jakarta_el_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/persistence:jakarta_persistence_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/persistence:jakarta_persistence_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/jakarta/persistence/jakarta_persistence_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/servlet:jakarta_servlet_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/jakarta/servlet/jakarta_servlet_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/transaction:jakarta_transaction_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/transaction:jakarta_transaction_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/jakarta/transaction/jakarta_transaction_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/validation:jakarta_validation_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/validation:jakarta_validation_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/jakarta/validation/jakarta_validation_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/websocket:jakarta_websocket_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/websocket:jakarta_websocket_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/jakarta/websocket/jakarta_websocket_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/jakarta/ws/rs/jakarta_ws_rs_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api": ["lang||||||java","name||||||//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/activation:jakarta_activation_api|||//external:jar/jakarta/xml/bind/jakarta_xml_bind_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/activation:javax_activation_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/activation:javax_activation_api","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/activation/javax_activation_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/annotation:javax_annotation_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/annotation:javax_annotation_api","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/annotation/javax_annotation_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/cache:cache_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/cache:cache_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/cache/cache_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/persistence:javax_persistence_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/persistence:javax_persistence_api","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/persistence/javax_persistence_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/servlet:javax_servlet_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/servlet:javax_servlet_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/servlet/javax_servlet_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/servlet:servlet_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/servlet:servlet_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/servlet/servlet_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/validation:validation_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/validation:validation_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/validation/validation_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/websocket:javax_websocket_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/websocket:javax_websocket_api","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/websocket/javax_websocket_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/websocket:javax_websocket_client_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/websocket:javax_websocket_client_api","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/websocket/javax_websocket_client_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/ws/rs:jsr311_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/ws/rs:jsr311_api","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/javax/ws/rs/jsr311_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/javax/xml/bind:jaxb_api": ["lang||||||java","name||||||//3rdparty/jvm/javax/xml/bind:jaxb_api","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/javax/activation:javax_activation_api|||//external:jar/javax/xml/bind/jaxb_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/joda_time:joda_time": ["lang||||||java","name||||||//3rdparty/jvm/joda_time:joda_time","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/joda_time/joda_time","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/junit:junit": ["lang||||||java","name||||||//3rdparty/jvm/junit:junit","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/hamcrest:hamcrest_core|||//external:jar/junit/junit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/log4j:log4j": ["lang||||||java","name||||||//3rdparty/jvm/log4j:log4j","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/log4j/log4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/net/bytebuddy:byte_buddy": ["lang||||||java","name||||||//3rdparty/jvm/net/bytebuddy:byte_buddy","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/net/bytebuddy/byte_buddy","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/net/bytebuddy:byte_buddy_agent": ["lang||||||java","name||||||//3rdparty/jvm/net/bytebuddy:byte_buddy_agent","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/net/bytebuddy/byte_buddy_agent","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/net/minidev:accessors_smart": ["lang||||||java","name||||||//3rdparty/jvm/net/minidev:accessors_smart","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/ow2/asm:asm|||//external:jar/net/minidev/accessors_smart","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/net/minidev:json_smart": ["lang||||||java","name||||||//3rdparty/jvm/net/minidev:json_smart","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/net/minidev:accessors_smart|||//external:jar/net/minidev/json_smart","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/net/sf/ezmorph:ezmorph": ["lang||||||java","name||||||//3rdparty/jvm/net/sf/ezmorph:ezmorph","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_lang:commons_lang|||//external:jar/net/sf/ezmorph/ezmorph","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/net/sf/jopt_simple:jopt_simple": ["lang||||||java","name||||||//3rdparty/jvm/net/sf/jopt_simple:jopt_simple","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/net/sf/jopt_simple/jopt_simple","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/net/sf/json_lib:json_lib_jar_jdk15": ["lang||||||java","name||||||//3rdparty/jvm/net/sf/json_lib:json_lib_jar_jdk15","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_logging:commons_logging|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/net/sf/ezmorph:ezmorph|||//external:jar/net/sf/json_lib/json_lib_jar_jdk15|||//3rdparty/jvm/commons_beanutils:commons_beanutils|||//3rdparty/jvm/commons_collections:commons_collections","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/net/sourceforge/argparse4j:argparse4j": ["lang||||||java","name||||||//3rdparty/jvm/net/sourceforge/argparse4j:argparse4j","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/net/sourceforge/argparse4j/argparse4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/net/sourceforge/htmlunit:neko_htmlunit": ["lang||||||java","name||||||//3rdparty/jvm/net/sourceforge/htmlunit:neko_htmlunit","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/net/sourceforge/htmlunit/neko_htmlunit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/commons:commons_collections4": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/commons:commons_collections4","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apache/commons/commons_collections4","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/commons:commons_lang3": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/commons:commons_lang3","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apache/commons/commons_lang3","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/commons:commons_text": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/commons:commons_text","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/commons:commons_lang3|||//external:jar/org/apache/commons/commons_text","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/httpcomponents:httpasyncclient": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/httpcomponents:httpasyncclient","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/httpcomponents:httpcore|||//3rdparty/jvm/commons_logging:commons_logging|||//3rdparty/jvm/org/apache/httpcomponents:httpcore_nio|||//3rdparty/jvm/org/apache/httpcomponents:httpclient|||//external:jar/org/apache/httpcomponents/httpasyncclient","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/httpcomponents:httpclient": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/httpcomponents:httpclient","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_logging:commons_logging|||//3rdparty/jvm/commons_codec:commons_codec|||//3rdparty/jvm/org/apache/httpcomponents:httpcore|||//external:jar/org/apache/httpcomponents/httpclient","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/httpcomponents:httpcore": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/httpcomponents:httpcore","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apache/httpcomponents/httpcore","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/httpcomponents:httpcore_nio": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/httpcomponents:httpcore_nio","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/httpcomponents:httpcore|||//external:jar/org/apache/httpcomponents/httpcore_nio","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5|||//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/org/apache/httpcomponents/client5/httpclient5","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apache/httpcomponents/core5/httpcore5","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5_h2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5|||//external:jar/org/apache/httpcomponents/core5/httpcore5_h2","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/kafka:kafka_2_12": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/kafka:kafka_2_12","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/scala_lang:scala_reflect|||//3rdparty/jvm/org/apache/zookeeper:zookeeper|||//3rdparty/jvm/com/typesafe/scala_logging:scala_logging_2_12|||//3rdparty/jvm/com/yammer/metrics:metrics_core|||//3rdparty/jvm/net/sf/jopt_simple:jopt_simple|||//external:jar/org/apache/kafka/kafka_2_12|||//3rdparty/jvm/org/scala_lang:scala_library|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/apache/kafka:kafka_clients|||//3rdparty/jvm/com/101tec:zkclient","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/kafka:kafka_clients": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/kafka:kafka_clients","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/github/luben:zstd_jni|||//external:jar/org/apache/kafka/kafka_clients|||//3rdparty/jvm/org/lz4:lz4_java|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/xerial/snappy:snappy_java","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/logging/log4j:log4j_api": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/logging/log4j:log4j_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apache/logging/log4j/log4j_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/apache/logging/log4j:log4j_api|||//external:jar/org/apache/logging/log4j/log4j_to_slf4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/sshd:sshd_common": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/sshd:sshd_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/slf4j:jcl_over_slf4j|||//external:jar/org/apache/sshd/sshd_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/sshd:sshd_core": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/sshd:sshd_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/slf4j:jcl_over_slf4j|||//3rdparty/jvm/org/apache/sshd:sshd_common|||//external:jar/org/apache/sshd/sshd_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/sshd:sshd_scp": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/sshd:sshd_scp","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/sshd:sshd_core|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/slf4j:jcl_over_slf4j|||//external:jar/org/apache/sshd/sshd_scp","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/tomcat:tomcat_annotations_api": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/tomcat:tomcat_annotations_api","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apache/tomcat/tomcat_annotations_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/tomcat/embed:tomcat_embed_core": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/tomcat/embed:tomcat_embed_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/tomcat:tomcat_annotations_api|||//external:jar/org/apache/tomcat/embed/tomcat_embed_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/tomcat/embed:tomcat_embed_websocket": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/tomcat/embed:tomcat_embed_websocket","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/tomcat/embed:tomcat_embed_core|||//external:jar/org/apache/tomcat/embed/tomcat_embed_websocket","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/xmlgraphics:batik_constants": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/xmlgraphics:batik_constants","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources|||//external:jar/org/apache/xmlgraphics/batik_constants","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/xmlgraphics:batik_css": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/xmlgraphics:batik_css","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/xml_apis:xml_apis_ext|||//3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons|||//3rdparty/jvm/org/apache/xmlgraphics:batik_util|||//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources|||//external:jar/org/apache/xmlgraphics/batik_css","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/xmlgraphics:batik_i18n": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/xmlgraphics:batik_i18n","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources|||//external:jar/org/apache/xmlgraphics/batik_i18n","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apache/xmlgraphics/batik_shared_resources","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/xmlgraphics:batik_util": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/xmlgraphics:batik_util","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/xmlgraphics:batik_constants|||//3rdparty/jvm/org/apache/xmlgraphics:batik_i18n|||//3rdparty/jvm/org/apache/xmlgraphics:batik_shared_resources|||//external:jar/org/apache/xmlgraphics/batik_util","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/xmlgraphics:xmlgraphics_commons","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/commons_logging:commons_logging|||//external:jar/org/apache/xmlgraphics/xmlgraphics_commons","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/yetus:audience_annotations": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/yetus:audience_annotations","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apache/yetus/audience_annotations","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/zookeeper:zookeeper": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/zookeeper:zookeeper","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_handler|||//3rdparty/jvm/org/apache/zookeeper:zookeeper_jute|||//3rdparty/jvm/io/netty:netty_transport_native_epoll|||//external:jar/org/apache/zookeeper/zookeeper|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/log4j:log4j|||//3rdparty/jvm/org/slf4j:slf4j_log4j12|||//3rdparty/jvm/org/apache/yetus:audience_annotations","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache/zookeeper:zookeeper_jute": ["lang||||||java","name||||||//3rdparty/jvm/org/apache/zookeeper:zookeeper_jute","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/yetus:audience_annotations|||//external:jar/org/apache/zookeeper/zookeeper_jute","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apache_extras/beanshell:bsh": ["lang||||||java","name||||||//3rdparty/jvm/org/apache_extras/beanshell:bsh","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apache_extras/beanshell/bsh","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/apiguardian:apiguardian_api": ["lang||||||java","name||||||//3rdparty/jvm/org/apiguardian:apiguardian_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/apiguardian/apiguardian_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/aspectj:aspectjrt": ["lang||||||java","name||||||//3rdparty/jvm/org/aspectj:aspectjrt","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/aspectj/aspectjrt","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/aspectj:aspectjweaver": ["lang||||||java","name||||||//3rdparty/jvm/org/aspectj:aspectjweaver","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/aspectj/aspectjweaver","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/assertj:assertj_core": ["lang||||||java","name||||||//3rdparty/jvm/org/assertj:assertj_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/assertj/assertj_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on": ["lang||||||java","name||||||//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on|||//external:jar/org/bouncycastle/bcpkix_jdk15on","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/bouncycastle:bcprov_jdk15on": ["lang||||||java","name||||||//3rdparty/jvm/org/bouncycastle:bcprov_jdk15on","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/bouncycastle/bcprov_jdk15on","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/checkerframework:checker_qual": ["lang||||||java","name||||||//3rdparty/jvm/org/checkerframework:checker_qual","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/checkerframework/checker_qual","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/conscrypt:conscrypt_openjdk_uber": ["lang||||||java","name||||||//3rdparty/jvm/org/conscrypt:conscrypt_openjdk_uber","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/conscrypt/conscrypt_openjdk_uber","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/dom4j:dom4j": ["lang||||||java","name||||||//3rdparty/jvm/org/dom4j:dom4j","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/dom4j/dom4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_alpn_conscrypt_server": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_alpn_conscrypt_server","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/conscrypt:conscrypt_openjdk_uber|||//3rdparty/jvm/org/eclipse/jetty:jetty_alpn_server|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//external:jar/org/eclipse/jetty/jetty_alpn_conscrypt_server","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_alpn_server": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_alpn_server","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_server|||//external:jar/org/eclipse/jetty/jetty_alpn_server","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_annotations": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_annotations","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_plus|||//3rdparty/jvm/org/ow2/asm:asm|||//3rdparty/jvm/org/eclipse/jetty:jetty_webapp|||//external:jar/org/eclipse/jetty/jetty_annotations|||//3rdparty/jvm/javax/annotation:javax_annotation_api|||//3rdparty/jvm/org/ow2/asm:asm_commons","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_client": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_client","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_http|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//external:jar/org/eclipse/jetty/jetty_client","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_continuation": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_continuation","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/eclipse/jetty/jetty_continuation","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_http": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_http","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//external:jar/org/eclipse/jetty/jetty_http","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_io": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_io","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//external:jar/org/eclipse/jetty/jetty_io","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_jndi": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_jndi","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//external:jar/org/eclipse/jetty/jetty_jndi","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_plus": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_plus","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_webapp|||//3rdparty/jvm/org/eclipse/jetty:jetty_jndi|||//external:jar/org/eclipse/jetty/jetty_plus","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_security": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_security","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_server|||//external:jar/org/eclipse/jetty/jetty_security","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_server": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_server","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//3rdparty/jvm/javax/servlet:javax_servlet_api|||//3rdparty/jvm/org/eclipse/jetty:jetty_http|||//external:jar/org/eclipse/jetty/jetty_server","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_servlet": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_servlet","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_util_ajax|||//3rdparty/jvm/org/eclipse/jetty:jetty_security|||//external:jar/org/eclipse/jetty/jetty_servlet","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_servlets": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_servlets","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_http|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//external:jar/org/eclipse/jetty/jetty_servlets|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//3rdparty/jvm/org/eclipse/jetty:jetty_continuation","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_util": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_util","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/eclipse/jetty/jetty_util","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_util_ajax": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_util_ajax","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//external:jar/org/eclipse/jetty/jetty_util_ajax","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_webapp": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_webapp","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_servlet|||//3rdparty/jvm/org/eclipse/jetty:jetty_xml|||//external:jar/org/eclipse/jetty/jetty_webapp","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty:jetty_xml": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty:jetty_xml","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//external:jar/org/eclipse/jetty/jetty_xml","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/http2:http2_common": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/http2:http2_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty/http2:http2_hpack|||//external:jar/org/eclipse/jetty/http2/http2_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/http2:http2_hpack": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/http2:http2_hpack","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//3rdparty/jvm/org/eclipse/jetty:jetty_http|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//external:jar/org/eclipse/jetty/http2/http2_hpack","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/http2:http2_server": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/http2:http2_server","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty/http2:http2_common|||//3rdparty/jvm/org/eclipse/jetty:jetty_server|||//external:jar/org/eclipse/jetty/http2/http2_server","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/toolchain/setuid:jetty_setuid_java": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/toolchain/setuid:jetty_setuid_java","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_server|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//external:jar/org/eclipse/jetty/toolchain/setuid/jetty_setuid_java","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/websocket:javax_websocket_client_impl": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/websocket:javax_websocket_client_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_client|||//3rdparty/jvm/javax/websocket:javax_websocket_client_api|||//external:jar/org/eclipse/jetty/websocket/javax_websocket_client_impl","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/websocket:javax_websocket_server_impl": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/websocket:javax_websocket_server_impl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/javax/websocket:javax_websocket_api|||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_server|||//external:jar/org/eclipse/jetty/websocket/javax_websocket_server_impl|||//3rdparty/jvm/org/eclipse/jetty/websocket:javax_websocket_client_impl|||//3rdparty/jvm/org/eclipse/jetty:jetty_annotations","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/websocket:websocket_api": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/eclipse/jetty/websocket/websocket_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/websocket:websocket_client": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_client","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//external:jar/org/eclipse/jetty/websocket/websocket_client|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_common|||//3rdparty/jvm/org/eclipse/jetty:jetty_client","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/websocket:websocket_common": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_io|||//3rdparty/jvm/org/eclipse/jetty:jetty_util|||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_api|||//external:jar/org/eclipse/jetty/websocket/websocket_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/websocket:websocket_server": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_server","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty:jetty_http|||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_servlet|||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_common|||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_client|||//external:jar/org/eclipse/jetty/websocket/websocket_server|||//3rdparty/jvm/org/eclipse/jetty:jetty_servlet","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/eclipse/jetty/websocket:websocket_servlet": ["lang||||||java","name||||||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_servlet","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_api|||//3rdparty/jvm/javax/servlet:javax_servlet_api|||//external:jar/org/eclipse/jetty/websocket/websocket_servlet","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish:jakarta_el": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish:jakarta_el","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/glassfish/jakarta_el","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2:class_model": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2:class_model","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/ow2/asm:asm_analysis|||//external:jar/org/glassfish/hk2/class_model","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2:hk2": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2:hk2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/hk2:hk2_core|||//3rdparty/jvm/org/glassfish/hk2:class_model|||//3rdparty/jvm/org/glassfish/hk2:hk2_runlevel|||//3rdparty/jvm/org/glassfish/hk2:hk2_api|||//external:jar/org/glassfish/hk2/hk2|||//3rdparty/jvm/org/glassfish/hk2:hk2_locator|||//3rdparty/jvm/org/glassfish/hk2:hk2_utils","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2:hk2_api": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2:hk2_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/org/glassfish/hk2:hk2_utils|||//external:jar/org/glassfish/hk2/hk2_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2:hk2_core": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2:hk2_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/hk2:hk2_locator|||//3rdparty/jvm/org/glassfish/hk2:hk2_utils|||//external:jar/org/glassfish/hk2/hk2_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2:hk2_locator": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2:hk2_locator","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api|||//3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged|||//external:jar/org/glassfish/hk2/hk2_locator|||//3rdparty/jvm/org/glassfish/hk2:hk2_api|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/org/javassist:javassist|||//3rdparty/jvm/org/glassfish/hk2:hk2_utils","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2:hk2_runlevel": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2:hk2_runlevel","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/hk2:hk2_api|||//3rdparty/jvm/org/glassfish/hk2:hk2_locator|||//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api|||//external:jar/org/glassfish/hk2/hk2_runlevel","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2:hk2_utils": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2:hk2_utils","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api|||//external:jar/org/glassfish/hk2/hk2_utils","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/glassfish/hk2/osgi_resource_locator","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2:spring_bridge": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2:spring_bridge","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/org/glassfish/hk2:hk2_api|||//3rdparty/jvm/org/springframework:spring_context|||//external:jar/org/glassfish/hk2/spring_bridge","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2/external:aopalliance_repackaged","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/glassfish/hk2/external/aopalliance_repackaged","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/glassfish/hk2/external/jakarta_inject","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jaxb:jaxb_runtime": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jaxb:jaxb_runtime","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/jaxb:txw2|||//external:jar/org/glassfish/jaxb/jaxb_runtime|||//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api|||//3rdparty/jvm/com/sun/istack:istack_commons_runtime|||//3rdparty/jvm/com/sun/activation:jakarta_activation","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jaxb:txw2": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jaxb:txw2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/glassfish/jaxb/txw2","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/bundles/repackaged:jersey_guava": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/bundles/repackaged:jersey_guava","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/glassfish/jersey/bundles/repackaged/jersey_guava","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_server|||//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core|||//external:jar/org/glassfish/jersey/containers/jersey_container_servlet","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_server|||//external:jar/org/glassfish/jersey/containers/jersey_container_servlet_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/core:jersey_client": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/core:jersey_client","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//external:jar/org/glassfish/jersey/core/jersey_client","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/core:jersey_common": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//external:jar/org/glassfish/jersey/core/jersey_common","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/core:jersey_server": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/core:jersey_server","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_jaxb|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//external:jar/org/glassfish/jersey/core/jersey_server|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_client","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/org/hibernate/validator:hibernate_validator|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/jakarta/el:jakarta_el_api|||//external:jar/org/glassfish/jersey/ext/jersey_bean_validation|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_server|||//3rdparty/jvm/org/glassfish:jakarta_el","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/ext:jersey_entity_filtering": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/ext:jersey_entity_filtering","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//external:jar/org/glassfish/jersey/ext/jersey_entity_filtering","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/ext:jersey_metainf_services": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/ext:jersey_metainf_services","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//external:jar/org/glassfish/jersey/ext/jersey_metainf_services","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/ext:jersey_spring5": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/ext:jersey_spring5","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/glassfish/jersey/ext/jersey_spring5|||//3rdparty/jvm/jakarta/ws/rs:jakarta_ws_rs_api|||//3rdparty/jvm/org/springframework:spring_context|||//3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2|||//3rdparty/jvm/org/glassfish/hk2:spring_bridge|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_server|||//3rdparty/jvm/org/glassfish/hk2:hk2|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/springframework:spring_aop|||//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/inject:jersey_hk2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/org/javassist:javassist|||//3rdparty/jvm/org/glassfish/hk2:hk2_locator|||//external:jar/org/glassfish/jersey/inject/jersey_hk2","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/media:jersey_media_jaxb": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_jaxb","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/org/glassfish/hk2/external:jakarta_inject|||//3rdparty/jvm/org/glassfish/hk2:osgi_resource_locator|||//external:jar/org/glassfish/jersey/media/jersey_media_jaxb","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/media:jersey_media_json_jackson": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_json_jackson","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_jaxb_annotations|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/org/glassfish/jersey/ext:jersey_entity_filtering|||//external:jar/org/glassfish/jersey/media/jersey_media_json_jackson|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/glassfish/jersey/media:jersey_media_multipart": ["lang||||||java","name||||||//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_multipart","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_common|||//3rdparty/jvm/org/jvnet/mimepull:mimepull|||//external:jar/org/glassfish/jersey/media/jersey_media_multipart","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/hamcrest:hamcrest": ["lang||||||java","name||||||//3rdparty/jvm/org/hamcrest:hamcrest","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/hamcrest/hamcrest","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/hamcrest:hamcrest_core": ["lang||||||java","name||||||//3rdparty/jvm/org/hamcrest:hamcrest_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/hamcrest:hamcrest|||//external:jar/org/hamcrest/hamcrest_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/hdrhistogram:HdrHistogram": ["lang||||||java","name||||||//3rdparty/jvm/org/hdrhistogram:HdrHistogram","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/hdrhistogram/HdrHistogram","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/hibernate:hibernate_core": ["lang||||||java","name||||||//3rdparty/jvm/org/hibernate:hibernate_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/jboss:jandex|||//external:jar/org/hibernate/hibernate_core|||//3rdparty/jvm/javax/activation:javax_activation_api|||//3rdparty/jvm/org/jboss/logging:jboss_logging|||//3rdparty/jvm/org/glassfish/jaxb:jaxb_runtime|||//3rdparty/jvm/javax/xml/bind:jaxb_api|||//3rdparty/jvm/net/bytebuddy:byte_buddy|||//3rdparty/jvm/org/dom4j:dom4j|||//3rdparty/jvm/javax/persistence:javax_persistence_api|||//3rdparty/jvm/org/javassist:javassist|||//3rdparty/jvm/antlr:antlr|||//3rdparty/jvm/org/jboss/spec/javax/transaction:jboss_transaction_api_1_2_spec|||//3rdparty/jvm/org/hibernate/common:hibernate_commons_annotations|||//3rdparty/jvm/com/fasterxml:classmate","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/hibernate/common:hibernate_commons_annotations": ["lang||||||java","name||||||//3rdparty/jvm/org/hibernate/common:hibernate_commons_annotations","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/jboss/logging:jboss_logging|||//external:jar/org/hibernate/common/hibernate_commons_annotations","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api": ["lang||||||java","name||||||//3rdparty/jvm/org/hibernate/javax/persistence:hibernate_jpa_2_1_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/hibernate/javax/persistence/hibernate_jpa_2_1_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/hibernate/validator:hibernate_validator": ["lang||||||java","name||||||//3rdparty/jvm/org/hibernate/validator:hibernate_validator","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml:classmate|||//3rdparty/jvm/jakarta/validation:jakarta_validation_api|||//3rdparty/jvm/org/jboss/logging:jboss_logging|||//external:jar/org/hibernate/validator/hibernate_validator","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/htmlunit:neko_htmlunit": ["lang||||||java","name||||||//3rdparty/jvm/org/htmlunit:neko_htmlunit","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/htmlunit/neko_htmlunit","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/immutables:value": ["lang||||||java","name||||||//3rdparty/jvm/org/immutables:value","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/immutables/value","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/javassist:javassist": ["lang||||||java","name||||||//3rdparty/jvm/org/javassist:javassist","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/javassist/javassist","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jboss:jandex": ["lang||||||java","name||||||//3rdparty/jvm/org/jboss:jandex","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/jboss/jandex","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jboss/logging:jboss_logging": ["lang||||||java","name||||||//3rdparty/jvm/org/jboss/logging:jboss_logging","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/jboss/logging/jboss_logging","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jboss/marshalling:jboss_marshalling": ["lang||||||java","name||||||//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/jboss/marshalling/jboss_marshalling","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river": ["lang||||||java","name||||||//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling|||//external:jar/org/jboss/marshalling/jboss_marshalling_river","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jboss/spec/javax/transaction:jboss_transaction_api_1_2_spec": ["lang||||||java","name||||||//3rdparty/jvm/org/jboss/spec/javax/transaction:jboss_transaction_api_1_2_spec","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/jboss/spec/javax/transaction/jboss_transaction_api_1_2_spec","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jdom:jdom2": ["lang||||||java","name||||||//3rdparty/jvm/org/jdom:jdom2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/jdom/jdom2","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jodd:jodd_bean": ["lang||||||java","name||||||//3rdparty/jvm/org/jodd:jodd_bean","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/jodd:jodd_core|||//external:jar/org/jodd/jodd_bean","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jodd:jodd_core": ["lang||||||java","name||||||//3rdparty/jvm/org/jodd:jodd_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/jodd/jodd_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/junit/jupiter:junit_jupiter": ["lang||||||java","name||||||//3rdparty/jvm/org/junit/jupiter:junit_jupiter","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api|||//3rdparty/jvm/org/junit/jupiter:junit_jupiter_params|||//3rdparty/jvm/org/junit/jupiter:junit_jupiter_engine|||//external:jar/org/junit/jupiter/junit_jupiter","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/junit/jupiter:junit_jupiter_api": ["lang||||||java","name||||||//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/junit/platform:junit_platform_commons|||//3rdparty/jvm/org/opentest4j:opentest4j|||//3rdparty/jvm/org/apiguardian:apiguardian_api|||//external:jar/org/junit/jupiter/junit_jupiter_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/junit/jupiter:junit_jupiter_engine": ["lang||||||java","name||||||//3rdparty/jvm/org/junit/jupiter:junit_jupiter_engine","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apiguardian:apiguardian_api|||//3rdparty/jvm/org/junit/platform:junit_platform_engine|||//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api|||//external:jar/org/junit/jupiter/junit_jupiter_engine","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/junit/jupiter:junit_jupiter_params": ["lang||||||java","name||||||//3rdparty/jvm/org/junit/jupiter:junit_jupiter_params","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apiguardian:apiguardian_api|||//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api|||//external:jar/org/junit/jupiter/junit_jupiter_params","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/junit/platform:junit_platform_commons": ["lang||||||java","name||||||//3rdparty/jvm/org/junit/platform:junit_platform_commons","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apiguardian:apiguardian_api|||//external:jar/org/junit/platform/junit_platform_commons","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/junit/platform:junit_platform_engine": ["lang||||||java","name||||||//3rdparty/jvm/org/junit/platform:junit_platform_engine","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/opentest4j:opentest4j|||//3rdparty/jvm/org/apiguardian:apiguardian_api|||//3rdparty/jvm/org/junit/platform:junit_platform_commons|||//external:jar/org/junit/platform/junit_platform_engine","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/junit/vintage:junit_vintage_engine": ["lang||||||java","name||||||//3rdparty/jvm/org/junit/vintage:junit_vintage_engine","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apiguardian:apiguardian_api|||//3rdparty/jvm/org/junit/platform:junit_platform_engine|||//3rdparty/jvm/junit:junit|||//external:jar/org/junit/vintage/junit_vintage_engine","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2": ["lang||||||java","name||||||//3rdparty/jvm/org/jvnet/hudson:ganymed_ssh2","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/jvnet/hudson/ganymed_ssh2","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/jvnet/mimepull:mimepull": ["lang||||||java","name||||||//3rdparty/jvm/org/jvnet/mimepull:mimepull","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/jvnet/mimepull/mimepull","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/latencyutils:LatencyUtils": ["lang||||||java","name||||||//3rdparty/jvm/org/latencyutils:LatencyUtils","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/hdrhistogram:HdrHistogram|||//external:jar/org/latencyutils/LatencyUtils","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/lz4:lz4_java": ["lang||||||java","name||||||//3rdparty/jvm/org/lz4:lz4_java","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/lz4/lz4_java","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/mapstruct:mapstruct": ["lang||||||java","name||||||//3rdparty/jvm/org/mapstruct:mapstruct","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/mapstruct/mapstruct","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/mockito:mockito_core": ["lang||||||java","name||||||//3rdparty/jvm/org/mockito:mockito_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/net/bytebuddy:byte_buddy_agent|||//3rdparty/jvm/org/objenesis:objenesis|||//3rdparty/jvm/net/bytebuddy:byte_buddy|||//external:jar/org/mockito/mockito_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/mockito:mockito_junit_jupiter": ["lang||||||java","name||||||//3rdparty/jvm/org/mockito:mockito_junit_jupiter","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/mockito:mockito_core|||//3rdparty/jvm/org/junit/jupiter:junit_jupiter_api|||//external:jar/org/mockito/mockito_junit_jupiter","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/objenesis:objenesis": ["lang||||||java","name||||||//3rdparty/jvm/org/objenesis:objenesis","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/objenesis/objenesis","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/opentest4j:opentest4j": ["lang||||||java","name||||||//3rdparty/jvm/org/opentest4j:opentest4j","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/opentest4j/opentest4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/ow2/asm:asm": ["lang||||||java","name||||||//3rdparty/jvm/org/ow2/asm:asm","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/ow2/asm/asm","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/ow2/asm:asm_analysis": ["lang||||||java","name||||||//3rdparty/jvm/org/ow2/asm:asm_analysis","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/ow2/asm:asm_tree|||//external:jar/org/ow2/asm/asm_analysis","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/ow2/asm:asm_commons": ["lang||||||java","name||||||//3rdparty/jvm/org/ow2/asm:asm_commons","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/ow2/asm:asm|||//3rdparty/jvm/org/ow2/asm:asm_tree|||//3rdparty/jvm/org/ow2/asm:asm_analysis|||//external:jar/org/ow2/asm/asm_commons","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/ow2/asm:asm_tree": ["lang||||||java","name||||||//3rdparty/jvm/org/ow2/asm:asm_tree","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/ow2/asm:asm|||//external:jar/org/ow2/asm/asm_tree","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/ow2/asm:asm_util": ["lang||||||java","name||||||//3rdparty/jvm/org/ow2/asm:asm_util","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/ow2/asm:asm|||//3rdparty/jvm/org/ow2/asm:asm_tree|||//3rdparty/jvm/org/ow2/asm:asm_analysis|||//external:jar/org/ow2/asm/asm_util","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/owasp/antisamy:antisamy": ["lang||||||java","name||||||//3rdparty/jvm/org/owasp/antisamy:antisamy","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/apache/xmlgraphics:batik_css|||//3rdparty/jvm/xml_apis:xml_apis|||//3rdparty/jvm/xml_apis:xml_apis_ext|||//external:jar/org/owasp/antisamy/antisamy|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/org/htmlunit:neko_htmlunit|||//3rdparty/jvm/xerces:xercesImpl|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/apache/httpcomponents/client5:httpclient5|||//3rdparty/jvm/org/apache/httpcomponents/core5:httpcore5","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/owasp/esapi:esapi": ["lang||||||java","name||||||//3rdparty/jvm/org/owasp/esapi:esapi","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/commons_configuration:commons_configuration|||//3rdparty/jvm/xml_apis:xml_apis|||//3rdparty/jvm/commons_io:commons_io|||//3rdparty/jvm/org/owasp/antisamy:antisamy|||//3rdparty/jvm/commons_lang:commons_lang|||//3rdparty/jvm/org/apache/commons:commons_collections4|||//3rdparty/jvm/com/io7m/xom:xom|||//3rdparty/jvm/net/sourceforge/htmlunit:neko_htmlunit|||//external:jar/org/owasp/esapi/esapi|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/apache_extras/beanshell:bsh|||//3rdparty/jvm/commons_beanutils:commons_beanutils|||//3rdparty/jvm/commons_fileupload:commons_fileupload|||//3rdparty/jvm/log4j:log4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/projectlombok:lombok": ["lang||||||java","name||||||//3rdparty/jvm/org/projectlombok:lombok","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/projectlombok/lombok","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/reactivestreams:reactive_streams": ["lang||||||java","name||||||//3rdparty/jvm/org/reactivestreams:reactive_streams","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/reactivestreams/reactive_streams","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/redisson:redisson": ["lang||||||java","name||||||//3rdparty/jvm/org/redisson:redisson","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/netty:netty_buffer|||//3rdparty/jvm/io/netty:netty_handler|||//3rdparty/jvm/io/netty:netty_common|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_core|||//3rdparty/jvm/org/jboss/marshalling:jboss_marshalling_river|||//3rdparty/jvm/io/projectreactor:reactor_core|||//3rdparty/jvm/io/reactivex/rxjava3:rxjava|||//3rdparty/jvm/com/fasterxml/jackson/dataformat:jackson_dataformat_yaml|||//3rdparty/jvm/javax/cache:cache_api|||//3rdparty/jvm/org/yaml:snakeyaml|||//3rdparty/jvm/org/jodd:jodd_bean|||//3rdparty/jvm/net/bytebuddy:byte_buddy|||//3rdparty/jvm/io/netty:netty_codec|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/org/redisson/redisson|||//3rdparty/jvm/io/netty:netty_transport|||//3rdparty/jvm/io/netty:netty_resolver_dns","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/reflections:reflections": ["lang||||||java","name||||||//3rdparty/jvm/org/reflections:reflections","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/javassist:javassist|||//3rdparty/jvm/com/google/guava:guava|||//external:jar/org/reflections/reflections","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/skyscreamer:jsonassert": ["lang||||||java","name||||||//3rdparty/jvm/org/skyscreamer:jsonassert","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/vaadin/external/google:android_json|||//external:jar/org/skyscreamer/jsonassert","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/slf4j:jcl_over_slf4j": ["lang||||||java","name||||||//3rdparty/jvm/org/slf4j:jcl_over_slf4j","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/org/slf4j/jcl_over_slf4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/slf4j:jul_to_slf4j": ["lang||||||java","name||||||//3rdparty/jvm/org/slf4j:jul_to_slf4j","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/org/slf4j/jul_to_slf4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/slf4j:log4j_over_slf4j": ["lang||||||java","name||||||//3rdparty/jvm/org/slf4j:log4j_over_slf4j","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/org/slf4j/log4j_over_slf4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/slf4j:slf4j_api": ["lang||||||java","name||||||//3rdparty/jvm/org/slf4j:slf4j_api","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/slf4j/slf4j_api","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/slf4j:slf4j_log4j12": ["lang||||||java","name||||||//3rdparty/jvm/org/slf4j:slf4j_log4j12","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/slf4j/slf4j_log4j12","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_aop": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_aop","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/springframework:spring_beans|||//external:jar/org/springframework/spring_aop","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_aspects": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_aspects","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/aspectj:aspectjweaver|||//external:jar/org/springframework/spring_aspects","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_beans": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_beans","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_core|||//external:jar/org/springframework/spring_beans","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_context": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_context","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/springframework/spring_context|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/springframework:spring_expression|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/springframework:spring_aop","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_context_support": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_context_support","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/springframework:spring_context|||//3rdparty/jvm/org/springframework:spring_core|||//external:jar/org/springframework/spring_context_support","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_core": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_jcl|||//external:jar/org/springframework/spring_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_expression": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_expression","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_core|||//external:jar/org/springframework/spring_expression","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_jcl": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_jcl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/springframework/spring_jcl","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_jdbc": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_jdbc","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/springframework:spring_tx|||//3rdparty/jvm/org/springframework:spring_beans|||//external:jar/org/springframework/spring_jdbc","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_messaging": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_messaging","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/springframework:spring_core|||//external:jar/org/springframework/spring_messaging","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_orm": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_orm","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_tx|||//external:jar/org/springframework/spring_orm|||//3rdparty/jvm/org/springframework:spring_jdbc|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/springframework:spring_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_test": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_test","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_core|||//external:jar/org/springframework/spring_test","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_tx": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_tx","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/springframework:spring_core|||//external:jar/org/springframework/spring_tx","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_web": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_web","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/springframework:spring_core|||//external:jar/org/springframework/spring_web","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework:spring_webmvc": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework:spring_webmvc","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_context|||//external:jar/org/springframework/spring_webmvc|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/org/springframework:spring_expression|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/springframework:spring_aop","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_context|||//3rdparty/jvm/org/springframework:spring_core|||//external:jar/org/springframework/boot/spring_boot","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_actuator": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_actuator","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot|||//external:jar/org/springframework/boot/spring_boot_actuator","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_actuator_autoconfigure": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_actuator_autoconfigure","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot_actuator|||//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure|||//3rdparty/jvm/org/springframework/boot:spring_boot|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//external:jar/org/springframework/boot/spring_boot_actuator_autoconfigure|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot|||//external:jar/org/springframework/boot/spring_boot_autoconfigure","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api|||//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure|||//3rdparty/jvm/org/yaml:snakeyaml|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/springframework/boot:spring_boot|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging|||//external:jar/org/springframework/boot/spring_boot_starter","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_actuator": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_actuator","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/io/micrometer:micrometer_core|||//3rdparty/jvm/org/springframework/boot:spring_boot_actuator_autoconfigure|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//external:jar/org/springframework/boot/spring_boot_starter_actuator","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/aspectj:aspectjweaver|||//3rdparty/jvm/org/springframework:spring_aop|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//external:jar/org/springframework/boot/spring_boot_starter_aop","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_data_jpa": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_data_jpa","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop|||//3rdparty/jvm/jakarta/transaction:jakarta_transaction_api|||//3rdparty/jvm/jakarta/persistence:jakarta_persistence_api|||//3rdparty/jvm/org/hibernate:hibernate_core|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jdbc|||//external:jar/org/springframework/boot/spring_boot_starter_data_jpa|||//3rdparty/jvm/org/springframework/data:spring_data_jpa|||//3rdparty/jvm/org/springframework:spring_aspects","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_jdbc": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jdbc","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_jdbc|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/com/zaxxer:HikariCP|||//external:jar/org/springframework/boot/spring_boot_starter_jdbc","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_jersey": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jersey","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/springframework/boot/spring_boot_starter_jersey|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_tomcat|||//3rdparty/jvm/org/glassfish/jersey/ext:jersey_bean_validation|||//3rdparty/jvm/org/glassfish/jersey/core:jersey_server|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet|||//3rdparty/jvm/org/glassfish/jersey/media:jersey_media_json_jackson|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_json|||//3rdparty/jvm/org/glassfish/jersey/containers:jersey_container_servlet_core|||//3rdparty/jvm/org/glassfish/jersey/ext:jersey_spring5","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_jetty": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_jetty","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/springframework/boot/spring_boot_starter_jetty|||//3rdparty/jvm/jakarta/servlet:jakarta_servlet_api|||//3rdparty/jvm/org/eclipse/jetty/websocket:websocket_server|||//3rdparty/jvm/jakarta/websocket:jakarta_websocket_api|||//3rdparty/jvm/org/eclipse/jetty/websocket:javax_websocket_server_impl|||//3rdparty/jvm/org/eclipse/jetty:jetty_webapp|||//3rdparty/jvm/org/eclipse/jetty:jetty_servlets|||//3rdparty/jvm/org/glassfish:jakarta_el","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_json": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_json","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/springframework/boot/spring_boot_starter_json|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jdk8|||//3rdparty/jvm/com/fasterxml/jackson/datatype:jackson_datatype_jsr310|||//3rdparty/jvm/com/fasterxml/jackson/module:jackson_module_parameter_names","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_logging","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/ch/qos/logback:logback_classic|||//3rdparty/jvm/org/apache/logging/log4j:log4j_to_slf4j|||//3rdparty/jvm/org/slf4j:jul_to_slf4j|||//external:jar/org/springframework/boot/spring_boot_starter_logging","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_test": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_test","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/jayway/jsonpath:json_path|||//3rdparty/jvm/org/hamcrest:hamcrest|||//3rdparty/jvm/org/junit/vintage:junit_vintage_engine|||//3rdparty/jvm/org/springframework:spring_test|||//3rdparty/jvm/jakarta/xml/bind:jakarta_xml_bind_api|||//3rdparty/jvm/org/xmlunit:xmlunit_core|||//external:jar/org/springframework/boot/spring_boot_starter_test|||//3rdparty/jvm/org/junit/jupiter:junit_jupiter|||//3rdparty/jvm/org/assertj:assertj_core|||//3rdparty/jvm/org/mockito:mockito_junit_jupiter|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/org/skyscreamer:jsonassert|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/springframework/boot:spring_boot_test|||//3rdparty/jvm/org/mockito:mockito_core|||//3rdparty/jvm/org/springframework/boot:spring_boot_test_autoconfigure","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_tomcat": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_tomcat","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/jakarta/annotation:jakarta_annotation_api|||//3rdparty/jvm/org/apache/tomcat/embed:tomcat_embed_core|||//3rdparty/jvm/org/apache/tomcat/embed:tomcat_embed_websocket|||//external:jar/org/springframework/boot/spring_boot_starter_tomcat|||//3rdparty/jvm/org/glassfish:jakarta_el","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_validation","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/hibernate/validator:hibernate_validator|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/org/glassfish:jakarta_el|||//external:jar/org/springframework/boot/spring_boot_starter_validation","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_starter_web": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_web","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_webmvc|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_tomcat|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_json|||//external:jar/org/springframework/boot/spring_boot_starter_web","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_test": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_test","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot|||//external:jar/org/springframework/boot/spring_boot_test","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/boot:spring_boot_test_autoconfigure": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/boot:spring_boot_test_autoconfigure","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot|||//3rdparty/jvm/org/springframework/boot:spring_boot_test|||//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure|||//external:jar/org/springframework/boot/spring_boot_test_autoconfigure","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_commons": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/security:spring_security_crypto|||//external:jar/org/springframework/cloud/spring_cloud_commons","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_config_client": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_config_client","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_annotations|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons|||//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure|||//3rdparty/jvm/org/springframework:spring_web|||//external:jar/org/springframework/cloud/spring_cloud_config_client|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/apache/httpcomponents:httpclient|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_context","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_context": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_context","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/security:spring_security_crypto|||//external:jar/org/springframework/cloud/spring_cloud_context","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_netflix_archaius": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_netflix_archaius","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/springframework/cloud/spring_cloud_netflix_archaius","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_netflix_ribbon": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_netflix_ribbon","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_netflix_archaius|||//external:jar/org/springframework/cloud/spring_cloud_netflix_ribbon","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_openfeign_core": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_openfeign_core","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop|||//3rdparty/jvm/org/springframework/boot:spring_boot_autoconfigure|||//3rdparty/jvm/io/github/openfeign/form:feign_form_spring|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_netflix_ribbon|||//external:jar/org/springframework/cloud/spring_cloud_openfeign_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_sleuth_core": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_sleuth_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_context|||//3rdparty/jvm/org/aspectj:aspectjrt|||//3rdparty/jvm/io/zipkin/brave:brave|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons|||//external:jar/org/springframework/cloud/spring_cloud_sleuth_core|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_rpc|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_rabbit|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_webmvc|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_spring_web|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpclient|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_messaging|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_clients|||//3rdparty/jvm/io/zipkin/brave:brave_context_slf4j|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_httpasyncclient|||//3rdparty/jvm/io/zipkin/reporter2:zipkin_reporter_metrics_micrometer|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_jms|||//3rdparty/jvm/io/zipkin/brave:brave_instrumentation_kafka_streams","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_starter": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/security:spring_security_rsa|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter|||//external:jar/org/springframework/cloud/spring_cloud_starter|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_context","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_config": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_config","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_config_client|||//3rdparty/jvm/com/fasterxml/jackson/core:jackson_databind|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter|||//external:jar/org/springframework/cloud/spring_cloud_starter_config","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_openfeign": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_openfeign","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/springframework/cloud/spring_cloud_starter_openfeign|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_openfeign_core|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_commons|||//3rdparty/jvm/io/github/openfeign:feign_core|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter|||//3rdparty/jvm/io/github/openfeign:feign_hystrix|||//3rdparty/jvm/org/springframework:spring_web|||//3rdparty/jvm/io/github/openfeign:feign_slf4j","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter_sleuth","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_starter|||//3rdparty/jvm/org/springframework/boot:spring_boot_starter_aop|||//3rdparty/jvm/org/springframework/cloud:spring_cloud_sleuth_core|||//external:jar/org/springframework/cloud/spring_cloud_starter_sleuth","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/data:spring_data_commons": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/data:spring_data_commons","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/org/springframework/data/spring_data_commons","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/data:spring_data_jpa": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/data:spring_data_jpa","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_context|||//3rdparty/jvm/org/aspectj:aspectjrt|||//3rdparty/jvm/org/springframework:spring_tx|||//3rdparty/jvm/org/springframework/data:spring_data_commons|||//3rdparty/jvm/org/springframework:spring_orm|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/springframework:spring_aop|||//external:jar/org/springframework/data/spring_data_jpa","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/plugin:spring_plugin_core": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/plugin:spring_plugin_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_context|||//external:jar/org/springframework/plugin/spring_plugin_core|||//3rdparty/jvm/org/springframework:spring_beans|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/springframework:spring_aop","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/plugin:spring_plugin_metadata","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//3rdparty/jvm/org/springframework/plugin:spring_plugin_core|||//external:jar/org/springframework/plugin/spring_plugin_metadata","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/retry:spring_retry": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/retry:spring_retry","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/springframework/retry/spring_retry","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/security:spring_security_crypto": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/security:spring_security_crypto","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/springframework/security/spring_security_crypto","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/security:spring_security_rsa": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/security:spring_security_rsa","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework/security:spring_security_crypto|||//3rdparty/jvm/org/springframework:spring_core|||//3rdparty/jvm/org/bouncycastle:bcpkix_jdk15on|||//external:jar/org/springframework/security/spring_security_rsa","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/session:spring_session_core": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/session:spring_session_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_jcl|||//external:jar/org/springframework/session/spring_session_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/springframework/statemachine:spring_statemachine_core": ["lang||||||java","name||||||//3rdparty/jvm/org/springframework/statemachine:spring_statemachine_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/springframework:spring_tx|||//3rdparty/jvm/org/springframework:spring_messaging|||//external:jar/org/springframework/statemachine/spring_statemachine_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/xerial/snappy:snappy_java": ["lang||||||java","name||||||//3rdparty/jvm/org/xerial/snappy:snappy_java","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/xerial/snappy/snappy_java","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/xmlunit:xmlunit_core": ["lang||||||java","name||||||//3rdparty/jvm/org/xmlunit:xmlunit_core","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/xmlunit/xmlunit_core","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/yaml:snakeyaml": ["lang||||||java","name||||||//3rdparty/jvm/org/yaml:snakeyaml","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/org/yaml/snakeyaml","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/zeroturnaround:zt_zip": ["lang||||||java","name||||||//3rdparty/jvm/org/zeroturnaround:zt_zip","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/org/slf4j:slf4j_api|||//external:jar/org/zeroturnaround/zt_zip","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/xalan:xalan": ["lang||||||java","name||||||//3rdparty/jvm/xalan:xalan","visibility||||||//3rdparty/jvm:__subpackages__","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/xml_apis:xml_apis|||//external:jar/xalan/xalan","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/xerces:xercesImpl": ["lang||||||java","name||||||//3rdparty/jvm/xerces:xercesImpl","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//3rdparty/jvm/xml_apis:xml_apis|||//external:jar/xerces/xercesImpl","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/xml_apis:xml_apis": ["lang||||||java","name||||||//3rdparty/jvm/xml_apis:xml_apis","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/xml_apis/xml_apis","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/xml_apis:xml_apis_ext": ["lang||||||java","name||||||//3rdparty/jvm/xml_apis:xml_apis_ext","visibility||||||//visibility:public","kind||||||library","deps|||L|||","jars|||L|||","sources|||L|||","exports|||L|||//external:jar/xml_apis/xml_apis_ext","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/scala_lang:scala_library": ["lang||||||scala/unmangled:2.11.11","name||||||//3rdparty/jvm/org/scala_lang:scala_library","visibility||||||//visibility:public","kind||||||import","deps|||L|||","jars|||L|||//external:jar/org/scala_lang/scala_library","sources|||L|||","exports|||L|||","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"],
"3rdparty/jvm/org/scala_lang:scala_reflect": ["lang||||||scala/unmangled:2.11.11","name||||||//3rdparty/jvm/org/scala_lang:scala_reflect","visibility||||||//visibility:public","kind||||||import","deps|||L|||","jars|||L|||//external:jar/org/scala_lang/scala_reflect","sources|||L|||","exports|||L|||//3rdparty/jvm/org/scala_lang:scala_library","runtimeDeps|||L|||","processorClasses|||L|||","generatesApi|||B|||false","licenses|||L|||","generateNeverlink|||B|||false"]
 }


def build_external_workspace(name):
  return build_external_workspace_from_opts(name = name, target_configs = list_target_data(), separator = list_target_data_separator(), build_header = build_header())

